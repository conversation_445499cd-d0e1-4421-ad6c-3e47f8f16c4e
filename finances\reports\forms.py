from django import forms

from students.utils.date_utils import get_months


class GetMothnlyReportForm(forms.Form):
    month_choices = get_months()
    month_field = forms.ChoiceField(
        choices=month_choices,
        label="Select Month",
        widget=forms.Select(
            attrs={
                'class': 'border-2 rounded outline-none py-1 px-4',
            }
        )
    )


class DatePicker(forms.Form):
    start_date = forms.DateField(
        widget=forms.DateInput(
            attrs={
                "type": "date",
                "class": "date-picker",
            }
        )
    )
    end_date = forms.DateField(
        widget=forms.DateInput(
            attrs={
                "type": "date",
                "class": "date-picker",
            }
        )
    )
