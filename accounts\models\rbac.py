from django.db import models
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


User = get_user_model()


class Permission(models.Model):
    """
    Represents a specific permission that can be granted to roles.
    Permissions are granular actions like 'view_student', 'edit_finance', etc.
    """
    name = models.CharField(
        max_length=100, 
        unique=True,
        help_text=_("Unique permission name (e.g., 'view_student', 'edit_finance')")
    )
    display_name = models.CharField(
        max_length=200,
        help_text=_("Human-readable permission name")
    )
    description = models.TextField(
        blank=True,
        help_text=_("Detailed description of what this permission allows")
    )
    category = models.CharField(
        max_length=50,
        help_text=_("Permission category (e.g., 'students', 'finances', 'academics')")
    )
    is_active = models.BooleanField(
        default=True,
        help_text=_("Whether this permission is currently active")
    )
    created_at = models.DateTime<PERSON>ield(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category', 'name']
        verbose_name = _("Permission")
        verbose_name_plural = _("Permissions")

    def __str__(self):
        return f"{self.display_name} ({self.name})"


class Role(models.Model):
    """
    Represents a role that can be assigned to users.
    Roles contain multiple permissions and define what users can do.
    """
    ROLE_LEVELS = [
        (1, _('Student')),
        (2, _('Teacher')),
        (3, _('Academic Coordinator')),
        (4, _('Finance Manager')),
        (5, _('Administrator')),
        (6, _('Super Administrator')),
    ]

    name = models.CharField(
        max_length=100,
        unique=True,
        help_text=_("Unique role name")
    )
    display_name = models.CharField(
        max_length=200,
        help_text=_("Human-readable role name")
    )
    description = models.TextField(
        blank=True,
        help_text=_("Description of the role and its responsibilities")
    )
    level = models.IntegerField(
        choices=ROLE_LEVELS,
        default=1,
        help_text=_("Role hierarchy level (higher numbers have more privileges)")
    )
    permissions = models.ManyToManyField(
        Permission,
        blank=True,
        related_name='roles',
        help_text=_("Permissions granted to this role")
    )
    is_active = models.BooleanField(
        default=True,
        help_text=_("Whether this role is currently active")
    )
    is_default = models.BooleanField(
        default=False,
        help_text=_("Whether this is a default role for new users")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['level', 'name']
        verbose_name = _("Role")
        verbose_name_plural = _("Roles")

    def __str__(self):
        return self.display_name

    def clean(self):
        """Validate role data"""
        super().clean()
        
        # Only one default role per level
        if self.is_default:
            existing_default = Role.objects.filter(
                level=self.level, 
                is_default=True
            ).exclude(pk=self.pk)
            if existing_default.exists():
                raise ValidationError(
                    _("Only one default role is allowed per level.")
                )

    def get_all_permissions(self):
        """
        Get all permissions for this role, including inherited permissions
        from lower-level roles if inheritance is enabled.
        """
        permissions = set(self.permissions.filter(is_active=True))
        
        # Add permissions from lower-level roles (inheritance)
        lower_roles = Role.objects.filter(
            level__lt=self.level,
            is_active=True
        )
        for role in lower_roles:
            permissions.update(role.permissions.filter(is_active=True))
        
        return list(permissions)

    def has_permission(self, permission_name):
        """Check if this role has a specific permission"""
        return self.permissions.filter(
            name=permission_name,
            is_active=True
        ).exists()


class UserRole(models.Model):
    """
    Represents the assignment of roles to users.
    Users can have multiple roles with different contexts.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='user_roles'
    )
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
        related_name='user_assignments'
    )
    is_active = models.BooleanField(
        default=True,
        help_text=_("Whether this role assignment is currently active")
    )
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='role_assignments_made',
        help_text=_("User who assigned this role")
    )
    assigned_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_("When this role assignment expires (optional)")
    )
    notes = models.TextField(
        blank=True,
        help_text=_("Additional notes about this role assignment")
    )

    class Meta:
        unique_together = ['user', 'role']
        ordering = ['-assigned_at']
        verbose_name = _("User Role Assignment")
        verbose_name_plural = _("User Role Assignments")

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.role.display_name}"

    def clean(self):
        """Validate user role assignment"""
        super().clean()

        # Skip validation for unsaved instances or when user/role are not set
        if not self.user or not self.role:
            return

        # Skip validation if user or role are not saved yet
        if not hasattr(self.user, 'pk') or not self.user.pk:
            return
        if not hasattr(self.role, 'pk') or not self.role.pk:
            return

        # Check if user already has this role (only for new assignments)
        if self.pk is None:  # New assignment
            existing = UserRole.objects.filter(
                user=self.user,
                role=self.role,
                is_active=True
            )
            if existing.exists():
                raise ValidationError(
                    _("User already has this role assigned.")
                )

    def is_expired(self):
        """Check if this role assignment has expired"""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False
