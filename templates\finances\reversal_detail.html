{% extends 'base.html' %} {% load static %} {% load humanize%}
<!-- title -->
{% block title %}Reversal {{ reversal.voucher }} | {% endblock %}
<!-- body -->
{% block content %}
<section class="w-full max-w-6xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex items-center gap-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-xl icon-float"
      >
        <i class="fas fa-undo text-white text-2xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
        >
          Reversal Details
        </h1>
        <p class="text-[#40657F] text-lg font-medium">{{ reversal.voucher }}</p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
  </div>

  <!-- Reversal Information -->
  <div class="card-modern p-8 reversal-info-fade-in">
    <h2 class="font-bold text-xl text-[#2C3E50] mb-6 flex items-center gap-3">
      <i class="fas fa-info-circle text-[#7AB2D3] section-icon-float"></i>
      Reversal Information
    </h2>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="space-y-4">
        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Voucher Number</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">{{ reversal.voucher }}</p>
        </div>

        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Date</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">{{ reversal.date }}</p>
        </div>

        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Reversed By</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            {{ reversal.created_by.get_full_name }}
          </p>
        </div>
      </div>

      <div class="space-y-4">
        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Status</label
          >
          <span
            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-[#F28C8C]/20 text-[#F28C8C]"
          >
            <i class="fas fa-undo mr-2"></i>
            Reversal Entry
          </span>
        </div>

        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Created At</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            {{ reversal.created_at|date:"M d, Y" }}
          </p>
        </div>

        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Term</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            {{ reversal.term.term_name }} of
            {{reversal.term.academic_year.name}}
          </p>
        </div>
      </div>
    </div>

    {% if reversal.reason %}

    <div class="mt-6 bg-[#E2F1F9] rounded-xl p-4">
      <label class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
        >Reversal Reason</label
      >
      <p class="text-[#2C3E50] mt-2">{{ reversal.reason }}</p>
    </div>
    {% endif %} {% if reversal.description %}
    <div class="mt-6 bg-[#E2F1F9] rounded-xl p-4">
      <label class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
        >Description</label
      >
      <p class="text-[#2C3E50] mt-2">{{ reversal.description }}</p>
    </div>
    {% endif %}
  </div>

  <!-- Original Transaction -->
  {% if original_transaction %}
  <div class="card-modern p-8 original-transaction-slide-in">
    <h2 class="font-bold text-xl text-[#2C3E50] mb-6 flex items-center gap-3">
      <i class="fas fa-file-invoice text-[#7AB2D3] section-icon-float"></i>
      Original Transaction
    </h2>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="space-y-4">
        <div class="bg-[#74C69D]/10 rounded-xl p-4 border border-[#74C69D]/30">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Original Voucher</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            {{ original_transaction.voucher }}
          </p>
        </div>

        <div class="bg-[#74C69D]/10 rounded-xl p-4 border border-[#74C69D]/30">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Original Date</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            {{ original_transaction.date }}
          </p>
        </div>
      </div>

      <div class="space-y-4">
        <div class="bg-[#74C69D]/10 rounded-xl p-4 border border-[#74C69D]/30">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Status</label
          >
          <span
            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-[#F28C8C]/20 text-[#F28C8C]"
          >
            <i class="fas fa-lock mr-2"></i>
            Locked (Reversed)
          </span>
        </div>

        <div class="bg-[#74C69D]/10 rounded-xl p-4 border border-[#74C69D]/30">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Created By</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            {{ original_transaction.created_by.get_full_name}}
          </p>
        </div>
      </div>
    </div>

    {% if original_transaction.description %}
    <div class="mt-6 bg-[#74C69D]/10 rounded-xl p-4 border border-[#74C69D]/30">
      <label class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
        >Original Description</label
      >
      <p class="text-[#2C3E50] mt-2">{{ original_transaction.description }}</p>
    </div>
    {% endif %}
  </div>
  {% endif %}

  <!-- Journal Lines -->
  <div class="card-modern p-8">
    <h2 class="font-bold text-xl text-[#2C3E50] mb-6 flex items-center gap-3">
      <i class="fas fa-list text-[#7AB2D3]"></i>
      Reversal Journal Lines
    </h2>

    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              Account
            </th>
            <th
              class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              Description
            </th>
            <th
              class="px-6 py-4 text-right text-xs font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              Debit
            </th>
            <th
              class="px-6 py-4 text-right text-xs font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              Credit
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#E2F1F9]">
          {% for line in reversal.journalline_set.all %}
          <tr class="hover:bg-[#E2F1F9]/50 transition-colors duration-200">
            <td class="px-6 py-4">
              <div class="font-bold text-[#2C3E50]">
                {{ line.account.name }}
              </div>
              <div class="text-sm text-[#40657F]">{{ line.account.code }}</div>
            </td>
            <td class="px-6 py-4">
              <div class="text-[#2C3E50]">
                {{ line.description|truncatewords:10 }}
              </div>
            </td>
            <td class="px-6 py-4 text-right">
              {% if line.line_type == 'Debit' %}
              <span class="font-bold text-[#F28C8C]"
                >MWK {{ line.amount|intcomma }}</span
              >
              {% else %}
              <span class="text-[#B9D8EB]">-</span>
              {% endif %}
            </td>
            <td class="px-6 py-4 text-right">
              {% if line.line_type == 'Credit' %}
              <span class="font-bold text-[#74C69D]"
                >MWK {{ line.amount|intcomma }}</span
              >
              {% else %}
              <span class="text-[#B9D8EB]">-</span>
              {% endif %}
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>

  <!-- Actions -->
  <div class="flex gap-4 actions-slide-in">
    <a
      href="{% url 'finances:reversal_list' %}"
      class="inline-flex items-center gap-3 bg-[#7AB2D3] text-white font-bold py-4 px-8 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl group"
    >
      <i
        class="fas fa-arrow-left group-hover:-translate-x-1 transition-all duration-300"
      ></i>
      <span>Back to Reversals</span>
    </a>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .reversal-info-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: reversalInfoFadeIn 0.8s ease-out 0.8s forwards;
  }

  .original-transaction-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: originalTransactionSlideIn 0.8s ease-out 1s forwards;
  }

  .actions-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionsSlideIn 0.8s ease-out 1.2s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 3s ease-in-out infinite;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes reversalInfoFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes originalTransactionSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes actionsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes sectionIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(3deg);
    }
  }
</style>
{% endblock %}
