# Development Setup Guide

## 🚀 Getting Started

This guide provides comprehensive instructions for setting up the Receipt Generator development environment, including all dependencies, configuration, and best practices.

## 📋 Prerequisites

### Required Software
- **Python 3.8+**: Main backend language
- **Node.js 16+**: For Tailwind CSS compilation
- **Git**: Version control
- **PostgreSQL 12+**: Production database (optional for development)
- **VS Code**: Recommended IDE with extensions

### Recommended VS Code Extensions
- Python
- Django
- Tailwind CSS IntelliSense
- Prettier - Code formatter
- GitLens
- Auto Rename Tag
- Bracket Pair Colorizer

## 🛠️ Environment Setup

### 1. Clone and Navigate
```bash
git clone <repository-url>
cd receipt_gen
```

### 2. Python Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt
```

### 3. Node.js Dependencies
```bash
# Install Node.js packages
npm install

# Verify Tailwind CSS installation
npx tailwindcss --help
```

### 4. Environment Configuration

Create a `.env` file in the project root:

```env
# Django Configuration
DJANGO_SECRET_KEY=your-super-secret-key-here
DEBUG=True
DEV_MODE=True
DJANGO_ALLOWED_HOST=localhost,127.0.0.1,0.0.0.0

# Database Configuration (Development)
# Leave DATABASE_URL empty to use SQLite in development
DATABASE_URL=

# Database Configuration (Production)
# DATABASE_URL=postgresql://username:password@localhost:5432/receipt_gen

# Email Configuration (Optional)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Security Settings
SECURE_SSL_REDIRECT=False
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True
```

### 5. Database Setup

#### Development (SQLite)
```bash
# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Load sample data (optional)
python manage.py loaddata fixtures/sample_data.json
```

#### Production (PostgreSQL)
```bash
# Create database
createdb receipt_gen

# Update .env with DATABASE_URL
# DATABASE_URL=postgresql://username:password@localhost:5432/receipt_gen

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

### 6. Static Files Setup

```bash
# Compile Tailwind CSS (development with watch)
npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css --watch

# Or compile once
npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css

# Collect static files (production)
python manage.py collectstatic
```

## 🏃‍♂️ Running the Development Server

### Start Development Server
```bash
# Activate virtual environment
source venv/bin/activate  # or venv\Scripts\activate on Windows

# Start Django development server
python manage.py runserver

# In another terminal, watch Tailwind CSS changes
npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css --watch
```

### Access the Application
- **Main Application**: http://127.0.0.1:8000
- **Admin Interface**: http://127.0.0.1:8000/admin
- **API Endpoints**: http://127.0.0.1:8000/api/ (if applicable)

## 🔧 Development Tools

### Django Management Commands

```bash
# Create new Django app
python manage.py startapp app_name

# Make migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic

# Run tests
python manage.py test

# Shell access
python manage.py shell

# Database shell
python manage.py dbshell
```

### Custom Management Commands

```bash
# Reset database (custom command)
python manage.py reset_db

# Import sample data
python manage.py import_sample_data

# Generate test receipts
python manage.py generate_test_receipts

# Cleanup old sessions
python manage.py clearsessions
```

## 🎨 Frontend Development

### Tailwind CSS Workflow

```bash
# Watch for changes (recommended during development)
npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css --watch

# Build for production (minified)
npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css --minify

# Build with specific config
npx tailwindcss -c ./tailwind.config.js -i ./src/tailwind.css -o ./assets/css/tailwind.css
```

### JavaScript Development

```bash
# Install additional packages
npm install package-name

# Update packages
npm update

# Audit for vulnerabilities
npm audit
npm audit fix
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test students

# Run specific test class
python manage.py test students.tests.StudentModelTest

# Run with coverage
pip install coverage
coverage run --source='.' manage.py test
coverage report
coverage html
```

### Test Database

```bash
# Create test database
python manage.py test --keepdb

# Reset test database
python manage.py flush --database=test
```

## 🐛 Debugging

### Django Debug Toolbar (Optional)

```bash
# Install debug toolbar
pip install django-debug-toolbar

# Add to INSTALLED_APPS in settings/development.py
'debug_toolbar',

# Add to MIDDLEWARE
'debug_toolbar.middleware.DebugToolbarMiddleware',

# Configure in settings
INTERNAL_IPS = ['127.0.0.1']
```

### Logging Configuration

Add to your settings:

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'debug.log',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

## 📦 Package Management

### Python Dependencies

```bash
# Install new package
pip install package-name

# Update requirements.txt
pip freeze > requirements.txt

# Install from requirements.txt
pip install -r requirements.txt

# Upgrade package
pip install --upgrade package-name
```

### Node.js Dependencies

```bash
# Install new package
npm install package-name

# Install as dev dependency
npm install --save-dev package-name

# Update package.json
npm update

# Remove package
npm uninstall package-name
```

## 🚀 Deployment Preparation

### Production Settings

```bash
# Set environment variables
export DEBUG=False
export DEV_MODE=False
export DJANGO_ALLOWED_HOST=yourdomain.com

# Collect static files
python manage.py collectstatic --noinput

# Compile CSS for production
npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css --minify
```

### Security Checklist

```bash
# Run Django security check
python manage.py check --deploy

# Update secret key
python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
```

## 🔍 Troubleshooting

### Common Issues

1. **Virtual Environment Issues**
   ```bash
   # Recreate virtual environment
   rm -rf venv
   python -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

2. **Database Issues**
   ```bash
   # Reset migrations
   python manage.py migrate --fake-initial
   
   # Reset database
   rm db.sqlite3
   python manage.py migrate
   ```

3. **Static Files Issues**
   ```bash
   # Clear static files
   rm -rf static/*
   python manage.py collectstatic
   ```

4. **Tailwind CSS Not Updating**
   ```bash
   # Clear Tailwind cache
   rm -rf node_modules/.cache
   npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css --watch
   ```

### Getting Help

- Check the [Administration Task Guide](administration-task-guide.md)
- Review the [Template Modernization Guide](template-modernization-guide.md)
- Create an issue in the repository
- Check Django documentation: https://docs.djangoproject.com/
- Check Tailwind CSS documentation: https://tailwindcss.com/docs

## 📚 Next Steps

1. Read the [Feature Development Guide](feature-development-guide.md)
2. Review the [Database Schema Guide](database-schema-guide.md)
3. Check the [API Development Guide](api-development-guide.md)
4. Explore the [Testing Guide](testing-guide.md)

---

**Happy Coding!** 🎉
