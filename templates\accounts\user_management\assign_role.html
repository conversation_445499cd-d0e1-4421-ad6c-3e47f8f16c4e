{% extends 'base.html' %}
{% load static %}
{% load rbac_tags %}

{% block title %}Assign Role to {{ user_obj.get_full_name|default:user_obj.username }} | {% endblock %}

{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div class="w-12 h-12 bg-gradient-to-br from-[#FFB84D] to-[#e6a43d] rounded-2xl flex items-center justify-center shadow-lg icon-float">
        <i class="fas fa-user-plus text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
          Assign Role
        </h1>
        <div class="w-20 h-1 bg-gradient-to-r from-[#FFB84D] to-[#e6a43d] rounded-full accent-line-grow"></div>
        <p class="text-[#40657F] mt-2">Assign a new role to {{ user_obj.get_full_name|default:user_obj.username }}</p>
      </div>
    </div>
    
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <a href="{% url 'accounts:user_management' %}" class="text-[#40657F] hover:text-[#7AB2D3]">User Management</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a href="{% url 'accounts:user_detail' user_obj.id %}" class="text-[#40657F] hover:text-[#7AB2D3]">{{ user_obj.get_full_name|default:user_obj.username }}</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Assign Role</span>
    </nav>
  </div>

  <!-- User Info Card -->
  <div class="card-modern p-6">
    <div class="flex items-center gap-4">
      <div class="w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg">
        <span class="text-white text-xl font-bold">
          {{ user_obj.first_name|first|upper }}{{ user_obj.last_name|first|upper }}
        </span>
      </div>
      <div class="flex-1">
        <h3 class="text-xl font-bold text-[#2C3E50]">{{ user_obj.get_full_name|default:user_obj.username }}</h3>
        <p class="text-[#40657F]">@{{ user_obj.username }} • {{ user_obj.email|default:"No email" }}</p>
        <div class="flex items-center gap-2 mt-2">
          <span class="text-sm text-[#40657F]">Current Role Level:</span>
          <span class="px-2 py-1 bg-[#7AB2D3] text-white rounded text-sm font-semibold">
            Level {{ user_obj.get_highest_role_level }}
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Current Roles -->
  {% if user_obj.get_active_roles %}
  <div class="card-modern p-6">
    <div class="flex items-center gap-3 mb-4">
      <div class="w-8 h-8 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-lg flex items-center justify-center">
        <i class="fas fa-shield-alt text-white text-sm"></i>
      </div>
      <h4 class="text-lg font-bold text-[#2C3E50]">Current Roles</h4>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
      {% for user_role in user_obj.get_active_roles %}
      <div class="p-3 border border-[#E2F1F9] rounded-lg bg-[#F7FAFC]">
        <div class="flex items-center justify-between">
          <div>
            <div class="font-semibold text-[#2C3E50]">{{ user_role.role.display_name }}</div>
            <div class="text-sm text-[#40657F]">Level {{ user_role.role.level }}</div>
          </div>
          {% if user_role.expires_at %}
            <span class="px-2 py-1 bg-[#FFB84D] text-white rounded text-xs">
              Expires {{ user_role.expires_at|date:"M d" }}
            </span>
          {% else %}
            <span class="px-2 py-1 bg-[#74C69D] text-white rounded text-xs">
              Permanent
            </span>
          {% endif %}
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
  {% endif %}

  <!-- Assignment Form -->
  <div class="card-modern p-8">
    <form method="post" class="space-y-8">
      {% csrf_token %}
      
      <!-- Role Selection -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-[#9B59B6] to-[#8e44ad] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-user-shield text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">Select Role</h3>
            <p class="text-[#40657F] text-sm">Choose a role to assign to this user</p>
          </div>
        </div>
        
        <div>
          <label for="{{ form.role.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-3">
            Available Roles <span class="text-[#F28C8C]">*</span>
          </label>
          
          <div class="space-y-3">
            {% for role in available_roles %}
            <label class="flex items-center p-4 border border-[#B9D8EB] rounded-xl hover:bg-[#F7FAFC] cursor-pointer transition-colors">
              <input type="radio" name="role" value="{{ role.id }}" 
                     class="w-4 h-4 text-[#7AB2D3] border-[#B9D8EB] focus:ring-[#7AB2D3] mr-4">
              <div class="flex-1">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="font-semibold text-[#2C3E50]">{{ role.display_name }}</div>
                    <div class="text-sm text-[#40657F]">{{ role.description|truncatechars:80 }}</div>
                  </div>
                  <div class="text-right">
                    <span class="px-3 py-1 bg-[#7AB2D3] text-white rounded-full text-sm font-semibold">
                      Level {{ role.level }}
                    </span>
                  </div>
                </div>
              </div>
            </label>
            {% endfor %}
          </div>
          
          {% if form.role.errors %}
            <div class="mt-2 text-sm text-[#F28C8C]">{{ form.role.errors.0 }}</div>
          {% endif %}
        </div>
      </div>

      <!-- Assignment Details -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-calendar-alt text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">Assignment Details</h3>
            <p class="text-[#40657F] text-sm">Configure the role assignment settings</p>
          </div>
        </div>
        
        <div>
          <label for="{{ form.expires_at.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
            Expiration Date (Optional)
          </label>
          {{ form.expires_at }}
          {% if form.expires_at.errors %}
            <div class="mt-1 text-sm text-[#F28C8C]">{{ form.expires_at.errors.0 }}</div>
          {% endif %}
          {% if form.expires_at.help_text %}
            <div class="mt-1 text-xs text-[#40657F]">{{ form.expires_at.help_text }}</div>
          {% endif %}
        </div>
        
        <div>
          <label for="{{ form.notes.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
            Assignment Notes (Optional)
          </label>
          {{ form.notes }}
          {% if form.notes.errors %}
            <div class="mt-1 text-sm text-[#F28C8C]">{{ form.notes.errors.0 }}</div>
          {% endif %}
        </div>
      </div>

      <!-- Assignment Preview -->
      <div class="p-6 bg-[#E2F1F9] rounded-xl border border-[#B9D8EB]">
        <div class="flex items-center gap-3 mb-3">
          <i class="fas fa-info-circle text-[#7AB2D3]"></i>
          <h4 class="font-semibold text-[#2C3E50]">Assignment Summary</h4>
        </div>
        <div class="text-sm text-[#40657F] space-y-1">
          <p><strong>User:</strong> {{ user_obj.get_full_name|default:user_obj.username }} (@{{ user_obj.username }})</p>
          <p><strong>Assigned by:</strong> {{ user.get_full_name|default:user.username }}</p>
          <p><strong>Assignment Date:</strong> {{ "now"|date:"M d, Y H:i" }}</p>
          <p id="role-preview" class="hidden"><strong>Selected Role:</strong> <span id="role-name"></span> (Level <span id="role-level"></span>)</p>
          <p id="expiry-preview" class="hidden"><strong>Expires:</strong> <span id="expiry-date">Never</span></p>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-between pt-6 border-t border-[#E2F1F9]">
        <a href="{% url 'accounts:user_detail' user_obj.id %}" 
           class="px-6 py-3 border border-[#B9D8EB] text-[#40657F] rounded-xl font-semibold hover:bg-[#F7FAFC] transition-colors">
          <i class="fas fa-arrow-left mr-2"></i>Cancel
        </a>
        
        <button type="submit" 
                class="px-8 py-3 bg-gradient-to-r from-[#FFB84D] to-[#e6a43d] text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
          <i class="fas fa-user-plus mr-2"></i>Assign Role
        </button>
      </div>
    </form>
  </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitButton = form.querySelector('button[type="submit"]');
    const roleRadios = document.querySelectorAll('input[name="role"]');
    const expiresInput = document.getElementById('{{ form.expires_at.id_for_label }}');
    
    // Role selection preview
    roleRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                const roleLabel = this.closest('label');
                const roleName = roleLabel.querySelector('.font-semibold').textContent;
                const roleLevel = roleLabel.querySelector('.bg-\\[\\#7AB2D3\\]').textContent.replace('Level ', '');
                
                document.getElementById('role-name').textContent = roleName;
                document.getElementById('role-level').textContent = roleLevel;
                document.getElementById('role-preview').classList.remove('hidden');
            }
        });
    });
    
    // Expiration date preview
    if (expiresInput) {
        expiresInput.addEventListener('change', function() {
            const expiryPreview = document.getElementById('expiry-preview');
            const expiryDate = document.getElementById('expiry-date');
            
            if (this.value) {
                const date = new Date(this.value);
                expiryDate.textContent = date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                expiryPreview.classList.remove('hidden');
            } else {
                expiryDate.textContent = 'Never';
                expiryPreview.classList.add('hidden');
            }
        });
    }
    
    // Form submission
    form.addEventListener('submit', function() {
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Assigning Role...';
        submitButton.disabled = true;
    });
});
</script>

{% endblock %}
