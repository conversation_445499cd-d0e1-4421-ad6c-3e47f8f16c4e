# API Development Guide

## 🚀 Overview

This guide provides comprehensive instructions for developing and maintaining APIs in the Receipt Generator system, including REST endpoints, authentication, and best practices.

## 🏗️ API Architecture

### Design Principles
- **RESTful Design**: Follow REST conventions for resource-based URLs
- **Consistent Response Format**: Standardized JSON responses
- **Authentication Required**: All endpoints require authentication
- **Pagination**: Large datasets are paginated
- **Error Handling**: Comprehensive error responses with proper HTTP status codes

### URL Structure
```
/api/v1/{app}/{resource}/
/api/v1/{app}/{resource}/{id}/
/api/v1/{app}/{resource}/{id}/{action}/
```

## 🔐 Authentication & Authorization

### Authentication Methods
```python
# views.py
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json

@login_required
@require_http_methods(["GET"])
def api_endpoint(request):
    """API endpoint with authentication"""
    # Your API logic here
    return JsonResponse({'status': 'success'})
```

### Permission Classes
```python
# permissions.py
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse

class APIPermissionMixin(LoginRequiredMixin):
    """Base mixin for API permissions"""
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return JsonResponse({
                'error': 'Authentication required',
                'code': 'AUTHENTICATION_REQUIRED'
            }, status=401)
        return super().dispatch(request, *args, **kwargs)

class StaffRequiredMixin(APIPermissionMixin):
    """Require staff permissions"""
    
    def dispatch(self, request, *args, **kwargs):
        response = super().dispatch(request, *args, **kwargs)
        if isinstance(response, JsonResponse):
            return response
            
        if not request.user.is_staff:
            return JsonResponse({
                'error': 'Staff permissions required',
                'code': 'INSUFFICIENT_PERMISSIONS'
            }, status=403)
        return super(APIPermissionMixin, self).dispatch(request, *args, **kwargs)
```

## 📊 Response Format Standards

### Success Response
```json
{
    "status": "success",
    "data": {
        "id": 1,
        "name": "Example",
        "created_at": "2024-01-01T12:00:00Z"
    },
    "meta": {
        "timestamp": "2024-01-01T12:00:00Z",
        "version": "1.0"
    }
}
```

### List Response with Pagination
```json
{
    "status": "success",
    "data": [
        {
            "id": 1,
            "name": "Example 1"
        },
        {
            "id": 2,
            "name": "Example 2"
        }
    ],
    "pagination": {
        "page": 1,
        "per_page": 25,
        "total": 100,
        "total_pages": 4,
        "has_next": true,
        "has_previous": false
    },
    "meta": {
        "timestamp": "2024-01-01T12:00:00Z",
        "version": "1.0"
    }
}
```

### Error Response
```json
{
    "status": "error",
    "error": {
        "message": "Validation failed",
        "code": "VALIDATION_ERROR",
        "details": {
            "name": ["This field is required"],
            "email": ["Enter a valid email address"]
        }
    },
    "meta": {
        "timestamp": "2024-01-01T12:00:00Z",
        "version": "1.0"
    }
}
```

## 🛠️ API Implementation

### Base API View
```python
# api/base.py
from django.http import JsonResponse
from django.views import View
from django.core.paginator import Paginator
from django.utils import timezone
import json

class BaseAPIView(View):
    """Base class for API views"""
    
    def dispatch(self, request, *args, **kwargs):
        """Override to handle JSON parsing and common headers"""
        if request.content_type == 'application/json':
            try:
                request.json = json.loads(request.body)
            except json.JSONDecodeError:
                return self.error_response(
                    'Invalid JSON format',
                    'INVALID_JSON',
                    status=400
                )
        else:
            request.json = {}
        
        return super().dispatch(request, *args, **kwargs)
    
    def success_response(self, data=None, status=200, meta=None):
        """Standard success response"""
        response_data = {
            'status': 'success',
            'meta': {
                'timestamp': timezone.now().isoformat(),
                'version': '1.0',
                **(meta or {})
            }
        }
        
        if data is not None:
            response_data['data'] = data
            
        return JsonResponse(response_data, status=status)
    
    def error_response(self, message, code=None, details=None, status=400):
        """Standard error response"""
        response_data = {
            'status': 'error',
            'error': {
                'message': message,
                'code': code or 'UNKNOWN_ERROR'
            },
            'meta': {
                'timestamp': timezone.now().isoformat(),
                'version': '1.0'
            }
        }
        
        if details:
            response_data['error']['details'] = details
            
        return JsonResponse(response_data, status=status)
    
    def paginated_response(self, queryset, page_number=1, per_page=25, serializer_func=None):
        """Paginated response helper"""
        paginator = Paginator(queryset, per_page)
        page_obj = paginator.get_page(page_number)
        
        if serializer_func:
            data = [serializer_func(item) for item in page_obj]
        else:
            data = list(page_obj.object_list.values())
        
        return self.success_response(
            data=data,
            meta={
                'pagination': {
                    'page': page_obj.number,
                    'per_page': per_page,
                    'total': paginator.count,
                    'total_pages': paginator.num_pages,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                }
            }
        )
```

### Model Serializers
```python
# api/serializers.py
from django.utils import timezone

class StudentSerializer:
    """Serialize student data for API responses"""
    
    @staticmethod
    def serialize(student):
        return {
            'id': student.id,
            'student_id': student.student_id,
            'name': student.name,
            'gender': student.gender,
            'level': {
                'id': student.level.id,
                'name': student.level.level_name,
                'abbreviation': student.level.abbrv
            } if student.level else None,
            'is_active': student.is_active,
            'created_at': student.created_at.isoformat() if student.created_at else None,
            'updated_at': student.updated_at.isoformat() if student.updated_at else None
        }
    
    @staticmethod
    def serialize_list(students):
        return [StudentSerializer.serialize(student) for student in students]

class ReceiptSerializer:
    """Serialize receipt data for API responses"""
    
    @staticmethod
    def serialize(receipt):
        return {
            'id': receipt.id,
            'receipt_number': receipt.receipt_number,
            'date': receipt.date.isoformat() if receipt.date else None,
            'amount_paid': receipt.amount_paid,
            'student': {
                'id': receipt.student.id,
                'name': receipt.student.name,
                'student_id': receipt.student.student_id
            },
            'fee_account': {
                'id': receipt.fee_account.id,
                'amount_due': receipt.fee_account.amount_due,
                'amount_paid': receipt.fee_account.amount_paid
            },
            'created_at': receipt.created_at.isoformat() if receipt.created_at else None
        }
```

### API Views Examples

#### Student API
```python
# students/api/views.py
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db.models import Q

from ..models import Student, Level
from .base import BaseAPIView
from .serializers import StudentSerializer

@method_decorator([csrf_exempt, login_required], name='dispatch')
class StudentAPIView(BaseAPIView):
    """Student API endpoints"""
    
    def get(self, request, student_id=None):
        """Get student(s)"""
        if student_id:
            try:
                student = Student.objects.select_related('level').get(
                    id=student_id, is_active=True
                )
                return self.success_response(
                    data=StudentSerializer.serialize(student)
                )
            except Student.DoesNotExist:
                return self.error_response(
                    'Student not found',
                    'STUDENT_NOT_FOUND',
                    status=404
                )
        
        # List students with search and pagination
        search = request.GET.get('search', '')
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 25))
        
        queryset = Student.objects.select_related('level').filter(is_active=True)
        
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(student_id__icontains=search)
            )
        
        return self.paginated_response(
            queryset=queryset,
            page_number=page,
            per_page=per_page,
            serializer_func=StudentSerializer.serialize
        )
    
    def post(self, request):
        """Create new student"""
        data = request.json
        
        # Validation
        required_fields = ['name', 'level_id']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            return self.error_response(
                'Missing required fields',
                'VALIDATION_ERROR',
                details={field: ['This field is required'] for field in missing_fields}
            )
        
        try:
            level = Level.objects.get(id=data['level_id'], is_active=True)
        except Level.DoesNotExist:
            return self.error_response(
                'Invalid level',
                'INVALID_LEVEL',
                status=400
            )
        
        # Create student
        student = Student.objects.create(
            name=data['name'],
            gender=data.get('gender', 'Male'),
            level=level
        )
        
        return self.success_response(
            data=StudentSerializer.serialize(student),
            status=201
        )
    
    def put(self, request, student_id):
        """Update student"""
        try:
            student = Student.objects.get(id=student_id, is_active=True)
        except Student.DoesNotExist:
            return self.error_response(
                'Student not found',
                'STUDENT_NOT_FOUND',
                status=404
            )
        
        data = request.json
        
        # Update fields
        if 'name' in data:
            student.name = data['name']
        if 'gender' in data:
            student.gender = data['gender']
        if 'level_id' in data:
            try:
                level = Level.objects.get(id=data['level_id'], is_active=True)
                student.level = level
            except Level.DoesNotExist:
                return self.error_response(
                    'Invalid level',
                    'INVALID_LEVEL',
                    status=400
                )
        
        student.save()
        
        return self.success_response(
            data=StudentSerializer.serialize(student)
        )
    
    def delete(self, request, student_id):
        """Deactivate student"""
        try:
            student = Student.objects.get(id=student_id, is_active=True)
        except Student.DoesNotExist:
            return self.error_response(
                'Student not found',
                'STUDENT_NOT_FOUND',
                status=404
            )
        
        student.is_active = False
        student.save()
        
        return self.success_response(
            data={'message': 'Student deactivated successfully'}
        )
```

#### Receipt API
```python
# finances/api/views.py
from django.db.models import Sum
from ..models import Receipt, FeeAccount
from .serializers import ReceiptSerializer

@method_decorator([csrf_exempt, login_required], name='dispatch')
class ReceiptAPIView(BaseAPIView):
    """Receipt API endpoints"""
    
    def get(self, request, receipt_id=None):
        """Get receipt(s)"""
        if receipt_id:
            try:
                receipt = Receipt.objects.select_related(
                    'student', 'fee_account'
                ).get(id=receipt_id)
                return self.success_response(
                    data=ReceiptSerializer.serialize(receipt)
                )
            except Receipt.DoesNotExist:
                return self.error_response(
                    'Receipt not found',
                    'RECEIPT_NOT_FOUND',
                    status=404
                )
        
        # List receipts with filters
        student_id = request.GET.get('student_id')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 25))
        
        queryset = Receipt.objects.select_related('student', 'fee_account')
        
        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)
        
        queryset = queryset.order_by('-date', '-id')
        
        return self.paginated_response(
            queryset=queryset,
            page_number=page,
            per_page=per_page,
            serializer_func=ReceiptSerializer.serialize
        )
    
    def post(self, request):
        """Create new receipt"""
        data = request.json
        
        # Validation
        required_fields = ['student_id', 'amount_paid', 'fee_account_id']
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            return self.error_response(
                'Missing required fields',
                'VALIDATION_ERROR',
                details={field: ['This field is required'] for field in missing_fields}
            )
        
        try:
            fee_account = FeeAccount.objects.select_related('student').get(
                id=data['fee_account_id']
            )
        except FeeAccount.DoesNotExist:
            return self.error_response(
                'Fee account not found',
                'FEE_ACCOUNT_NOT_FOUND',
                status=404
            )
        
        # Validate amount
        amount_paid = int(data['amount_paid'])
        if amount_paid <= 0:
            return self.error_response(
                'Amount must be positive',
                'INVALID_AMOUNT',
                status=400
            )
        
        # Create receipt
        receipt = Receipt.objects.create(
            student=fee_account.student,
            fee_account=fee_account,
            amount_paid=amount_paid,
            date=data.get('date', timezone.now().date())
        )
        
        return self.success_response(
            data=ReceiptSerializer.serialize(receipt),
            status=201
        )
```

## 🔗 URL Configuration

```python
# api/urls.py
from django.urls import path, include

app_name = 'api'

urlpatterns = [
    path('v1/students/', include('students.api.urls')),
    path('v1/finances/', include('finances.api.urls')),
    path('v1/academics/', include('academics.api.urls')),
]

# students/api/urls.py
from django.urls import path
from .views import StudentAPIView

urlpatterns = [
    path('', StudentAPIView.as_view(), name='student_list_create'),
    path('<int:student_id>/', StudentAPIView.as_view(), name='student_detail'),
]

# finances/api/urls.py
from django.urls import path
from .views import ReceiptAPIView

urlpatterns = [
    path('receipts/', ReceiptAPIView.as_view(), name='receipt_list_create'),
    path('receipts/<int:receipt_id>/', ReceiptAPIView.as_view(), name='receipt_detail'),
]
```

## 🧪 API Testing

### Test Cases
```python
# tests/test_api.py
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
import json

User = get_user_model()

class StudentAPITestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client = Client()
        self.client.login(username='testuser', password='testpass123')
    
    def test_get_students(self):
        response = self.client.get('/api/v1/students/')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertEqual(data['status'], 'success')
        self.assertIn('data', data)
        self.assertIn('pagination', data.get('meta', {}))
    
    def test_create_student(self):
        data = {
            'name': 'Test Student',
            'gender': 'Male',
            'level_id': 1
        }
        response = self.client.post(
            '/api/v1/students/',
            json.dumps(data),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, 201)
        
        response_data = json.loads(response.content)
        self.assertEqual(response_data['status'], 'success')
        self.assertEqual(response_data['data']['name'], 'Test Student')
```

## 📚 Documentation

### API Documentation Template
```markdown
## GET /api/v1/students/

Get list of students with optional search and pagination.

### Parameters
- `search` (optional): Search term for name or student ID
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 25, max: 100)

### Response
```json
{
    "status": "success",
    "data": [...],
    "meta": {
        "pagination": {...}
    }
}
```

### Error Codes
- `AUTHENTICATION_REQUIRED`: User not authenticated
- `VALIDATION_ERROR`: Invalid input data
- `STUDENT_NOT_FOUND`: Student does not exist
```

## 🚀 Best Practices

### Performance
- Use `select_related()` and `prefetch_related()` for database optimization
- Implement proper pagination for large datasets
- Cache frequently accessed data
- Use database indexes for filtered fields

### Security
- Always require authentication
- Validate all input data
- Use proper HTTP status codes
- Implement rate limiting for production
- Sanitize output data

### Error Handling
- Provide meaningful error messages
- Use consistent error codes
- Log errors for debugging
- Handle edge cases gracefully

### Testing
- Write comprehensive test cases
- Test both success and error scenarios
- Use factory classes for test data
- Test API endpoints with different user permissions

---

This guide provides the foundation for building robust APIs in the Receipt Generator system. Always follow RESTful conventions and maintain consistency across all endpoints.
