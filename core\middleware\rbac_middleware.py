from django.http import HttpResponseForbidden, JsonResponse
from django.shortcuts import redirect
from django.urls import reverse
from django.contrib import messages
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class RBACMiddleware(MiddlewareMixin):
    """
    Middleware to handle role-based access control.
    This middleware checks user permissions for protected views.
    """
    
    # Views that require specific permissions
    PROTECTED_VIEWS = {
        # Students management
        'students:home': ['view_students'],
        'students:add_student': ['add_student'],
        'students:edit_student': ['edit_student'],
        'students:delete_student': ['delete_student'],
        'students:student_details': ['view_student_details'],
        
        # Finances management
        'finances:dashboard': ['view_finances'],
        'finances:add_fee': ['add_fee'],
        'finances:edit_fee': ['edit_fee'],
        'finances:delete_fee': ['delete_fee'],
        'finances:fee_collection': ['manage_fee_collection'],
        'finances:financial_reports': ['view_financial_reports'],
        
        # Academics management
        'academics:dashboard': ['view_academics'],
        'academics:add_subject': ['add_subject'],
        'academics:edit_subject': ['edit_subject'],
        'academics:manage_assessments': ['manage_assessments'],
        'academics:view_grades': ['view_grades'],
        'academics:edit_grades': ['edit_grades'],
        
        # Teachers management
        'accounts:teachers': ['view_teachers'],
        'accounts:add_teacher': ['add_teacher'],
        'accounts:edit_teacher': ['edit_teacher'],
        'accounts:teacher_details': ['view_teacher_details'],
        'accounts:add_assignment': ['manage_teacher_assignments'],
        'accounts:remove_assignment': ['manage_teacher_assignments'],
        
        # Core administrative functions
        'core:run_create_student_accounts': ['manage_system'],
        'core:run_recalculate_accounts': ['manage_system'],
        'core:start_new_term': ['manage_academic_periods'],
        'core:rearrange_receipts': ['manage_system'],
        'core:allign_receipts_journals': ['manage_system'],
        'core:discrepancy_check': ['manage_system'],
    }
    
    # Views that require specific roles
    ROLE_PROTECTED_VIEWS = {
        'admin:index': ['administrator', 'super_administrator'],
    }
    
    # Public views that don't require authentication
    PUBLIC_VIEWS = [
        'accounts:login',
        'accounts:logout',
        'core:philosophical_moment',
        'core:musical_moment',
        'core:artistic_moment',
        'core:cosmic_moment',
        'core:travel_moment',
        'coffe',
        'teapot',
        'custom_502',
        'custom_429',
    ]
    
    # API endpoints that need special handling
    API_ENDPOINTS = [
        '/api/',
        '/admin/jsi18n/',
    ]

    def process_view(self, request, view_func, view_args, view_kwargs):
        """
        Process the view and check permissions before it's called.
        """
        # Skip middleware for certain paths
        if self._should_skip_middleware(request):
            return None
        
        # Get the view name
        view_name = self._get_view_name(request)
        
        # Skip if it's a public view
        if view_name in self.PUBLIC_VIEWS:
            return None
        
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return self._handle_unauthenticated(request)
        
        # Check role-based permissions
        if view_name in self.ROLE_PROTECTED_VIEWS:
            required_roles = self.ROLE_PROTECTED_VIEWS[view_name]
            if not self._user_has_any_role(request.user, required_roles):
                return self._handle_forbidden(request, f"Requires one of these roles: {', '.join(required_roles)}")
        
        # Check permission-based access
        if view_name in self.PROTECTED_VIEWS:
            required_permissions = self.PROTECTED_VIEWS[view_name]
            if not self._user_has_any_permission(request.user, required_permissions):
                return self._handle_forbidden(request, f"Requires one of these permissions: {', '.join(required_permissions)}")
        
        # Log access for audit purposes
        if hasattr(request.user, 'get_highest_role_level'):
            logger.info(f"User {request.user.username} (role level: {request.user.get_highest_role_level()}) accessed {view_name}")
        
        return None

    def _should_skip_middleware(self, request):
        """Check if middleware should be skipped for this request"""
        # Skip for static files, media files, and admin static files
        skip_paths = ['/static/', '/media/', '/admin/jsi18n/']
        
        for path in skip_paths:
            if request.path.startswith(path):
                return True
        
        # Skip for API endpoints that handle their own auth
        for endpoint in self.API_ENDPOINTS:
            if request.path.startswith(endpoint):
                return True
        
        return False

    def _get_view_name(self, request):
        """Extract view name from request"""
        if hasattr(request, 'resolver_match') and request.resolver_match:
            if request.resolver_match.namespace:
                return f"{request.resolver_match.namespace}:{request.resolver_match.url_name}"
            return request.resolver_match.url_name
        return None

    def _user_has_any_role(self, user, roles):
        """Check if user has any of the required roles"""
        if user.is_superuser:
            return True
        
        for role in roles:
            if user.has_role(role):
                return True
        return False

    def _user_has_any_permission(self, user, permissions):
        """Check if user has any of the required permissions"""
        if user.is_superuser:
            return True
        
        for permission in permissions:
            if user.has_permission(permission):
                return True
        return False

    def _handle_unauthenticated(self, request):
        """Handle unauthenticated users"""
        if request.path.startswith('/api/'):
            return JsonResponse({
                'error': 'Authentication required',
                'code': 'AUTHENTICATION_REQUIRED'
            }, status=401)
        
        # Redirect to login page
        login_url = reverse('accounts:login')
        return redirect(f"{login_url}?next={request.path}")

    def _handle_forbidden(self, request, message="Access denied"):
        """Handle forbidden access"""
        if request.path.startswith('/api/'):
            return JsonResponse({
                'error': message,
                'code': 'INSUFFICIENT_PERMISSIONS'
            }, status=403)
        
        # For web requests, show error message and redirect
        messages.error(request, f"Access denied: {message}")
        
        # Redirect based on user's highest role
        if hasattr(request.user, 'get_highest_role_level'):
            role_level = request.user.get_highest_role_level()
            if role_level >= 5:  # Administrator
                return redirect('students:home')
            elif role_level == 4 or request.user.has_permission('manage_finances'):  # Finance Manager
                return redirect('finances:expenditures')
            elif role_level >= 2:  # Teacher/Academic Coordinator
                return redirect('academics:dashboard')
            else:  # Student or lower
                return redirect('accounts:login')
        
        return redirect('accounts:login')


class RBACContextMiddleware(MiddlewareMixin):
    """
    Middleware to add RBAC context to all requests.
    This makes user roles and permissions available in templates.
    """
    
    def process_request(self, request):
        """Add RBAC context to request"""
        if request.user.is_authenticated:
            # Add user roles and permissions to request context
            request.user_roles = request.user.get_active_roles()
            request.user_permissions = request.user.get_all_permissions()
            request.user_role_level = request.user.get_highest_role_level()
            
            # Add convenience flags
            request.is_student = request.user.is_student()
            request.is_teacher = request.user.is_teacher()
            request.is_admin = request.user.is_admin()
            request.can_manage_students = request.user.can_manage_students()
            request.can_manage_finances = request.user.can_manage_finances()
            request.can_manage_academics = request.user.can_manage_academics()
        
        return None
