from django.contrib import admin
from .models import (
    NotificationType, Notification, NotificationRead,
    EventCategory, CalendarEvent, EventAttendee
)


@admin.register(NotificationType)
class NotificationTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'icon', 'color', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)
    list_editable = ('is_active',)


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'notification_type', 'priority', 'is_active', 'created_at', 'expires_at', 'target_user_count')
    list_filter = ('notification_type', 'priority', 'is_active', 'created_at')
    search_fields = ('title', 'message')
    list_editable = ('is_active',)
    readonly_fields = ('created_at', 'updated_at', 'target_user_count')
    raw_id_fields = ('related_student', 'related_fee_account')  # Use raw ID fields for better performance

    # Temporarily disable target_users field to prevent timeout
    # This will be re-enabled once autocomplete is working properly
    exclude = ('target_users',) if not hasattr(admin.ModelAdmin, 'autocomplete_fields') else ()

    # Use autocomplete_fields for better performance (Django 2.0+)
    autocomplete_fields = ('target_users',) if hasattr(admin.ModelAdmin, 'autocomplete_fields') else ()

    def target_user_count(self, obj):
        """Show count of target users instead of loading all users"""
        if obj.pk:
            return obj.target_users.count()
        return 0
    target_user_count.short_description = 'Target Users Count'

    def get_queryset(self, request):
        """Optimize queryset to reduce database hits"""
        return super().get_queryset(request).select_related(
            'notification_type'
        ).prefetch_related('target_users')

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'message', 'notification_type', 'priority')
        }),
        ('Status', {
            'fields': ('is_active', 'expires_at')
        }),
        ('Targeting', {
            'fields': ('target_users',),
            'description': 'Leave empty for system-wide notifications. Use search to find specific users.'
        }),
        ('Related Objects', {
            'fields': ('related_student', 'related_fee_account'),
            'classes': ('collapse',),
            'description': 'Use the magnifying glass to search for specific records.'
        }),
        ('Statistics', {
            'fields': ('target_user_count',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def formfield_for_manytomany(self, db_field, request, **kwargs):
        """Optimize ManyToMany field queries"""
        if db_field.name == "target_users":
            # Limit the queryset to active users only to reduce load time
            from django.contrib.auth import get_user_model
            User = get_user_model()
            kwargs["queryset"] = User.objects.filter(is_active=True).order_by('username')
        return super().formfield_for_manytomany(db_field, request, **kwargs)


@admin.register(NotificationRead)
class NotificationReadAdmin(admin.ModelAdmin):
    list_display = ('notification', 'user', 'read_at')
    list_filter = ('read_at',)
    search_fields = ('notification__title', 'user__username')


@admin.register(EventCategory)
class EventCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'color', 'icon', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)
    list_editable = ('is_active',)


@admin.register(CalendarEvent)
class CalendarEventAdmin(admin.ModelAdmin):
    list_display = ('title', 'event_type', 'category', 'start_date', 'end_date', 'is_all_day', 'is_active', 'created_by')
    list_filter = ('event_type', 'category', 'is_all_day', 'is_active', 'start_date', 'created_by')
    search_fields = ('title', 'description', 'location')
    list_editable = ('is_active',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'event_type', 'category')
        }),
        ('Date & Time', {
            'fields': ('start_date', 'end_date', 'start_time', 'end_time', 'is_all_day')
        }),
        ('Location & Recurrence', {
            'fields': ('location', 'is_recurring', 'recurrence_pattern')
        }),
        ('Status & Visibility', {
            'fields': ('is_active', 'is_public', 'created_by')
        }),
        ('Related Objects', {
            'fields': ('related_term', 'related_level', 'related_subject'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(EventAttendee)
class EventAttendeeAdmin(admin.ModelAdmin):
    list_display = ('event', 'user', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('event__title', 'user__username')
