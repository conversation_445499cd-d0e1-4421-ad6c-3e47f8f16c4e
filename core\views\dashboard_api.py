from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.db.models import Q
from datetime import datetime, timedelta
import json

from core.models import Notification, CalendarEvent, NotificationRead
from students.models import Student, Term
from finances.fee_management.models import FeeAccount


@login_required(login_url="accounts:login")
@require_http_methods(["GET"])
def get_notifications(request):
    """
    API endpoint to fetch notifications for the dashboard
    """
    try:
        # Get query parameters
        limit = int(request.GET.get('limit', 10))
        unread_only = request.GET.get('unread_only', 'false').lower() == 'true'
        
        # Base queryset - active notifications that haven't expired
        notifications = Notification.objects.filter(
            is_active=True
        ).filter(
            Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
        ).select_related('notification_type')
        
        # Filter by read status if requested
        if unread_only:
            read_notifications = NotificationRead.objects.filter(
                user=request.user
            ).values_list('notification_id', flat=True)
            notifications = notifications.exclude(id__in=read_notifications)
        
        # Filter by target users (if notification has specific targets, user must be in them)
        notifications = notifications.filter(
            Q(target_users__isnull=True) | Q(target_users=request.user)
        ).distinct()
        
        # Limit results
        notifications = notifications[:limit]
        
        # Serialize notifications
        notifications_data = []
        for notification in notifications:
            # Check if user has read this notification
            is_read = NotificationRead.objects.filter(
                notification=notification,
                user=request.user
            ).exists()
            
            notifications_data.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'priority': notification.priority,
                'priority_color': notification.priority_color,
                'type': {
                    'name': notification.notification_type.name,
                    'icon': notification.notification_type.icon,
                    'color': notification.notification_type.color,
                },
                'is_read': is_read,
                'created_at': notification.created_at.isoformat(),
                'time_ago': get_time_ago(notification.created_at),
                'related_student': notification.related_student.name if notification.related_student else None,
            })
        
        return JsonResponse({
            'success': True,
            'notifications': notifications_data,
            'count': len(notifications_data)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required(login_url="accounts:login")
@require_http_methods(["POST"])
@csrf_exempt
def mark_notification_read(request):
    """
    Mark a notification as read
    """
    try:
        data = json.loads(request.body)
        notification_id = data.get('notification_id')
        
        if not notification_id:
            return JsonResponse({
                'success': False,
                'error': 'Notification ID is required'
            }, status=400)
        
        notification = Notification.objects.get(id=notification_id)
        
        # Create or update read status
        read_status, created = NotificationRead.objects.get_or_create(
            notification=notification,
            user=request.user
        )
        
        return JsonResponse({
            'success': True,
            'message': 'Notification marked as read'
        })
        
    except Notification.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Notification not found'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required(login_url="accounts:login")
@require_http_methods(["GET"])
def get_calendar_events(request):
    """
    API endpoint to fetch calendar events for the dashboard
    """
    try:
        # Get query parameters
        start_date = request.GET.get('start')
        end_date = request.GET.get('end')

        # Default to current month if no dates provided
        if not start_date or not end_date:
            today = timezone.now().date()
            start_date = today.replace(day=1)
            end_date = (start_date + timedelta(days=32)).replace(day=1) - timedelta(days=1)
        else:
            # Parse dates with multiple format support
            try:
                # Try ISO format first (YYYY-MM-DD)
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                try:
                    # Try ISO datetime format (YYYY-MM-DDTHH:MM:SS+TZ)
                    start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00')).date()
                    end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00')).date()
                except ValueError:
                    # Fallback to current month if parsing fails
                    today = timezone.now().date()
                    start_date = today.replace(day=1)
                    end_date = (start_date + timedelta(days=32)).replace(day=1) - timedelta(days=1)
        
        # Get events in the date range
        events = CalendarEvent.objects.filter(
            is_active=True,
            start_date__lte=end_date,
            end_date__gte=start_date
        ).select_related('category', 'related_term', 'related_level', 'related_subject')
        
        # Serialize events for FullCalendar
        events_data = []
        for event in events:
            # Handle missing category gracefully
            category_color = '#7AB2D3'  # Default color
            category_name = 'General'   # Default category name

            if event.category:
                category_color = event.category.color
                category_name = event.category.name

            event_data = {
                'id': event.id,
                'title': event.title,
                'start': event.start_date.isoformat(),
                'end': event.end_date.isoformat(),
                'allDay': event.is_all_day,
                'backgroundColor': category_color,
                'borderColor': category_color,
                'textColor': '#ffffff',
                'extendedProps': {
                    'description': event.description or '',
                    'event_type': event.event_type,
                    'category': category_name,
                    'location': event.location or '',
                    'status': getattr(event, 'status_display', 'Active'),
                    'related_term': event.related_term.term_name if event.related_term else None,
                    'related_level': event.related_level.level_name if event.related_level else None,
                    'related_subject': event.related_subject.name if event.related_subject else None,
                }
            }
            
            # Add time if not all day
            if not event.is_all_day and event.start_time:
                event_data['start'] = f"{event.start_date.isoformat()}T{event.start_time.isoformat()}"
                if event.end_time:
                    event_data['end'] = f"{event.end_date.isoformat()}T{event.end_time.isoformat()}"
            
            events_data.append(event_data)
        
        return JsonResponse({
            'success': True,
            'events': events_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@login_required(login_url="accounts:login")
@require_http_methods(["GET"])
def get_upcoming_events(request):
    """
    Get upcoming events for the next 7 days
    """
    try:
        today = timezone.now().date()
        next_week = today + timedelta(days=7)
        
        events = CalendarEvent.objects.filter(
            is_active=True,
            start_date__gte=today,
            start_date__lte=next_week
        ).select_related('category').order_by('start_date', 'start_time')[:5]
        
        events_data = []
        for event in events:
            events_data.append({
                'id': event.id,
                'title': event.title,
                'start_date': event.start_date.isoformat(),
                'start_time': event.start_time.strftime('%H:%M') if event.start_time else None,
                'category': {
                    'name': event.category.name,
                    'color': event.category.color,
                    'icon': event.category.icon,
                },
                'location': event.location,
                'days_until': (event.start_date - today).days,
                'is_today': event.is_today,
                'is_all_day': event.is_all_day,
            })
        
        return JsonResponse({
            'success': True,
            'events': events_data
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


def get_time_ago(datetime_obj):
    """
    Helper function to get human-readable time difference
    """
    now = timezone.now()
    diff = now - datetime_obj
    
    if diff.days > 0:
        return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours} hour{'s' if hours > 1 else ''} ago"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
    else:
        return "Just now"
