{% extends 'academics/base.html' %}
<!--  -->
{% block title %}{{ title }} | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex items-center gap-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-2xl flex items-center justify-center shadow-xl icon-float"
      >
        <i class="fas fa-tags text-white text-2xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
        >
          {{ title }}
        </h1>
        <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
          {% if activity_type %}
            Update activity type information
          {% else %}
            Create a new activity type for categorizing activities
          {% endif %}
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#40657F] to-[#2C3E50] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
  </div>

  <!-- Navigation Breadcrumb -->
  <div class="breadcrumb-fade-in">
    <nav class="flex items-center gap-2 text-sm text-[#40657F]">
      <a
        href="{% url 'academics:academic_management_dashboard' %}"
        class="hover:text-[#40657F] transition-colors duration-200"
      >
        <i class="fas fa-graduation-cap mr-1"></i>
        Academic Management
      </a>
      <i class="fas fa-chevron-right text-[#B9D8EB]"></i>
      <a
        href="{% url 'academics:activity_type_management' %}"
        class="hover:text-[#40657F] transition-colors duration-200"
      >
        Activity Types
      </a>
      <i class="fas fa-chevron-right text-[#B9D8EB]"></i>
      <span class="text-[#2C3E50] font-medium">{{ title }}</span>
    </nav>
  </div>

  <!-- Activity Type Form -->
  <div class="card-modern p-8 form-slide-in">
    <form method="POST" class="space-y-8">
      {% csrf_token %}

      <!-- Activity Type Information Section -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg section-icon-float"
          >
            <i class="fas fa-info-circle text-white text-lg"></i>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
              Activity Type Information
            </h2>
            <p class="text-[#40657F] text-sm">
              Basic information about the activity type
            </p>
          </div>
          <div
            class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
          ></div>
        </div>

        <!-- Activity Type Name -->
        <div class="form-group">
          <label
            for="{{ form.name.id_for_label }}"
            class="block text-sm font-bold text-[#2C3E50] mb-2"
          >
            <i class="fas fa-tags text-[#40657F] mr-2"></i>
            {{ form.name.label }}
          </label>
          {{ form.name }}
          {% if form.name.help_text %}
          <p class="text-sm text-[#40657F] mt-2">{{ form.name.help_text }}</p>
          {% endif %}
          {% if form.name.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.name.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>

        <!-- Activity Type Description -->
        <div class="form-group">
          <label
            for="{{ form.description.id_for_label }}"
            class="block text-sm font-bold text-[#2C3E50] mb-2"
          >
            <i class="fas fa-align-left text-[#74C69D] mr-2"></i>
            {{ form.description.label }}
          </label>
          {{ form.description }}
          {% if form.description.help_text %}
          <p class="text-sm text-[#40657F] mt-2">{{ form.description.help_text }}</p>
          {% endif %}
          {% if form.description.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.description.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Activity Type Guidelines -->
      <div class="bg-gradient-to-r from-[#E2F1F9] to-[#F7FAFC] border border-[#B9D8EB] rounded-xl p-6 guidelines-fade-in">
        <div class="flex items-start gap-4">
          <div
            class="w-10 h-10 bg-[#40657F] rounded-full flex items-center justify-center flex-shrink-0"
          >
            <i class="fas fa-lightbulb text-white"></i>
          </div>
          <div>
            <h3 class="font-bold text-[#2C3E50] mb-3">Activity Type Guidelines</h3>
            <ul class="space-y-2 text-sm text-[#40657F]">
              <li class="flex items-start gap-2">
                <i class="fas fa-check text-[#74C69D] mt-1 flex-shrink-0"></i>
                <span><strong>Name:</strong> Use clear, descriptive names (e.g., "Quiz", "Project", "Field Trip")</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-check text-[#74C69D] mt-1 flex-shrink-0"></i>
                <span><strong>Description:</strong> Explain what this activity type involves and its purpose</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-check text-[#74C69D] mt-1 flex-shrink-0"></i>
                <span><strong>Categories:</strong> Consider grouping similar activities (Assessment, Learning, Social, etc.)</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-info-circle text-[#7AB2D3] mt-1 flex-shrink-0"></i>
                <span><strong>Examples:</strong> Quiz, Test, Project, Presentation, Field Trip, Sports Day, Assembly</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-[#B9D8EB] buttons-slide-in">
        <button
          type="submit"
          class="flex-1 bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white font-bold py-4 px-8 rounded-xl hover:from-[#2C3E50] hover:to-[#40657F] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-save mr-3 group-hover:scale-110 transition-transform duration-300"
          ></i>
          {{ submit_text }}
        </button>
        <a
          href="{% url 'academics:activity_type_management' %}"
          class="flex-1 bg-[#B9D8EB] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:bg-[#E2F1F9] focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 text-center group"
        >
          <i
            class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-300"
          ></i>
          Back to Activity Types
        </a>
      </div>
    </form>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.8s forwards;
  }

  .form-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: formSlideIn 0.8s ease-out 1s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 4s ease-in-out infinite;
  }

  .form-group {
    opacity: 0;
    transform: translateX(-20px);
    animation: formGroupSlideIn 0.4s ease-out forwards;
  }

  .form-group:nth-child(2) { animation-delay: 1.2s; }
  .form-group:nth-child(3) { animation-delay: 1.3s; }

  .guidelines-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: guidelinesFadeIn 0.8s ease-out 1.4s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(20px);
    animation: buttonsSlideIn 0.8s ease-out 1.6s forwards;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes breadcrumbFadeIn {
    to { opacity: 1; }
  }

  @keyframes formSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes sectionIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(3deg); }
  }

  @keyframes formGroupSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes guidelinesFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes buttonsSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }
</style>

{% endblock %}
