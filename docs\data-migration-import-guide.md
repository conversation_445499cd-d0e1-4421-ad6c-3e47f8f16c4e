# Data Migration & Import Guide

## 📊 Overview

This guide provides comprehensive instructions for handling bulk data operations, Excel imports, data migrations, and student progression workflows in the Receipt Generator system.

## 📥 Excel Import System

### Student Admission by Excel

#### File Format Requirements
```python
# Expected Excel columns for student import
REQUIRED_COLUMNS = [
    'name',           # Student full name
    'gender',         # Male/Female
    'level',          # Class level (e.g., "Primary 1", "JSS 1")
    'student_id',     # Optional: Auto-generated if not provided
]

OPTIONAL_COLUMNS = [
    'date_of_birth',  # Format: YYYY-MM-DD
    'parent_name',    # Guardian information
    'phone_number',   # Contact information
    'address',        # Student address
]
```

#### Excel Processing Implementation
```python
import pandas as pd
from django.contrib import messages
from students.models import Student, Level

def process_student_excel(file_path, request):
    """Process Excel file for student admission"""
    try:
        # Read Excel file with error handling
        df = pd.read_excel(file_path, engine='openpyxl')
        
        # Validate required columns
        required_cols = ['name', 'gender', 'level']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            raise ValueError(f"Missing required columns: {', '.join(missing_cols)}")
        
        # Clean and validate data
        df = clean_student_data(df)
        
        # Process in batches for better performance
        batch_size = 100
        total_processed = 0
        errors = []
        
        for i in range(0, len(df), batch_size):
            batch = df.iloc[i:i + batch_size]
            processed, batch_errors = process_student_batch(batch)
            total_processed += processed
            errors.extend(batch_errors)
        
        # Report results
        if errors:
            messages.warning(request, f"Processed {total_processed} students with {len(errors)} errors")
            return total_processed, errors
        else:
            messages.success(request, f"Successfully imported {total_processed} students")
            return total_processed, []
            
    except Exception as e:
        messages.error(request, f"Import failed: {str(e)}")
        return 0, [str(e)]

def clean_student_data(df):
    """Clean and validate student data"""
    # Remove empty rows
    df = df.dropna(subset=['name'])
    
    # Clean names
    df['name'] = df['name'].str.strip().str.title()
    
    # Validate gender
    df['gender'] = df['gender'].str.strip().str.title()
    df['gender'] = df['gender'].replace({'M': 'Male', 'F': 'Female'})
    
    # Validate levels exist
    valid_levels = set(Level.objects.values_list('name', flat=True))
    invalid_levels = df[~df['level'].isin(valid_levels)]
    
    if not invalid_levels.empty:
        raise ValueError(f"Invalid levels found: {invalid_levels['level'].unique()}")
    
    return df

def process_student_batch(batch_df):
    """Process a batch of students"""
    students_to_create = []
    errors = []
    processed = 0
    
    for index, row in batch_df.iterrows():
        try:
            # Get level object
            level = Level.objects.get(name=row['level'])
            
            # Check if student already exists
            if Student.objects.filter(name=row['name'], level=level).exists():
                errors.append(f"Row {index + 1}: Student {row['name']} already exists in {level.name}")
                continue
            
            # Create student object
            student = Student(
                name=row['name'],
                gender=row['gender'],
                level=level,
                is_active=True
            )
            
            # Add optional fields if present
            if 'student_id' in row and pd.notna(row['student_id']):
                student.student_id = row['student_id']
            
            students_to_create.append(student)
            processed += 1
            
        except Exception as e:
            errors.append(f"Row {index + 1}: {str(e)}")
    
    # Bulk create students
    if students_to_create:
        Student.objects.bulk_create(students_to_create, ignore_conflicts=True)
    
    return processed, errors
```

### Excel Template Generation

#### Creating Import Templates
```python
def generate_student_import_template():
    """Generate Excel template for student import"""
    import pandas as pd
    from django.http import HttpResponse
    
    # Sample data for template
    sample_data = {
        'name': ['John Doe', 'Jane Smith', 'Mike Johnson'],
        'gender': ['Male', 'Female', 'Male'],
        'level': ['Primary 1', 'Primary 2', 'Primary 1'],
        'student_id': ['', '', ''],  # Optional
        'date_of_birth': ['2010-01-15', '2009-05-20', '2010-03-10'],
        'parent_name': ['Mr. Doe', 'Mrs. Smith', 'Mr. Johnson'],
        'phone_number': ['+1234567890', '+0987654321', '+1122334455'],
        'address': ['123 Main St', '456 Oak Ave', '789 Pine Rd']
    }
    
    df = pd.DataFrame(sample_data)
    
    # Create Excel response
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="student_import_template.xlsx"'
    
    with pd.ExcelWriter(response, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Students', index=False)
        
        # Add instructions sheet
        instructions = pd.DataFrame({
            'Column': ['name', 'gender', 'level', 'student_id'],
            'Required': ['Yes', 'Yes', 'Yes', 'No'],
            'Description': [
                'Full name of the student',
                'Male or Female',
                'Must match existing class levels',
                'Auto-generated if left blank'
            ]
        })
        instructions.to_excel(writer, sheet_name='Instructions', index=False)
    
    return response
```

## 🔄 Data Migration Strategies

### Student Progression System

#### Academic Year Progression
```python
from students.models import Student, AcademicYear, Level
from finances.fee_management.models import FeeAccount

def progress_students_to_next_level():
    """Progress all students to the next academic level"""
    current_year = AcademicYear.objects.get(is_active=True)
    
    # Define progression mapping
    progression_map = {
        'Nursery 1': 'Nursery 2',
        'Nursery 2': 'Primary 1',
        'Primary 1': 'Primary 2',
        'Primary 2': 'Primary 3',
        'Primary 3': 'Primary 4',
        'Primary 4': 'Primary 5',
        'Primary 5': 'Primary 6',
        'Primary 6': 'JSS 1',
        'JSS 1': 'JSS 2',
        'JSS 2': 'JSS 3',
        'JSS 3': 'SSS 1',
        'SSS 1': 'SSS 2',
        'SSS 2': 'SSS 3',
        'SSS 3': 'GRADUATED'
    }
    
    progression_results = {
        'progressed': 0,
        'graduated': 0,
        'failed': 0,
        'errors': []
    }
    
    for current_level_name, next_level_name in progression_map.items():
        try:
            current_level = Level.objects.get(name=current_level_name)
            
            # Get students who passed
            passing_students = Student.objects.filter(
                level=current_level,
                is_active=True
            ).filter(
                # Add your passing criteria here
                # e.g., average_score__gte=40
            )
            
            if next_level_name == 'GRADUATED':
                # Mark as graduated
                passing_students.update(is_active=False)
                progression_results['graduated'] += passing_students.count()
            else:
                # Progress to next level
                next_level = Level.objects.get(name=next_level_name)
                passing_students.update(level=next_level)
                progression_results['progressed'] += passing_students.count()
                
                # Create new fee accounts for progressed students
                create_fee_accounts_for_students(passing_students, current_year)
        
        except Level.DoesNotExist:
            progression_results['errors'].append(f"Level not found: {current_level_name} or {next_level_name}")
        except Exception as e:
            progression_results['errors'].append(f"Error processing {current_level_name}: {str(e)}")
    
    return progression_results

def create_fee_accounts_for_students(students, academic_year):
    """Create fee accounts for progressed students"""
    from finances.fee_management.models import FeeCategory
    
    active_term = academic_year.term_set.filter(is_active=True).first()
    if not active_term:
        return
    
    fee_categories = FeeCategory.objects.filter(is_active=True)
    fee_accounts = []
    
    for student in students:
        for category in fee_categories:
            fee_account = FeeAccount(
                student=student,
                category=category,
                term=active_term,
                amount=category.amount
            )
            fee_accounts.append(fee_account)
    
    FeeAccount.objects.bulk_create(fee_accounts, ignore_conflicts=True)
```

### Legacy Data Migration

#### Migrating from Old System
```python
def migrate_legacy_student_data(legacy_csv_path):
    """Migrate student data from legacy CSV format"""
    import csv
    from datetime import datetime
    
    migration_log = {
        'total_records': 0,
        'successful': 0,
        'failed': 0,
        'errors': []
    }
    
    with open(legacy_csv_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        
        for row_num, row in enumerate(reader, 1):
            migration_log['total_records'] += 1
            
            try:
                # Map legacy fields to new format
                student_data = map_legacy_fields(row)
                
                # Validate and create student
                student = create_student_from_legacy(student_data)
                
                if student:
                    migration_log['successful'] += 1
                else:
                    migration_log['failed'] += 1
                    
            except Exception as e:
                migration_log['failed'] += 1
                migration_log['errors'].append(f"Row {row_num}: {str(e)}")
    
    return migration_log

def map_legacy_fields(legacy_row):
    """Map legacy field names to current system"""
    field_mapping = {
        'student_name': 'name',
        'sex': 'gender',
        'class_name': 'level',
        'admission_number': 'student_id',
        'dob': 'date_of_birth'
    }
    
    mapped_data = {}
    for legacy_field, new_field in field_mapping.items():
        if legacy_field in legacy_row:
            mapped_data[new_field] = legacy_row[legacy_field]
    
    # Clean and validate data
    if 'gender' in mapped_data:
        mapped_data['gender'] = 'Male' if mapped_data['gender'].lower() in ['m', 'male'] else 'Female'
    
    return mapped_data
```

## 🔍 Data Validation and Cleaning

### Validation Rules

#### Student Data Validation
```python
from django.core.exceptions import ValidationError
import re

def validate_student_data(student_data):
    """Comprehensive student data validation"""
    errors = []
    
    # Name validation
    if not student_data.get('name'):
        errors.append("Name is required")
    elif len(student_data['name']) < 2:
        errors.append("Name must be at least 2 characters")
    elif not re.match(r'^[a-zA-Z\s\-\.]+$', student_data['name']):
        errors.append("Name contains invalid characters")
    
    # Gender validation
    if student_data.get('gender') not in ['Male', 'Female']:
        errors.append("Gender must be 'Male' or 'Female'")
    
    # Level validation
    if student_data.get('level'):
        if not Level.objects.filter(name=student_data['level']).exists():
            errors.append(f"Level '{student_data['level']}' does not exist")
    
    # Student ID validation (if provided)
    if student_data.get('student_id'):
        if Student.objects.filter(student_id=student_data['student_id']).exists():
            errors.append(f"Student ID '{student_data['student_id']}' already exists")
    
    # Phone number validation
    if student_data.get('phone_number'):
        phone_pattern = r'^\+?[\d\s\-\(\)]{10,15}$'
        if not re.match(phone_pattern, student_data['phone_number']):
            errors.append("Invalid phone number format")
    
    return errors

def clean_and_validate_batch(data_batch):
    """Clean and validate a batch of student data"""
    cleaned_batch = []
    validation_errors = []
    
    for index, record in enumerate(data_batch):
        # Clean data
        cleaned_record = clean_student_record(record)
        
        # Validate cleaned data
        errors = validate_student_data(cleaned_record)
        
        if errors:
            validation_errors.append({
                'row': index + 1,
                'errors': errors,
                'data': record
            })
        else:
            cleaned_batch.append(cleaned_record)
    
    return cleaned_batch, validation_errors

def clean_student_record(record):
    """Clean individual student record"""
    cleaned = {}
    
    # Clean name
    if 'name' in record:
        cleaned['name'] = ' '.join(record['name'].strip().split())
        cleaned['name'] = cleaned['name'].title()
    
    # Clean gender
    if 'gender' in record:
        gender = record['gender'].strip().lower()
        cleaned['gender'] = 'Male' if gender in ['m', 'male', '1'] else 'Female'
    
    # Clean level
    if 'level' in record:
        cleaned['level'] = record['level'].strip()
    
    # Clean phone number
    if 'phone_number' in record:
        phone = re.sub(r'[^\d\+]', '', str(record['phone_number']))
        cleaned['phone_number'] = phone if phone else None
    
    return cleaned
```

## 📋 Progress Tracking and Logging

### Import Progress Monitoring

#### Progress Tracking System
```python
import logging
from django.core.cache import cache

logger = logging.getLogger('data_import')

class ImportProgressTracker:
    def __init__(self, import_id, total_records):
        self.import_id = import_id
        self.total_records = total_records
        self.processed = 0
        self.successful = 0
        self.failed = 0
        self.errors = []
    
    def update_progress(self, processed=1, successful=0, failed=0, error=None):
        """Update import progress"""
        self.processed += processed
        self.successful += successful
        self.failed += failed
        
        if error:
            self.errors.append(error)
        
        # Cache progress for real-time updates
        progress_data = {
            'total': self.total_records,
            'processed': self.processed,
            'successful': self.successful,
            'failed': self.failed,
            'percentage': (self.processed / self.total_records) * 100,
            'errors': self.errors[-10:]  # Keep last 10 errors
        }
        
        cache.set(f'import_progress_{self.import_id}', progress_data, 3600)
        
        # Log progress
        if self.processed % 100 == 0:  # Log every 100 records
            logger.info(f"Import {self.import_id}: {self.processed}/{self.total_records} processed")
    
    def get_progress(self):
        """Get current progress"""
        return cache.get(f'import_progress_{self.import_id}')
    
    def complete_import(self):
        """Mark import as complete"""
        final_report = {
            'status': 'completed',
            'total': self.total_records,
            'successful': self.successful,
            'failed': self.failed,
            'errors': self.errors
        }
        
        cache.set(f'import_result_{self.import_id}', final_report, 86400)  # Keep for 24 hours
        cache.delete(f'import_progress_{self.import_id}')
        
        logger.info(f"Import {self.import_id} completed: {self.successful} successful, {self.failed} failed")
        
        return final_report
```

### Error Handling and Rollback

#### Transaction Management
```python
from django.db import transaction
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'Import students from Excel file with rollback capability'
    
    def add_arguments(self, parser):
        parser.add_argument('file_path', type=str, help='Path to Excel file')
        parser.add_argument('--dry-run', action='store_true', help='Validate without importing')
        parser.add_argument('--rollback-on-error', action='store_true', help='Rollback all changes on any error')
    
    def handle(self, *args, **options):
        file_path = options['file_path']
        dry_run = options['dry_run']
        rollback_on_error = options['rollback_on_error']
        
        try:
            if rollback_on_error:
                with transaction.atomic():
                    result = self.process_import(file_path, dry_run)
                    if result['failed'] > 0:
                        raise Exception(f"Import failed with {result['failed']} errors")
            else:
                result = self.process_import(file_path, dry_run)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"Import completed: {result['successful']} successful, {result['failed']} failed"
                )
            )
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Import failed: {str(e)}"))
    
    def process_import(self, file_path, dry_run):
        """Process the import with optional dry run"""
        # Implementation here
        pass
```

## 🔧 Best Practices

### Import Guidelines
1. **Always validate data** before importing
2. **Use batch processing** for large datasets
3. **Implement progress tracking** for user feedback
4. **Provide detailed error reporting** with row numbers
5. **Create backup** before major imports
6. **Test with small datasets** first
7. **Use transactions** for data integrity
8. **Log all import activities** for audit trails

### Performance Considerations
- Process data in batches (100-1000 records)
- Use bulk_create() for database efficiency
- Implement caching for progress tracking
- Clean up temporary files after processing
- Monitor memory usage for large files

### Error Recovery
- Provide detailed error messages with row numbers
- Allow partial imports with error reporting
- Implement rollback mechanisms for critical imports
- Create import logs for troubleshooting
- Validate data before processing

This guide ensures reliable and efficient data migration and import operations for the Receipt Generator system.
