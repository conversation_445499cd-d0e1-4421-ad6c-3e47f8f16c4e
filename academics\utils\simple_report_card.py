"""
Simplified report card generation as a backup/alternative
"""

from io import BytesIO
from datetime import datetime
import os
from reportlab.lib.pagesizes import A4
from reportlab.lib.colors import HexColor, black, white
from reportlab.pdfgen import canvas

# Simple color palette
COLORS = {
    'primary': HexColor('#40657F'),
    'text': black,
    'success': HexColor('#74C69D'),
    'warning': HexColor('#F28C8C'),
}

def create_simple_report_card_pdf(student, term):
    """
    Create a simple, reliable report card PDF
    """
    buffer = BytesIO()
    c = canvas.Canvas(buffer, pagesize=A4)
    width, height = A4
    
    # Simple header
    y_position = height - 80
    
    # Title
    c.setFont("Helvetica-Bold", 18)
    c.setFillColor(COLORS['primary'])
    title = "TINY FEET ACADEMY - ACADEMIC REPORT CARD"
    title_width = c.stringWidth(title, "Helvetica-Bold", 18)
    c.drawString((width - title_width) / 2, y_position, title)
    
    y_position -= 50
    
    # Student Information
    c.setFont("Helvetica-Bold", 12)
    c.setFillColor(COLORS['text'])
    c.drawString(50, y_position, "STUDENT INFORMATION")
    
    y_position -= 25
    c.setFont("Helvetica", 10)
    
    # Student details
    c.drawString(50, y_position, f"Name: {student.name}")
    c.drawString(300, y_position, f"Student ID: {student.student_id}")
    
    y_position -= 15
    c.drawString(50, y_position, f"Class: {student.level}")
    c.drawString(300, y_position, f"Term: {term.term_name}")
    
    y_position -= 15
    c.drawString(50, y_position, f"Gender: {student.gender}")
    c.drawString(300, y_position, f"Academic Year: {term.academic_year}")
    
    y_position -= 40
    
    # Academic Performance
    c.setFont("Helvetica-Bold", 12)
    c.setFillColor(COLORS['primary'])
    c.drawString(50, y_position, "ACADEMIC PERFORMANCE")
    
    y_position -= 30
    
    # Get enrollments
    from academics.models import Enrollment
    
    enrollments = Enrollment.objects.filter(
        student=student,
        term=term
    ).select_related('subject').prefetch_related('assessments__assessment_type')
    
    if enrollments.exists():
        # Table headers
        c.setFont("Helvetica-Bold", 10)
        c.setFillColor(COLORS['text'])
        
        headers = ["Subject", "End-Term Exam", "Grade", "Remarks"]
        x_positions = [50, 200, 300, 380]
        
        for i, header in enumerate(headers):
            c.drawString(x_positions[i], y_position, header)
        
        # Line under headers
        y_position -= 5
        c.line(50, y_position, 500, y_position)
        y_position -= 15
        
        # Table data
        c.setFont("Helvetica", 9)
        
        total_subjects = 0
        total_score = 0
        passed_subjects = 0
        
        for enrollment in enrollments:
            subject_name = enrollment.subject.name[:20]
            
            # Get end-term exam score
            final_score = "-"
            final_assessment = enrollment.assessments.filter(
                assessment_type__is_final=True
            ).first()
            if final_assessment:
                final_score = str(final_assessment.score)
            
            subject_total = final_assessment.score if final_assessment else 0
            subject_grade = enrollment.grade
            
            # Remarks
            if enrollment.has_passed:
                remarks = "Pass"
                remarks_color = COLORS['success']
                passed_subjects += 1
            else:
                remarks = "Fail"
                remarks_color = COLORS['warning']
            
            # Draw row
            c.setFillColor(COLORS['text'])
            c.drawString(x_positions[0], y_position, subject_name)
            c.drawString(x_positions[1], y_position, final_score)
            c.drawString(x_positions[2], y_position, subject_grade)
            
            c.setFillColor(remarks_color)
            c.drawString(x_positions[3], y_position, remarks)
            
            y_position -= 15
            total_subjects += 1
            total_score += subject_total
        
        # Summary
        y_position -= 10
        c.setFont("Helvetica-Bold", 10)
        c.setFillColor(COLORS['primary'])
        
        average_score = total_score / total_subjects if total_subjects > 0 else 0
        from academics.maps import get_grade
        overall_grade = get_grade(average_score)
        
        c.drawString(50, y_position, "OVERALL PERFORMANCE")
        c.drawString(200, y_position, f"{average_score:.1f}")
        c.drawString(300, y_position, overall_grade)
        c.drawString(380, y_position, f"{passed_subjects}/{total_subjects}")
        
        y_position -= 30
        
        # Status
        status = "PROMOTED" if passed_subjects >= total_subjects * 0.6 else "REPEAT"
        status_color = COLORS['success'] if status == "PROMOTED" else COLORS['warning']
        pass_rate = (passed_subjects/total_subjects*100) if total_subjects > 0 else 0
        
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(status_color)
        c.drawString(50, y_position, f"Status: {status}")
        
        c.setFillColor(COLORS['text'])
        c.drawString(200, y_position, f"Pass Rate: {pass_rate:.1f}%")
        c.drawString(350, y_position, f"Overall Grade: {overall_grade}")
        
    else:
        c.setFont("Helvetica", 10)
        c.setFillColor(COLORS['text'])
        c.drawString(50, y_position, "No academic records found for this term.")
    
    y_position -= 60
    
    # Signature section
    c.setFont("Helvetica", 10)
    c.setFillColor(COLORS['text'])
    
    c.drawString(50, y_position, "Class Teacher: _________________________")
    c.drawString(350, y_position, "Date: ______________")
    
    y_position -= 25
    c.drawString(50, y_position, "Principal: _____________________________")
    c.drawString(350, y_position, "School Stamp")
    
    # Footer
    y_position -= 40
    c.setFont("Helvetica-Oblique", 8)
    c.setFillColor(COLORS['text'])
    timestamp = datetime.now().strftime('%B %d, %Y at %I:%M %p')
    footer_text = f"Generated on {timestamp}"
    footer_width = c.stringWidth(footer_text, "Helvetica-Oblique", 8)
    c.drawString((width - footer_width) / 2, y_position, footer_text)
    
    # Save PDF
    c.save()
    buffer.seek(0)
    return buffer
