{% load static %} {% load humanize %}
<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-color: #7AB2D3;
        --primary-dark: #5a9bd4;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .font-display {
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      @media print {
        body {
          background: white !important;
        }
        .no-print {
          display: none !important;
        }
      }
    </style>

    <title>Outstanding Balances Report - Tiny Feet Academy</title>
  </head>
  <body class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
    <div class="w-full max-w-7xl mx-auto space-y-8">
      <!-- Header Section -->
      <div class="bg-white rounded-3xl shadow-2xl p-8 border border-gray-200/50">
        <div class="flex flex-col lg:flex-row items-center justify-between gap-6">
          <div class="flex items-center gap-6">
            <div class="w-20 h-20 bg-gradient-to-br from-[var(--primary-color)] to-[var(--primary-dark)] rounded-3xl flex items-center justify-center shadow-xl">
              <img src="{% static 'img/tinyfeet.jpg' %}" alt="Tiny Feet Academy" class="w-16 h-16 rounded-2xl object-cover" />
            </div>
            <div>
              <h1 class="font-display font-bold text-3xl text-gray-800">Tiny Feet Academy</h1>
              <p class="text-gray-600 text-lg font-medium">Outstanding Balances Report</p>
              <div class="w-32 h-1 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] rounded-full mt-2"></div>
            </div>
          </div>
          <div class="bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl p-4 border border-red-200/50">
            <p class="text-xs font-bold text-gray-600 uppercase tracking-wider mb-1">Report Period</p>
            <p class="text-red-600 font-bold text-xl">{{cycle}} Payments</p>
          </div>
        </div>
      </div>

      <!-- Outstanding Balances Table -->
      <div class="bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-200/50">
        <div class="p-6 bg-gradient-to-r from-gray-50 to-blue-50 border-b border-gray-200/50">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
              <i class="fas fa-exclamation-triangle text-white text-sm"></i>
            </div>
            <h2 class="text-xl font-bold text-gray-800 font-display">Outstanding Balances</h2>
            <div class="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"></div>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full">
            <thead class="bg-gradient-to-r from-gray-50 to-blue-50">
              <tr>
                <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-id-card text-blue-500"></i>
                    Student ID
                  </div>
                </th>
                <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-user text-green-500"></i>
                    Student Name
                  </div>
                </th>
                <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-graduation-cap text-purple-500"></i>
                    Level
                  </div>
                </th>
                <th class="px-6 py-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div class="flex items-center justify-end gap-2">
                    <i class="fas fa-money-bill text-emerald-500"></i>
                    Total Fees
                  </div>
                </th>
                <th class="px-6 py-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div class="flex items-center justify-end gap-2">
                    <i class="fas fa-exclamation-circle text-red-500"></i>
                    Outstanding Balance
                  </div>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              {% for account in fee_accounts %}
              <tr class="hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 transition-all duration-300">
                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                  <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                      {{account.student.student_id|slice:":2"}}
                    </div>
                    {{account.student.student_id}}
                  </div>
                </td>
                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                  <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                      <i class="fas fa-user text-white text-xs"></i>
                    </div>
                    {{account.student.name}}
                  </div>
                </td>
                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                  <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-xs font-bold">
                    {{account.student.level.level_name}}
                  </span>
                </td>
                <td class="px-6 py-4 text-sm font-bold text-right text-emerald-600">
                  MWK {{account.total_due|intcomma}}
                </td>
                <td class="px-6 py-4 text-sm font-bold text-right">
                  MWK {{account.balance|intcomma}}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <script>
      onload(window.print());
    </script>
  </body>
</html>
