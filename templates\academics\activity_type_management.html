{% extends 'academics/base.html' %} {% load humanize %}
<!--  -->
{% block title %}Activity Type Management | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-tags text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Activity Type Management
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Manage and organize activity types for academic activities
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#40657F] to-[#2C3E50] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 action-buttons-slide-in">
        <a
          href="{% url 'academics:add_activity_type' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white font-bold py-4 px-8 rounded-xl hover:from-[#2C3E50] hover:to-[#40657F] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-plus group-hover:rotate-90 transition-all duration-300"
          ></i>
          <span>Add Activity Type</span>
        </a>
        <a
          href="{% url 'academics:bulk_delete_activity_types' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] text-white font-bold py-4 px-8 rounded-xl hover:from-[#e74c3c] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-trash group-hover:scale-110 transition-all duration-300"
          ></i>
          <span>Bulk Delete</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Navigation Breadcrumb -->
  <div class="breadcrumb-fade-in">
    <nav class="flex items-center gap-2 text-sm text-[#40657F]">
      <a
        href="{% url 'academics:academic_management_dashboard' %}"
        class="hover:text-[#40657F] transition-colors duration-200"
      >
        <i class="fas fa-graduation-cap mr-1"></i>
        Academic Management
      </a>
      <i class="fas fa-chevron-right text-[#B9D8EB]"></i>
      <span class="text-[#2C3E50] font-medium">Activity Types</span>
    </nav>
  </div>

  <!-- Statistics Section -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 stats-cards-fade-in">
    <!-- Total Activity Types -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#40657F]/5 to-[#2C3E50]/5 border-l-4 border-[#40657F]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#40657F] rounded-xl flex items-center justify-center">
          <i class="fas fa-tags text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Total Activity Types</h3>
          <p class="text-[#40657F] font-medium text-2xl">{{ stats.total_activity_types }}</p>
        </div>
      </div>
    </div>

    <!-- Total Activities -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5 border-l-4 border-[#74C69D]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#74C69D] rounded-xl flex items-center justify-center">
          <i class="fas fa-calendar-alt text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Total Activities</h3>
          <p class="text-[#40657F] font-medium text-2xl">{{ stats.total_activities }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Activity Types Section -->
  <div class="card-modern p-8 activity-types-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
      >
        <i class="fas fa-tags text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Activity Types
        </h3>
        <p class="text-[#40657F] text-sm">
          {% if search_query %}
            Search results for "{{ search_query }}"
          {% else %}
            All activity types in the system
          {% endif %}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div class="flex items-center gap-2">
        <div
          class="flex items-center gap-2 bg-[#40657F]/20 text-[#40657F] px-4 py-2 rounded-full font-bold border border-[#40657F]/30"
        >
          <i class="fas fa-tags text-sm"></i>
          <span>{{ total_count }} type{{ total_count|pluralize }}</span>
        </div>
      </div>
    </div>

    <!-- Search -->
    <form method="GET" class="mb-6">
      <div class="flex gap-4">
        <div class="relative flex-1">
          <input
            type="text"
            name="search"
            value="{{ search_query }}"
            placeholder="Search activity types..."
            class="w-full px-4 py-3 pl-12 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#40657F] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]/60"
          />
          <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#40657F]"></i>
        </div>
        <div class="flex gap-2">
          <button
            type="submit"
            class="bg-[#40657F] text-white px-6 py-3 rounded-xl hover:bg-[#2C3E50] transition-colors duration-200 font-medium"
          >
            <i class="fas fa-search mr-2"></i>
            Search
          </button>
          {% if search_query %}
          <a
            href="{% url 'academics:activity_type_management' %}"
            class="bg-[#B9D8EB] text-[#40657F] px-4 py-3 rounded-xl hover:bg-[#E2F1F9] transition-colors duration-200 font-medium"
          >
            <i class="fas fa-times"></i>
          </a>
          {% endif %}
        </div>
      </div>
    </form>

    <!-- Activity Types Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {% for activity_type in activity_types %}
      <div
        class="activity-type-card bg-gradient-to-br from-white via-[#F7FAFC] to-[#E2F1F9] border-2 border-[#B9D8EB] hover:border-[#40657F] hover:shadow-xl hover:-translate-y-2 transition-all duration-300 p-6 rounded-2xl relative overflow-hidden group"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-12 h-12 bg-gradient-to-br from-[#40657F]/20 to-[#2C3E50]/10 rounded-full -translate-y-6 translate-x-6 group-hover:scale-125 transition-transform duration-500"
        ></div>

        <!-- Activity Type Header -->
        <div class="relative z-10 mb-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-lg font-bold text-[#2C3E50]">
              {{ activity_type.name }}
            </h4>
            <div class="w-8 h-8 bg-[#40657F] rounded-lg flex items-center justify-center">
              <i class="fas fa-tag text-white text-sm"></i>
            </div>
          </div>
        </div>

        <!-- Activity Type Details -->
        <div class="relative z-10 mb-4">
          {% if activity_type.description %}
          <p class="text-sm text-[#40657F] line-clamp-3">
            {{ activity_type.description }}
          </p>
          {% else %}
          <p class="text-sm text-[#B9D8EB] italic">
            No description provided
          </p>
          {% endif %}
        </div>

        <!-- Activity Count -->
        <div class="relative z-10 mb-4">
          <div class="flex items-center gap-2 text-sm text-[#40657F]">
            <i class="fas fa-calendar-alt"></i>
            <span>{{ activity_type.activities.count }} activit{{ activity_type.activities.count|pluralize:"y,ies" }}</span>
          </div>
        </div>

        <!-- Activity Type Actions -->
        <div class="relative z-10 flex gap-2">
          <a
            href="{% url 'academics:activity_type_details' activity_type.pk %}"
            class="flex-1 bg-[#7AB2D3] text-white text-center py-2 px-3 rounded-lg hover:bg-[#40657F] transition-colors duration-200 text-sm font-medium"
          >
            <i class="fas fa-eye mr-1"></i>
            View
          </a>
          <a
            href="{% url 'academics:edit_activity_type' activity_type.pk %}"
            class="flex-1 bg-[#40657F] text-white text-center py-2 px-3 rounded-lg hover:bg-[#2C3E50] transition-colors duration-200 text-sm font-medium"
          >
            <i class="fas fa-edit mr-1"></i>
            Edit
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#40657F]/5 to-[#2C3E50]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>
      {% empty %}
      <div class="col-span-full text-center py-12">
        <div class="flex flex-col items-center gap-4">
          <div
            class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-tags text-[#B9D8EB] text-2xl"></i>
          </div>
          <div>
            <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
              No Activity Types Found
            </h3>
            <p class="text-[#40657F]">
              {% if search_query %}
                No activity types match your search criteria.
              {% else %}
                Get started by creating your first activity type.
              {% endif %}
            </p>
          </div>
          <a
            href="{% url 'academics:add_activity_type' %}"
            class="inline-flex items-center gap-2 bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white font-bold py-3 px-6 rounded-xl hover:from-[#2C3E50] hover:to-[#40657F] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
          >
            <i class="fas fa-plus"></i>
            <span>Add First Activity Type</span>
          </a>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- Pagination -->
    {% if activity_types.has_other_pages %}
    <div class="flex justify-center items-center gap-4 mt-8">
      <div class="flex items-center gap-2">
        {% if activity_types.has_previous %}
        <a
          href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#40657F] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-left"></i>
        </a>
        <a
          href="?page={{ activity_types.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#40657F] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-left"></i>
        </a>
        {% endif %}

        <span class="px-4 py-2 bg-[#40657F] text-white rounded-lg font-semibold">
          Page {{ activity_types.number }} of {{ activity_types.paginator.num_pages }}
        </span>

        {% if activity_types.has_next %}
        <a
          href="?page={{ activity_types.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#40657F] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-right"></i>
        </a>
        <a
          href="?page={{ activity_types.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#40657F] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-right"></i>
        </a>
        {% endif %}
      </div>
    </div>
    {% endif %}
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 1s forwards;
  }

  /* Stats Cards Animation */
  .stats-cards-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: statsCardsFadeIn 0.8s ease-out 1.2s forwards;
  }

  /* Activity Types Section Animation */
  .activity-types-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: activityTypesSectionFadeIn 0.8s ease-out 1.4s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  .activity-type-card {
    opacity: 0;
    transform: translateY(20px);
    animation: activityTypeCardSlideIn 0.4s ease-out forwards;
  }

  .activity-type-card:nth-child(1) { animation-delay: 1.6s; }
  .activity-type-card:nth-child(2) { animation-delay: 1.7s; }
  .activity-type-card:nth-child(3) { animation-delay: 1.8s; }
  .activity-type-card:nth-child(4) { animation-delay: 1.9s; }
  .activity-type-card:nth-child(5) { animation-delay: 2s; }
  .activity-type-card:nth-child(6) { animation-delay: 2.1s; }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes actionButtonsSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes breadcrumbFadeIn {
    to { opacity: 1; }
  }

  @keyframes statsCardsFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes activityTypesSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes tableIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes activityTypeCardSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .activity-type-card {
      animation-delay: 1.4s;
    }

    .activity-type-card:nth-child(n) {
      animation-delay: calc(1.4s + 0.1s * var(--item-index, 1));
    }
  }
</style>

{% endblock %}
