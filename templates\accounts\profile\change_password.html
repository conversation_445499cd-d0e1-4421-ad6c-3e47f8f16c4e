{% extends 'base.html' %}
{% load static %}
{% load rbac_tags %}

{% block title %}Change Password | {% endblock %}

{% block content %}
<section class="w-full max-w-3xl mx-auto px-4 py-8 space-y-8">
  <!-- Header -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div class="w-12 h-12 bg-gradient-to-br from-[#FFB84D] to-[#e6a43d] rounded-2xl flex items-center justify-center shadow-lg icon-float">
        <i class="fas fa-key text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
          Change Password
        </h1>
        <div class="w-20 h-1 bg-gradient-to-r from-[#FFB84D] to-[#e6a43d] rounded-full accent-line-grow"></div>
        <p class="text-[#40657F] mt-2">Update your account password</p>
      </div>
    </div>
    
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <a href="{% url 'accounts:user_profile' %}{% if request.GET.from == 'academics' %}?from=academics{% endif %}" class="text-[#40657F] hover:text-[#7AB2D3]">My Account</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Change Password</span>
    </nav>
  </div>

  <!-- Form -->
  <div class="card-modern p-8">
    <form method="post" class="space-y-8">
      {% csrf_token %}
      
      <!-- Password Change Form -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-shield-alt text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">Security Update</h3>
            <p class="text-[#40657F] text-sm">Enter your current password and choose a new one</p>
          </div>
        </div>
        
        <div class="space-y-6">
          <div>
            <label for="{{ form.old_password.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Current Password <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.old_password }}
            {% if form.old_password.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.old_password.errors.0 }}</div>
            {% endif %}
            <div class="mt-1 text-xs text-[#40657F]">Enter your current password to verify your identity</div>
          </div>
          
          <div>
            <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              New Password <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.new_password1 }}
            {% if form.new_password1.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.new_password1.errors.0 }}</div>
            {% endif %}
            <div class="mt-1 text-xs text-[#40657F]">Choose a strong password with at least 8 characters</div>
          </div>
          
          <div>
            <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Confirm New Password <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.new_password2 }}
            {% if form.new_password2.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.new_password2.errors.0 }}</div>
            {% endif %}
            <div class="mt-1 text-xs text-[#40657F]">Re-enter your new password to confirm</div>
          </div>
        </div>
      </div>

      <!-- Password Requirements -->
      <div class="p-6 bg-[#E2F1F9] rounded-xl border border-[#B9D8EB]">
        <div class="flex items-start gap-3">
          <i class="fas fa-info-circle text-[#7AB2D3] mt-1"></i>
          <div>
            <h4 class="font-semibold text-[#2C3E50] mb-3">Password Requirements</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-[#40657F]">
              <div class="flex items-center gap-2">
                <i class="fas fa-check text-[#74C69D] text-xs"></i>
                <span>At least 8 characters long</span>
              </div>
              <div class="flex items-center gap-2">
                <i class="fas fa-check text-[#74C69D] text-xs"></i>
                <span>Mix of letters and numbers</span>
              </div>
              <div class="flex items-center gap-2">
                <i class="fas fa-check text-[#74C69D] text-xs"></i>
                <span>Include special characters</span>
              </div>
              <div class="flex items-center gap-2">
                <i class="fas fa-check text-[#74C69D] text-xs"></i>
                <span>Avoid common passwords</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Security Notice -->
      <div class="p-6 bg-[#FFF5F5] rounded-xl border border-[#F28C8C]">
        <div class="flex items-start gap-3">
          <i class="fas fa-exclamation-triangle text-[#F28C8C] mt-1"></i>
          <div>
            <h4 class="font-semibold text-[#2C3E50] mb-2">Important Security Notice</h4>
            <div class="text-sm text-[#40657F] space-y-1">
              <p>• You will remain logged in after changing your password.</p>
              <p>• Choose a password that you haven't used before on this system.</p>
              <p>• Don't share your password with anyone.</p>
              <p>• If you suspect your account has been compromised, change your password immediately.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Password Strength Indicator -->
      <div class="space-y-3">
        <label class="block text-sm font-semibold text-[#2C3E50]">Password Strength</label>
        <div class="flex items-center gap-3">
          <div class="flex-1 bg-[#E2F1F9] rounded-full h-2">
            <div id="password-strength-bar" class="h-2 rounded-full transition-all duration-300 bg-[#F28C8C]" style="width: 0%"></div>
          </div>
          <span id="password-strength-text" class="text-sm font-medium text-[#F28C8C]">Weak</span>
        </div>
        <div class="text-xs text-[#40657F]">
          <span id="password-feedback">Enter a password to see strength</span>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-between pt-6 border-t border-[#E2F1F9]">
        <a href="{% url 'accounts:user_profile' %}{% if request.GET.from == 'academics' %}?from=academics{% endif %}"
           class="px-6 py-3 border border-[#B9D8EB] text-[#40657F] rounded-xl font-semibold hover:bg-[#F7FAFC] transition-colors">
          <i class="fas fa-arrow-left mr-2"></i>Cancel
        </a>
        
        <button type="submit" 
                class="px-8 py-3 bg-gradient-to-r from-[#FFB84D] to-[#e6a43d] text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
          <i class="fas fa-key mr-2"></i>Change Password
        </button>
      </div>
    </form>
  </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitButton = form.querySelector('button[type="submit"]');
    const newPasswordField = document.getElementById('{{ form.new_password1.id_for_label }}');
    const confirmPasswordField = document.getElementById('{{ form.new_password2.id_for_label }}');
    const strengthBar = document.getElementById('password-strength-bar');
    const strengthText = document.getElementById('password-strength-text');
    const passwordFeedback = document.getElementById('password-feedback');
    
    // Password strength checker
    function checkPasswordStrength(password) {
        let score = 0;
        let feedback = [];
        
        if (password.length >= 8) score += 1;
        else feedback.push('At least 8 characters');
        
        if (/[a-z]/.test(password)) score += 1;
        else feedback.push('Lowercase letters');
        
        if (/[A-Z]/.test(password)) score += 1;
        else feedback.push('Uppercase letters');
        
        if (/[0-9]/.test(password)) score += 1;
        else feedback.push('Numbers');
        
        if (/[^A-Za-z0-9]/.test(password)) score += 1;
        else feedback.push('Special characters');
        
        return { score, feedback };
    }
    
    // Update password strength indicator
    function updatePasswordStrength(password) {
        const { score, feedback } = checkPasswordStrength(password);
        const percentage = (score / 5) * 100;
        
        strengthBar.style.width = percentage + '%';
        
        if (score <= 2) {
            strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-[#F28C8C]';
            strengthText.textContent = 'Weak';
            strengthText.className = 'text-sm font-medium text-[#F28C8C]';
        } else if (score <= 3) {
            strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-[#FFB84D]';
            strengthText.textContent = 'Fair';
            strengthText.className = 'text-sm font-medium text-[#FFB84D]';
        } else if (score <= 4) {
            strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-[#7AB2D3]';
            strengthText.textContent = 'Good';
            strengthText.className = 'text-sm font-medium text-[#7AB2D3]';
        } else {
            strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-[#74C69D]';
            strengthText.textContent = 'Strong';
            strengthText.className = 'text-sm font-medium text-[#74C69D]';
        }
        
        if (feedback.length > 0) {
            passwordFeedback.textContent = 'Missing: ' + feedback.join(', ');
        } else {
            passwordFeedback.textContent = 'Password meets all requirements';
        }
    }
    
    // Password strength checking
    if (newPasswordField) {
        newPasswordField.addEventListener('input', function() {
            updatePasswordStrength(this.value);
        });
    }
    
    // Password confirmation checking
    if (confirmPasswordField && newPasswordField) {
        confirmPasswordField.addEventListener('input', function() {
            if (this.value && this.value !== newPasswordField.value) {
                this.classList.add('border-[#F28C8C]');
            } else {
                this.classList.remove('border-[#F28C8C]');
            }
        });
    }
    
    // Form submission
    form.addEventListener('submit', function() {
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Changing Password...';
        submitButton.disabled = true;
    });
});
</script>

{% endblock %}
