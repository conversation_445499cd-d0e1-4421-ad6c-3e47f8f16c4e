# Generated manually to fix AssessmentType ID sequence issue

from django.db import migrations


def fix_assessmenttype_sequence(apps, schema_editor):
    """
    Custom function to fix the AssessmentType ID sequence issue.
    This approach is more compatible with Django's test framework.
    """
    if schema_editor.connection.vendor != 'postgresql':
        return  # Only run on PostgreSQL

    with schema_editor.connection.cursor() as cursor:
        # Check if the table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'academics_assessmenttype'
            );
        """)
        table_exists = cursor.fetchone()[0]

        if not table_exists:
            # Skip if table doesn't exist (e.g., in fresh test database)
            return

        # Check if ID column is an identity column or already has a default
        cursor.execute("""
            SELECT column_default, is_identity
            FROM information_schema.columns
            WHERE table_name = 'academics_assessmenttype'
            AND column_name = 'id';
        """)
        result = cursor.fetchone()

        if result:
            column_default, is_identity = result
            # Skip if it's already an identity column or has a sequence default
            if is_identity == 'YES' or (column_default and 'nextval' in str(column_default)):
                return

        # Create sequence and set as default (only if not identity column)
        try:
            cursor.execute(
                "CREATE SEQUENCE IF NOT EXISTS academics_assessmenttype_new_id_seq;")

            # Set sequence to start from a safe number (max existing ID + 1, or 1000 if table is empty)
            # Using 1000 as minimum to avoid conflicts in test databases
            cursor.execute(
                "SELECT COALESCE(MAX(id), 0) FROM academics_assessmenttype;")
            max_id = cursor.fetchone()[0]
            start_value = max(max_id + 1, 1000)
            cursor.execute(
                f"SELECT setval('academics_assessmenttype_new_id_seq', {start_value});")

            cursor.execute(
                "ALTER TABLE academics_assessmenttype ALTER COLUMN id SET DEFAULT nextval('academics_assessmenttype_new_id_seq');")
        except Exception as e:
            # If it fails due to identity column restrictions, skip silently
            # The column might already be properly configured
            if 'identity column' in str(e).lower():
                pass
            else:
                raise


def reverse_fix_assessmenttype_sequence(apps, schema_editor):
    """Reverse the sequence fix"""
    if schema_editor.connection.vendor != 'postgresql':
        return

    with schema_editor.connection.cursor() as cursor:
        cursor.execute(
            "ALTER TABLE academics_assessmenttype ALTER COLUMN id DROP DEFAULT;")
        cursor.execute(
            "DROP SEQUENCE IF EXISTS academics_assessmenttype_new_id_seq CASCADE;")


class Migration(migrations.Migration):

    dependencies = [
        ('academics', '0003_alter_activity_table_alter_assessmenttype_table_and_more'),
    ]

    operations = [
        migrations.RunPython(
            fix_assessmenttype_sequence,
            reverse_fix_assessmenttype_sequence,
        ),
    ]
