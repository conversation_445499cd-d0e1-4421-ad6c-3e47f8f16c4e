{% extends 'academics/base.html' %}
{% load static %}
{% load rbac_tags %}

{% block title %}My Account | {% endblock %}

{% block content %}
<section class="w-full max-w-6xl mx-auto px-4 py-8 space-y-8">
  <!-- Header -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-4">
        <div class="w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg icon-float">
          <span class="text-white text-2xl font-bold">
            {{ user_obj.first_name|first|upper }}{{ user_obj.last_name|first|upper }}
          </span>
        </div>
        <div>
          <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
            My Account
          </h1>
          <div class="w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full accent-line-grow"></div>
          <p class="text-[#40657F] mt-2">{{ user_obj.get_full_name|default:user_obj.username }}</p>
        </div>
      </div>
      
      <div class="flex gap-3">
        <a href="{% url 'academics:edit_profile' %}" 
           class="btn-secondary flex items-center gap-2 px-4 py-2 rounded-lg font-medium">
          <i class="fas fa-edit"></i>
          Edit Profile
        </a>
        
        <a href="{% url 'academics:change_password' %}" 
           class="btn-primary flex items-center gap-2 px-4 py-2 rounded-lg font-medium">
          <i class="fas fa-key"></i>
          Change Password
        </a>
      </div>
    </div>
  </div>

  <!-- Account Information Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    
    <!-- Main Information -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Personal Information -->
      <div class="card-modern p-8">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-user text-white text-lg"></i>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-[#2C3E50]">Personal Information</h2>
            <div class="w-16 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full"></div>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <label class="text-sm font-semibold text-[#40657F] uppercase tracking-wider">First Name</label>
            <p class="text-lg font-medium text-[#2C3E50] bg-[#F7FAFC] p-3 rounded-lg border border-[#E2F1F9]">
              {{ user_obj.first_name|default:"Not provided" }}
            </p>
          </div>
          
          <div class="space-y-2">
            <label class="text-sm font-semibold text-[#40657F] uppercase tracking-wider">Last Name</label>
            <p class="text-lg font-medium text-[#2C3E50] bg-[#F7FAFC] p-3 rounded-lg border border-[#E2F1F9]">
              {{ user_obj.last_name|default:"Not provided" }}
            </p>
          </div>
          
          <div class="space-y-2">
            <label class="text-sm font-semibold text-[#40657F] uppercase tracking-wider">Username</label>
            <p class="text-lg font-medium text-[#2C3E50] bg-[#F7FAFC] p-3 rounded-lg border border-[#E2F1F9]">
              {{ user_obj.username }}
            </p>
          </div>
          
          <div class="space-y-2">
            <label class="text-sm font-semibold text-[#40657F] uppercase tracking-wider">Email</label>
            <p class="text-lg font-medium text-[#2C3E50] bg-[#F7FAFC] p-3 rounded-lg border border-[#E2F1F9]">
              {{ user_obj.email|default:"Not provided" }}
            </p>
          </div>
          
          <div class="space-y-2">
            <label class="text-sm font-semibold text-[#40657F] uppercase tracking-wider">Gender</label>
            <p class="text-lg font-medium text-[#2C3E50] bg-[#F7FAFC] p-3 rounded-lg border border-[#E2F1F9]">
              {{ user_obj.gender|default:"Not specified" }}
            </p>
          </div>
          
          <div class="space-y-2">
            <label class="text-sm font-semibold text-[#40657F] uppercase tracking-wider">Phone Number</label>
            <p class="text-lg font-medium text-[#2C3E50] bg-[#F7FAFC] p-3 rounded-lg border border-[#E2F1F9]">
              {{ user_obj.phone_number|default:"Not provided" }}
            </p>
          </div>
        </div>
      </div>

      <!-- Roles and Permissions -->
      {% if active_roles %}
      <div class="card-modern p-8">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-shield-alt text-white text-lg"></i>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-[#2C3E50]">Roles & Permissions</h2>
            <div class="w-16 h-1 bg-gradient-to-r from-[#74C69D] to-[#5fb085] rounded-full"></div>
          </div>
        </div>
        
        <!-- Active Roles -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-[#2C3E50] mb-3">Active Roles</h3>
          <div class="flex flex-wrap gap-2">
            {% for user_role in active_roles %}
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30">
                <i class="fas fa-user-tag mr-2"></i>
                {{ user_role.role.display_name }}
                <span class="ml-2 text-xs opacity-75">(Level {{ user_role.role.level }})</span>
              </span>
            {% endfor %}
          </div>
        </div>
        
        <!-- Permissions by Category -->
        {% if permissions_by_category %}
        <div>
          <h3 class="text-lg font-semibold text-[#2C3E50] mb-3">Permissions</h3>
          <div class="space-y-4">
            {% for category, permissions in permissions_by_category.items %}
              <div class="bg-[#F7FAFC] p-4 rounded-lg border border-[#E2F1F9]">
                <h4 class="font-semibold text-[#40657F] mb-2 capitalize">{{ category }}</h4>
                <div class="flex flex-wrap gap-1">
                  {% for permission in permissions %}
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30">
                      {{ permission.display_name }}
                    </span>
                  {% endfor %}
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
        {% endif %}
      </div>
      {% endif %}
    </div>
    
    <!-- Sidebar -->
    <div class="space-y-8">
      
      <!-- Account Stats -->
      <div class="card-modern p-6">
        <div class="flex items-center gap-3 mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-[#FFB84D] to-[#e6a43d] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-chart-bar text-white"></i>
          </div>
          <h3 class="text-xl font-bold text-[#2C3E50]">Account Overview</h3>
        </div>
        
        <div class="space-y-4">
          <div class="flex justify-between items-center p-3 bg-[#F7FAFC] rounded-lg border border-[#E2F1F9]">
            <span class="text-sm font-medium text-[#40657F]">Role Level</span>
            <span class="font-bold text-[#2C3E50]">{{ audit_data.role_level|default:"N/A" }}</span>
          </div>
          
          <div class="flex justify-between items-center p-3 bg-[#F7FAFC] rounded-lg border border-[#E2F1F9]">
            <span class="text-sm font-medium text-[#40657F]">Active Roles</span>
            <span class="font-bold text-[#2C3E50]">{{ audit_data.total_roles }}</span>
          </div>
          
          <div class="flex justify-between items-center p-3 bg-[#F7FAFC] rounded-lg border border-[#E2F1F9]">
            <span class="text-sm font-medium text-[#40657F]">Total Permissions</span>
            <span class="font-bold text-[#2C3E50]">{{ audit_data.total_permissions }}</span>
          </div>
          
          <div class="flex justify-between items-center p-3 bg-[#F7FAFC] rounded-lg border border-[#E2F1F9]">
            <span class="text-sm font-medium text-[#40657F]">Member Since</span>
            <span class="font-bold text-[#2C3E50]">{{ user_obj.date_joined|date:"M Y" }}</span>
          </div>
        </div>
      </div>
      
      <!-- Quick Actions -->
      <div class="card-modern p-6">
        <div class="flex items-center gap-3 mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-[#F28C8C] to-[#e74c3c] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-bolt text-white"></i>
          </div>
          <h3 class="text-xl font-bold text-[#2C3E50]">Quick Actions</h3>
        </div>
        
        <div class="space-y-3">
          <a href="{% url 'academics:edit_profile' %}" 
             class="w-full flex items-center gap-3 p-3 bg-[#F7FAFC] hover:bg-[#E2F1F9] border border-[#E2F1F9] rounded-lg transition-colors">
            <i class="fas fa-edit text-[#7AB2D3]"></i>
            <span class="text-[#2C3E50] font-medium">Edit Profile Information</span>
          </a>
          
          <a href="{% url 'academics:change_password' %}" 
             class="w-full flex items-center gap-3 p-3 bg-[#F7FAFC] hover:bg-[#E2F1F9] border border-[#E2F1F9] rounded-lg transition-colors">
            <i class="fas fa-key text-[#FFB84D]"></i>
            <span class="text-[#2C3E50] font-medium">Change Password</span>
          </a>
          
          <a href="{% url 'academics:dashboard' %}" 
             class="w-full flex items-center gap-3 p-3 bg-[#F7FAFC] hover:bg-[#E2F1F9] border border-[#E2F1F9] rounded-lg transition-colors">
            <i class="fas fa-graduation-cap text-[#74C69D]"></i>
            <span class="text-[#2C3E50] font-medium">Back to Academics</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

{% endblock %}
