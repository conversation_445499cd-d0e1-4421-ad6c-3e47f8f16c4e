/* Mobile-First Responsive Styles */

/* Base Mobile Styles (320px and up) */
.mobile-container {
    padding: 0.5rem;
    max-width: 100%;
}

/* Touch-Friendly Elements */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
}

.btn-mobile {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-mobile:active {
    transform: scale(0.98);
}

/* Mobile Navigation */
.mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.mobile-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    height: 64px;
}

.mobile-nav-logo {
    height: 32px;
    width: auto;
}

.mobile-nav-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-left: 0.75rem;
}

.mobile-menu-button {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    color: #6b7280;
    transition: all 0.2s ease;
}

.mobile-menu-button:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.mobile-menu {
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    max-height: calc(100vh - 64px);
    overflow-y: auto;
}

.mobile-menu-item {
    display: block;
    padding: 1rem;
    color: #374151;
    text-decoration: none;
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
}

.mobile-menu-item:hover {
    background-color: #e5e7eb;
    color: #1f2937;
}

.mobile-menu-item i {
    margin-right: 0.75rem;
    width: 1.25rem;
    text-align: center;
}

/* Mobile Content Layout */
.mobile-content {
    margin-top: 64px; /* Account for fixed nav */
    padding: 1rem 0.5rem;
}

.mobile-page-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.mobile-page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.mobile-page-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Mobile Cards */
.mobile-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.mobile-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
}

.mobile-card-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.mobile-card-action {
    flex: 1;
    padding: 0.75rem;
    background: #f3f4f6;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    color: #374151;
    text-align: center;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.mobile-card-action:hover {
    background: #e5e7eb;
}

.mobile-card-action.primary {
    background: #40657F;
    color: white;
}

.mobile-card-action.primary:hover {
    background: #2c3e50;
}

/* Mobile Forms */
.mobile-form {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.mobile-form-group {
    margin-bottom: 1rem;
}

.mobile-form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.mobile-form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 16px; /* Prevents zoom on iOS */
    background: white;
    color: #1f2937;
}

.mobile-form-input:focus {
    outline: none;
    border-color: #40657F;
    box-shadow: 0 0 0 1px #40657F;
}

.mobile-form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 16px;
    background: white;
    color: #1f2937;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.mobile-form-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1.5rem;
}

.mobile-form-button {
    width: 100%;
    padding: 0.875rem;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-form-button.primary {
    background: #40657F;
    color: white;
}

.mobile-form-button.primary:hover {
    background: #2c3e50;
}

.mobile-form-button.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.mobile-form-button.secondary:hover {
    background: #e5e7eb;
}

/* Mobile Tables */
.mobile-table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.mobile-table-header {
    background: #f9fafb;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
    color: #374151;
}

.mobile-table-row {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
}

.mobile-table-row:last-child {
    border-bottom: none;
}

.mobile-table-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.mobile-table-cell:last-child {
    margin-bottom: 0;
}

.mobile-table-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.mobile-table-value {
    font-size: 0.875rem;
    color: #1f2937;
    text-align: right;
}

/* Mobile Statistics */
.mobile-stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.mobile-stat-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    font-size: 1.25rem;
}

.mobile-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.mobile-stat-label {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Mobile Alerts */
.mobile-alert {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.mobile-alert.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.mobile-alert.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.mobile-alert.warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.mobile-alert.info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.mobile-alert-icon {
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.mobile-alert-content {
    flex: 1;
}

.mobile-alert-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.mobile-alert-message {
    font-size: 0.875rem;
}

/* Responsive Breakpoints */

/* Small devices (480px and up) */
@media (min-width: 480px) {
    .mobile-container {
        padding: 1rem;
    }
    
    .mobile-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .mobile-form-actions {
        flex-direction: row;
    }
    
    .mobile-card-actions {
        justify-content: flex-end;
    }
    
    .mobile-card-action {
        flex: none;
        min-width: 80px;
    }
}

/* Medium devices (768px and up) */
@media (min-width: 768px) {
    .mobile-nav {
        position: static;
        box-shadow: none;
    }
    
    .mobile-content {
        margin-top: 0;
        padding: 2rem 1rem;
    }
    
    .mobile-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .mobile-table-container {
        display: none; /* Use regular table on larger screens */
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .btn-mobile,
    .mobile-menu-item,
    .mobile-card-action,
    .mobile-form-button {
        transition: none;
    }
    
    .btn-mobile:active {
        transform: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .mobile-card,
    .mobile-form,
    .mobile-table-container {
        border: 2px solid #000;
    }
    
    .mobile-form-input,
    .mobile-form-select {
        border: 2px solid #000;
    }
    
    .mobile-form-input:focus,
    .mobile-form-select:focus {
        border-color: #000;
        box-shadow: 0 0 0 2px #000;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .mobile-nav {
        background: #1f2937;
        border-color: #374151;
    }
    
    .mobile-nav-title {
        color: #f9fafb;
    }
    
    .mobile-menu {
        background: #111827;
        border-color: #374151;
    }
    
    .mobile-menu-item {
        color: #d1d5db;
        border-color: #374151;
    }
    
    .mobile-menu-item:hover {
        background: #374151;
        color: #f9fafb;
    }
    
    .mobile-card,
    .mobile-form,
    .mobile-table-container {
        background: #1f2937;
        color: #f9fafb;
    }
    
    .mobile-form-input,
    .mobile-form-select {
        background: #374151;
        color: #f9fafb;
        border-color: #4b5563;
    }
}
