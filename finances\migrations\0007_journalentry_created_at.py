# Generated by Django 5.1.2 on 2025-07-03 15:42

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('finances', '0006_add_reversal_fields_to_outflow'),
    ]

    operations = [
        migrations.AddField(
            model_name='journalentry',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
    ]
