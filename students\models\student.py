from django.db import models

from helpers.strings import generate_unique_ids


from students.utils.fee_calculations import calculate_monthly_fee


class Student(models.Model):
    name = models.CharField(max_length=200)
    student_id = models.SlugField(unique=True, blank=True, null=True)
    gender = models.CharField(
        max_length=10, choices=[
            ('Male', 'Male'), ('Female', 'Female')
        ], default='Male'
    )
    # if the student is enrolled or transferred.
    level = models.ForeignKey('students.Level', on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)

    @property
    def enrollments(self):
        return self.enrollment_set.filter(term__is_active=True)

    @property
    def overall_score(self):
        return sum(
            enrollment.final_grade for enrollment in self.enrollments)

    @property
    def avarage_score(self):
        return self.overall_score / self.enrollments.count()

    @property
    def has_passed(self):
        return self.avarage_score >= 40

    def generate_student_id(self):
        prefix = f"{self.level.abbrv}"
        self.student_id = generate_unique_ids(self, prefix)

    def delete_accounts_if_not_active(self):
        from finances.fee_management.models import FeeAccount
        FeeAccount.objects.filter(
            student=self, term__is_active=True).delete()

    def create_fee_accounts(self):
        from finances.fee_management.models import FeeAccount, FeeCategory
        from students.models import Term
        import logging

        logger = logging.getLogger(__name__)

        try:
            term = Term.objects.get(is_active=True)
        except Term.DoesNotExist:
            logger.error(
                f"No active term found when creating fee accounts for student {self.name}")
            return False
        except Term.MultipleObjectsReturned:
            logger.error(
                f"Multiple active terms found when creating fee accounts for student {self.name}")
            return False

        excluded_categories = (
            ["Primary Tuition Fees", "Primary Food Fees", "PTA"]
            if self.level.education_stage == "Nursery"
            else ["Nursery Tuition Fees", "Nursery Food Fees", "PTA"]
        )

        fee_categories = FeeCategory.objects.filter(is_active=True).exclude(
            name__in=excluded_categories)

        if not fee_categories.exists():
            logger.warning(
                f"No active fee categories found for student {self.name}")
            return False

        exisiting_monthly = set(
            FeeAccount.objects.filter(
                student=self, term__is_active=True, billing_cycle="Monthly"
            ).values_list("category", "month")
        )

        exisiting_termly = set(
            FeeAccount.objects.filter(
                student=self, term__is_active=True, billing_cycle="Termly"
            ).values_list("category", flat=True)
        )

        fee_accounts = []  # List for bulk creation (reduces DB hits)

        for fee_category in fee_categories:
            category_name = fee_category.name

            if "Food" in category_name:

                fees = calculate_monthly_fee(fee_category, term)
                for fee in fees:
                    key = (fee_category.id, fee["month"])

                    if key not in exisiting_monthly:
                        fee_accounts.append(
                            FeeAccount(
                                student=self,
                                term=term,
                                category=fee_category,
                                billing_cycle="Monthly",
                                month=fee["month"],
                                total_due=fee["fee"],
                            )
                        )
            else:
                if fee_category.id not in exisiting_termly:
                    fee = fee_category.amount
                    fee_accounts.append(
                        FeeAccount(
                            student=self,
                            term=term,
                            total_due=fee,
                            category=fee_category,
                            billing_cycle="Termly",
                        )
                    )

        # Bulk create new FeeAccounts (faster than multiple inserts)
        try:
            if fee_accounts:
                FeeAccount.objects.bulk_create(fee_accounts)
                logger.info(
                    f"Created {len(fee_accounts)} fee accounts for student {self.name}")
            else:
                logger.info(
                    f"No new fee accounts needed for student {self.name}")
        except Exception as e:
            logger.error(
                f"Error creating fee accounts for student {self.name}: {str(e)}")
            return False

        # IncomeTotal removed - using modern budget/ledger system
        return True

    def save(self, *args, **kwargs):
        # Handle new student creation
        if not self.id:
            super().save(*args, **kwargs)

        # Generate student ID if missing
        if not self.student_id:
            self.generate_student_id()
            super().save(update_fields=["student_id", "slug"])
            return  # Exit early to avoid multiple saves

        # Handle fee account creation/deletion based on active status
        if self.is_active:
            self.create_fee_accounts()
        else:
            self.delete_accounts_if_not_active()

        # Final save only if we haven't already saved above
        if self.id and self.student_id:
            super().save(*args, **kwargs)

    def __str__(self):
        return self.name
