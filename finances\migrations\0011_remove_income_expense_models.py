# Generated migration to remove deprecated Income and Expense models

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('finances', '0010_auto_20250706_0028'),
    ]

    operations = [
        # Drop Income table
        migrations.RunSQL(
            "DROP TABLE IF EXISTS finances_income CASCADE;",
            reverse_sql="-- Cannot reverse dropping Income table"
        ),

        # Drop Expenditure table
        migrations.RunSQL(
            "DROP TABLE IF EXISTS finances_expenditure CASCADE;",
            reverse_sql="-- Cannot reverse dropping Expenditure table"
        ),

        # Drop Expense table
        migrations.RunSQL(
            "DROP TABLE IF EXISTS finances_expense CASCADE;",
            reverse_sql="-- Cannot reverse dropping Expense table"
        ),

        # Drop ExpenseCategory table
        migrations.RunSQL(
            "DROP TABLE IF EXISTS finances_expensecategory CASCADE;",
            reverse_sql="-- Cannot reverse dropping ExpenseCategory table"
        ),
    ]
