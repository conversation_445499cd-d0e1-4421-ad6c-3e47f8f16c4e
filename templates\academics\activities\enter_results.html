{% extends "academics/base.html" %} {% load static %}
<!-- title -->
{% block title %}Enter Activity Results | {% endblock %}
<!-- title -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-trophy text-white text-lg sm:text-xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Enter Activity Results
          </h1>
          <p
            class="text-[#40657F] text-base sm:text-lg font-medium mt-1 subtitle-fade-in"
          >
            Record results for {{activity.activity_type}} - {{activity.class_assigned}}
          </p>
          <div
            class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3 action-buttons-slide-in">
        <a
          href="{% url 'academics:activities' %}"
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#7AB2D3] hover:text-white focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-arrow-left mr-2 group-hover:translate-x-[-2px] transition-transform duration-300"
          ></i>
          Back to Activities
        </a>
        <a
          href="{% url 'academics:view_activity_results' %}"
          class="bg-[#40657F] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#2C3E50] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-chart-line mr-2 group-hover:scale-110 transition-transform duration-300"
          ></i>
          View Results
        </a>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200 breadcrumb-fade-in"
    >
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#40657F]">Activities</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Enter Results</span>
    </nav>
  </div>

  <!-- Results Form -->
  <div class="card-modern p-8 results-form-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-users text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Participants & Results
        </h2>
        <p class="text-[#40657F] text-sm">
          Enter scores for {{participants|length}} participant{{ participants|length|pluralize }}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <form method="post" class="space-y-8">
      {% csrf_token %}

      <div class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/30 shadow-lg table-container-slide-in">
        <table class="min-w-full table-modern">
          <thead class="bg-[#E2F1F9] border-b border-[#B9D8EB]">
            <tr>
              <th class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider">
                <div class="flex items-center gap-2">
                  <i class="fas fa-hashtag text-[#7AB2D3]"></i>
                  Position
                </div>
              </th>
              <th class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider">
                <div class="flex items-center gap-2">
                  <i class="fas fa-user text-[#74C69D]"></i>
                  Participant Name
                </div>
              </th>
              <th class="px-6 py-4 text-center text-xs font-bold text-[#2C3E50] uppercase tracking-wider">
                <div class="flex items-center justify-center gap-2">
                  <i class="fas fa-trophy text-[#F28C8C]"></i>
                  Result/Score
                </div>
              </th>
              <th class="px-6 py-4 text-center text-xs font-bold text-[#2C3E50] uppercase tracking-wider">
                <div class="flex items-center justify-center gap-2">
                  <i class="fas fa-medal text-[#40657F]"></i>
                  Status
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#B9D8EB]">
            {% for participant in participants %}
            <tr class="participant-row hover:bg-gradient-to-r hover:from-[#E2F1F9]/50 hover:to-[#B9D8EB]/30 transition-all duration-300">
              <td class="px-6 py-4 text-sm font-medium text-[#2C3E50]">
                <div class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg">
                  {{ forloop.counter }}
                </div>
              </td>
              <td class="px-6 py-4 text-sm font-medium text-[#2C3E50]">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-full flex items-center justify-center shadow-sm">
                    <span class="text-white text-sm font-bold">{{ participant.name|first }}</span>
                  </div>
                  <div>
                    <span class="font-semibold text-[#2C3E50]">{{ participant.name }}</span>
                    <p class="text-xs text-[#40657F]">{{ participant.level.level_name|default:"Participant" }}</p>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 text-center">
                <div class="flex flex-col items-center gap-2">
                  <input
                    type="number"
                    name="{{ participant.id }}"
                    min="0"
                    max="1000"
                    step="any"
                    placeholder="Enter score"
                    class="w-32 px-4 py-3 bg-[#F7FAFC] border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] focus:bg-white outline-none transition-all duration-300 text-sm font-bold text-center text-[#2C3E50] result-input"
                    onchange="updateResultStatus(this)"
                    oninput="validateResultInput(this)"
                  />
                  <div class="result-error-message hidden text-xs text-[#F28C8C] font-medium bg-[#FEF2F2] px-2 py-1 rounded border border-[#F28C8C]/30">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    <span class="error-text"></span>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 text-center">
                <span class="result-status inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gray-200 text-gray-600">
                  <i class="fas fa-clock mr-1"></i>Pending
                </span>
              </td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="4" class="py-16 text-center">
                  <div class="flex flex-col items-center gap-6">
                    <div class="w-24 h-24 bg-gradient-to-br from-[#E2F1F9] to-[#B9D8EB] rounded-full flex items-center justify-center">
                      <i class="fas fa-users text-[#40657F] text-3xl"></i>
                    </div>
                    <div>
                      <h3 class="font-display font-bold text-2xl text-[#2C3E50] mb-2">
                        No Participants Found
                      </h3>
                      <p class="text-[#40657F] text-lg">
                        No participants have been added to this activity yet.
                      </p>
                      <p class="text-[#40657F]/70 text-sm mt-2">
                        Contact your administrator to add participants.
                      </p>
                    </div>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>

      <!-- Progress Section -->
      <div class="bg-[#E2F1F9] rounded-xl p-6 progress-section-fade-in">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-[#7AB2D3] rounded-lg flex items-center justify-center">
              <i class="fas fa-chart-pie text-white text-sm"></i>
            </div>
            <div>
              <h3 class="font-bold text-[#2C3E50] text-lg">Entry Progress</h3>
              <p class="text-[#40657F] text-sm">Track your result entry completion</p>
            </div>
          </div>
          <div class="text-right">
            <span class="text-2xl font-bold text-[#74C69D]" id="completed-results">0</span>
            <span class="text-[#40657F] text-sm">/ {{ participants|length }}</span>
            <p class="text-xs text-[#40657F] font-medium">Results Entered</p>
          </div>
        </div>
        <div class="w-full bg-[#B9D8EB]/30 rounded-full h-3 overflow-hidden">
          <div
            class="bg-gradient-to-r from-[#74C69D] to-[#5fb085] h-full rounded-full transition-all duration-500 ease-out"
            id="results-progress-bar"
            style="width: 0%"
          ></div>
        </div>
      </div>

      <!-- Submit Section -->
      <div class="flex flex-col sm:flex-row gap-4 justify-between items-center mt-8 submit-section-slide-in">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 bg-[#F28C8C]/20 rounded-lg flex items-center justify-center">
            <i class="fas fa-info-circle text-[#F28C8C] text-sm"></i>
          </div>
          <p class="text-[#40657F] text-sm">
            Make sure all scores are entered correctly before submitting.
          </p>
        </div>
        <button
          type="submit"
          class="bg-[#74C69D] text-white font-semibold py-3 px-8 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group"
        >
          <i class="fas fa-paper-plane mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
          Submit Results
        </button>
      </div>
    </form>
  </div>
</section>

<script>
  // Real-time validation for result inputs
  function validateResultInput(input) {
    const value = parseFloat(input.value);
    const errorContainer = input.closest('td').querySelector('.result-error-message');
    const errorText = errorContainer.querySelector('.error-text');

    // Reset styles and hide error message
    input.style.borderColor = "#B9D8EB";
    input.style.backgroundColor = "#F7FAFC";
    errorContainer.classList.add('hidden');

    // Check for validation errors
    if (input.value !== "") {
      if (value < 0) {
        // Show error for negative values
        input.style.borderColor = "#F28C8C";
        input.style.backgroundColor = "#FEF2F2";
        errorText.textContent = "Score cannot be negative";
        errorContainer.classList.remove('hidden');
      }
    }
  }

  // Update result status based on input
  function updateResultStatus(input) {
    const statusElement = input.closest("tr").querySelector(".result-status");
    const value = parseFloat(input.value);
    const errorContainer = input.closest('td').querySelector('.result-error-message');

    // First run validation to show any errors
    validateResultInput(input);

    // Don't update status if there are validation errors
    if (!errorContainer.classList.contains('hidden')) {
      // Keep status as invalid if there are errors
      statusElement.className =
        "result-status inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30";
      statusElement.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>Invalid';
      updateResultsProgress();
      return;
    }

    // Update status based on valid input
    if (input.value === "") {
      statusElement.className =
        "result-status inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gray-200 text-gray-600";
      statusElement.innerHTML = '<i class="fas fa-clock mr-1"></i>Pending';
    } else {
      statusElement.className =
        "result-status inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30";
      statusElement.innerHTML = '<i class="fas fa-check mr-1"></i>Entered';
    }

    updateResultsProgress();
  }

  // Update progress bar
  function updateResultsProgress() {
    const resultInputs = document.querySelectorAll(".result-input");
    const completedInputs = Array.from(resultInputs).filter((input) => {
      // Only count as completed if input has value and no error message is shown
      const errorContainer = input.closest('td').querySelector('.result-error-message');
      return input.value !== "" && errorContainer.classList.contains('hidden');
    });
    const completedCount = completedInputs.length;
    const totalCount = resultInputs.length;
    const percentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

    document.getElementById("completed-results").textContent = completedCount;
    document.getElementById("results-progress-bar").style.width = percentage + "%";
  }

  // Initialize progress on page load
  document.addEventListener('DOMContentLoaded', function() {
    updateResultsProgress();
  });
</script>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Form Animations */
  .results-form-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: resultsFormFadeIn 0.8s ease-out 1s forwards;
  }

  .table-container-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: tableContainerSlideIn 0.8s ease-out 1.2s forwards;
  }

  .participant-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: participantRowSlideIn 0.4s ease-out forwards;
  }

  .participant-row:nth-child(1) {
    animation-delay: 1.4s;
  }
  .participant-row:nth-child(2) {
    animation-delay: 1.5s;
  }
  .participant-row:nth-child(3) {
    animation-delay: 1.6s;
  }
  .participant-row:nth-child(4) {
    animation-delay: 1.7s;
  }
  .participant-row:nth-child(5) {
    animation-delay: 1.8s;
  }
  .participant-row:nth-child(6) {
    animation-delay: 1.9s;
  }
  .participant-row:nth-child(7) {
    animation-delay: 2.0s;
  }
  .participant-row:nth-child(8) {
    animation-delay: 2.1s;
  }

  .progress-section-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: progressSectionFadeIn 0.8s ease-out 2.2s forwards;
  }

  .submit-section-slide-in {
    opacity: 0;
    transform: translateY(20px);
    animation: submitSectionSlideIn 0.8s ease-out 2.4s forwards;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes resultsFormFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes tableContainerSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes participantRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes progressSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes submitSectionSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .participant-row {
      animation-delay: 1.2s;
    }

    .participant-row:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--row-index, 1));
    }
  }
</style>

{% endblock %}
