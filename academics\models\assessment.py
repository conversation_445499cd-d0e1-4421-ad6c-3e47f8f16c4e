from django.db import models


from core.base_models import NameSlugModels


class AssessmentType(NameSlugModels):
    weight = models.IntegerField()
    is_final = models.BooleanField(default=False)
    sequence = models.IntegerField()
    description = models.TextField(
        null=True, blank=True,
        help_text="Description of the assessment type, e.g., Continuous Assessment, Final Exam"
    )

    class Meta:
        verbose_name = "Assessment Type"
        verbose_name_plural = "Assessment Types"
        ordering = ['sequence']


class Assessment(models.Model):
    enrollment = models.ForeignKey(
        'academics.Enrollment', on_delete=models.CASCADE, related_name="assessments")
    assessment_type = models.ForeignKey(
        AssessmentType, on_delete=models.CASCADE)
    score = models.IntegerField(null=True, blank=True)
    # created_by =
    created_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.enrollment} | {self.assessment_type}"

    class Meta:
        verbose_name = "Assessment"
        verbose_name_plural = "Assessments"
        ordering = ['-created_at']
