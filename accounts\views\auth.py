from django.shortcuts import redirect, render

from django.contrib.auth import authenticate, login, logout


def _redirect_based_on_role(user):
    """
    Redirect user based on their role level and permissions.
    """
    if hasattr(user, 'get_highest_role_level'):
        role_level = user.get_highest_role_level()

        # Administrator (level 5+) - go to students dashboard
        if role_level >= 5:
            return redirect('students:home')

        # Finance Manager (level 4) - go to finances dashboard
        elif role_level == 4 or user.has_permission('manage_finances'):
            return redirect('finances:expenditures')

        # Academic Coordinator (level 3) - go to academics dashboard
        elif role_level == 3:
            return redirect('academics:dashboard')

        # Teacher (level 2) - go to academics dashboard
        elif role_level >= 2:
            return redirect('academics:dashboard')

        # Student or lower (level 1) - go to academics dashboard
        else:
            return redirect('academics:dashboard')

    # Fallback for users without role levels
    if user.is_staff or user.is_superuser:
        return redirect('students:home')
    else:
        return redirect('academics:dashboard')


def login_view(request):
    user = request.user

    if user.is_authenticated:
        return _redirect_based_on_role(user)

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(
            request,
            username=username,
            password=password
        )

        if user is not None:
            login(request, user)
            return _redirect_based_on_role(user)

    return render(request, 'accounts/login.html')


def logout_view(request):
    logout(request)
    return redirect('accounts:login')
