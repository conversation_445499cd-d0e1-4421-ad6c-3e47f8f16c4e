{% load static %}

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'img/favicon.ico' %}"
    />

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Google Fonts - Enhanced Typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Main CSS -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />

    <style>
      :root {
        --primary-color: #7ab2d3;
        --primary-dark: #5a9bd4;
        --primary-light: #a8c8e1;
        --secondary-color: #f8fafc;
        --accent-color: #667eea;
        --text-primary: #1a202c;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
          0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --gradient-primary: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--primary-dark) 100%
        );
        --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        --border-radius-sm: 0.375rem;
        --border-radius-md: 0.5rem;
        --border-radius-lg: 0.75rem;
        --border-radius-xl: 1rem;
        --transition-fast: 150ms ease-in-out;
        --transition-normal: 250ms ease-in-out;
        --transition-slow: 350ms ease-in-out;
      }

      * {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .font-display {
        font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      /* Smooth scrolling and modern animations */
      html {
        scroll-behavior: smooth;
      }

      /* Enhanced focus styles */
      *:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
      }

      /* Modern glassmorphism effect */
      .glass-effect {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      /* Enhanced button styles */
      .btn-modern {
        position: relative;
        overflow: hidden;
        transition: all var(--transition-normal);
        transform: translateY(0);
      }

      .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .btn-modern:active {
        transform: translateY(0);
      }

      /* Modern card styles */
      .card-modern {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--border-color);
        transition: all var(--transition-normal);
      }

      .card-modern:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
      }

      /* Loading animation */
      .loading-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      /* Print styles */
      @media print {
        body * {
          display: none;
        }

        .print-section {
          display: flex;
        }
      }

      /* Dark mode support preparation */
      @media (prefers-color-scheme: dark) {
        :root {
          --text-primary: #f7fafc;
          --text-secondary: #e2e8f0;
          --text-muted: #a0aec0;
          --secondary-color: #2d3748;
          --border-color: #4a5568;
        }
      }
    </style>
    {% block style %}{% endblock %}

    <title>
      Academics | {% block title %} {% endblock %} Tiny Feet Academy
    </title>
  </head>
  <body
    class="flex flex-col text-[var(--text-primary)] justify-center items-center w-full min-h-screen bg-gradient-to-br from-slate-50 to-blue-50"
  >
    <header
      class="w-full bg-white/95 backdrop-blur-md shadow-lg border-b border-white/20 sticky top-0 z-50"
    >
      <nav
        class="flex flex-row justify-between items-center w-full px-6 py-4 relative max-w-7xl mx-auto"
      >
        <section class="flex items-center gap-4">
          <div class="relative">
            <img
              src="{% static 'img/tinyfeet.jpg' %}"
              alt="Tiny Feet Academy Logo"
              width="42"
              height="42"
              class="rounded-xl shadow-md ring-2 ring-white/50 transition-transform duration-300 hover:scale-105"
            />
            <div
              class="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"
            ></div>
          </div>
          <div class="flex flex-col">
            <h1
              class="font-display font-bold text-base tracking-tight text-gray-800 text-no-wrap"
            >
              Tiny Feet <span class="text-[var(--primary-color)]">Academy</span>
            </h1>
            <p class="text-xs text-gray-500 font-medium text-no-wrap">
              Academic Management System
            </p>
          </div>
        </section>

        <!-- Mobile menu button -->
        <button
          id="mobile-menu-button"
          class="md:hidden flex items-center justify-center w-11 h-11 text-gray-600 hover:text-[var(--primary-color)] focus:outline-none rounded-xl hover:bg-gray-100/80 transition-all duration-300 btn-modern"
          aria-label="Toggle mobile menu"
          onclick="toggleMobileMenu()"
        >
          <svg
            class="w-6 h-6 transition-transform duration-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2.5"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
        </button>

        <!-- Desktop navigation -->
        <section
          class="hidden md:flex items-center gap-8 justify-center w-full"
        >
          <ul
            class="flex items-center gap-4 text-gray-600 font-medium text-sm justify-center w-full"
          >
            <li>
              <a
                href="{% url 'academics:dashboard' %}"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if request.resolver_match.url_name == 'dashboard' %}nav-active{% endif %} group"
              >
                <i class="fas fa-chart-line mr-2 text-sm"></i>Dashboard
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </a>
            </li>
            <li>
              <a
                href="{% url 'academics:levels' %}"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if request.resolver_match.url_name == 'levels' %}nav-active{% endif %} group"
              >
                <i class="fas fa-chalkboard mr-2 text-sm"></i>Classes
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </a>
            </li>
            <li class="relative group">
              <button
                id="management-dropdown"
                type="button"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if 'management' in request.resolver_match.url_name or 'subject' in request.resolver_match.url_name or 'assessment' in request.resolver_match.url_name or 'activity' in request.resolver_match.url_name %}nav-active{% endif %} flex items-center gap-2 focus:outline-none group"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <i class="fas fa-cogs mr-2 text-sm"></i>Management
                <svg
                  class="w-4 h-4 transition-transform duration-200 group-hover:rotate-180"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </button>

              <!-- Management Dropdown Menu -->
              <div
                class="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-xl border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-50"
              >
                <div class="p-2">
                  <a
                    href="{% url 'academics:academic_management_dashboard' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <div class="w-8 h-8 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300">
                      <i class="fas fa-graduation-cap text-white text-sm"></i>
                    </div>
                    <div>
                      <div class="font-semibold text-sm">Academic Management</div>
                      <div class="text-xs text-gray-500">Overview & Dashboard</div>
                    </div>
                  </a>

                  <a
                    href="{% url 'academics:academic_management_dashboard' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <div class="w-8 h-8 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300">
                      <i class="fas fa-book text-white text-sm"></i>
                    </div>
                    <div>
                      <div class="font-semibold text-sm">Subjects</div>
                      <div class="text-xs text-gray-500">Manage curriculum subjects</div>
                    </div>
                  </a>

                  <a
                    href="{% url 'academics:academic_management_dashboard' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <div class="w-8 h-8 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-lg flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300">
                      <i class="fas fa-clipboard-check text-white text-sm"></i>
                    </div>
                    <div>
                      <div class="font-semibold text-sm">Assessment Types</div>
                      <div class="text-xs text-gray-500">Configure grading methods</div>
                    </div>
                  </a>

                  <a
                    href="{% url 'academics:activity_type_management' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <div class="w-8 h-8 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-lg flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300">
                      <i class="fas fa-tags text-white text-sm"></i>
                    </div>
                    <div>
                      <div class="font-semibold text-sm">Activity Types</div>
                      <div class="text-xs text-gray-500">Manage activity categories</div>
                    </div>
                  </a>

                  <a
                    href="{% url 'academics:academic_management_dashboard' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <div class="w-8 h-8 bg-gradient-to-br from-[#F28C8C] to-[#e74c3c] rounded-lg flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300">
                      <i class="fas fa-calendar-alt text-white text-sm"></i>
                    </div>
                    <div>
                      <div class="font-semibold text-sm">Activities</div>
                      <div class="text-xs text-gray-500">Schedule & manage activities</div>
                    </div>
                  </a>
                </div>
              </div>
            </li>
            {% if request.user.is_staff %}
            <li class="relative group">
              <button
                id="teachers-dropdown"
                type="button"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if request.resolver_match.url_name == 'teachers' %}nav-active{% endif %} flex items-center gap-2 focus:outline-none group"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <i class="fas fa-chalkboard-teacher mr-2 text-sm"></i>Teachers
                <svg
                  class="w-4 h-4 text-gray-400 group-hover:text-[var(--primary-color)] transition-all duration-300 transform group-hover:rotate-180"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2.5"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </button>
              <div class="hover-buffer"></div>
              <section
                id="teachers-flyout"
                class="opacity-0 scale-95 pointer-events-none invisible absolute left-1/2 -translate-x-1/2 top-full mt-3 min-w-[280px] bg-white/95 backdrop-blur-md shadow-2xl rounded-2xl border border-white/20 flex-col items-start py-4 px-3 transition-all duration-300 ease-out group-hover:opacity-100 group-hover:scale-100 group-hover:pointer-events-auto group-hover:visible focus-within:opacity-100 focus-within:scale-100 focus-within:pointer-events-auto focus-within:visible z-50"
                tabindex="-1"
              >
                <div class="flex flex-col gap-2 w-full">
                  <a
                    href="{% url 'accounts:teachers' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <i
                      class="fas fa-users text-blue-500 group-hover/item:scale-110 transition-transform duration-300"
                    ></i>
                    <span>All Teachers</span>
                  </a>
                  <a
                    href="{% url 'accounts:add_teacher' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <i
                      class="fas fa-user-plus text-green-500 group-hover/item:scale-110 transition-transform duration-300"
                    ></i>
                    <span>Add Teachers</span>
                  </a>
                </div>
              </section>
            </li>
            {% endif %}

            <li class="relative group">
              <button
                id="performance-dropdown"
                type="button"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if request.resolver_match.url_name == 'performance' %}nav-active{% endif %} flex items-center gap-2 focus:outline-none group"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <i class="fas fa-chart-bar mr-2 text-sm"></i>Performance
                <svg
                  class="w-4 h-4 text-gray-400 group-hover:text-[var(--primary-color)] transition-all duration-300 transform group-hover:rotate-180"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2.5"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </button>
              <div class="hover-buffer"></div>
              <section
                id="performance-flyout"
                class="opacity-0 scale-95 pointer-events-none invisible absolute left-1/2 -translate-x-1/2 top-full mt-3 min-w-[320px] bg-white/95 backdrop-blur-md shadow-2xl rounded-2xl border border-white/20 flex-col items-start py-4 px-3 transition-all duration-300 ease-out group-hover:opacity-100 group-hover:scale-100 group-hover:pointer-events-auto group-hover:visible focus-within:opacity-100 focus-within:scale-100 focus-within:pointer-events-auto focus-within:visible z-50"
                tabindex="-1"
              >
                <div class="flex flex-col gap-2 w-full">
                  <a
                    href="{% url 'academics:enter_by_level' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <i
                      class="fas fa-edit text-green-500 group-hover/item:scale-110 transition-transform duration-300"
                    ></i>
                    <span>Enter Exam Grades</span>
                  </a>
                  <a
                    href="{% url 'academics:view_grades' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <i
                      class="fas fa-eye text-blue-500 group-hover/item:scale-110 transition-transform duration-300"
                    ></i>
                    <span>View Exam Grades</span>
                  </a>
                  <a
                    href="{% url 'academics:activities' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-violet-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <i
                      class="fas fa-tasks text-purple-500 group-hover/item:scale-110 transition-transform duration-300"
                    ></i>
                    <span>Enter Activity Results</span>
                  </a>
                  <a
                    href="{% url 'academics:view_activity_results' %}"
                    class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-orange-50 hover:to-amber-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  >
                    <i
                      class="fas fa-chart-line text-orange-500 group-hover/item:scale-110 transition-transform duration-300"
                    ></i>
                    <span>View Activity Results</span>
                  </a>
                </div>
              </section>
            </li>
          </ul>

          <!-- desktop user menu -->
          <div class="relative group flex items-center gap-3 justify-center">
            <div
              class="flex items-center gap-3 cursor-pointer group/profile hover:bg-gray-50/80 rounded-xl px-3 py-2 transition-all duration-300"
              tabindex="0"
            >
              <div class="relative">
                <img
                  src="{% static 'img/person.png' %}"
                  alt="Profile"
                  class="rounded-full bg-gradient-to-br from-gray-100 to-gray-200 border-2 border-white shadow-lg ring-2 ring-gray-100/50 transition-transform duration-300 group-hover/profile:scale-105"
                  width="36"
                  height="36"
                />
                <div
                  class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"
                ></div>
              </div>
              <div class="hidden sm:flex flex-col">
                <span
                  class="text-sm font-bold capitalize text-gray-800 leading-tight"
                  >{{ request.user }}</span
                >
                <span class="text-xs text-gray-500 font-medium">
                  {% if request.user.is_staff %}Administrator
                  <!--  -->
                  {% else %}Teacher{% endif %}
                </span>
              </div>
              <i
                class="fa-solid fa-chevron-down text-xs text-gray-400 group-hover:text-[var(--primary-color)] transition-all duration-300 transform group-hover:rotate-180"
              ></i>
            </div>
            <div class="hover-buffer"></div>
            <div
              class="opacity-0 scale-95 pointer-events-none invisible absolute right-0 top-full min-w-[280px] bg-white/95 backdrop-blur-md shadow-2xl rounded-2xl border border-white/20 flex flex-col items-start py-4 px-3 transition-all duration-300 ease-out group-hover:opacity-100 group-hover:scale-100 group-hover:pointer-events-auto group-hover:visible group-focus-within:opacity-100 group-focus-within:scale-100 group-focus-within:pointer-events-auto group-focus-within:visible hover:opacity-100 hover:pointer-events-auto hover:visible z-50 mt-3"
              tabindex="-1"
            >
              <!-- User Info Header -->
              <div
                class="flex items-center gap-3 w-full px-3 py-3 mb-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl border border-gray-100"
              >
                <img
                  src="{% static 'img/person.png' %}"
                  alt="Profile"
                  class="rounded-full bg-gradient-to-br from-gray-100 to-gray-200 border-2 border-white shadow-md"
                  width="40"
                  height="40"
                />
                <div class="flex-1">
                  <span
                    class="text-sm font-bold capitalize text-gray-800 block leading-tight"
                    >{{ request.user }}</span
                  >
                  <span class="text-xs text-gray-500 font-medium">
                    {% if request.user.is_staff %}Administrator Access
                    <!--  -->
                    {% else %}Teacher Account{% endif %}
                  </span>
                </div>
              </div>

              <!-- Menu Items -->
              <div class="flex flex-col gap-1 w-full">
                <a
                  href="{% url 'academics:user_profile' %}"
                  class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  title="My Account"
                >
                  <i
                    class="fas fa-user text-blue-500 group-hover/item:scale-110 transition-transform duration-300"
                  ></i>
                  <span>My Account</span>
                </a>

                <a
                  href="{% url 'accounts:change_password' %}"
                  class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-orange-50 hover:to-amber-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  title="Change Password"
                >
                  <i
                    class="fas fa-key text-orange-500 group-hover/item:scale-110 transition-transform duration-300"
                  ></i>
                  <span>Change Password</span>
                </a>

                {% if request.user.is_staff %}
                <div
                  class="w-full h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent my-2"
                ></div>
                <a
                  href="{% url 'core:administration' %}"
                  class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  title="Administration"
                >
                  <i
                    class="fas fa-cog text-blue-500 group-hover/item:scale-110 transition-transform duration-300"
                  ></i>
                  <span>Administration</span>
                </a>
                <a
                  href="{% url 'students:home' %}"
                  class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group/item"
                  title="Finance"
                >
                  <i
                    class="fas fa-coins text-green-500 group-hover/item:scale-110 transition-transform duration-300"
                  ></i>
                  <span>Finance & Students</span>
                </a>
                {% endif %}
                <div
                  class="w-full h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent my-2"
                ></div>
                <a
                  href="{% url 'accounts:logout' %}"
                  class="flex items-center gap-3 w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-600 transition-all duration-300 font-medium group/item"
                  title="Logout"
                >
                  <i
                    class="fas fa-sign-out-alt text-red-500 group-hover/item:scale-110 transition-transform duration-300"
                  ></i>
                  <span>Sign Out</span>
                </a>
              </div>
            </div>
          </div>
        </section>

        <!-- Mobile navigation menu -->
        <div
          id="mobile-menu"
          class="md:hidden absolute top-full left-0 w-full bg-white shadow-lg border-t border-gray-100 hidden transition-all duration-200 ease-out z-50"
        >
          <div class="px-6 py-4 space-y-4">
            <a
              href="{% url 'academics:dashboard' %}"
              class="block py-2 text-gray-500 hover:text-[#7AB2D3] transition font-medium {% if request.resolver_match.url_name == 'home' %}nav-active{% endif %}"
              >Dashboard</a
            >
            <a
              href="{% url 'academics:levels' %}"
              class="block py-2 text-gray-500 hover:text-[#7AB2D3] transition font-medium {% if request.resolver_match.url_name == 'classes' %}nav-active{% endif %}"
              >Classes</a
            >
            <!-- Mobile Management dropdown -->
            <div class="space-y-2">
              <button
                id="mobile-management-toggle"
                class="flex items-center justify-between w-full py-2 text-gray-500 hover:text-[#7AB2D3] transition font-medium"
              >
                <span><i class="fas fa-cogs mr-2"></i>Management</span>
                <svg
                  class="w-4 h-4 transition-transform duration-200"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              <div id="mobile-management-menu" class="hidden pl-4 space-y-2">
                <a
                  href="{% url 'academics:academic_management_dashboard' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  ><i class="fas fa-graduation-cap mr-2"></i>Academic Management</a
                >
                <a
                  href="{% url 'academics:academic_management_dashboard' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  ><i class="fas fa-book mr-2"></i>Subjects</a
                >
                <a
                  href="{% url 'academics:academic_management_dashboard' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  ><i class="fas fa-clipboard-check mr-2"></i>Assessment Types</a
                >
                <a
                  href="{% url 'academics:activity_type_management' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  ><i class="fas fa-tags mr-2"></i>Activity Types</a
                >
                <a
                  href="{% url 'academics:academic_management_dashboard' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  ><i class="fas fa-calendar-alt mr-2"></i>Activities</a
                >
              </div>
            </div>

            {% if request.user.is_staff %}
            <!-- Mobile Teachers dropdown -->
            <div class="space-y-2">
              <button
                id="mobile-teachers-toggle"
                class="flex items-center justify-between w-full py-2 text-gray-500 hover:text-[#7AB2D3] transition font-medium focus:outline-none"
              >
                Teachers
                <svg
                  class="w-4 h-4 transition-transform"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              <div id="mobile-teachers-menu" class="hidden pl-4 space-y-2">
                <a
                  href="{% url 'accounts:teachers' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  >All Teachers</a
                >
                <a
                  href="{% url 'accounts:add_teacher' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  >Add Teachers</a
                >
              </div>
            </div>
            {% endif %}

            <!-- Mobile Performance dropdown -->
            <div class="space-y-2">
              <button
                id="mobile-performance-toggle"
                class="flex items-center justify-between w-full py-2 text-gray-500 hover:text-[#7AB2D3] transition font-medium focus:outline-none"
              >
                Performance
                <svg
                  class="w-4 h-4 transition-transform"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              <div id="mobile-performance-menu" class="hidden pl-4 space-y-2">
                <a
                  href="{% url 'academics:enter_by_level' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  >Enter Exam Grades</a
                >
                <a
                  href="{% url 'academics:view_grades' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  >View Exam Grades</a
                >
                <a
                  href="{% url 'academics:activities' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  >Enter Activity Results</a
                >
                <a
                  href="{% url 'academics:view_activity_results' %}"
                  class="block py-1 text-sm text-gray-600 hover:text-[#7AB2D3] transition"
                  >View Activity Results</a
                >
              </div>
            </div>

            <!-- Mobile user menu -->
            <div class="border-t border-gray-100 pt-4 mt-4">
              <div class="flex items-center gap-3 mb-3">
                <img
                  src="{% static 'img/person.png' %}"
                  alt="Profile"
                  class="rounded-full bg-gray-200 border border-gray-100 shadow-sm"
                  width="32"
                  height="32"
                />
                <div>
                  <span class="text-sm font-medium capitalize text-gray-700"
                    >{{ request.user }}</span
                  >
                  <p class="text-xs text-gray-400">
                    {% if request.user.is_staff %}
                    <!-- user role -->
                    Admin{% else %}
                    <!-- user role -->
                    Teacher{% endif %}
                  </p>
                </div>
              </div>
              <div class="space-y-2">
                <a
                  href="{% url 'academics:user_profile' %}"
                  class="flex items-center gap-2 py-2 text-gray-500 hover:text-[#7AB2D3] transition"
                >
                  <i class="fa-solid fa-user"></i>
                  My Account
                </a>
                <a
                  href="{% url 'academics:change_password' %}"
                  class="flex items-center gap-2 py-2 text-gray-500 hover:text-[#7AB2D3] transition"
                >
                  <i class="fa-solid fa-key"></i>
                  Change Password
                </a>
                {% if request.user.is_staff %}
                <a
                  href="{% url 'core:administration' %}"
                  class="flex items-center gap-2 py-2 text-gray-500 hover:text-[#7AB2D3] transition"
                >
                  <i class="fa-solid fa-gear"></i>
                  Administration
                </a>
                <a
                  href="{% url 'academics:dashboard' %}"
                  class="flex items-center gap-2 py-2 text-gray-500 hover:text-[#7AB2D3] transition"
                >
                  <i class="fa-solid fa-coins"></i>
                  Finance
                </a>
                {% endif %}
                <a
                  href="{% url 'accounts:logout' %}"
                  class="flex items-center gap-2 py-2 text-gray-500 hover:text-red-500 transition"
                >
                  <i class="fa-solid fa-right-from-bracket"></i>
                  Logout
                </a>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </header>
    <main
      class="flex flex-col justify-center gap-8 items-center px-4 md:px-8 py-8 bg-gradient-to-br from-gray-50 to-blue-50/30 w-full min-h-[85vh]"
    >
      {% block content %} {% endblock %}
    </main>

    {% block scripts %} {% endblock %}

    <!-- Mobile menu toggle function -->
    <script>
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobile-menu");
        if (mobileMenu) {
          const isHidden = mobileMenu.classList.contains("hidden");
          if (isHidden) {
            mobileMenu.classList.remove("hidden");
          } else {
            mobileMenu.classList.add("hidden");
          }
        }
      }

      // Close mobile menu when clicking outside
      document.addEventListener("click", function (event) {
        const mobileMenu = document.getElementById("mobile-menu");
        const mobileMenuButton = document.getElementById("mobile-menu-button");

        if (mobileMenu && mobileMenuButton) {
          const isClickInsideMenu = mobileMenu.contains(event.target);
          const isClickOnButton = mobileMenuButton.contains(event.target);

          if (
            !isClickInsideMenu &&
            !isClickOnButton &&
            !mobileMenu.classList.contains("hidden")
          ) {
            mobileMenu.classList.add("hidden");
          }
        }
      });

      // Close mobile menu on window resize to desktop size
      window.addEventListener("resize", function () {
        const mobileMenu = document.getElementById("mobile-menu");
        if (window.innerWidth >= 768 && mobileMenu) {
          mobileMenu.classList.add("hidden");
        }
      });

      // Mobile dropdown toggles
      document.addEventListener("DOMContentLoaded", function () {
        const dropdownToggles = [
          { button: "mobile-teachers-toggle", menu: "mobile-teachers-menu" },
          {
            button: "mobile-performance-toggle",
            menu: "mobile-performance-menu",
          },
          {
            button: "mobile-management-toggle",
            menu: "mobile-management-menu",
          },
        ];

        dropdownToggles.forEach(({ button, menu }) => {
          const toggleButton = document.getElementById(button);
          const dropdownMenu = document.getElementById(menu);

          if (toggleButton && dropdownMenu) {
            toggleButton.addEventListener("click", function () {
              const isOpen = !dropdownMenu.classList.contains("hidden");
              const arrow = toggleButton.querySelector("svg");

              if (isOpen) {
                dropdownMenu.classList.add("hidden");
                if (arrow) arrow.style.transform = "rotate(0deg)";
              } else {
                dropdownMenu.classList.remove("hidden");
                if (arrow) arrow.style.transform = "rotate(180deg)";
              }
            });
          }
        });
      });
    </script>

    <!-- <script src="//unpkg.com/alpinejs" defer></script> -->
    <script src="{% static 'js/alpine.js' %}"></script>
    <script src="{% static 'js/main.js' %}"></script>
  </body>
</html>
