from django.db.models import Sum


def calculate_sum_of_receipts(instance):
    """
    Calculates the total sum of receipts for a given instance.
    Used by FeeAccount models to calculate actual payments received.
    Note: Previously used by deprecated Income model - now uses modern Ledger system.
    Returns the sum of amount_paid from matching Receipt records.
    """
    from finances.fee_management.models import Receipt

    filters = {}

    if hasattr(instance, "category") and instance.category:
        filters.update({
            "fee_account__category": instance.category,
            "date__month": instance.month_year.month,
        })

    else:
        filters.update({
            "student": instance.student,
            "fee_account": instance.fee_account,
            "fee_account__term": instance.fee_account.term,
        })

    total = Receipt.objects.filter(
        is_reversed=False,  # Exclude reversed receipts
        **filters).aggregate(total=Sum("amount_paid"))["total"]

    return total or 0
