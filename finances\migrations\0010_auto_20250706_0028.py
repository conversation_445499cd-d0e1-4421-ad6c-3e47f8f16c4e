# Generated by Django 5.1.2 on 2025-07-05 22:28

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('finances', '0009_remove_deprecated_models'),
    ]

    operations = [
        # Drop deprecated IncomeTotal table
        migrations.RunSQL(
            "DROP TABLE IF EXISTS finances_incometotal CASCADE;",
            reverse_sql="-- Cannot reverse dropping IncomeTotal table"
        ),

        # Drop deprecated ExpenseTotal table
        migrations.RunSQL(
            "DROP TABLE IF EXISTS finances_expensetotal CASCADE;",
            reverse_sql="-- Cannot reverse dropping ExpenseTotal table"
        ),
    ]
