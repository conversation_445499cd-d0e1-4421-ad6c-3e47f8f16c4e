from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.auth import update_session_auth_hash
from django.db import transaction

from accounts.forms.user_forms import CustomPasswordChangeForm, UserProfileForm
from accounts.utils import RBACManager


@login_required
def user_profile(request, template_name='accounts/profile/user_profile.html'):
    """View for users to see their account information"""
    user = request.user
    
    # Get user's roles and permissions
    active_roles = user.get_active_roles()
    permissions_by_category = {}
    
    if active_roles:
        rbac_manager = RBACManager()
        audit_data = rbac_manager.audit_user_permissions(user)
        all_permissions = audit_data.get('permissions', [])

        # Group permissions by category
        for perm_data in all_permissions:
            permission = perm_data['permission']
            category = perm_data['category'] or 'General'
            if category not in permissions_by_category:
                permissions_by_category[category] = []
            permissions_by_category[category].append(permission)
    
    # Get audit data
    audit_data = {
        'role_level': user.get_highest_role_level(),
        'total_roles': active_roles.count() if active_roles else 0,
        'total_permissions': sum(len(perms) for perms in permissions_by_category.values()),
        'account_age_days': (user.date_joined.date() - user.date_joined.date()).days if user.date_joined else 0,
    }
    
    context = {
        'user_obj': user,
        'active_roles': active_roles,
        'permissions_by_category': permissions_by_category,
        'audit_data': audit_data,
    }
    
    return render(request, template_name, context)


@login_required
def edit_profile(request, template_name='accounts/profile/edit_profile.html'):
    """View for users to edit their profile information"""
    user = request.user
    
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=user)
        if form.is_valid():
            try:
                with transaction.atomic():
                    form.save()
                    messages.success(request, 'Your profile has been updated successfully!')
                    return redirect('accounts:user_profile')
            except Exception as e:
                messages.error(request, f'Error updating profile: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = UserProfileForm(instance=user)
    
    context = {
        'form': form,
        'user_obj': user,
    }
    
    return render(request, template_name, context)


@login_required
def change_password(request, template_name='accounts/profile/change_password.html'):
    """View for users to change their password"""
    user = request.user
    
    if request.method == 'POST':
        form = CustomPasswordChangeForm(user, request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    user = form.save()
                    # Keep user logged in after password change
                    update_session_auth_hash(request, user)
                    messages.success(request, 'Your password has been changed successfully!')
                    return redirect('accounts:user_profile')
            except Exception as e:
                messages.error(request, f'Error changing password: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = CustomPasswordChangeForm(user)
    
    context = {
        'form': form,
        'user_obj': user,
    }
    
    return render(request, template_name, context)
