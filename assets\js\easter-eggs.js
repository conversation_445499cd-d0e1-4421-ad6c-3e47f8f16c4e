/**
 * Easter Eggs JavaScript Module
 * Handles all interactive functionality for easter egg pages
 */

class EasterEggManager {
    constructor() {
        this.audioEnabled = true;
        this.particleInterval = null;
        this.init();
    }

    init() {
        this.setupAudio();
        this.setupParticles();
        this.setupEventListeners();
        this.playDiscoverySound();
    }

    setupAudio() {
        this.sounds = {
            discovery: this.createAudio('/static/audio/discovery.mp3'),
            ambient: this.createAudio('/static/audio/ambient.mp3'),
            transition: this.createAudio('/static/audio/transition.mp3')
        };

        // Set volume levels
        Object.values(this.sounds).forEach(sound => {
            if (sound) sound.volume = 0.3;
        });
    }

    createAudio(src) {
        try {
            const audio = new Audio(src);
            audio.preload = 'auto';
            return audio;
        } catch (error) {
            console.log('Audio not available:', src);
            return null;
        }
    }

    playSound(soundName) {
        if (!this.audioEnabled || !this.sounds[soundName]) return;
        
        try {
            this.sounds[soundName].currentTime = 0;
            this.sounds[soundName].play().catch(e => {
                console.log('Audio play failed:', e);
            });
        } catch (error) {
            console.log('Sound play error:', error);
        }
    }

    playAmbient() {
        if (!this.audioEnabled || !this.sounds.ambient) return;
        
        try {
            this.sounds.ambient.loop = true;
            this.sounds.ambient.play().catch(e => {
                console.log('Ambient audio failed:', e);
            });
        } catch (error) {
            console.log('Ambient play error:', error);
        }
    }

    stopAmbient() {
        if (this.sounds.ambient) {
            this.sounds.ambient.pause();
            this.sounds.ambient.currentTime = 0;
        }
    }

    playDiscoverySound() {
        // Play discovery sound when page loads
        setTimeout(() => {
            this.playSound('discovery');
            setTimeout(() => {
                this.playAmbient();
            }, 1000);
        }, 500);
    }

    setupParticles() {
        const floatingElements = document.querySelector('.floating-elements');
        if (!floatingElements) return;

        // Create particles periodically
        this.particleInterval = setInterval(() => {
            this.createParticle(floatingElements);
        }, 500);
    }

    createParticle(container) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            pointer-events: none;
            animation: particleFloat 4s linear infinite;
            left: ${Math.random() * 100}%;
            animation-delay: ${Math.random() * 4}s;
        `;

        container.appendChild(particle);

        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 4000);
    }

    setupEventListeners() {
        // New quote button
        const newQuoteBtn = document.querySelector('[onclick*="getNewQuote"]');
        if (newQuoteBtn) {
            newQuoteBtn.removeAttribute('onclick');
            newQuoteBtn.addEventListener('click', (e) => this.getNewQuote(e));
        }

        // Share button
        const shareBtn = document.querySelector('[onclick*="shareQuote"]');
        if (shareBtn) {
            shareBtn.removeAttribute('onclick');
            shareBtn.addEventListener('click', (e) => this.shareQuote(e));
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    async getNewQuote(event) {
        const button = event.target.closest('.btn-easter');
        const icon = button.querySelector('i');
        
        // Add loading animation
        icon.classList.add('fa-spin');
        button.disabled = true;
        
        try {
            // Get the API endpoint from the page
            const apiUrl = this.getQuoteApiUrl();
            
            const response = await fetch(apiUrl);
            const data = await response.json();
            
            if (data.success) {
                await this.animateQuoteChange(data);
                this.playSound('transition');
            } else {
                this.showNotification('Failed to load new quote', 'error');
            }
        } catch (error) {
            console.error('Error fetching new quote:', error);
            this.showNotification('Failed to load new quote', 'error');
        } finally {
            // Remove loading animation
            icon.classList.remove('fa-spin');
            button.disabled = false;
        }
    }

    getQuoteApiUrl() {
        // Determine API URL based on current page
        const path = window.location.pathname;
        
        if (path.includes('philosophical')) {
            return '/core/api/new-quote/';
        } else if (path.includes('musical')) {
            return '/core/api/new-music-quote/';
        } else if (path.includes('cosmic')) {
            return '/core/api/new-space-quote/';
        } else if (path.includes('artistic')) {
            return '/core/api/new-art-quote/';
        } else if (path.includes('travel')) {
            return '/core/api/new-travel-quote/';
        }
        
        return '/core/api/new-quote/'; // Default fallback
    }

    async animateQuoteChange(data) {
        const quoteText = document.getElementById('quoteText');
        const quoteAuthor = document.getElementById('quoteAuthor');
        
        if (!quoteText || !quoteAuthor) return;

        // Fade out
        quoteText.style.transition = 'opacity 0.3s ease';
        quoteAuthor.style.transition = 'opacity 0.3s ease';
        quoteText.style.opacity = '0';
        quoteAuthor.style.opacity = '0';

        // Wait for fade out
        await new Promise(resolve => setTimeout(resolve, 300));

        // Update content
        quoteText.textContent = data.quote;
        quoteAuthor.textContent = `— ${data.author}`;

        // Fade in
        quoteText.style.opacity = '1';
        quoteAuthor.style.opacity = '1';
    }

    shareQuote() {
        const quoteText = document.getElementById('quoteText')?.textContent || '';
        const quoteAuthor = document.getElementById('quoteAuthor')?.textContent || '';
        const shareText = `${quoteText} ${quoteAuthor}`;
        
        if (navigator.share) {
            navigator.share({
                title: 'Inspirational Quote',
                text: shareText,
                url: window.location.href
            }).catch(error => {
                console.log('Share failed:', error);
                this.copyToClipboard(shareText);
            });
        } else {
            this.copyToClipboard(shareText);
        }
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showNotification('Quote copied to clipboard!', 'success');
        } catch (error) {
            console.log('Clipboard copy failed:', error);
            this.showNotification('Unable to copy quote', 'error');
        }
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = 'easter-notification';
        notification.textContent = message;
        
        // Set color based on type
        if (type === 'error') {
            notification.style.background = '#F28C8C';
        } else {
            notification.style.background = '#74C69D';
        }

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 3000);
    }

    cleanup() {
        // Stop ambient audio
        this.stopAmbient();
        
        // Clear particle interval
        if (this.particleInterval) {
            clearInterval(this.particleInterval);
        }
    }
}

// Touch gesture handling for mobile
class TouchGestureHandler {
    constructor() {
        this.startX = 0;
        this.startY = 0;
        this.isScrolling = false;
        this.init();
    }

    init() {
        // Only initialize on touch devices
        if ('ontouchstart' in window) {
            this.setupTouchEvents();
        }
    }

    setupTouchEvents() {
        const swipeableRows = document.querySelectorAll('.swipeable-row');
        
        swipeableRows.forEach(row => {
            row.addEventListener('touchstart', (e) => this.handleTouchStart(e));
            row.addEventListener('touchmove', (e) => this.handleTouchMove(e, row));
            row.addEventListener('touchend', () => this.handleTouchEnd());
        });
    }

    handleTouchStart(e) {
        this.startX = e.touches[0].clientX;
        this.startY = e.touches[0].clientY;
        this.isScrolling = false;
    }

    handleTouchMove(e, row) {
        if (!this.startX || !this.startY) return;

        const currentX = e.touches[0].clientX;
        const currentY = e.touches[0].clientY;
        const diffX = this.startX - currentX;
        const diffY = this.startY - currentY;

        // Determine if user is scrolling vertically
        if (Math.abs(diffY) > Math.abs(diffX)) {
            this.isScrolling = true;
            return;
        }

        // Handle horizontal swipe
        if (!this.isScrolling && Math.abs(diffX) > 50) {
            this.showRowActions(row);
        }
    }

    handleTouchEnd() {
        this.startX = 0;
        this.startY = 0;
        this.isScrolling = false;
    }

    showRowActions(row) {
        // Implementation for showing action buttons on swipe
        const studentId = row.dataset.studentId;
        if (studentId) {
            // Show contextual action menu
            console.log('Show actions for student:', studentId);
        }
    }
}

// Responsive chart handling
class ResponsiveChartManager {
    constructor() {
        this.charts = new Map();
        this.init();
    }

    init() {
        this.setupResponsiveCharts();
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
    }

    setupResponsiveCharts() {
        const chartContainers = document.querySelectorAll('.chart-container');
        
        chartContainers.forEach(container => {
            this.setResponsiveHeight(container);
        });
    }

    setResponsiveHeight(container) {
        const width = window.innerWidth;
        
        if (width < 768) {
            container.style.height = '250px';
        } else if (width < 1024) {
            container.style.height = '300px';
        } else {
            container.style.height = '400px';
        }
    }

    handleResize() {
        this.setupResponsiveCharts();
        
        // Trigger chart resize if Chart.js is available
        if (window.Chart) {
            this.charts.forEach(chart => {
                chart.resize();
            });
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    registerChart(id, chart) {
        this.charts.set(id, chart);
    }
}

// Mobile navigation handler
class MobileNavigation {
    constructor() {
        this.init();
    }

    init() {
        const mobileMenuButton = document.querySelector('.mobile-menu-button');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', () => {
                this.toggleMobileMenu(mobileMenuButton, mobileMenu);
            });
        }
    }

    toggleMobileMenu(button, menu) {
        menu.classList.toggle('hidden');
        
        // Update aria-expanded
        const expanded = button.getAttribute('aria-expanded') === 'true';
        button.setAttribute('aria-expanded', !expanded);
        
        // Update icon
        const icon = button.querySelector('i');
        if (icon) {
            if (menu.classList.contains('hidden')) {
                icon.className = 'fas fa-bars text-xl';
            } else {
                icon.className = 'fas fa-times text-xl';
            }
        }
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize easter egg functionality if on easter egg page
    if (document.querySelector('.easter-egg-container')) {
        new EasterEggManager();
    }
    
    // Initialize touch gestures
    new TouchGestureHandler();
    
    // Initialize responsive charts
    new ResponsiveChartManager();
    
    // Initialize mobile navigation
    new MobileNavigation();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        EasterEggManager,
        TouchGestureHandler,
        ResponsiveChartManager,
        MobileNavigation
    };
}
