{% load humanize %} {% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'img/favicon.ico' %}"
    />

    <!-- font awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css"
      integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- google fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />

    <!-- style css -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />

    <title>{{term}} Income Statement</title>
  </head>
  <body class="bg-white font-sans">
    <div class="max-w-4xl mx-auto p-8">
      <!-- Header Section -->
      <div class="text-center mb-12 border-b-2 border-[#7AB2D3] pb-8">
        <div class="flex justify-center items-center mb-6">
          <img
            src="{% static 'img/tinyfeet.jpg' %}"
            alt="Tiny Feet Academy Logo"
            class="w-24 h-24 rounded-full shadow-lg border-4 border-[#E2F1F9]"
          />
        </div>
        <h1 class="text-4xl font-bold text-[#2C3E50] mb-2 font-display">
          Tiny Feet Academy
        </h1>
        <h2 class="text-2xl font-semibold text-[#40657F] mb-3">
          Income Statement
        </h2>
        <div
          class="inline-flex items-center gap-2 border border-[#40657F] px-6 py-2 rounded"
        >
          <i class="fas fa-calendar text-[#40657F]"></i>
          <p class="font-semibold text-[#40657F]">{{term}}</p>
        </div>
        <div class="w-32 h-1 bg-[#2C3E50] mx-auto mt-4"></div>
      </div>

      <!-- Financial Summary Cards -->
      <div class="grid grid-cols-3 gap-6 mb-10">
        <!-- Revenue Summary -->
        <div class="bg-[#F7FAFC] border-l-4 border-[#74C69D] p-6 rounded">
          <div class="flex items-center gap-3 mb-4">
            <div
              class="w-10 h-10 bg-[#74C69D] rounded flex items-center justify-center"
            >
              <i class="fas fa-arrow-up text-white"></i>
            </div>
            <div>
              <h3 class="font-bold text-[#2C3E50] text-lg">Total Revenue</h3>
              <p class="text-[#40657F] text-sm">Income Generated</p>
            </div>
          </div>
          <p class="text-2xl font-bold text-[#74C69D]">
            MWK {{total_revenue|intcomma}}
          </p>
        </div>

        <!-- Expenses Summary -->
        <div class="bg-[#F7FAFC] border-l-4 border-[#F28C8C] p-6 rounded">
          <div class="flex items-center gap-3 mb-4">
            <div
              class="w-10 h-10 bg-[#F28C8C] rounded flex items-center justify-center"
            >
              <i class="fas fa-arrow-down text-white"></i>
            </div>
            <div>
              <h3 class="font-bold text-[#2C3E50] text-lg">Total Expenses</h3>
              <p class="text-[#40657F] text-sm">Money Spent</p>
            </div>
          </div>
          <p class="text-2xl font-bold text-[#F28C8C]">
            MWK {{total_expense|intcomma}}
          </p>
        </div>

        <!-- Net Profit Summary -->
        <div class="bg-[#F7FAFC] border-l-4 border-[#7AB2D3] p-6 rounded">
          <div class="flex items-center gap-3 mb-4">
            <div
              class="w-10 h-10 bg-[#7AB2D3] rounded flex items-center justify-center"
            >
              <i class="fas fa-chart-pie text-white"></i>
            </div>
            <div>
              <h3 class="font-bold text-[#2C3E50] text-lg">Net Profit</h3>
              <p class="text-[#40657F] text-sm">Revenue - Expenses</p>
            </div>
          </div>
          <p class="text-2xl font-bold text-[#7AB2D3]">
            MWK {{net_profit|intcomma}}
          </p>
        </div>
      </div>

      <!-- Detailed Financial Statement -->
      <div class="bg-white rounded border border-[#2C3E50] overflow-hidden">
        <div class="bg-[#F7FAFC] px-6 py-4 border-b border-[#2C3E50]">
          <h3 class="text-xl font-bold text-[#2C3E50] flex items-center gap-3">
            <i class="fas fa-table text-[#40657F]"></i>
            Detailed Financial Statement
          </h3>
        </div>

        <table class="w-full">
          <!-- Table Header -->
          <thead class="bg-[#F7FAFC]">
            <tr class="border-b border-[#B9D8EB]/30">
              <th
                class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center gap-2">
                  <i class="fas fa-list text-[#7AB2D3]"></i>
                  Category
                </div>
              </th>
              <th
                class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center gap-2">
                  <i class="fas fa-info-circle text-[#74C69D]"></i>
                  Description
                </div>
              </th>
              <th
                class="px-6 py-4 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center justify-end gap-2">
                  <i class="fas fa-money-bill text-[#F28C8C]"></i>
                  Amount
                </div>
              </th>
            </tr>
          </thead>

          <tbody>
            <!-- Revenue Section -->
            <tr class="bg-[#F7FAFC] border-b-2 border-[#2C3E50]">
              <td class="px-6 py-4 font-bold text-[#74C69D] text-lg">
                <div class="flex items-center gap-3">
                  <i class="fas fa-arrow-up"></i>
                  REVENUE
                </div>
              </td>
              <td class="px-6 py-4"></td>
              <td class="px-6 py-4"></td>
            </tr>

            {% for revenue in revenue %}
            <tr class="border-b border-gray-200">
              <td class="px-6 py-3 text-[#2C3E50] font-medium">
                {{revenue.name}}
              </td>
              <td class="px-6 py-3 text-[#40657F]">Revenue stream</td>
              <td class="px-6 py-3 text-right font-semibold text-[#74C69D]">
                MWK {{revenue.total_amount|intcomma}}
              </td>
            </tr>
            {% endfor %}

            <tr class="bg-[#F7FAFC] border-b-2 border-[#2C3E50]">
              <td class="px-6 py-4 font-bold text-[#2C3E50] text-lg">
                TOTAL REVENUE
              </td>
              <td class="px-6 py-4"></td>
              <td class="px-6 py-4 text-right font-bold text-[#74C69D] text-xl">
                MWK {{total_revenue|intcomma}}
              </td>
            </tr>

            <!-- Spacer Row -->
            <tr>
              <td class="px-6 py-3"></td>
              <td class="px-6 py-3"></td>
              <td class="px-6 py-3"></td>
            </tr>

            <!-- Expenses Section -->
            <tr class="bg-[#F7FAFC] border-b-2 border-[#2C3E50]">
              <td class="px-6 py-4 font-bold text-[#F28C8C] text-lg">
                <div class="flex items-center gap-3">
                  <i class="fas fa-arrow-down"></i>
                  EXPENSES
                </div>
              </td>
              <td class="px-6 py-4"></td>
              <td class="px-6 py-4"></td>
            </tr>

            {% for expense in expenses %}
            <tr class="border-b border-gray-200">
              <td class="px-6 py-3 text-[#2C3E50] font-medium">
                {{expense.name}}
              </td>
              <td class="px-6 py-3 text-[#40657F]">Operating expense</td>
              <td class="px-6 py-3 text-right font-semibold text-[#F28C8C]">
                MWK {{expense.total_amount|intcomma}}
              </td>
            </tr>
            {% endfor %}

            <tr class="bg-[#F7FAFC] border-b-2 border-[#2C3E50]">
              <td class="px-6 py-4 font-bold text-[#2C3E50] text-lg">
                TOTAL EXPENSES
              </td>
              <td class="px-6 py-4"></td>
              <td class="px-6 py-4 text-right font-bold text-[#F28C8C] text-xl">
                MWK {{total_expense|intcomma}}
              </td>
            </tr>

            <!-- Spacer Row -->
            <tr>
              <td class="px-6 py-3"></td>
              <td class="px-6 py-3"></td>
              <td class="px-6 py-3"></td>
            </tr>

            <!-- Net Profit Section -->
            <tr class="bg-[#F7FAFC] border-2 border-[#2C3E50]">
              <td class="px-6 py-5 font-bold text-[#2C3E50] text-xl">
                <div class="flex items-center gap-3">
                  <i class="fas fa-chart-pie text-[#7AB2D3]"></i>
                  NET PROFIT
                </div>
              </td>
              <td class="px-6 py-5 text-[#40657F] font-medium">
                Revenue - Expenses
              </td>
              <td
                class="px-6 py-5 text-right text-[#7AB2D3] text-2xl font-bold"
              >
                MWK {{net_profit|intcomma}}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Footer -->
      <div class="mt-10 pt-6 border-t border-[#B9D8EB]/50 text-center">
        <div class="flex justify-between items-center text-sm text-[#40657F]">
          <div class="flex items-center gap-2">
            <i class="fas fa-calendar"></i>
            <span>Generated on: {{ "now"|date:"F d, Y" }}</span>
          </div>
          <div class="flex items-center gap-2">
            <i class="fas fa-building"></i>
            <span>Tiny Feet Academy</span>
          </div>
          <div class="flex items-center gap-2">
            <i class="fas fa-file-alt"></i>
            <span>Income Statement</span>
          </div>
        </div>
      </div>
    </div>

    <style>
      /* Print-specific styles */
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }

        .grid {
          break-inside: avoid;
        }

        table {
          break-inside: avoid;
        }

        tr {
          break-inside: avoid;
        }

        .shadow-lg,
        .shadow-sm {
          box-shadow: none !important;
        }
      }

      /* Font styling */
      .font-display {
        font-family: "Inter", "Roboto", sans-serif;
      }

      /* Clean, printer-friendly styling */
      body {
        font-family: "Inter", "Roboto", sans-serif;
      }

      /* Ensure consistent borders for printing */
      .border-l-4 {
        border-left-width: 4px;
      }

      .border-2 {
        border-width: 2px;
      }
    </style>

    <script>
      window.onload = function () {
        window.print();
      };
    </script>
  </body>
</html>
