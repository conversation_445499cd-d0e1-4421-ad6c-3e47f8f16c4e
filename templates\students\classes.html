{% extends 'base.html' %} {% load static %}
<!-- title -->
{% block title %}Classes | {% endblock %}
<!-- body -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row items-start justify-between w-full gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4 w-full">
        <div
          class="w-16 h-16 md:bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-3xl flex items-center justify-center md:shadow-xl icon-float text-[#40657f]"
        >
          <i class="fas fa-users md:text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Classes
          </h1>
          <p class="text-[#40657F] font-medium subtitle-fade-in">
            Manage student classes and enrollment
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-3 buttons-slide-in w-full md: justify-end items-center">
        <a
          href="{% url 'students:students' %}"
          class="bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl btn-modern group w-full md:w-fit"
        >
          <i
            class="fas fa-list mr-2 group-hover:scale-110 group-hover:rotate-12 transition-transform duration-300"
          ></i>
          All Students
        </a>
        <a
          href="{% url 'students:admission' %}"
          class="bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl btn-modern group w-full md:w-fit"
        >
          <i
            class="fas fa-user-plus mr-2 group-hover:scale-110 group-hover:rotate-12 transition-transform duration-300"
          ></i>
          Add Student
        </a>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-[#B9D8EB] breadcrumb-fade-in"
    >
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#40657F]">Students</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Classes</span>
    </nav>
  </div>

  <!-- Classes Grid -->
  <section
    class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 w-full"
  >
    {% for level, total, female, male in level_student_count %}
    <a
      class="class-card group relative overflow-hidden bg-gradient-to-br from-white via-[#F7FAFC] to-[#E2F1F9] border-2 border-[#B9D8EB] rounded-2xl p-6 hover:border-[#7AB2D3] hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 cursor-pointer"
      href="{% url 'students:class_details' level.slug %}"
    >
      <!-- Animated Background Pattern -->
      <div
        class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/5 rounded-full -translate-y-16 translate-x-16 group-hover:scale-150 transition-transform duration-700"
      ></div>
      <div
        class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-[#74C69D]/10 to-[#B9D8EB]/10 rounded-full translate-y-12 -translate-x-12 group-hover:scale-125 transition-transform duration-500"
      ></div>

      <!-- Class Header -->
      <div class="flex items-center justify-between mb-6 relative z-10">
        <div
          class="w-14 h-14 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        >
          <i class="fas fa-graduation-cap text-white text-xl"></i>
        </div>
        <div class="flex flex-col items-end gap-2">
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#74C69D] rounded-full animate-pulse"></div>
            <span class="text-xs text-[#74C69D] font-semibold">Active</span>
          </div>
          <div class="text-xs text-[#40657F] font-medium">
            {{ level.education_stage }}
          </div>
        </div>
      </div>

      <!-- Class Name -->
      <div class="relative z-10 mb-6">
        <h1
          class="font-bold text-2xl text-[#2C3E50] mb-2 font-display group-hover:text-[#7AB2D3] transition-colors duration-300"
        >
          {{ level.level_name }}
        </h1>
        <div
          class="w-12 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full group-hover:w-20 transition-all duration-300"
        ></div>
      </div>

      <!-- Total Students -->
      <div class="flex items-center gap-3 mb-6 relative z-10">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#40657F]/20 to-[#2C3E50]/10 rounded-xl flex items-center justify-center"
        >
          <i class="fas fa-users text-[#40657F] text-lg"></i>
        </div>
        <div>
          <span
            class="text-3xl font-bold text-[#2C3E50] font-display block leading-none"
            >{{ total }}</span
          >
          <span class="text-sm text-[#40657F] font-medium"
            >student{{total|pluralize}} enrolled</span
          >
        </div>
      </div>

      <!-- Gender Breakdown -->
      <div class="flex gap-3 text-sm font-semibold mb-6 relative z-10">
        <div
          class="flex items-center gap-2 bg-gradient-to-r from-[#7AB2D3]/20 to-[#40657F]/10 text-[#40657F] rounded-xl px-3 py-2 flex-1 group-hover:from-[#7AB2D3]/30 group-hover:to-[#40657F]/20 transition-all duration-300"
        >
          <i class="fas fa-mars text-[#7AB2D3]"></i>
          <span>{{ male }} Male{{male|pluralize}}</span>
        </div>
        <div
          class="flex items-center gap-2 bg-gradient-to-r from-[#F28C8C]/20 to-[#e07575]/10 text-[#2C3E50] rounded-xl px-3 py-2 flex-1 group-hover:from-[#F28C8C]/30 group-hover:to-[#e07575]/20 transition-all duration-300"
        >
          <i class="fas fa-venus text-[#F28C8C]"></i>
          <span>{{ female }} Female{{female|pluralize}}</span>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="mt-6 pt-6 border-t border-[#B9D8EB]/50 relative z-10">
        <div
          class="flex justify-between items-center text-xs text-[#40657F] mb-3"
        >
          <span class="font-semibold">Class Capacity</span>
          <span class="font-bold">{{ total }}/30</span>
        </div>
        <div class="w-full bg-[#B9D8EB]/30 rounded-full h-3 overflow-hidden">
          <div
            class="bg-gradient-to-r from-[#7AB2D3] to-[#40657F] h-3 rounded-full transition-all duration-1000 ease-out progress-bar-animate"
            style="width: {% widthratio total 30 100 %}%"
          ></div>
        </div>
        <div
          class="flex justify-between items-center text-xs text-[#40657F] mt-2"
        >
          <span>{% widthratio total 30 100 %}% Full</span>
          <span class="font-semibold"
            >{{ total|add:"-30"|default:"0" }} spots left</span
          >
        </div>
      </div>

      <!-- Hover Effect Arrow -->
      <div class="flex justify-end mt-6 relative z-10">
        <div
          class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transform translate-x-4 group-hover:translate-x-0 transition-all duration-300"
        >
          <i class="fas fa-arrow-right text-white text-sm"></i>
        </div>
      </div>

      <!-- Floating Elements -->
      <div
        class="absolute top-4 left-4 w-2 h-2 bg-[#74C69D] rounded-full opacity-20 group-hover:opacity-60 transition-opacity duration-300 floating-dot-1"
      ></div>
      <div
        class="absolute bottom-8 right-8 w-1 h-1 bg-[#F28C8C] rounded-full opacity-30 group-hover:opacity-70 transition-opacity duration-300 floating-dot-2"
      ></div>
    </a>
    {% empty %}
    <div class="col-span-full">
      <div class="card-modern p-16 text-center empty-state-animation">
        <div
          class="w-32 h-32 bg-gradient-to-br from-[#B9D8EB] to-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-8 empty-icon-float"
        >
          <i class="fas fa-users text-[#40657F] text-4xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-[#2C3E50] mb-4 font-display">
          No Classes Found
        </h3>
        <p class="text-[#40657F] text-lg mb-8 max-w-md mx-auto">
          There are no classes set up yet. Create your first class to get
          started with student management.
        </p>
        <a
          href="{% url 'students:admission' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl"
        >
          <i class="fas fa-plus text-lg"></i>
          <span>Add First Student</span>
        </a>
      </div>
    </div>
    {% endfor %}
  </section>

  <!-- Quick Stats Summary -->
  {% if level_student_count %}
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 stats-fade-in">
    <div
      class="card-modern p-6 text-center border-l-4 border-[#7AB2D3] stat-card"
    >
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-layer-group text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Total Classes
      </h3>
      <p class="text-3xl font-bold text-[#7AB2D3]">
        {{ level_student_count|length }}
      </p>
      <p class="text-[#40657F] text-sm">Active class levels</p>
    </div>

    <div
      class="card-modern p-6 text-center border-l-4 border-[#74C69D] stat-card"
    >
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-users text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Total Students
      </h3>
      <p class="text-3xl font-bold text-[#74C69D]">145</p>
      <p class="text-[#40657F] text-sm">Across all classes</p>
    </div>

    <div
      class="card-modern p-6 text-center border-l-4 border-[#F28C8C] stat-card"
    >
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-chart-line text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Average Class Size
      </h3>
      <p class="text-3xl font-bold text-[#F28C8C]">18</p>
      <p class="text-[#40657F] text-sm">Students per class</p>
    </div>

    <div
      class="card-modern p-6 text-center border-l-4 border-[#40657F] stat-card"
    >
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-graduation-cap text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Capacity Usage
      </h3>
      <p class="text-3xl font-bold text-[#40657F]">75%</p>
      <p class="text-[#40657F] text-sm">Overall utilization</p>
    </div>
  </div>
  {% endif %}
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateX(50px);
    animation: buttonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 1s forwards;
  }

  /* Class Card Animations */
  .class-card {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
    animation: classCardSlideUp 0.8s ease-out forwards;
  }

  .class-card:nth-child(1) {
    animation-delay: 0.1s;
  }
  .class-card:nth-child(2) {
    animation-delay: 0.2s;
  }
  .class-card:nth-child(3) {
    animation-delay: 0.3s;
  }
  .class-card:nth-child(4) {
    animation-delay: 0.4s;
  }
  .class-card:nth-child(5) {
    animation-delay: 0.5s;
  }
  .class-card:nth-child(6) {
    animation-delay: 0.6s;
  }
  .class-card:nth-child(7) {
    animation-delay: 0.7s;
  }
  .class-card:nth-child(8) {
    animation-delay: 0.8s;
  }

  .progress-bar-animate {
    transform: scaleX(0);
    transform-origin: left;
    animation: progressBarGrow 1.5s ease-out 0.5s forwards;
  }

  .floating-dot-1 {
    animation: floatingDot1 4s ease-in-out infinite;
  }

  .floating-dot-2 {
    animation: floatingDot2 3s ease-in-out infinite 1s;
  }

  /* Empty State Animations */
  .empty-state-animation {
    opacity: 0;
    transform: translateY(30px);
    animation: emptyStateSlideUp 0.8s ease-out 0.3s forwards;
  }

  .empty-icon-float {
    animation: emptyIconFloat 3s ease-in-out infinite;
  }

  /* Stats Animations */
  .stats-fade-in {
    opacity: 0;
    animation: statsFadeIn 0.8s ease-out 1.2s forwards;
  }

  .stat-card {
    transform: translateY(20px);
    animation: statCardSlideUp 0.6s ease-out forwards;
  }

  .stat-card:nth-child(1) {
    animation-delay: 1.3s;
  }
  .stat-card:nth-child(2) {
    animation-delay: 1.4s;
  }
  .stat-card:nth-child(3) {
    animation-delay: 1.5s;
  }
  .stat-card:nth-child(4) {
    animation-delay: 1.6s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes buttonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes classCardSlideUp {
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes progressBarGrow {
    to {
      transform: scaleX(1);
    }
  }

  @keyframes floatingDot1 {
    0%,
    100% {
      transform: translate(0, 0);
    }
    25% {
      transform: translate(5px, -5px);
    }
    50% {
      transform: translate(-3px, -8px);
    }
    75% {
      transform: translate(3px, -3px);
    }
  }

  @keyframes floatingDot2 {
    0%,
    100% {
      transform: translate(0, 0);
    }
    33% {
      transform: translate(-4px, 6px);
    }
    66% {
      transform: translate(6px, -4px);
    }
  }

  @keyframes emptyStateSlideUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes emptyIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-15px) rotate(5deg);
    }
  }

  @keyframes statsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes statCardSlideUp {
    to {
      transform: translateY(0);
    }
  }

  /* Hover Enhancements */
  .class-card:hover .progress-bar-animate {
    animation: progressBarPulse 0.6s ease-in-out;
  }

  @keyframes progressBarPulse {
    0%,
    100% {
      transform: scaleX(1) scaleY(1);
    }
    50% {
      transform: scaleX(1) scaleY(1.2);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .class-card {
      animation-delay: 0.1s;
    }

    .class-card:nth-child(n) {
      animation-delay: calc(0.1s * var(--card-index, 1));
    }
  }
</style>

{% endblock %}
