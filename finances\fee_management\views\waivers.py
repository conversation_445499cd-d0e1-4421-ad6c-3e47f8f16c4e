from django.shortcuts import redirect, render, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.views.decorators.http import require_POST

from students.models import Student
from finances.fee_management.forms import WaiverForm
from finances.fee_management.models import FeesWaiver


def is_superuser(user):
    """Check if user is a superuser"""
    return user.is_superuser


@login_required(login_url="accounts:login")
def waive_fees(request, slug):
    student = Student.objects.get(student_id=slug)

    form = WaiverForm()
    if request.method == 'POST':
        form = WaiverForm(request.POST)
        if form.is_valid():
            waive_fees = form.save(student=student)

            waive_fees.save()
            messages.success(
                request, f"{waive_fees.student} created successfully")

            return redirect('students:student_details', slug)

    context = {
        'form': form,
        'student': student,
    }

    return render(request, "receipts/waiver_form.html", context)


@login_required(login_url="accounts:login")
def view_waiver(request, waiver_id):
    """View individual waiver details"""
    waiver = get_object_or_404(FeesWaiver, waiver_id=waiver_id)

    context = {
        'waiver': waiver,
    }

    return render(request, "receipts/view_waiver.html", context)


@login_required(login_url="accounts:login")
def waiver_list(request):
    """View list of all waivers with search and pagination"""
    # Get search query from request
    search_query = request.GET.get('search', '').strip()

    # Base queryset with related data
    waivers = FeesWaiver.objects.select_related(
        'account__student',
        'account__category',
        'account__student__level',
        'account__term'
    ).order_by('-date_waived', '-waiver_id')

    # Apply search filters if search query exists
    if search_query:
        waivers = waivers.filter(
            Q(waiver_id__icontains=search_query) |
            Q(account__student__name__icontains=search_query) |
            Q(account__student__student_id__icontains=search_query) |
            Q(account__category__name__icontains=search_query) |
            Q(waived_by__icontains=search_query) |
            Q(reason__icontains=search_query) |
            Q(account__student__level__level_name__icontains=search_query)
        )

    # Add pagination
    paginator = Paginator(waivers, 25)  # Show 25 waivers per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'waivers': page_obj,
        'search_query': search_query,
        'total_count': waivers.count(),
        'page_obj': page_obj,
    }

    return render(request, 'receipts/waiver_list.html', context)


@login_required(login_url="accounts:login")
@user_passes_test(is_superuser, login_url="accounts:login")
@require_POST
def delete_waiver(request, waiver_id):
    """Delete a waiver (superuser only)"""
    try:
        waiver = get_object_or_404(FeesWaiver, waiver_id=waiver_id)

        # Store waiver details for the success message
        student_name = waiver.account.student.name
        amount_waived = waiver.amount_waived

        # Delete the waiver
        waiver.delete()

        # Add success message
        messages.success(
            request,
            f"Waiver {waiver_id} for {student_name} (K {amount_waived:,}) has been successfully deleted."
        )

        # Return JSON response for AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f"Waiver {waiver_id} has been successfully deleted.",
                'redirect_url': request.META.get('HTTP_REFERER', '/finances/waivers/')
            })

        # Redirect to waiver list for regular requests
        return redirect('finances:waiver_list')

    except Exception as e:
        error_message = f"Error deleting waiver {waiver_id}: {str(e)}"
        messages.error(request, error_message)

        # Return JSON response for AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': error_message
            })

        # Redirect back for regular requests
        return redirect(request.META.get('HTTP_REFERER', 'finances:waiver_list'))
