# Role-Based Access Control (RBAC) Guide

## 📋 Overview

The School Management System implements a comprehensive Role-Based Access Control (RBAC) system that provides fine-grained permission management for different user types. This system replaces simple `is_staff` checks with a flexible, hierarchical role and permission structure.

## 🏗️ Architecture

### Core Components

1. **Permissions**: Granular actions (e.g., `view_students`, `edit_finances`)
2. **Roles**: Collections of permissions with hierarchy levels
3. **User Role Assignments**: Many-to-many relationships between users and roles
4. **Middleware**: Automatic permission checking for views
5. **Decorators & Mixins**: Reusable permission checking for views

### Role Hierarchy

| Level | Role | Description |
|-------|------|-------------|
| 1 | Student | Basic student access |
| 2 | Teacher | Academic management and grading |
| 3 | Academic Coordinator | Academic oversight and coordination |
| 4 | Finance Manager | Financial management and reporting |
| 5 | Administrator | Full system administration |
| 6 | Super Administrator | Complete system access |

## 🚀 Quick Start

### 1. Set Up RBAC System

```bash
# Create default roles and permissions
python manage.py setup_rbac

# Assign roles to existing users
python manage.py setup_rbac --assign-superuser-roles --migrate-staff

# Reset and recreate (WARNING: Deletes all RBAC data)
python manage.py setup_rbac --reset
```

### 2. Assign Roles to Users

```python
from accounts.utils import RBACManager
from django.contrib.auth import get_user_model

User = get_user_model()

# Assign administrator role to a user
user = User.objects.get(username='john_doe')
RBACManager.assign_role_to_user(
    user=user,
    role='administrator',
    notes="Promoted to administrator"
)

# Assign role with expiration
from datetime import datetime, timedelta
RBACManager.assign_role_to_user(
    user=user,
    role='teacher',
    expires_at=datetime.now() + timedelta(days=365),
    notes="Temporary teaching assignment"
)
```

## 🔒 Using RBAC in Views

### Function-Based Views

```python
from accounts.decorators import require_permission, require_role, admin_required

# Require specific permissions
@require_permission('view_students', 'manage_students')
def student_list(request):
    # View code here
    pass

# Require specific roles
@require_role('teacher', 'administrator')
def grade_management(request):
    # View code here
    pass

# Require administrator access
@admin_required
def system_settings(request):
    # View code here
    pass

# Backward compatibility with staff check
@staff_or_permission_required('manage_finances')
def finance_view(request):
    # View code here
    pass
```

### Class-Based Views

```python
from accounts.mixins import (
    PermissionRequiredMixin, RoleRequiredMixin, 
    AdminRequiredMixin, TeacherRequiredMixin
)

# Permission-based access
class StudentListView(PermissionRequiredMixin, ListView):
    required_permissions = ['view_students']
    model = Student
    template_name = 'students/list.html'

# Role-based access
class GradeManagementView(RoleRequiredMixin, TemplateView):
    required_roles = ['teacher', 'academic_coordinator']
    template_name = 'academics/grades.html'

# Convenience mixins
class FinanceReportView(FinanceManagementMixin, TemplateView):
    template_name = 'finances/reports.html'

# API views
class StudentAPIView(APIPermissionMixin, APIView):
    required_permissions = ['view_students', 'manage_students']
    
    def get(self, request):
        # API logic here
        pass
```

## 🛠️ Permission Management

### Default Permissions

The system includes these permission categories:

#### Students
- `view_students` - View student information
- `add_student` - Add new students
- `edit_student` - Edit student information
- `delete_student` - Delete students
- `manage_students` - Full student management

#### Finances
- `view_finances` - View financial information
- `add_fee` - Add new fees
- `edit_fee` - Edit fees
- `delete_fee` - Delete fees
- `manage_finances` - Full financial management
- `manage_fee_collection` - Manage fee collection
- `view_financial_reports` - View financial reports

#### Academics
- `view_academics` - View academic information
- `add_subject` - Add new subjects
- `edit_subject` - Edit subjects
- `manage_academics` - Full academic management
- `manage_assessments` - Manage assessments
- `view_grades` - View grades
- `edit_grades` - Edit grades

#### Teachers
- `view_teachers` - View teacher information
- `add_teacher` - Add new teachers
- `edit_teacher` - Edit teacher information
- `view_teacher_details` - View detailed teacher information
- `manage_teacher_assignments` - Manage teacher assignments

#### System
- `manage_system` - Manage system operations
- `manage_academic_periods` - Manage academic periods

#### Reports
- `view_reports` - View reports
- `view_financial_reports` - View financial reports
- `view_academic_reports` - View academic reports

### Creating Custom Permissions

```python
from accounts.utils import RBACManager

# Create a new permission
permission = RBACManager.create_permission(
    name='manage_library',
    display_name='Manage Library',
    category='library',
    description='Can manage library books and borrowing'
)

# Add permission to existing role
role = Role.objects.get(name='teacher')
role.permissions.add(permission)
```

## 👥 User Management

### Check User Permissions

```python
# In views
if request.user.has_permission('view_students'):
    # User can view students
    pass

if request.user.has_role('teacher'):
    # User is a teacher
    pass

if request.user.get_highest_role_level() >= 3:
    # User has Academic Coordinator level or higher
    pass

# Convenience methods
if request.user.can_manage_students():
    # User can manage students
    pass
```

### Template Usage

```html
<!-- Check permissions in templates -->
{% if user.has_permission:'view_students' %}
    <a href="{% url 'students:list' %}">View Students</a>
{% endif %}

<!-- Check roles -->
{% if user.has_role:'teacher' %}
    <a href="{% url 'academics:grades' %}">Manage Grades</a>
{% endif %}

<!-- Use context variables (added by RBACContextMiddleware) -->
{% if can_manage_finances %}
    <a href="{% url 'finances:dashboard' %}">Finance Dashboard</a>
{% endif %}

<!-- Check role level -->
{% if user_role_level >= 5 %}
    <a href="{% url 'admin:index' %}">Admin Panel</a>
{% endif %}
```

## 🔧 Advanced Usage

### Bulk Role Assignment

```python
from accounts.utils import RBACManager

# Assign roles to multiple users
assignments = [
    {
        'user': user1,
        'role': 'teacher',
        'notes': 'New teacher assignment'
    },
    {
        'user': user2,
        'role': 'academic_coordinator',
        'expires_at': datetime.now() + timedelta(days=365)
    }
]

RBACManager.bulk_assign_roles(assignments)
```

### Permission Auditing

```python
from accounts.utils import RBACManager

# Audit user permissions
audit_data = RBACManager.audit_user_permissions(user)
print(f"User: {audit_data['user']}")
print(f"Role Level: {audit_data['role_level']}")
print(f"Roles: {[r['role'].display_name for r in audit_data['roles']]}")
print(f"Permissions: {[p['permission'].display_name for p in audit_data['permissions']]}")
```

### Cleanup Expired Roles

```python
# Clean up expired role assignments (can be run as a cron job)
from accounts.utils import RBACManager

expired_count = RBACManager.cleanup_expired_roles()
print(f"Cleaned up {expired_count} expired role assignments")
```

## 🎯 Best Practices

### 1. Use Specific Permissions
```python
# Good: Specific permission
@require_permission('edit_student_grades')
def update_grades(request):
    pass

# Avoid: Too broad
@require_permission('manage_everything')
def update_grades(request):
    pass
```

### 2. Combine Permissions Logically
```python
# Good: Related permissions
@require_permission('view_finances', 'manage_fee_collection')
def fee_collection_view(request):
    pass
```

### 3. Use Role Hierarchy
```python
# Good: Use role levels for hierarchical access
@require_role_level(3)  # Academic Coordinator or higher
def academic_reports(request):
    pass
```

### 4. Provide Fallback Access
```python
# Good: Backward compatibility
@staff_or_permission_required('manage_students')
def legacy_view(request):
    pass
```

## 🚨 Security Considerations

1. **Always check permissions** in views, even if UI elements are hidden
2. **Use HTTPS** in production to protect session data
3. **Regularly audit** user permissions and role assignments
4. **Set expiration dates** for temporary role assignments
5. **Monitor** permission changes through Django admin logs
6. **Test permission logic** thoroughly with different user types

## 🔍 Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Check if user has required permissions
   - Verify role assignments are active and not expired
   - Ensure middleware is properly configured

2. **Middleware Not Working**
   - Verify middleware order in settings
   - Check if view names match PROTECTED_VIEWS configuration

3. **Template Permission Checks Failing**
   - Ensure RBACContextMiddleware is enabled
   - Use correct permission/role names in templates

### Debug Commands

```bash
# Check user's current roles and permissions
python manage.py shell
>>> from django.contrib.auth import get_user_model
>>> User = get_user_model()
>>> user = User.objects.get(username='testuser')
>>> print("Roles:", [ur.role.display_name for ur in user.get_active_roles()])
>>> print("Permissions:", [p.display_name for p in user.get_all_permissions()])
```

## 📚 API Reference

See the following files for detailed API documentation:
- `accounts/models/rbac.py` - Model definitions
- `accounts/decorators.py` - View decorators
- `accounts/mixins.py` - Class-based view mixins
- `accounts/utils.py` - Utility functions
- `core/middleware/rbac_middleware.py` - Middleware implementation
