# Income models removed - deprecated in favor of Budget and Ledger system
# All income tracking is now handled through the modern double-entry bookkeeping system:
# - Budget model for income planning
# - BudgetLine model for detailed budget allocations
# - Ledger model for account tracking
# - JournalEntry/JournalLine for double-entry bookkeeping

# from django.contrib import admin
# from finances.income.models import Income, IncomeTotal
#
# These models have been removed and replaced with:
# - finances.book_keeping.Budget (for income planning)
# - finances.book_keeping.BudgetLine (for detailed allocations)
# - finances.book_keeping.Ledger (for income account tracking)
#
# See finances.admin.book_keeping_admin for the modern income management interface.
