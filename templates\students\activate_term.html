{% extends 'base.html' %}
<!--  -->
{% block title %}{{ title }} | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex items-center gap-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-2xl flex items-center justify-center shadow-xl icon-float"
      >
        <i class="fas fa-play-circle text-white text-2xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
        >
          {{ title }}
        </h1>
        <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
          Activate a term to make it the current active academic period
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#74C69D] to-[#5fb085] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
  </div>

  <!-- Information Section -->
  <div class="card-modern p-6 bg-gradient-to-r from-[#74C69D]/10 to-[#5fb085]/10 border-l-4 border-[#74C69D] info-fade-in">
    <div class="flex items-center gap-3 mb-4">
      <i class="fas fa-info-circle text-[#74C69D] text-2xl"></i>
      <h3 class="font-bold text-[#74C69D] text-xl">Term Activation</h3>
    </div>
    <div class="text-[#2C3E50] space-y-3">
      <p class="font-medium">
        Activating a term will:
      </p>
      <ul class="space-y-2 ml-4">
        <li class="flex items-start gap-2">
          <i class="fas fa-check-circle text-[#74C69D] mt-1 text-sm"></i>
          <span>Make this term the current active academic period</span>
        </li>
        <li class="flex items-start gap-2">
          <i class="fas fa-check-circle text-[#74C69D] mt-1 text-sm"></i>
          <span>Deactivate all other terms automatically</span>
        </li>
        <li class="flex items-start gap-2">
          <i class="fas fa-check-circle text-[#74C69D] mt-1 text-sm"></i>
          <span>Activate the academic year if not already active</span>
        </li>
        <li class="flex items-start gap-2">
          <i class="fas fa-check-circle text-[#74C69D] mt-1 text-sm"></i>
          <span>Enable data entry and operations for this term</span>
        </li>
      </ul>
      <p class="font-medium text-[#74C69D] mt-4">
        Only one term can be active at a time across the entire system.
      </p>
    </div>
  </div>

  <!-- Form Section -->
  <div class="card-modern p-8 form-fade-in">
    <form method="POST" class="space-y-6">
      {% csrf_token %}
      
      <!-- Term Selection -->
      <div class="form-group">
        <label for="{{ form.term.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-3">
          <div class="flex items-center gap-2">
            <i class="fas fa-calendar-alt text-[#74C69D]"></i>
            {{ form.term.label }}
          </div>
        </label>
        {{ form.term }}
        {% if form.term.help_text %}
        <p class="text-sm text-[#40657F]/70 mt-2">{{ form.term.help_text }}</p>
        {% endif %}
        {% if form.term.errors %}
        <div class="mt-2">
          {% for error in form.term.errors %}
          <p class="text-sm text-[#F28C8C] flex items-center gap-2">
            <i class="fas fa-exclamation-circle"></i>
            {{ error }}
          </p>
          {% endfor %}
        </div>
        {% endif %}
      </div>

      <!-- Form-wide Errors -->
      {% if form.non_field_errors %}
      <div class="bg-[#F28C8C]/10 border border-[#F28C8C]/30 rounded-xl p-4">
        <div class="flex items-center gap-2 mb-2">
          <i class="fas fa-exclamation-triangle text-[#F28C8C]"></i>
          <h4 class="font-bold text-[#F28C8C]">Please correct the following errors:</h4>
        </div>
        {% for error in form.non_field_errors %}
        <p class="text-sm text-[#F28C8C]">{{ error }}</p>
        {% endfor %}
      </div>
      {% endif %}

      <!-- Guidelines Section -->
      <div class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] rounded-xl p-6 border border-[#B9D8EB]/50">
        <div class="flex items-center gap-3 mb-4">
          <i class="fas fa-lightbulb text-[#7AB2D3] text-lg"></i>
          <h3 class="font-bold text-[#2C3E50]">Activation Guidelines</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-3">
            <h4 class="font-semibold text-[#2C3E50] text-sm">Before Activation:</h4>
            <ul class="space-y-2">
              <li class="flex items-start gap-2">
                <i class="fas fa-check text-[#74C69D] mt-1 text-xs"></i>
                <span class="text-sm text-[#40657F]">Ensure the term dates are correct</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-check text-[#74C69D] mt-1 text-xs"></i>
                <span class="text-sm text-[#40657F]">Verify academic year is set up</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-check text-[#74C69D] mt-1 text-xs"></i>
                <span class="text-sm text-[#40657F]">Complete previous term tasks</span>
              </li>
            </ul>
          </div>
          <div class="space-y-3">
            <h4 class="font-semibold text-[#2C3E50] text-sm">After Activation:</h4>
            <ul class="space-y-2">
              <li class="flex items-start gap-2">
                <i class="fas fa-arrow-right text-[#7AB2D3] mt-1 text-xs"></i>
                <span class="text-sm text-[#40657F]">Create fee accounts for students</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-arrow-right text-[#7AB2D3] mt-1 text-xs"></i>
                <span class="text-sm text-[#40657F]">Set up assessment schedules</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-arrow-right text-[#7AB2D3] mt-1 text-xs"></i>
                <span class="text-sm text-[#40657F]">Begin academic activities</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Current Status -->
      <div class="bg-gradient-to-r from-[#F7FAFC] to-[#E2F1F9] rounded-xl p-6 border border-[#B9D8EB]/30">
        <div class="flex items-center gap-3 mb-4">
          <i class="fas fa-clock text-[#7AB2D3] text-lg"></i>
          <h3 class="font-bold text-[#2C3E50]">Current Status</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 class="font-semibold text-[#2C3E50] text-sm mb-2">Active Academic Year:</h4>
            <p class="text-[#40657F] text-sm">
              {% if active_academic_year %}
                {{ active_academic_year.name }} ({{ active_academic_year.start_date }} - {{ active_academic_year.end_date }})
              {% else %}
                <span class="text-[#F28C8C]">No active academic year</span>
              {% endif %}
            </p>
          </div>
          <div>
            <h4 class="font-semibold text-[#2C3E50] text-sm mb-2">Active Term:</h4>
            <p class="text-[#40657F] text-sm">
              {% if active_term %}
                {{ active_term.term_name }} of {{ active_term.academic_year.name }}
              {% else %}
                <span class="text-[#F28C8C]">No active term</span>
              {% endif %}
            </p>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-[#B9D8EB]/30">
        <button
          type="submit"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-play-circle group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>{{ submit_text }}</span>
        </button>
        <a
          href="{% url 'students:academic_year_management' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-arrow-left group-hover:scale-110 group-hover:-translate-x-1 transition-all duration-300"
          ></i>
          <span>Back to Management</span>
        </a>
      </div>
    </form>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  /* Info Animation */
  .info-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: infoFadeIn 0.8s ease-out 0.8s forwards;
  }

  /* Form Animation */
  .form-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: formFadeIn 0.8s ease-out 1s forwards;
  }

  /* Form Group Animation */
  .form-group {
    opacity: 0;
    transform: translateX(-20px);
    animation: formGroupSlideIn 0.4s ease-out 1.2s forwards;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes infoFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes formFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes formGroupSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  /* Form Styling */
  .form-group input,
  .form-group select {
    transition: all 0.3s ease;
  }

  .form-group input:focus,
  .form-group select:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(116, 198, 157, 0.15);
  }
</style>

{% endblock %}
