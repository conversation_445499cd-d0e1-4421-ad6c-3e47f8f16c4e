{% extends 'base.html' %}
{% load static %}
{% load rbac_tags %}

{% block title %}All Students | {% endblock %}

{% block content %}
<!-- Add RBAC context to template -->
{% rbac_context %}

<!-- RBAC Debug Info (only in DEBUG mode) -->
{% rbac_debug user %}

<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg icon-float">
        <i class="fas fa-users text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
          All Students
        </h1>
        <div class="w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full accent-line-grow"></div>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">All Students</span>
    </nav>
  </div>

  <!-- Action Buttons Section - Only show if user has appropriate permissions -->
  {% if user|has_permission:"add_student" or user|has_permission:"manage_students" %}
  <div class="card-modern p-6">
    <div class="flex flex-wrap gap-4">
      {% if user|has_permission:"add_student" %}
      <a href="{% url 'students:admission' %}" 
         class="btn-primary flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
        <i class="fas fa-plus"></i>
        Add New Student
      </a>
      {% endif %}
      
      {% if user|has_permission:"add_student" %}
      <a href="{% url 'students:admission_by_excel' %}" 
         class="btn-secondary flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
        <i class="fas fa-file-excel"></i>
        Bulk Import
      </a>
      {% endif %}
      
      {% if user|has_any_permission:"view_financial_reports,manage_finances" %}
      <a href="{% url 'students:outstanding_balances' %}" 
         class="btn-accent flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
        <i class="fas fa-chart-line"></i>
        Outstanding Balances
      </a>
      {% endif %}
    </div>
  </div>
  {% endif %}

  <!-- Search Section -->
  <div class="card-modern p-8 search-section-fade-in">
    <div class="flex items-center gap-4 mb-6">
      <div class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg search-icon-float">
        <i class="fas fa-search text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">Search Students</h3>
        <p class="text-[#40657F] text-sm">Find students by ID, name, or class</p>
      </div>
    </div>

    <!-- Search Form -->
    <form method="GET" class="search-form-slide-in">
      <div class="flex gap-4">
        <div class="flex-1">
          <input type="text" 
                 name="q" 
                 value="{{ search_query }}" 
                 placeholder="Search by student ID, name, or class..." 
                 class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300">
        </div>
        <button type="submit" 
                class="px-8 py-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
          <i class="fas fa-search mr-2"></i>Search
        </button>
      </div>
    </form>

    {% if search_query %}
    <div class="mt-4 p-4 bg-[#E2F1F9] rounded-xl border border-[#B9D8EB]">
      <p class="text-[#40657F] font-medium">
        <i class="fas fa-info-circle mr-2"></i>
        Showing results for: <span class="font-bold">"{{ search_query }}"</span>
        ({{ total_count }} student{{ total_count|pluralize }} found)
      </p>
    </div>
    {% endif %}
  </div>

  <!-- Students List -->
  <div class="card-modern p-8">
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg">
          <i class="fas fa-list text-white text-lg"></i>
        </div>
        <div>
          <h3 class="text-2xl font-bold text-[#2C3E50] font-display">Students Directory</h3>
          <p class="text-[#40657F] text-sm">{{ total_count }} student{{ total_count|pluralize }} total</p>
        </div>
      </div>
      
      <!-- Role-based information display -->
      <div class="text-sm text-[#40657F]">
        {% if user|has_role:"administrator" %}
          <span class="px-3 py-1 bg-[#F28C8C] text-white rounded-full font-semibold">
            <i class="fas fa-crown mr-1"></i>Administrator View
          </span>
        {% elif user|has_role:"teacher" %}
          <span class="px-3 py-1 bg-[#74C69D] text-white rounded-full font-semibold">
            <i class="fas fa-chalkboard-teacher mr-1"></i>Teacher View
          </span>
        {% elif user|has_role:"academic_coordinator" %}
          <span class="px-3 py-1 bg-[#7AB2D3] text-white rounded-full font-semibold">
            <i class="fas fa-graduation-cap mr-1"></i>Academic Coordinator View
          </span>
        {% endif %}
      </div>
    </div>

    {% if students %}
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-[#B9D8EB]">
            <th class="text-left py-4 px-4 font-semibold text-[#2C3E50]">Student ID</th>
            <th class="text-left py-4 px-4 font-semibold text-[#2C3E50]">Name</th>
            <th class="text-left py-4 px-4 font-semibold text-[#2C3E50]">Class</th>
            <th class="text-left py-4 px-4 font-semibold text-[#2C3E50]">Status</th>
            {% if user|has_any_permission:"edit_student,view_students,manage_students" %}
            <th class="text-center py-4 px-4 font-semibold text-[#2C3E50]">Actions</th>
            {% endif %}
          </tr>
        </thead>
        <tbody>
          {% for student in students %}
          <tr class="border-b border-[#E2F1F9] hover:bg-[#F7FAFC] transition-colors duration-200">
            <td class="py-4 px-4">
              <span class="font-mono text-[#40657F] font-semibold">{{ student.student_id }}</span>
            </td>
            <td class="py-4 px-4">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-full flex items-center justify-center text-white font-semibold">
                  {{ student.name|first|upper }}
                </div>
                <span class="font-semibold text-[#2C3E50]">{{ student.name }}</span>
              </div>
            </td>
            <td class="py-4 px-4">
              <span class="px-3 py-1 bg-[#E2F1F9] text-[#40657F] rounded-full text-sm font-medium">
                {{ student.level.level_name }}
              </span>
            </td>
            <td class="py-4 px-4">
              {% if student.is_active %}
                <span class="px-3 py-1 bg-[#74C69D] text-white rounded-full text-sm font-semibold">
                  <i class="fas fa-check-circle mr-1"></i>Active
                </span>
              {% else %}
                <span class="px-3 py-1 bg-[#F28C8C] text-white rounded-full text-sm font-semibold">
                  <i class="fas fa-times-circle mr-1"></i>Inactive
                </span>
              {% endif %}
            </td>
            {% if user|has_any_permission:"edit_student,view_students,manage_students" %}
            <td class="py-4 px-4 text-center">
              <div class="flex items-center justify-center gap-2">
                {% if user|has_permission:"view_students" %}
                <a href="{% url 'students:student_details' student.student_id %}" 
                   class="px-3 py-2 bg-[#7AB2D3] text-white rounded-lg hover:bg-[#40657F] transition-colors duration-200"
                   title="View Details">
                  <i class="fas fa-eye"></i>
                </a>
                {% endif %}
                
                {% if user|has_permission:"edit_student" %}
                <a href="{% url 'students:edit_student' student.student_id %}" 
                   class="px-3 py-2 bg-[#74C69D] text-white rounded-lg hover:bg-[#5fb085] transition-colors duration-200"
                   title="Edit Student">
                  <i class="fas fa-edit"></i>
                </a>
                {% endif %}
                
                {% if user|has_any_permission:"view_financial_reports,manage_finances" %}
                <a href="{% url 'finances:student_account' student.student_id %}" 
                   class="px-3 py-2 bg-[#F28C8C] text-white rounded-lg hover:bg-[#e07575] transition-colors duration-200"
                   title="Financial Records">
                  <i class="fas fa-dollar-sign"></i>
                </a>
                {% endif %}
              </div>
            </td>
            {% endif %}
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
    {% else %}
    <div class="text-center py-12">
      <div class="w-24 h-24 bg-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-users text-[#7AB2D3] text-3xl"></i>
      </div>
      <h3 class="text-xl font-bold text-[#2C3E50] mb-2">No Students Found</h3>
      <p class="text-[#40657F] mb-6">
        {% if search_query %}
          No students match your search criteria. Try a different search term.
        {% else %}
          No students have been added yet.
        {% endif %}
      </p>
      {% if user|has_permission:"add_student" %}
      <a href="{% url 'students:admission' %}" 
         class="btn-primary inline-flex items-center gap-2 px-6 py-3 rounded-xl font-semibold">
        <i class="fas fa-plus"></i>
        Add First Student
      </a>
      {% endif %}
    </div>
    {% endif %}
  </div>

  <!-- User Role Information Panel (for demonstration) -->
  {% if user.is_authenticated %}
  <div class="card-modern p-6 bg-gradient-to-r from-[#E2F1F9] to-[#F7FAFC]">
    <h4 class="text-lg font-bold text-[#2C3E50] mb-4">
      <i class="fas fa-user-shield mr-2"></i>Your Access Level
    </h4>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="text-center">
        <div class="text-2xl font-bold text-[#7AB2D3]">{{ user_role_level }}</div>
        <div class="text-sm text-[#40657F]">Role Level</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-[#74C69D]">
          {% user_roles user as roles %}{{ roles|length }}
        </div>
        <div class="text-sm text-[#40657F]">Active Roles</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-[#F28C8C]">
          {% user_permissions user as permissions %}{{ permissions|length }}
        </div>
        <div class="text-sm text-[#40657F]">Permissions</div>
      </div>
    </div>
  </div>
  {% endif %}
</section>

{% endblock %}
