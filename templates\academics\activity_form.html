{% extends 'academics/base.html' %}
<!--  -->
{% block title %}{{ title }} | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex items-center gap-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#F28C8C] to-[#e74c3c] rounded-2xl flex items-center justify-center shadow-xl icon-float"
      >
        <i class="fas fa-calendar-plus text-white text-2xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
        >
          {{ title }}
        </h1>
        <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
          {% if activity %}
            Update activity details and schedule
          {% else %}
            Schedule a new academic activity
          {% endif %}
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
  </div>

  <!-- Navigation Breadcrumb -->
  <div class="breadcrumb-fade-in">
    <nav class="flex items-center gap-2 text-sm text-[#40657F]">
      <a
        href="{% url 'academics:academic_management_dashboard' %}"
        class="hover:text-[#F28C8C] transition-colors duration-200"
      >
        <i class="fas fa-graduation-cap mr-1"></i>
        Academic Management
      </a>
      <i class="fas fa-chevron-right text-[#B9D8EB]"></i>
      <span class="text-[#2C3E50] font-medium">{{ title }}</span>
    </nav>
  </div>

  <!-- Activity Form -->
  <div class="card-modern p-8 form-slide-in">
    <form method="POST" class="space-y-8">
      {% csrf_token %}

      <!-- Activity Details Section -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg section-icon-float"
          >
            <i class="fas fa-info-circle text-white text-lg"></i>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
              Activity Details
            </h2>
            <p class="text-[#40657F] text-sm">
              Basic information about the activity
            </p>
          </div>
          <div
            class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
          ></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Activity Type -->
          <div class="form-group">
            <label
              for="{{ form.activity_type.id_for_label }}"
              class="block text-sm font-bold text-[#2C3E50] mb-2"
            >
              <i class="fas fa-tags text-[#F28C8C] mr-2"></i>
              {{ form.activity_type.label }}
            </label>
            {{ form.activity_type }}
            {% if form.activity_type.help_text %}
            <p class="text-sm text-[#40657F] mt-2">{{ form.activity_type.help_text }}</p>
            {% endif %}
            {% if form.activity_type.errors %}
            <div class="text-[#F28C8C] text-sm mt-2">
              {% for error in form.activity_type.errors %}
              <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
              {% endfor %}
            </div>
            {% endif %}
          </div>

          <!-- Activity Date -->
          <div class="form-group">
            <label
              for="{{ form.date.id_for_label }}"
              class="block text-sm font-bold text-[#2C3E50] mb-2"
            >
              <i class="fas fa-calendar text-[#74C69D] mr-2"></i>
              {{ form.date.label }}
            </label>
            {{ form.date }}
            {% if form.date.help_text %}
            <p class="text-sm text-[#40657F] mt-2">{{ form.date.help_text }}</p>
            {% endif %}
            {% if form.date.errors %}
            <div class="text-[#F28C8C] text-sm mt-2">
              {% for error in form.date.errors %}
              <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
              {% endfor %}
            </div>
            {% endif %}
          </div>
        </div>

        <!-- Subject (Optional) -->
        <div class="form-group">
          <label
            for="{{ form.subject.id_for_label }}"
            class="block text-sm font-bold text-[#2C3E50] mb-2"
          >
            <i class="fas fa-book text-[#7AB2D3] mr-2"></i>
            {{ form.subject.label }}
            <span class="text-[#40657F] text-xs font-normal">(Optional)</span>
          </label>
          {{ form.subject }}
          {% if form.subject.help_text %}
          <p class="text-sm text-[#40657F] mt-2">{{ form.subject.help_text }}</p>
          {% endif %}
          {% if form.subject.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.subject.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Schedule & Assignment Section -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg schedule-icon-float"
          >
            <i class="fas fa-calendar-check text-white text-lg"></i>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
              Schedule & Assignment
            </h2>
            <p class="text-[#40657F] text-sm">
              When and for whom this activity is scheduled
            </p>
          </div>
          <div
            class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
          ></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Academic Term -->
          <div class="form-group">
            <label
              for="{{ form.term.id_for_label }}"
              class="block text-sm font-bold text-[#2C3E50] mb-2"
            >
              <i class="fas fa-calendar-alt text-[#40657F] mr-2"></i>
              {{ form.term.label }}
            </label>
            {{ form.term }}
            {% if form.term.help_text %}
            <p class="text-sm text-[#40657F] mt-2">{{ form.term.help_text }}</p>
            {% endif %}
            {% if form.term.errors %}
            <div class="text-[#F28C8C] text-sm mt-2">
              {% for error in form.term.errors %}
              <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
              {% endfor %}
            </div>
            {% endif %}
          </div>

          <!-- Class Assignment (Optional) -->
          <div class="form-group">
            <label
              for="{{ form.class_assigned.id_for_label }}"
              class="block text-sm font-bold text-[#2C3E50] mb-2"
            >
              <i class="fas fa-users text-[#F28C8C] mr-2"></i>
              {{ form.class_assigned.label }}
              <span class="text-[#40657F] text-xs font-normal">(Optional)</span>
            </label>
            {{ form.class_assigned }}
            {% if form.class_assigned.help_text %}
            <p class="text-sm text-[#40657F] mt-2">{{ form.class_assigned.help_text }}</p>
            {% endif %}
            {% if form.class_assigned.errors %}
            <div class="text-[#F28C8C] text-sm mt-2">
              {% for error in form.class_assigned.errors %}
              <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
              {% endfor %}
            </div>
            {% endif %}
          </div>
        </div>

        <!-- Activity Notes -->
        <div class="form-group">
          <label
            for="{{ form.notes.id_for_label }}"
            class="block text-sm font-bold text-[#2C3E50] mb-2"
          >
            <i class="fas fa-sticky-note text-[#74C69D] mr-2"></i>
            {{ form.notes.label }}
            <span class="text-[#40657F] text-xs font-normal">(Optional)</span>
          </label>
          {{ form.notes }}
          {% if form.notes.help_text %}
          <p class="text-sm text-[#40657F] mt-2">{{ form.notes.help_text }}</p>
          {% endif %}
          {% if form.notes.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.notes.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Activity Guidelines -->
      <div class="bg-gradient-to-r from-[#E2F1F9] to-[#F7FAFC] border border-[#B9D8EB] rounded-xl p-6 guidelines-fade-in">
        <div class="flex items-start gap-4">
          <div
            class="w-10 h-10 bg-[#F28C8C] rounded-full flex items-center justify-center flex-shrink-0"
          >
            <i class="fas fa-lightbulb text-white"></i>
          </div>
          <div>
            <h3 class="font-bold text-[#2C3E50] mb-3">Activity Planning Guidelines</h3>
            <ul class="space-y-2 text-sm text-[#40657F]">
              <li class="flex items-start gap-2">
                <i class="fas fa-check text-[#74C69D] mt-1 flex-shrink-0"></i>
                <span><strong>Activity Type:</strong> Choose from existing types or create new ones as needed</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-check text-[#74C69D] mt-1 flex-shrink-0"></i>
                <span><strong>Date:</strong> Must be within the selected academic term dates</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-check text-[#74C69D] mt-1 flex-shrink-0"></i>
                <span><strong>Subject:</strong> Link to specific subject if activity is subject-specific</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-check text-[#74C69D] mt-1 flex-shrink-0"></i>
                <span><strong>Class:</strong> Assign to specific class level or leave blank for school-wide activities</span>
              </li>
              <li class="flex items-start gap-2">
                <i class="fas fa-info-circle text-[#7AB2D3] mt-1 flex-shrink-0"></i>
                <span><strong>Notes:</strong> Add instructions, requirements, or additional details</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-[#B9D8EB] buttons-slide-in">
        <button
          type="submit"
          class="flex-1 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] text-white font-bold py-4 px-8 rounded-xl hover:from-[#e74c3c] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-save mr-3 group-hover:scale-110 transition-transform duration-300"
          ></i>
          {{ submit_text }}
        </button>
        <a
          href="{% url 'academics:academic_management_dashboard' %}"
          class="flex-1 bg-[#B9D8EB] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:bg-[#E2F1F9] focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 text-center group"
        >
          <i
            class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-300"
          ></i>
          Cancel
        </a>
      </div>
    </form>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.8s forwards;
  }

  .form-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: formSlideIn 0.8s ease-out 1s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 4s ease-in-out infinite;
  }

  .schedule-icon-float {
    animation: scheduleIconFloat 4s ease-in-out infinite;
  }

  .form-group {
    opacity: 0;
    transform: translateX(-20px);
    animation: formGroupSlideIn 0.4s ease-out forwards;
  }

  .form-group:nth-child(2) { animation-delay: 1.2s; }
  .form-group:nth-child(3) { animation-delay: 1.3s; }
  .form-group:nth-child(4) { animation-delay: 1.4s; }
  .form-group:nth-child(5) { animation-delay: 1.5s; }
  .form-group:nth-child(6) { animation-delay: 1.6s; }
  .form-group:nth-child(7) { animation-delay: 1.7s; }

  .guidelines-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: guidelinesFadeIn 0.8s ease-out 1.8s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(20px);
    animation: buttonsSlideIn 0.8s ease-out 2s forwards;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes breadcrumbFadeIn {
    to { opacity: 1; }
  }

  @keyframes formSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes sectionIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(3deg); }
  }

  @keyframes scheduleIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes formGroupSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes guidelinesFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes buttonsSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }
</style>

{% endblock %}
