{% extends 'academics/base.html' %} {% load humanize %}
<!--  -->
{% block title %}{{ activity_type.name }} Details | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-tag text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            {{ activity_type.name }}
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Activity type details and related activities
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#40657F] to-[#2C3E50] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 action-buttons-slide-in">
        <a
          href="{% url 'academics:edit_activity_type' activity_type.pk %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-edit group-hover:scale-110 transition-all duration-300"
          ></i>
          <span>Edit Activity Type</span>
        </a>
        <a
          href="{% url 'academics:add_activity' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] text-white font-bold py-4 px-8 rounded-xl hover:from-[#e74c3c] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-plus group-hover:rotate-90 transition-all duration-300"
          ></i>
          <span>Add Activity</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Navigation Breadcrumb -->
  <div class="breadcrumb-fade-in">
    <nav class="flex items-center gap-2 text-sm text-[#40657F]">
      <a
        href="{% url 'academics:academic_management_dashboard' %}"
        class="hover:text-[#40657F] transition-colors duration-200"
      >
        <i class="fas fa-graduation-cap mr-1"></i>
        Academic Management
      </a>
      <i class="fas fa-chevron-right text-[#B9D8EB]"></i>
      <a
        href="{% url 'academics:activity_type_management' %}"
        class="hover:text-[#40657F] transition-colors duration-200"
      >
        Activity Types
      </a>
      <i class="fas fa-chevron-right text-[#B9D8EB]"></i>
      <span class="text-[#2C3E50] font-medium">{{ activity_type.name }}</span>
    </nav>
  </div>

  <!-- Activity Type Information -->
  <div class="card-modern p-8 info-section-fade-in">
    <div class="flex items-center gap-4 mb-6">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg section-icon-float"
      >
        <i class="fas fa-info-circle text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Activity Type Information
        </h2>
        <p class="text-[#40657F] text-sm">
          Details about this activity type
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Basic Information -->
      <div class="space-y-4">
        <div class="info-item">
          <label class="block text-sm font-bold text-[#2C3E50] mb-1">
            <i class="fas fa-tag text-[#40657F] mr-2"></i>
            Activity Type Name
          </label>
          <p class="text-[#40657F] text-lg font-medium">{{ activity_type.name }}</p>
        </div>

        <div class="info-item">
          <label class="block text-sm font-bold text-[#2C3E50] mb-1">
            <i class="fas fa-calendar-alt text-[#74C69D] mr-2"></i>
            Total Activities
          </label>
          <p class="text-[#40657F] text-lg font-medium">
            {{ activity_count }} activit{{ activity_count|pluralize:"y,ies" }}
          </p>
        </div>
      </div>

      <!-- Description -->
      <div class="space-y-4">
        <div class="info-item">
          <label class="block text-sm font-bold text-[#2C3E50] mb-1">
            <i class="fas fa-align-left text-[#F28C8C] mr-2"></i>
            Description
          </label>
          {% if activity_type.description %}
          <p class="text-[#40657F] leading-relaxed">{{ activity_type.description }}</p>
          {% else %}
          <p class="text-[#B9D8EB] italic">No description provided</p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Related Activities -->
  <div class="card-modern p-8 activities-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e74c3c] rounded-xl flex items-center justify-center shadow-lg activities-icon-float"
      >
        <i class="fas fa-calendar-alt text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Related Activities
        </h3>
        <p class="text-[#40657F] text-sm">
          Activities using this activity type
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div class="flex items-center gap-2">
        <div
          class="flex items-center gap-2 bg-[#F28C8C]/20 text-[#F28C8C] px-4 py-2 rounded-full font-bold border border-[#F28C8C]/30"
        >
          <i class="fas fa-calendar-alt text-sm"></i>
          <span>{{ activity_count }} activit{{ activity_count|pluralize:"y,ies" }}</span>
        </div>
      </div>
    </div>

    <!-- Activities List -->
    {% if activities %}
    <div class="space-y-4">
      {% for activity in activities %}
      <div
        class="activity-card bg-gradient-to-r from-white to-[#F7FAFC] border border-[#B9D8EB] hover:border-[#F28C8C] hover:shadow-lg transition-all duration-300 p-6 rounded-xl group"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <div class="w-12 h-12 bg-[#F28C8C] rounded-xl flex items-center justify-center">
              <i class="fas fa-calendar-day text-white"></i>
            </div>
            <div>
              <h4 class="font-bold text-[#2C3E50] text-lg">
                {{ activity.activity_type.name }}
              </h4>
              <div class="flex items-center gap-4 text-sm text-[#40657F]">
                <span>
                  <i class="fas fa-calendar mr-1"></i>
                  {{ activity.date }}
                </span>
                {% if activity.subject %}
                <span>
                  <i class="fas fa-book mr-1"></i>
                  {{ activity.subject.name }}
                </span>
                {% endif %}
                {% if activity.class_assigned %}
                <span>
                  <i class="fas fa-users mr-1"></i>
                  {{ activity.class_assigned.level_name }}
                </span>
                {% endif %}
                <span>
                  <i class="fas fa-clock mr-1"></i>
                  {{ activity.term.name }}
                </span>
              </div>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <a
              href="{% url 'academics:activity_details' activity.pk %}"
              class="bg-[#7AB2D3] text-white px-3 py-2 rounded-lg hover:bg-[#40657F] transition-colors duration-200 text-sm"
              title="View Details"
            >
              <i class="fas fa-eye"></i>
            </a>
            <a
              href="{% url 'academics:edit_activity' activity.pk %}"
              class="bg-[#F28C8C] text-white px-3 py-2 rounded-lg hover:bg-[#e74c3c] transition-colors duration-200 text-sm"
              title="Edit"
            >
              <i class="fas fa-edit"></i>
            </a>
          </div>
        </div>
        {% if activity.notes %}
        <div class="mt-4 pt-4 border-t border-[#B9D8EB]/30">
          <p class="text-sm text-[#40657F]">{{ activity.notes }}</p>
        </div>
        {% endif %}
      </div>
      {% endfor %}

      {% if activity_count > 10 %}
      <div class="text-center pt-4">
        <p class="text-[#40657F] text-sm">
          Showing 10 of {{ activity_count }} activities.
          <a
            href="{% url 'academics:academic_management_dashboard' %}?activity_search={{ activity_type.name }}"
            class="text-[#7AB2D3] hover:text-[#40657F] font-medium"
          >
            View all activities
          </a>
        </p>
      </div>
      {% endif %}
    </div>
    {% else %}
    <div class="text-center py-12">
      <div class="flex flex-col items-center gap-4">
        <div
          class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
        >
          <i class="fas fa-calendar-alt text-[#B9D8EB] text-2xl"></i>
        </div>
        <div>
          <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
            No Activities Found
          </h3>
          <p class="text-[#40657F]">
            No activities have been created using this activity type yet.
          </p>
        </div>
        <a
          href="{% url 'academics:add_activity' %}"
          class="inline-flex items-center gap-2 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] text-white font-bold py-3 px-6 rounded-xl hover:from-[#e74c3c] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-plus"></i>
          <span>Create First Activity</span>
        </a>
      </div>
    </div>
    {% endif %}
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 1s forwards;
  }

  /* Info Section Animation */
  .info-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: infoSectionFadeIn 0.8s ease-out 1.2s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 4s ease-in-out infinite;
  }

  .info-item {
    opacity: 0;
    transform: translateX(-20px);
    animation: infoItemSlideIn 0.4s ease-out forwards;
  }

  .info-item:nth-child(1) { animation-delay: 1.4s; }
  .info-item:nth-child(2) { animation-delay: 1.5s; }
  .info-item:nth-child(3) { animation-delay: 1.6s; }

  /* Activities Section Animation */
  .activities-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: activitiesSectionFadeIn 0.8s ease-out 1.8s forwards;
  }

  .activities-icon-float {
    animation: activitiesIconFloat 4s ease-in-out infinite;
  }

  .activity-card {
    opacity: 0;
    transform: translateX(-20px);
    animation: activityCardSlideIn 0.4s ease-out forwards;
  }

  .activity-card:nth-child(1) { animation-delay: 2s; }
  .activity-card:nth-child(2) { animation-delay: 2.1s; }
  .activity-card:nth-child(3) { animation-delay: 2.2s; }
  .activity-card:nth-child(4) { animation-delay: 2.3s; }
  .activity-card:nth-child(5) { animation-delay: 2.4s; }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes actionButtonsSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes breadcrumbFadeIn {
    to { opacity: 1; }
  }

  @keyframes infoSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes sectionIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(3deg); }
  }

  @keyframes infoItemSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes activitiesSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes activitiesIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes activityCardSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }
</style>

{% endblock %}
