"""
Views for managing student progression to next class levels
"""

from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

from students.models import Student, AcademicYear, Level
from students.utils.progression import (
    progress_students_and_setup_new_year,
    get_next_level,
    progress_student_to_next_level
)


def is_admin_or_staff(user):
    """Check if user is admin or staff"""
    return user.is_authenticated and (user.is_staff or user.is_superuser)


@login_required(login_url="accounts:login")
@user_passes_test(is_admin_or_staff)
def student_progression(request):
    """Display student progression management page"""
    
    # Get all academic years
    academic_years = AcademicYear.objects.all().order_by('-id')
    
    # Get current active academic year
    current_year = AcademicYear.objects.filter(is_active=True).first()
    
    # Get all active students with their current levels
    students = Student.objects.filter(is_active=True).select_related('level').order_by('level__level_name', 'name')

    
    # Group students by level
    students_by_level = {}
    progression_preview = []
    passed_students = 0
    failed_students = 0
    
    for student in students:
        level_name = student.level.level_name
        if level_name not in students_by_level:
            students_by_level[level_name] = []
        students_by_level[level_name].append(student)

        if student.has_passed:
            passed_students += 1
        else:
            failed_students += 1
        
        # Get next level for preview
        next_level = get_next_level(student.level)
        progression_preview.append({
            'student': student,
            'current_level': student.level.level_name,
            'next_level': next_level.level_name if next_level else 'Graduated',
            'will_graduate': next_level is None
        })
    
    context = {
        'academic_years': academic_years,
        'current_year': current_year,
        'students_by_level': students_by_level,
        'progression_preview': progression_preview,
        'total_students': students.count(),
        'total_levels': len(students_by_level),
        'passed_students': passed_students,
        'failed_students': failed_students
    }
    
    return render(request, 'students/progression/student_progression.html', context)


@login_required(login_url="accounts:login")
@user_passes_test(is_admin_or_staff)
@require_http_methods(["POST"])
def progress_students_view(request):
    """Handle student progression to new academic year"""
    
    try:
        data = json.loads(request.body)
        academic_year_name = data.get('academic_year')
        
        if not academic_year_name:
            return JsonResponse({
                'success': False,
                'message': 'Academic year name is required'
            })
        
        # Get or create the academic year
        academic_year, created = AcademicYear.objects.get_or_create(
            name=academic_year_name,
            defaults={'is_active': False}
        )
        
        if created:
            messages.success(request, f'Created new academic year: {academic_year_name}')
        
        # Perform the progression
        results = progress_students_and_setup_new_year(academic_year, request.user)
        
        # Prepare response
        summary = results['summary']
        
        response_data = {
            'success': True,
            'message': f'Student progression completed for {academic_year_name}',
            'results': {
                'academic_year': academic_year_name,
                'total_students': summary['total_students'],
                'progressed': summary['progressed'],
                'repeated': summary['repeated'],
                'graduated': summary['graduated'],
                'progression_errors': summary['progression_errors'],
                'fee_accounts_created': summary['fee_accounts_created'],
                'fee_account_errors': summary['fee_account_errors']
            },
            'details': results['progression']['details'][:10]  # Limit details for response size
        }
        
        # Add success message
        messages.success(
            request,
            f'Successfully progressed {summary["progressed"]} students, '
            f'{summary["repeated"]} students will repeat, '
            f'{summary["graduated"]} students graduated.'
        )
        
        if summary['progression_errors'] > 0:
            messages.warning(
                request,
                f'{summary["progression_errors"]} errors occurred during progression.'
            )
        
        return JsonResponse(response_data)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid JSON data'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error during progression: {str(e)}'
        })


@login_required(login_url="accounts:login")
@user_passes_test(is_admin_or_staff)
def progress_single_student(request, student_id):
    """Progress a single student to the next level"""
    
    if request.method != 'POST':
        return JsonResponse({
            'success': False,
            'message': 'Only POST method allowed'
        })
    
    try:
        student = Student.objects.get(student_id=student_id)
        
        # Get the target academic year from request
        data = json.loads(request.body)
        academic_year_name = data.get('academic_year')
        
        if not academic_year_name:
            return JsonResponse({
                'success': False,
                'message': 'Academic year is required'
            })
        
        academic_year = AcademicYear.objects.get(name=academic_year_name)
        
        # Progress the student
        result = progress_student_to_next_level(student, academic_year, request.user)
        
        if result['status'] in ['progressed', 'graduated', 'repeat']:
            return JsonResponse({
                'success': True,
                'message': result['message'],
                'status': result['status'],
                'old_level': result['old_level'],
                'new_level': result['new_level']
            })
        else:
            return JsonResponse({
                'success': False,
                'message': result['message']
            })
            
    except Student.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Student not found'
        })
    except AcademicYear.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Academic year not found'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error: {str(e)}'
        })
