{% extends 'base.html' %}
{% load static %}
{% load rbac_tags %}
<!-- title -->
{% block title %}System Discrepancy Check | {% endblock %}
<!-- title -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#F28C8C] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-search text-white text-lg sm:text-xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            System Discrepancy Check
          </h1>
          <p
            class="text-[#40657F] text-base sm:text-lg font-medium mt-1 subtitle-fade-in"
          >
            Analyze financial data integrity and identify inconsistencies
          </p>
          <div
            class="w-16 sm:w-20 h-1 bg-[#F28C8C] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3 action-buttons-slide-in">
        <a
          href="{% url 'core:administration' %}"
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#7AB2D3] hover:text-white focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-arrow-left mr-2 group-hover:scale-110 transition-transform duration-300"
          ></i>
          Back to Administration
        </a>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200 breadcrumb-fade-in"
    >
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a href="{% url 'core:administration' %}" class="text-[#40657F] hover:text-[#7AB2D3]">Administration</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#F28C8C] font-semibold">Discrepancy Check</span>
    </nav>
  </div>

  <!-- Info Section -->
  <div
    class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]/30 border-2 border-[#7AB2D3]/30 rounded-2xl p-8 flex items-center space-x-6 shadow-lg backdrop-blur-sm info-section-fade-in"
  >
    <div
      class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-full flex items-center justify-center shadow-lg"
    >
      <i class="fas fa-info-circle text-white text-lg"></i>
    </div>
    <div class="flex-1">
      <div class="flex items-center gap-2 mb-2">
        <i class="fas fa-chart-line text-[#7AB2D3]"></i>
        <p class="text-[#7AB2D3] font-bold text-xl">System Diagnostic Tool</p>
      </div>
      <p class="text-[#40657F] text-base font-medium leading-relaxed">
        This tool analyzes your financial data to identify inconsistencies between ledgers, receipts, and account calculations. 
        It helps ensure data integrity across the entire system.
      </p>
      <div class="flex items-center gap-2 mt-3">
        <i class="fas fa-clock text-[#40657F] text-sm"></i>
        <p class="text-[#40657F] text-sm font-medium">
          Analysis typically takes 30-60 seconds depending on data volume.
        </p>
      </div>
    </div>
  </div>

  <!-- Diagnostic Options -->
  <div class="diagnostic-options-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e67373] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-cogs text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Diagnostic Options
        </h2>
        <p class="text-[#40657F] text-sm">
          Choose the type of analysis to perform
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Comprehensive Check -->
      <div
        class="diagnostic-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#F28C8C]/5 to-[#e67373]/5"
      >
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e67373] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-search-plus text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display group-hover:text-[#F28C8C] transition-colors duration-300">
              Comprehensive Analysis
            </h3>
            <p class="text-[#40657F] text-sm">Full system check</p>
          </div>
        </div>
        
        <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
          Analyzes all financial data across the entire system to identify any inconsistencies between ledgers, receipts, and calculations.
        </p>

        <button
          onclick="runDiscrepancyCheck('comprehensive')"
          class="bg-[#F28C8C] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#e67373] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
        >
          <i class="fas fa-play mr-2 group-hover/btn:scale-110 transition-transform duration-300"></i>
          Run Full Analysis
        </button>
      </div>

      <!-- Category Specific Check -->
      <div
        class="diagnostic-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5"
      >
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-filter text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display group-hover:text-[#7AB2D3] transition-colors duration-300">
              Category Analysis
            </h3>
            <p class="text-[#40657F] text-sm">Focus on specific category</p>
          </div>
        </div>
        
        <p class="text-[#40657F] text-sm mb-4 leading-relaxed">
          Analyze a specific fee category for targeted troubleshooting.
        </p>

        <div class="mb-6">
          <input
            type="text"
            id="categoryInput"
            placeholder="Enter category name (e.g., Primary Tuition)"
            class="w-full px-4 py-3 border border-[#B9D8EB] text-[#2C3E50] rounded-xl focus:ring-4 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] transition-all duration-300 text-sm"
          >
        </div>

        <button
          onclick="runDiscrepancyCheck('category')"
          class="bg-[#7AB2D3] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
        >
          <i class="fas fa-search mr-2 group-hover/btn:scale-110 transition-transform duration-300"></i>
          Analyze Category
        </button>
      </div>

      <!-- Dynamic Amount Analysis -->
      <div
        class="diagnostic-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <div class="flex items-center gap-4 mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-calculator text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display group-hover:text-[#74C69D] transition-colors duration-300">
              Dynamic Amount Analysis
            </h3>
            <p class="text-[#40657F] text-sm">Payment distribution debugging</p>
          </div>
        </div>

        <div class="relative z-10">
          <p class="text-[#40657F] text-sm mb-4 leading-relaxed">
            Debug dynamic amount calculations and payment distribution for specific fee categories.
          </p>

          <div class="mb-6">
            <select
              id="dynamicCategorySelect"
              class="w-full px-4 py-3 border border-[#B9D8EB] text-[#2C3E50] rounded-xl focus:ring-4 focus:ring-[#74C69D]/30 focus:border-[#74C69D] transition-all duration-300 text-sm bg-white"
            >
              <option value="" class="text-[#2C3E50]">Select fee category...</option>
              <!-- Categories will be populated via JavaScript -->
            </select>
          </div>

          <button
            onclick="runDiscrepancyCheck('dynamic_amount')"
            class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
          >
            <i class="fas fa-calculator mr-2 group-hover/btn:scale-110 transition-transform duration-300"></i>
            Debug Dynamic Amount
          </button>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#74C69D]/5 to-[#5fb085]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>
    </div>
  </div>

  <!-- Results Section -->
  <div id="resultsSection" class="hidden">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-chart-bar text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Analysis Results
        </h2>
        <p class="text-[#40657F] text-sm">
          System diagnostic output
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div class="bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8">
      <div id="loadingIndicator" class="text-center py-8 hidden">
        <div class="inline-flex items-center gap-3">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F28C8C]"></div>
          <span class="text-[#40657F] font-medium">Running analysis...</span>
        </div>
      </div>
      
      <div id="resultsOutput" class="hidden">
        <pre class="bg-[#F7FAFC] border border-[#B9D8EB]/30 rounded-xl p-6 text-sm font-mono overflow-x-auto whitespace-pre-wrap text-[#2C3E50]"></pre>
      </div>
      
      <div id="errorOutput" class="hidden">
        <div class="bg-gradient-to-r from-[#FEF2F2] to-[#F28C8C]/10 border-2 border-[#F28C8C]/30 rounded-xl p-6">
          <div class="flex items-center gap-3 mb-4">
            <i class="fas fa-exclamation-triangle text-[#F28C8C] text-lg"></i>
            <h3 class="text-[#F28C8C] font-bold text-lg">Analysis Error</h3>
          </div>
          <pre class="text-[#40657F] text-sm font-mono whitespace-pre-wrap"></pre>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
function runDiscrepancyCheck(checkType) {
    const resultsSection = document.getElementById('resultsSection');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const resultsOutput = document.getElementById('resultsOutput');
    const errorOutput = document.getElementById('errorOutput');
    
    // Show results section and loading indicator
    resultsSection.classList.remove('hidden');
    loadingIndicator.classList.remove('hidden');
    resultsOutput.classList.add('hidden');
    errorOutput.classList.add('hidden');
    
    // Scroll to results section
    resultsSection.scrollIntoView({ behavior: 'smooth' });
    
    // Prepare form data
    const formData = new FormData();
    formData.append('check_type', checkType);
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
    
    if (checkType === 'category') {
        const category = document.getElementById('categoryInput').value.trim();
        if (!category) {
            showError('Please enter a category name.');
            return;
        }
        formData.append('category', category);
    } else if (checkType === 'dynamic_amount') {
        const category = document.getElementById('dynamicCategorySelect').value.trim();
        if (!category) {
            showError('Please select a fee category.');
            return;
        }
        formData.append('category', category);
    }
    
    // Make AJAX request
    fetch('{% url "core:discrepancy_check" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        loadingIndicator.classList.add('hidden');
        
        if (data.success) {
            resultsOutput.classList.remove('hidden');
            resultsOutput.querySelector('pre').textContent = data.output;
        } else {
            showError(data.error || 'An error occurred during analysis.');
        }
    })
    .catch(error => {
        loadingIndicator.classList.add('hidden');
        showError('Network error: ' + error.message);
    });
}

function showError(message) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    const errorOutput = document.getElementById('errorOutput');

    loadingIndicator.classList.add('hidden');
    errorOutput.classList.remove('hidden');
    errorOutput.querySelector('pre').textContent = message;
}

// Populate fee categories dropdown on page load
document.addEventListener('DOMContentLoaded', function() {
    populateFeeCategories();
});

function populateFeeCategories() {
    // Fetch fee categories from the server
    fetch('{% url "core:get_fee_categories" %}', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const select = document.getElementById('dynamicCategorySelect');
            // Clear existing options except the first one
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }

            // Add categories
            data.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                option.className = 'text-[#2C3E50]';
                select.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Error fetching fee categories:', error);
    });
}
</script>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  .info-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: infoSectionFadeIn 0.8s ease-out 1s forwards;
  }

  .diagnostic-options-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: diagnosticOptionsFadeIn 0.8s ease-out 1.2s forwards;
  }

  .diagnostic-card {
    opacity: 0;
    transform: translateY(30px);
    animation: diagnosticCardSlideIn 0.6s ease-out forwards;
  }

  .diagnostic-card:nth-child(1) {
    animation-delay: 1.4s;
  }
  .diagnostic-card:nth-child(2) {
    animation-delay: 1.5s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes infoSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes diagnosticOptionsFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes diagnosticCardSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>

{% endblock %}
