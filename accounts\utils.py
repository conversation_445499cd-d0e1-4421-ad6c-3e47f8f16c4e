from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils import timezone
from typing import List, Dict, Optional, Union
import logging

from .models.rbac import Role, Permission, UserRole

User = get_user_model()
logger = logging.getLogger(__name__)


class RBACManager:
    """
    Utility class for managing RBAC operations.
    Provides high-level methods for role and permission management.
    """
    
    @staticmethod
    def create_role(name: str, display_name: str, level: int, 
                   description: str = "", permissions: List[str] = None,
                   is_default: bool = False) -> Role:
        """
        Create a new role with specified permissions.
        
        Args:
            name: Unique role name
            display_name: Human-readable role name
            level: Role hierarchy level
            description: Role description
            permissions: List of permission names to assign
            is_default: Whether this is a default role
        
        Returns:
            Created Role instance
        """
        try:
            with transaction.atomic():
                role = Role.objects.create(
                    name=name,
                    display_name=display_name,
                    level=level,
                    description=description,
                    is_default=is_default
                )
                
                if permissions:
                    permission_objects = Permission.objects.filter(
                        name__in=permissions,
                        is_active=True
                    )
                    role.permissions.set(permission_objects)
                
                logger.info(f"Created role: {role.name} with {len(permissions or [])} permissions")
                return role
                
        except Exception as e:
            logger.error(f"Error creating role {name}: {str(e)}")
            raise

    @staticmethod
    def create_permission(name: str, display_name: str, category: str,
                         description: str = "") -> Permission:
        """
        Create a new permission.
        
        Args:
            name: Unique permission name
            display_name: Human-readable permission name
            category: Permission category
            description: Permission description
        
        Returns:
            Created Permission instance
        """
        try:
            permission = Permission.objects.create(
                name=name,
                display_name=display_name,
                category=category,
                description=description
            )
            
            logger.info(f"Created permission: {permission.name}")
            return permission
            
        except Exception as e:
            logger.error(f"Error creating permission {name}: {str(e)}")
            raise

    @staticmethod
    def assign_role_to_user(user: User, role: Union[str, Role], 
                           assigned_by: User = None, expires_at = None,
                           notes: str = "") -> UserRole:
        """
        Assign a role to a user.
        
        Args:
            user: User to assign role to
            role: Role name or Role instance
            assigned_by: User who is making the assignment
            expires_at: When the role assignment expires
            notes: Additional notes
        
        Returns:
            UserRole instance
        """
        try:
            if isinstance(role, str):
                role = Role.objects.get(name=role, is_active=True)
            
            user_role = user.assign_role(
                role=role,
                assigned_by=assigned_by,
                expires_at=expires_at,
                notes=notes
            )
            
            logger.info(f"Assigned role {role.name} to user {user.username}")
            return user_role
            
        except Exception as e:
            logger.error(f"Error assigning role {role} to user {user.username}: {str(e)}")
            raise

    @staticmethod
    def remove_role_from_user(user: User, role: Union[str, Role]) -> bool:
        """
        Remove a role from a user.
        
        Args:
            user: User to remove role from
            role: Role name or Role instance
        
        Returns:
            True if role was removed, False if user didn't have the role
        """
        try:
            if isinstance(role, str):
                role = Role.objects.get(name=role)
            
            user.remove_role(role)
            logger.info(f"Removed role {role.name} from user {user.username}")
            return True
            
        except Role.DoesNotExist:
            logger.warning(f"Role {role} does not exist")
            return False
        except Exception as e:
            logger.error(f"Error removing role {role} from user {user.username}: {str(e)}")
            raise

    @staticmethod
    def get_users_with_role(role: Union[str, Role]) -> List[User]:
        """
        Get all users with a specific role.
        
        Args:
            role: Role name or Role instance
        
        Returns:
            List of users with the role
        """
        try:
            if isinstance(role, str):
                role = Role.objects.get(name=role, is_active=True)
            
            user_roles = UserRole.objects.filter(
                role=role,
                is_active=True
            ).select_related('user')
            
            return [ur.user for ur in user_roles if not ur.is_expired()]
            
        except Role.DoesNotExist:
            logger.warning(f"Role {role} does not exist")
            return []

    @staticmethod
    def get_users_with_permission(permission: Union[str, Permission]) -> List[User]:
        """
        Get all users with a specific permission.
        
        Args:
            permission: Permission name or Permission instance
        
        Returns:
            List of users with the permission
        """
        try:
            if isinstance(permission, str):
                permission = Permission.objects.get(name=permission, is_active=True)
            
            # Get all roles that have this permission
            roles = Role.objects.filter(
                permissions=permission,
                is_active=True
            )
            
            users = set()
            for role in roles:
                users.update(RBACManager.get_users_with_role(role))
            
            return list(users)
            
        except Permission.DoesNotExist:
            logger.warning(f"Permission {permission} does not exist")
            return []

    @staticmethod
    def bulk_assign_roles(user_role_assignments: List[Dict]) -> List[UserRole]:
        """
        Bulk assign roles to multiple users.
        
        Args:
            user_role_assignments: List of dicts with 'user', 'role', and optional fields
        
        Returns:
            List of created UserRole instances
        """
        created_assignments = []
        
        try:
            with transaction.atomic():
                for assignment in user_role_assignments:
                    user_role = RBACManager.assign_role_to_user(**assignment)
                    created_assignments.append(user_role)
                
                logger.info(f"Bulk assigned {len(created_assignments)} roles")
                return created_assignments
                
        except Exception as e:
            logger.error(f"Error in bulk role assignment: {str(e)}")
            raise

    @staticmethod
    def cleanup_expired_roles():
        """
        Clean up expired role assignments.
        Sets expired assignments to inactive.
        """
        try:
            now = timezone.now()
            expired_assignments = UserRole.objects.filter(
                expires_at__lt=now,
                is_active=True
            )
            
            count = expired_assignments.update(is_active=False)
            logger.info(f"Cleaned up {count} expired role assignments")
            return count
            
        except Exception as e:
            logger.error(f"Error cleaning up expired roles: {str(e)}")
            raise

    @staticmethod
    def get_role_hierarchy() -> Dict:
        """
        Get the role hierarchy structure.
        
        Returns:
            Dictionary representing role hierarchy
        """
        roles = Role.objects.filter(is_active=True).order_by('level')
        hierarchy = {}
        
        for role in roles:
            hierarchy[role.level] = {
                'role': role,
                'users_count': RBACManager.get_users_with_role(role).count(),
                'permissions_count': role.permissions.filter(is_active=True).count()
            }
        
        return hierarchy

    @staticmethod
    def audit_user_permissions(user: User) -> Dict:
        """
        Generate an audit report of user's permissions.
        
        Args:
            user: User to audit
        
        Returns:
            Dictionary with audit information
        """
        audit_data = {
            'user': user,
            'roles': [],
            'permissions': [],
            'role_level': user.get_highest_role_level(),
            'is_superuser': user.is_superuser,
            'audit_timestamp': timezone.now()
        }
        
        # Get active roles
        for user_role in user.get_active_roles():
            role_data = {
                'role': user_role.role,
                'assigned_at': user_role.assigned_at,
                'assigned_by': user_role.assigned_by,
                'expires_at': user_role.expires_at,
                'is_expired': user_role.is_expired(),
                'notes': user_role.notes
            }
            audit_data['roles'].append(role_data)
        
        # Get all permissions
        permissions = user.get_all_permissions()
        audit_data['permissions'] = [
            {
                'permission': perm,
                'category': perm.category
            }
            for perm in permissions
        ]
        
        return audit_data


class PermissionChecker:
    """
    Utility class for checking permissions in templates and views.
    """
    
    @staticmethod
    def user_can(user: User, permission: str) -> bool:
        """
        Check if user has a specific permission.
        
        Args:
            user: User to check
            permission: Permission name
        
        Returns:
            True if user has permission
        """
        if not user.is_authenticated:
            return False
        
        return user.has_permission(permission)

    @staticmethod
    def user_has_role(user: User, role: str) -> bool:
        """
        Check if user has a specific role.
        
        Args:
            user: User to check
            role: Role name
        
        Returns:
            True if user has role
        """
        if not user.is_authenticated:
            return False
        
        return user.has_role(role)

    @staticmethod
    def user_level_at_least(user: User, level: int) -> bool:
        """
        Check if user has at least the specified role level.
        
        Args:
            user: User to check
            level: Minimum role level
        
        Returns:
            True if user has sufficient level
        """
        if not user.is_authenticated:
            return False
        
        return user.get_highest_role_level() >= level


def setup_default_permissions():
    """
    Create default permissions for the school management system.
    """
    default_permissions = [
        # Student permissions
        ('view_students', 'View Students', 'students', 'Can view student information'),
        ('add_student', 'Add Student', 'students', 'Can add new students'),
        ('edit_student', 'Edit Student', 'students', 'Can edit student information'),
        ('delete_student', 'Delete Student', 'students', 'Can delete students'),
        ('manage_students', 'Manage Students', 'students', 'Full student management access'),
        
        # Finance permissions
        ('view_finances', 'View Finances', 'finances', 'Can view financial information'),
        ('add_fee', 'Add Fee', 'finances', 'Can add new fees'),
        ('edit_fee', 'Edit Fee', 'finances', 'Can edit fees'),
        ('delete_fee', 'Delete Fee', 'finances', 'Can delete fees'),
        ('manage_finances', 'Manage Finances', 'finances', 'Full financial management access'),
        ('manage_fee_collection', 'Manage Fee Collection', 'finances', 'Can manage fee collection'),
        ('view_financial_reports', 'View Financial Reports', 'finances', 'Can view financial reports'),
        
        # Academic permissions
        ('view_academics', 'View Academics', 'academics', 'Can view academic information'),
        ('add_subject', 'Add Subject', 'academics', 'Can add new subjects'),
        ('edit_subject', 'Edit Subject', 'academics', 'Can edit subjects'),
        ('manage_academics', 'Manage Academics', 'academics', 'Full academic management access'),
        ('manage_assessments', 'Manage Assessments', 'academics', 'Can manage assessments'),
        ('view_grades', 'View Grades', 'academics', 'Can view grades'),
        ('edit_grades', 'Edit Grades', 'academics', 'Can edit grades'),
        
        # Teacher permissions
        ('view_teachers', 'View Teachers', 'teachers', 'Can view teacher information'),
        ('add_teacher', 'Add Teacher', 'teachers', 'Can add new teachers'),
        ('edit_teacher', 'Edit Teacher', 'teachers', 'Can edit teacher information'),
        ('view_teacher_details', 'View Teacher Details', 'teachers', 'Can view detailed teacher information'),
        ('manage_teacher_assignments', 'Manage Teacher Assignments', 'teachers', 'Can manage teacher assignments'),
        
        # System permissions
        ('manage_system', 'Manage System', 'system', 'Can manage system operations'),
        ('manage_academic_periods', 'Manage Academic Periods', 'system', 'Can manage academic periods'),
        ('view_reports', 'View Reports', 'reports', 'Can view reports'),
        ('view_academic_reports', 'View Academic Reports', 'reports', 'Can view academic reports'),
    ]
    
    created_count = 0
    for name, display_name, category, description in default_permissions:
        permission, created = Permission.objects.get_or_create(
            name=name,
            defaults={
                'display_name': display_name,
                'category': category,
                'description': description
            }
        )
        if created:
            created_count += 1
    
    logger.info(f"Created {created_count} default permissions")
    return created_count


def setup_default_roles():
    """
    Create default roles for the school management system.
    """
    # First ensure permissions exist
    setup_default_permissions()
    
    default_roles = [
        {
            'name': 'student',
            'display_name': 'Student',
            'level': 1,
            'description': 'Basic student access',
            'permissions': ['view_academics'],
            'is_default': True
        },
        {
            'name': 'teacher',
            'display_name': 'Teacher',
            'level': 2,
            'description': 'Teacher access with academic management',
            'permissions': [
                'view_academics', 'manage_assessments', 'view_grades', 'edit_grades',
                'view_students', 'view_teachers'
            ],
            'is_default': True
        },
        {
            'name': 'academic_coordinator',
            'display_name': 'Academic Coordinator',
            'level': 3,
            'description': 'Academic coordination and management',
            'permissions': [
                'view_academics', 'manage_academics', 'manage_assessments',
                'view_grades', 'edit_grades', 'view_students', 'edit_student',
                'view_teachers', 'manage_teacher_assignments', 'view_academic_reports'
            ]
        },
        {
            'name': 'finance_manager',
            'display_name': 'Finance Manager',
            'level': 4,
            'description': 'Financial management and reporting',
            'permissions': [
                'view_finances', 'manage_finances', 'manage_fee_collection',
                'view_financial_reports', 'view_students', 'edit_student',
                'view_academics'  # Added to allow access to academic info for financial reporting
            ]
        },
        {
            'name': 'administrator',
            'display_name': 'Administrator',
            'level': 5,
            'description': 'Full system administration',
            'permissions': [
                'view_students', 'add_student', 'edit_student', 'delete_student', 'manage_students',
                'view_finances', 'add_finance', 'edit_finance', 'delete_finance', 'manage_finances',
                'view_academics', 'add_subject', 'edit_subject', 'manage_academics', 'manage_assessments', 'view_grades', 'edit_grades',
                'view_teachers', 'add_teacher', 'edit_teacher', 'view_teacher_details',
                'manage_teacher_assignments', 'manage_academic_periods',
                'view_reports', 'view_financial_reports', 'view_academic_reports'
            ]
        },
        {
            'name': 'super_administrator',
            'display_name': 'Super Administrator',
            'level': 6,
            'description': 'Complete system access and management',
            'permissions': []  # Will get all permissions through inheritance
        }
    ]
    
    created_count = 0
    for role_data in default_roles:
        permissions = role_data.pop('permissions', [])
        
        role, created = Role.objects.get_or_create(
            name=role_data['name'],
            defaults=role_data
        )
        
        if created:
            created_count += 1
            
            # Assign permissions
            if permissions:
                permission_objects = Permission.objects.filter(
                    name__in=permissions,
                    is_active=True
                )
                role.permissions.set(permission_objects)
    
    logger.info(f"Created {created_count} default roles")
    return created_count
