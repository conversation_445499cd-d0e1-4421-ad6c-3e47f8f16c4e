{% extends 'academics/base.html' %}
<!--  -->
{% block title %}{{ title }} | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex items-center gap-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#F28C8C] to-[#e74c3c] rounded-2xl flex items-center justify-center shadow-xl icon-float"
      >
        <i class="fas fa-trash text-white text-2xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
        >
          {{ title }}
        </h1>
        <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
          Select activity types to delete permanently
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
  </div>

  <!-- Navigation Breadcrumb -->
  <div class="breadcrumb-fade-in">
    <nav class="flex items-center gap-2 text-sm text-[#40657F]">
      <a
        href="{% url 'academics:academic_management_dashboard' %}"
        class="hover:text-[#F28C8C] transition-colors duration-200"
      >
        <i class="fas fa-graduation-cap mr-1"></i>
        Academic Management
      </a>
      <i class="fas fa-chevron-right text-[#B9D8EB]"></i>
      <a
        href="{% url 'academics:activity_type_management' %}"
        class="hover:text-[#F28C8C] transition-colors duration-200"
      >
        Activity Types
      </a>
      <i class="fas fa-chevron-right text-[#B9D8EB]"></i>
      <span class="text-[#2C3E50] font-medium">{{ title }}</span>
    </nav>
  </div>

  <!-- Warning Section -->
  <div class="bg-gradient-to-r from-[#F28C8C]/10 to-[#e74c3c]/10 border-l-4 border-[#F28C8C] rounded-xl p-6 warning-fade-in">
    <div class="flex items-start gap-4">
      <div
        class="w-10 h-10 bg-[#F28C8C] rounded-full flex items-center justify-center flex-shrink-0"
      >
        <i class="fas fa-exclamation-triangle text-white"></i>
      </div>
      <div>
        <h3 class="font-bold text-[#2C3E50] mb-3">⚠️ Important Warning</h3>
        <ul class="space-y-2 text-sm text-[#40657F]">
          <li class="flex items-start gap-2">
            <i class="fas fa-times text-[#F28C8C] mt-1 flex-shrink-0"></i>
            <span><strong>Permanent Action:</strong> This action cannot be undone</span>
          </li>
          <li class="flex items-start gap-2">
            <i class="fas fa-times text-[#F28C8C] mt-1 flex-shrink-0"></i>
            <span><strong>Related Data:</strong> All activities using these types will also be deleted</span>
          </li>
          <li class="flex items-start gap-2">
            <i class="fas fa-times text-[#F28C8C] mt-1 flex-shrink-0"></i>
            <span><strong>Participation Records:</strong> All student participation records will be lost</span>
          </li>
          <li class="flex items-start gap-2">
            <i class="fas fa-info-circle text-[#7AB2D3] mt-1 flex-shrink-0"></i>
            <span><strong>Recommendation:</strong> Consider archiving instead of deleting if you might need the data later</span>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Bulk Delete Form -->
  <div class="card-modern p-8 form-slide-in">
    <form method="POST" class="space-y-8">
      {% csrf_token %}

      <!-- Activity Types Selection Section -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg section-icon-float"
          >
            <i class="fas fa-list text-white text-lg"></i>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
              Select Activity Types to Delete
            </h2>
            <p class="text-[#40657F] text-sm">
              Choose the activity types you want to permanently remove
            </p>
          </div>
          <div
            class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
          ></div>
        </div>

        <!-- Activity Types List -->
        <div class="form-group">
          <label class="block text-sm font-bold text-[#2C3E50] mb-4">
            <i class="fas fa-tags text-[#F28C8C] mr-2"></i>
            {{ form.activity_types.label }}
          </label>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto border border-[#B9D8EB] rounded-xl p-4">
            {% for choice in form.activity_types %}
            <div class="activity-type-item flex items-start gap-3 p-4 bg-gradient-to-r from-[#F7FAFC] to-[#E2F1F9] border border-[#B9D8EB] rounded-lg hover:border-[#F28C8C] transition-colors duration-200">
              {{ choice.tag }}
              <div class="flex-1">
                <label for="{{ choice.id_for_label }}" class="cursor-pointer">
                  <div class="font-medium text-[#2C3E50]">{{ choice.choice_label }}</div>
                  <div class="text-sm text-[#40657F] mt-1">
                    <i class="fas fa-calendar-alt mr-1"></i>
                    {{ choice.choice_value.activities.count }} activit{{ choice.choice_value.activities.count|pluralize:"y,ies" }}
                  </div>
                </label>
              </div>
            </div>
            {% endfor %}
          </div>
          
          {% if form.activity_types.help_text %}
          <p class="text-sm text-[#40657F] mt-2">{{ form.activity_types.help_text }}</p>
          {% endif %}
          {% if form.activity_types.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.activity_types.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Confirmation Section -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e74c3c] rounded-xl flex items-center justify-center shadow-lg confirm-icon-float"
          >
            <i class="fas fa-check-circle text-white text-lg"></i>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
              Confirm Deletion
            </h2>
            <p class="text-[#40657F] text-sm">
              Please confirm that you understand the consequences
            </p>
          </div>
          <div
            class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
          ></div>
        </div>

        <!-- Confirmation Checkbox -->
        <div class="form-group">
          <div class="flex items-start gap-3 p-6 bg-gradient-to-r from-[#F28C8C]/5 to-[#e74c3c]/5 border-2 border-[#F28C8C]/20 rounded-xl">
            {{ form.confirm_deletion }}
            <div class="flex-1">
              <label
                for="{{ form.confirm_deletion.id_for_label }}"
                class="text-sm font-bold text-[#2C3E50] cursor-pointer"
              >
                <i class="fas fa-exclamation-triangle text-[#F28C8C] mr-2"></i>
                {{ form.confirm_deletion.label }}
              </label>
              {% if form.confirm_deletion.help_text %}
              <p class="text-sm text-[#40657F] mt-2">{{ form.confirm_deletion.help_text }}</p>
              {% endif %}
            </div>
          </div>
          {% if form.confirm_deletion.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.confirm_deletion.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-[#B9D8EB] buttons-slide-in">
        <button
          type="submit"
          class="flex-1 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] text-white font-bold py-4 px-8 rounded-xl hover:from-[#e74c3c] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
          onclick="return confirm('Are you absolutely sure you want to delete the selected activity types? This action cannot be undone and will delete all related activities and participation records.')"
        >
          <i
            class="fas fa-trash mr-3 group-hover:scale-110 transition-transform duration-300"
          ></i>
          {{ submit_text }}
        </button>
        <a
          href="{% url 'academics:activity_type_management' %}"
          class="flex-1 bg-[#B9D8EB] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:bg-[#E2F1F9] focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 text-center group"
        >
          <i
            class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-300"
          ></i>
          Cancel
        </a>
      </div>
    </form>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.8s forwards;
  }

  .warning-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: warningFadeIn 0.8s ease-out 1s forwards;
  }

  .form-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: formSlideIn 0.8s ease-out 1.2s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 4s ease-in-out infinite;
  }

  .confirm-icon-float {
    animation: confirmIconFloat 4s ease-in-out infinite;
  }

  .form-group {
    opacity: 0;
    transform: translateX(-20px);
    animation: formGroupSlideIn 0.4s ease-out forwards;
  }

  .form-group:nth-child(2) { animation-delay: 1.4s; }
  .form-group:nth-child(3) { animation-delay: 1.6s; }

  .activity-type-item {
    opacity: 0;
    transform: translateY(10px);
    animation: activityTypeItemSlideIn 0.3s ease-out forwards;
  }

  .activity-type-item:nth-child(1) { animation-delay: 1.5s; }
  .activity-type-item:nth-child(2) { animation-delay: 1.6s; }
  .activity-type-item:nth-child(3) { animation-delay: 1.7s; }
  .activity-type-item:nth-child(4) { animation-delay: 1.8s; }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(20px);
    animation: buttonsSlideIn 0.8s ease-out 1.8s forwards;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes breadcrumbFadeIn {
    to { opacity: 1; }
  }

  @keyframes warningFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes formSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes sectionIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(3deg); }
  }

  @keyframes confirmIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes formGroupSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes activityTypeItemSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes buttonsSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }
</style>

{% endblock %}
