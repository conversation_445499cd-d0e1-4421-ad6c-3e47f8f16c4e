{% extends 'base.html' %} {% load static %}
<!-- Title -->
{% block title %}Receipt Months | {% endblock %}

<!-- body -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-3 sm:gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl"
        >
          <i class="fas fa-calendar-alt text-white text-lg sm:text-xl"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight"
          >
            Payment Months
          </h1>
          <p class="text-[#40657F] text-base sm:text-lg font-medium mt-1">
            View receipt collections by month
          </p>
          <div class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2"></div>
        </div>
      </div>

      <!-- Academic Year Selector -->
       <!-- TODO: add a searchable field -->
      <div class="flex flex-col gap-2">
        <label class="text-sm font-semibold text-[#2C3E50] font-['Roboto']"
          >Academic Year</label
        >
        <select
          name="academic_year"
          id="academic_year"
          class="px-4 py-3 rounded-xl border-2 border-[#B9D8EB] bg-white focus:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/20 outline-none transition-all duration-300 text-[#2C3E50] font-medium shadow-sm hover:shadow-md min-w-[180px]"
        >
          <option value="2024-2025">2024-2025</option>
          <option value="2025-2026">2025-2026</option>
          <option value="2023-2024">2023-2024</option>
        </select>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200"
    >
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#40657F]">Finances</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Payment Months</span>
    </nav>

    <!-- Action Buttons -->
    <div
      class="flex flex-col sm:flex-row gap-4 mt-6 pt-6 border-t border-gray-200"
    >
      <a
        href="{% url 'finances:with_excel' %}"
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
      >
        <i
          class="fas fa-plus group-hover:rotate-90 transition-all duration-300"
        ></i>
        <span>Add Payment</span>
      </a>
      <a
        href="{% url 'finances:receipt_list' %}"
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
      >
        <i
          class="fas fa-search group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        ></i>
        <span>Search All Receipts</span>
      </a>
    </div>
  </div>
  <!-- Months Grid -->
  <div class="card-modern p-8">
    <div class="flex items-center gap-3 mb-6 sm:mb-8">
      <div
        class="w-8 h-8 sm:w-10 sm:h-10 bg-[#40657F] rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-list text-white text-xs sm:text-sm"></i>
      </div>
      <div>
        <h2 class="font-display font-bold text-2xl text-[#2C3E50]">
          Monthly Collections
        </h2>
        <p class="text-[#40657F] text-sm">
          Select a month to view detailed receipts
        </p>
      </div>
    </div>

    {% if months_and_receipts %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {% for month, number_of_receipts in months_and_receipts %}
      <a
        href="{% url 'finances:receipt_months' month %}"
        class="group relative overflow-hidden bg-white border-2 border-[#B9D8EB] rounded-2xl p-6 hover:border-[#7AB2D3] hover:bg-[#E2F1F9] hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-[#7AB2D3]/10 rounded-full -translate-y-10 translate-x-10 group-hover:scale-150 transition-transform duration-500"
        ></div>

        <!-- Month Icon -->
        <div class="flex items-center gap-3 sm:gap-4 mb-4">
          <div
            class="w-10 h-10 sm:w-12 sm:h-12 bg-[#7AB2D3] rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300"
          >
            <i class="fas fa-calendar text-white text-base sm:text-lg"></i>
          </div>
          <div class="flex-1">
            <h3
              class="font-display font-bold text-lg sm:text-xl text-[#2C3E50] group-hover:text-[#7AB2D3] transition-colors duration-300"
            >
              {{ month }}
            </h3>
          </div>
        </div>

        <!-- Receipt Count -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <div
              class="w-6 h-6 sm:w-8 sm:h-8 bg-[#E2F1F9] rounded-md sm:rounded-lg flex items-center justify-center"
            >
              <i class="fas fa-receipt text-[#40657F] text-xs sm:text-sm"></i>
            </div>
            <span class="text-[#40657F] text-xs sm:text-sm font-medium"
              >Total Receipts</span
            >
          </div>
          <div class="text-right">
            <span
              class="text-xl sm:text-2xl font-bold text-[#2C3E50] group-hover:text-[#7AB2D3] transition-colors duration-300"
            >
              {{ number_of_receipts }}
            </span>
            <p class="text-xs text-[#40657F] font-medium">receipts</p>
          </div>
        </div>

        <!-- Hover Arrow -->
        <div
          class="absolute top-4 md:bottom-4 right-4 md:right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        >
          <i class="fas fa-arrow-right text-[#7AB2D3] text-lg"></i>
        </div>
      </a>
      {% endfor %}
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-16">
      <div class="flex flex-col items-center gap-6">
        <div
          class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center"
        >
          <i class="fas fa-calendar-times text-gray-400 text-3xl"></i>
        </div>
        <div>
          <h3 class="font-display font-bold text-2xl text-[#2C3E50] mb-2">
            No Payment Months Found
          </h3>
          <p class="text-[#40657F] text-lg">
            No receipt collections available for the selected academic year.
          </p>
          <p class="text-[#40657F] text-sm mt-2">
            Try selecting a different academic year or check back later.
          </p>
        </div>
        <button
          class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-plus mr-2"></i>
          Add New Receipt
        </button>
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Quick Stats -->
  {% if months_and_receipts %}
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="card-modern p-4 sm:p-6 text-center border-l-4 border-[#7AB2D3]">
      <div
        class="w-10 h-10 sm:w-12 sm:h-12 bg-[#7AB2D3] rounded-lg sm:rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-md"
      >
        <i class="fas fa-chart-line text-white text-base sm:text-lg"></i>
      </div>
      <h3
        class="font-display font-bold text-base sm:text-lg text-[#2C3E50] mb-1"
      >
        Total Months
      </h3>
      <p class="text-2xl sm:text-3xl font-bold text-[#7AB2D3]">
        {{ months_and_receipts|length }}
      </p>
      <p class="text-[#40657F] text-xs sm:text-sm">Active collection periods</p>
    </div>

    <div class="card-modern p-4 sm:p-6 text-center border-l-4 border-[#40657F]">
      <div
        class="w-10 h-10 sm:w-12 sm:h-12 bg-[#40657F] rounded-lg sm:rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-md"
      >
        <i class="fas fa-receipt text-white text-base sm:text-lg"></i>
      </div>
      <h3
        class="font-display font-bold text-base sm:text-lg text-[#2C3E50] mb-1"
      >
        Total Receipts
      </h3>
      <p class="text-2xl sm:text-3xl font-bold text-[#40657F]">
        {% widthratio months_and_receipts|length 1 1 as total_receipts %}
        <!--  -->
        {{ total_receipts|default:"0" }}
      </p>
      <p class="text-[#40657F] text-xs sm:text-sm">Across all months</p>
    </div>

    <div class="card-modern p-4 sm:p-6 text-center border-l-4 border-[#74C69D]">
      <div
        class="w-10 h-10 sm:w-12 sm:h-12 bg-[#74C69D] rounded-lg sm:rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-md"
      >
        <i class="fas fa-calendar-check text-white text-base sm:text-lg"></i>
      </div>
      <h3
        class="font-display font-bold text-base sm:text-lg text-[#2C3E50] mb-1"
      >
        Current Month
      </h3>
      <p class="text-base sm:text-lg font-bold text-[#74C69D]">
        {{ current_month|default:"N/A" }}
      </p>
      <p class="text-[#40657F] text-xs sm:text-sm">Active collection period</p>
    </div>
  </div>
  {% endif %}
</section>
{% endblock %}
