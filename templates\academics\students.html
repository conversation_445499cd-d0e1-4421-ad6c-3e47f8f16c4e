{% extends 'academics/base.html' %} {% load static %}
<!-- title -->

{% block title %}Students | {% endblock %}
<!-- title -->
{% block content %}

<section class="tf-page-container px-4 py-6 lg:px-8 lg:py-6">
  <div class="flex flex-col justify-center w-full gap-2">
    <h1 class="font-bold text-xl">Students</h1>
    <p>
      Home
      <span class="text-[#7AB2D3]"
        ><i class="fa-solid fa-angle-right px-2"></i>All Students</span
      >
    </p>
  </div>

  <!-- Enhanced Search Section -->
  <div class="card-modern p-6 search-fade-in">
    <div class="flex items-center gap-3 mb-6">
      <div
        class="w-10 h-10 bg-[#7AB2D3] rounded-xl flex items-center justify-center shadow-md"
      >
        <i class="fas fa-search text-white text-sm"></i>
      </div>
      <div>
        <h3 class="text-xl font-bold text-[#2C3E50] font-display">
          Student Search
        </h3>
        <p class="text-[#40657F] text-sm">Find students by ID, name, class, or gender</p>
      </div>
    </div>

    <form method="GET" class="space-y-6">
      <!-- Main Search -->
      <div class="flex flex-col lg:flex-row gap-4">
        <div class="flex-1">
          <div class="relative">
            <input
              type="text"
              name="q"
              value="{{ search_query }}"
              placeholder="Search by Student ID, name, or class level..."
              class="w-full px-4 py-3 pl-12 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]/60"
            />
            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#7AB2D3]"></i>
          </div>
        </div>
        <div class="flex gap-3">
          <button
            type="submit"
            class="px-6 py-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] transition-all duration-200 font-semibold"
          >
            <i class="fas fa-search mr-2"></i>
            Search
          </button>
          {% if search_query or level_filter or gender_filter %}
          <a
            href="{% url 'academics:students' %}"
            class="px-6 py-3 bg-[#B9D8EB] text-[#40657F] rounded-xl hover:bg-[#E2F1F9] transition-all duration-200 font-semibold"
          >
            <i class="fas fa-times mr-2"></i>
            Clear All
          </a>
          {% endif %}
        </div>
      </div>

      <!-- Advanced Filters -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Class Level</label>
          <select
            name="level"
            class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white"
          >
            <option value="" class="text-[#2C3E50]">All Levels</option>
            {% for level in levels %}
            <option value="{{ level }}" class="text-[#2C3E50]" {% if level == level_filter %}selected{% endif %}>
              {{ level }}
            </option>
            {% endfor %}
          </select>
        </div>
        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Gender</label>
          <select
            name="gender"
            class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white"
          >
            <option value="" class="text-[#2C3E50]">All Genders</option>
            {% for gender in genders %}
            <option value="{{ gender }}" class="text-[#2C3E50]" {% if gender == gender_filter %}selected{% endif %}>
              {{ gender }}
            </option>
            {% endfor %}
          </select>
        </div>
      </div>
    </form>
  </div>

  <!-- Enhanced Student List Section -->
  <div class="card-modern p-8 table-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
      >
        <i class="fas fa-users text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Student Directory
        </h3>
        <p class="text-[#40657F] text-sm">
          {% if search_query %}
            Search results for "{{ search_query }}"
          {% else %}
            Complete list of active students
          {% endif %}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div
        class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30"
      >
        <i class="fas fa-users text-sm"></i>
        <span>{{ total_count }} student{{ total_count|pluralize }}</span>
      </div>
    </div>

    <div
      class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg table-fade-in"
    >
      <table class="min-w-full bg-white">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-hashtag text-[#7AB2D3]"></i>
                Student ID
              </div>
            </th>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-user text-[#40657F]"></i>
                Name
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-venus-mars text-[#F28C8C]"></i>
                Gender
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-graduation-cap text-[#74C69D]"></i>
                Class Level
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-cog text-[#7AB2D3]"></i>
                Actions
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#B9D8EB]/30">
          {% for student in students %}
          <tr
            class="student-row hover:bg-[#E2F1F9]/50 transition-colors duration-200"
          >
            <td class="px-6 py-4">
              <a
                href="{% url 'academics:student_details' student.slug %}"
                class="inline-flex items-center gap-2 text-[#7AB2D3] hover:text-[#40657F] font-bold hover:underline transition-colors duration-200"
              >
                <i class="fas fa-external-link-alt text-xs"></i>
                {{ student.student_id }}
              </a>
            </td>
            <td class="px-6 py-4">
              <div class="flex items-center gap-3">
                <div
                  class="w-10 h-10 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-full flex items-center justify-center text-white text-sm font-bold"
                >
                  {{ student.name|first|upper }}
                </div>
                <div>
                  <div class="font-bold text-[#2C3E50] capitalize">
                    {{ student.name }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-center">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold {% if student.gender == 'Male' %}bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30{% else %}bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30{% endif %}"
              >
                <i class="fas {% if student.gender == 'Male' %}fa-mars{% else %}fa-venus{% endif %} mr-1"></i>
                {{ student.gender }}
              </span>
            </td>
            <td class="px-6 py-4 text-center">
              <a
                href="{% url 'academics:level_details' student.level.slug %}"
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30 hover:bg-[#74C69D] hover:text-white transition-all duration-200"
              >
                <i class="fas fa-graduation-cap mr-1"></i>
                {{ student.level }}
              </a>
            </td>
            <td class="px-6 py-4 text-center">
              <div class="flex items-center justify-center gap-2">
                <a
                  href="{% url 'academics:student_details' student.slug %}"
                  class="inline-flex items-center gap-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-1 px-3 rounded-lg hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-2 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 text-xs group"
                  title="View Details"
                >
                  <i
                    class="fas fa-eye group-hover:scale-110 transition-all duration-300"
                  ></i>
                  <span>View</span>
                </a>
                <a
                  href="{% url 'academics:report_card_selection' student.student_id %}"
                  class="inline-flex items-center gap-1 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-1 px-3 rounded-lg hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-2 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 text-xs group"
                  title="Report Card"
                >
                  <i
                    class="fas fa-file-pdf group-hover:rotate-12 transition-all duration-300"
                  ></i>
                  <span>Report</span>
                </a>
              </div>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="5" class="text-center py-12">
              <div class="flex flex-col items-center gap-4">
                <div
                  class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
                >
                  <i class="fas fa-search text-[#B9D8EB] text-2xl"></i>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
                    No Students Found
                  </h3>
                  <p class="text-[#40657F]">
                    {% if search_query %}
                      No students match your search criteria. Try adjusting your filters.
                    {% else %}
                      There are no active students to display.
                    {% endif %}
                  </p>
                </div>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex justify-center items-center gap-4 mt-8 pagination-fade-in">
      <div class="flex items-center gap-2">
        {% if page_obj.has_previous %}
        <a
          href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}{% if level_filter %}&level={{ level_filter }}{% endif %}{% if gender_filter %}&gender={{ gender_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-left"></i>
        </a>
        <a
          href="?page={{ page_obj.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if level_filter %}&level={{ level_filter }}{% endif %}{% if gender_filter %}&gender={{ gender_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-left"></i>
        </a>
        {% endif %}

        <span class="px-4 py-2 bg-[#7AB2D3] text-white rounded-lg font-semibold">
          Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
        </span>

        {% if page_obj.has_next %}
        <a
          href="?page={{ page_obj.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if level_filter %}&level={{ level_filter }}{% endif %}{% if gender_filter %}&gender={{ gender_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-right"></i>
        </a>
        <a
          href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}{% if level_filter %}&level={{ level_filter }}{% endif %}{% if gender_filter %}&gender={{ gender_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-right"></i>
        </a>
        {% endif %}
      </div>
    </div>
    {% endif %}
  </div>

  <!-- paginator controls -->
  <div class="flex justify-center mt-4">
    <span class="flex items-center gap-5 font-semibold text-[#999] text-sm">
      {% if students.has_previous %}
      <a href="?page={{ students.previous_page_number}}">
        <i class="text-sm fa-solid fa-chevron-left text-[#7AB2D3]"></i>
      </a>
      <span>{{students.previous_page_number}}</span>
      {% endif %}
      <span
        class="flex bg-[#7AB2D3] h-7 items-center justify-center w-7 rounded-full text-white"
      >
        {{ students.number }}
      </span>
      {% if students.has_next %}
      <span>{{ students.next_page_number}}</span>
      <a href="?page={{students.next_page_number}}">
        <i class="text-sm fa-solid fa-chevron-right text-[#7AB2D3]"></i>
      </a>
      {% endif %}
    </span>
  </div>
  <!-- end of paginator controls -->
</section>
{% endblock %}

<style>
  /* Search Section Animations */
  .search-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: searchFadeIn 0.8s ease-out 0.5s forwards;
  }

  /* Table Section Animations */
  .table-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: tableSectionFadeIn 0.8s ease-out 0.8s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  .table-fade-in {
    opacity: 0;
    animation: tableFadeIn 0.8s ease-out 1s forwards;
  }

  .student-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: studentRowSlideIn 0.4s ease-out forwards;
  }

  .student-row:nth-child(1) { animation-delay: 1.2s; }
  .student-row:nth-child(2) { animation-delay: 1.3s; }
  .student-row:nth-child(3) { animation-delay: 1.4s; }
  .student-row:nth-child(4) { animation-delay: 1.5s; }
  .student-row:nth-child(5) { animation-delay: 1.6s; }
  .student-row:nth-child(6) { animation-delay: 1.7s; }
  .student-row:nth-child(7) { animation-delay: 1.8s; }
  .student-row:nth-child(8) { animation-delay: 1.9s; }
  .student-row:nth-child(9) { animation-delay: 2s; }
  .student-row:nth-child(10) { animation-delay: 2.1s; }

  .pagination-fade-in {
    opacity: 0;
    animation: paginationFadeIn 0.8s ease-out 2.2s forwards;
  }

  /* Keyframe Definitions */
  @keyframes searchFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes tableSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes tableIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes tableFadeIn {
    to { opacity: 1; }
  }

  @keyframes studentRowSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes paginationFadeIn {
    to { opacity: 1; }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .student-row {
      animation-delay: 1s;
    }

    .student-row:nth-child(n) {
      animation-delay: calc(1s + 0.1s * var(--row-index, 1));
    }
  }
</style>
