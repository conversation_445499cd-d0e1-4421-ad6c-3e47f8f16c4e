{% extends "academics/base.html" %} {% load static %}
<!-- title -->
{% block title %}View Activity Results | {% endblock %}
<!-- title -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-chart-line text-white text-lg sm:text-xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            View Activity Results
          </h1>
          <p
            class="text-[#40657F] text-base sm:text-lg font-medium mt-1 subtitle-fade-in"
          >
            Filter and analyze classroom activity performance
          </p>
          <div
            class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3 action-buttons-slide-in">
        <a
          href="{% url 'academics:activities' %}"
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#7AB2D3] hover:text-white focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-tasks mr-2 group-hover:scale-110 transition-transform duration-300"
          ></i>
          All Activities
        </a>
        <a
          href="{% url 'academics:activities' %}"
          class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-edit mr-2 group-hover:translate-y-[-2px] transition-transform duration-300"
          ></i>
          Enter Results
        </a>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200 breadcrumb-fade-in"
    >
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#40657F]">Activities</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">View Results</span>
    </nav>
  </div>

  <!-- Filter Section -->
  <div class="card-modern p-8 filter-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-filter text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Filter Activity Results
        </h2>
        <p class="text-[#40657F] text-sm">
          Select criteria to view specific activity performance data
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <form method="post" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 items-end">
      {% csrf_token %}
      <div class="form-field">
        <label for="level" class="block text-xs font-bold text-[#40657F] uppercase tracking-wider mb-3">
          <i class="fas fa-school mr-1"></i>
          Class Level
        </label>
        <select name="level" id="level" class="w-full bg-[#F7FAFC] px-4 py-3 rounded-xl border-2 border-[#B9D8EB] focus:ring-4 focus:ring-[#7AB2D3]/20 focus:border-[#7AB2D3] focus:bg-white outline-none transition-all duration-300 text-sm font-medium text-[#2C3E50]">
          <option value="">Select Class Level</option>
          {% for level in levels %}
          <option value="{{ level.id }}" {% if level.id == selected_level %}selected{% endif %}>{{ level.level_name }}</option>
          {% endfor %}
        </select>
      </div>

      <div class="form-field">
        <label for="activity_type" class="block text-xs font-bold text-[#40657F] uppercase tracking-wider mb-3">
          <i class="fas fa-tasks mr-1"></i>
          Activity Type
        </label>
        <select name="activity_type" id="activity_type" class="w-full bg-[#F7FAFC] px-4 py-3 rounded-xl border-2 border-[#B9D8EB] focus:ring-4 focus:ring-[#7AB2D3]/20 focus:border-[#7AB2D3] focus:bg-white outline-none transition-all duration-300 text-sm font-medium text-[#2C3E50]">
          <option value="">Select Activity Type</option>
          {% for activity in activities %}
          <option value="{{ activity.id }}" {% if activity.id == selected_activity_type %}selected{% endif %}>{{ activity.name }}</option>
          {% endfor %}
        </select>
      </div>

      <div class="form-field">
        <label for="term" class="block text-xs font-bold text-[#40657F] uppercase tracking-wider mb-3">
          <i class="fas fa-calendar-alt mr-1"></i>
          Academic Term
        </label>
        <select name="term" id="term" class="w-full bg-[#F7FAFC] px-4 py-3 rounded-xl border-2 border-[#B9D8EB] focus:ring-4 focus:ring-[#7AB2D3]/20 focus:border-[#7AB2D3] focus:bg-white outline-none transition-all duration-300 text-sm font-medium text-[#2C3E50]">
          <option value="">Select Term</option>
          {% for term in terms %}
          <option value="{{ term.id }}" {% if term.id == selected_term %}selected{% endif %}>{{ term }}</option>
          {% endfor %}
        </select>
      </div>

      <button type="submit" class="bg-[#7AB2D3] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group text-sm">
        <i class="fas fa-search mr-2 group-hover:scale-110 transition-transform duration-300"></i>
        View Results
      </button>
    </form>
  </div>

  <!-- Results Section -->
  {% if results %}
  <div class="card-modern p-8 results-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg">
        <i class="fas fa-trophy text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">Activity Results</h2>
        <p class="text-[#40657F] text-sm">{{ results|length }} student{{ results|length|pluralize }} with performance data</p>
      </div>
      <div class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"></div>
    </div>

    <div class="overflow-x-auto">
      <div class="table-modern">
        <table class="min-w-full">
          <thead>
            <tr class="bg-[#E2F1F9] border-b border-[#B9D8EB]">
              <th class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm">Position</th>
              <th class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm">Student</th>
              <th class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm">Score</th>
              <th class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm">Status</th>
              <th class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm">Performance</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#B9D8EB]">
            {% for result in results %}
            <tr class="hover:bg-gradient-to-r hover:from-[#E2F1F9]/50 hover:to-[#B9D8EB]/30 transition-all duration-200">
              <td class="py-4 px-6">
                <div class="flex items-center gap-2">
                  {% if forloop.counter <= 3 %}
                    <div class="w-10 h-10 bg-gradient-to-br from-[#F28C8C] to-[#e67373] rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg">
                      {{ forloop.counter }}
                    </div>
                  {% else %}
                    <div class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-full flex items-center justify-center text-white text-sm font-bold">
                      {{ forloop.counter }}
                    </div>
                  {% endif %}
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-full flex items-center justify-center shadow-sm">
                    <span class="text-white text-sm font-bold">{{ result.student.name|first }}</span>
                  </div>
                  <div>
                    <span class="font-semibold text-[#2C3E50] text-sm">{{ result.student.name }}</span>
                    <p class="text-xs text-[#40657F]">{{ result.student.level.level_name|default:"N/A" }}</p>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6 text-center">
                <div class="flex flex-col items-center gap-1">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30">
                    {{ result.score }}
                  </span>
                </div>
              </td>
              <td class="py-4 px-6 text-center">
                {% if result.participation_status == "attended" %}
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#74C69D]/20 text-[#74C69D]">
                    <i class="fas fa-check-circle mr-1"></i>
                    Attended
                  </span>
                {% elif result.participation_status == "absent" %}
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#F28C8C]/20 text-[#F28C8C]">
                    <i class="fas fa-times-circle mr-1"></i>
                    Absent
                  </span>
                {% else %}
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#B9D8EB]/50 text-[#40657F]">
                    <i class="fas fa-question-circle mr-1"></i>
                    {{ result.participation_status|title }}
                  </span>
                {% endif %}
              </td>
              <td class="py-4 px-6 text-center">
                {% if forloop.counter <= 3 %}
                  <div class="flex items-center justify-center gap-1">
                    <i class="fas fa-star text-[#F28C8C]"></i>
                    <span class="text-xs font-medium text-[#F28C8C]">Excellent</span>
                  </div>
                {% elif forloop.counter > results|length|add:"-3" %}
                  <div class="flex items-center justify-center gap-1">
                    <i class="fas fa-arrow-up text-[#40657F]"></i>
                    <span class="text-xs font-medium text-[#40657F]">Needs Improvement</span>
                  </div>
                {% else %}
                  <div class="flex items-center justify-center gap-1">
                    <i class="fas fa-thumbs-up text-[#7AB2D3]"></i>
                    <span class="text-xs font-medium text-[#7AB2D3]">Good</span>
                  </div>
                {% endif %}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
  {% else %}
  <div class="card-modern p-12 text-center">
    <div class="flex flex-col items-center gap-6">
      <div
        class="w-24 h-24 bg-gradient-to-br from-[#E2F1F9] to-[#B9D8EB] rounded-full flex items-center justify-center"
      >
        <i class="fas fa-search text-[#40657F] text-3xl"></i>
      </div>
      <div>
        <h3 class="font-display font-bold text-2xl text-[#2C3E50] mb-2">
          Ready to View Results
        </h3>
        <p class="text-[#40657F] text-lg">
          Select your filter criteria above to display activity results.
        </p>
        <p class="text-[#40657F]/70 text-sm mt-2">
          Choose class level, activity type, and term to get started.
        </p>
      </div>
      <div class="flex gap-4">
        <button
          onclick="document.getElementById('level').focus()"
          class="bg-[#7AB2D3] text-white font-semibold py-2 px-4 rounded-lg hover:bg-[#40657F] transition-all duration-300 text-sm"
        >
          Select Class Level
        </button>
        <button
          onclick="document.getElementById('activity_type').focus()"
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-2 px-4 rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-300 text-sm"
        >
          Choose Activity
        </button>
      </div>
    </div>
  </div>
  {% endif %}
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Filter Section Animations */
  .filter-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: filterSectionFadeIn 0.8s ease-out 1s forwards;
  }

  /* Results Section Animations */
  .results-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: resultsSectionFadeIn 0.8s ease-out 1.2s forwards;
  }

  /* Table Row Animations */
  .table-modern tbody tr {
    opacity: 0;
    transform: translateX(-20px);
    animation: tableRowSlideIn 0.4s ease-out forwards;
  }

  .table-modern tbody tr:nth-child(1) {
    animation-delay: 1.4s;
  }
  .table-modern tbody tr:nth-child(2) {
    animation-delay: 1.5s;
  }
  .table-modern tbody tr:nth-child(3) {
    animation-delay: 1.6s;
  }
  .table-modern tbody tr:nth-child(4) {
    animation-delay: 1.7s;
  }
  .table-modern tbody tr:nth-child(5) {
    animation-delay: 1.8s;
  }
  .table-modern tbody tr:nth-child(6) {
    animation-delay: 1.9s;
  }
  .table-modern tbody tr:nth-child(7) {
    animation-delay: 2.0s;
  }
  .table-modern tbody tr:nth-child(8) {
    animation-delay: 2.1s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes filterSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes resultsSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes tableRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .table-modern tbody tr {
      animation-delay: 1.2s;
    }

    .table-modern tbody tr:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--row-index, 1));
    }
  }
</style>

{% endblock %}
