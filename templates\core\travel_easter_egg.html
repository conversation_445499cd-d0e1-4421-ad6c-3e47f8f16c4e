<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} | Tiny Feet MIS</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Comfortaa:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .font-display { font-family: 'Comfortaa', cursive; }
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="overflow-x-hidden">

<section class="w-full min-h-screen flex items-center justify-center px-4 py-8 bg-gradient-to-br from-emerald-100 via-teal-50 to-cyan-100 relative overflow-hidden">
  <!-- Animated Background Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <!-- Travel Icons -->
    <div class="travel-icon icon-1">✈️</div>
    <div class="travel-icon icon-2">🗺️</div>
    <div class="travel-icon icon-3">🎒</div>
    <div class="travel-icon icon-4">🧭</div>
    <div class="travel-icon icon-5">🏔️</div>
    <div class="travel-icon icon-6">🏖️</div>
    <div class="travel-icon icon-7">🌍</div>
    <div class="travel-icon icon-8">📸</div>
    <div class="travel-icon icon-9">🚂</div>
    <div class="travel-icon icon-10">⛵</div>
    
    <!-- Floating Clouds -->
    <div class="cloud cloud-1"></div>
    <div class="cloud cloud-2"></div>
    <div class="cloud cloud-3"></div>
    <div class="cloud cloud-4"></div>
    
    <!-- Path Lines -->
    <div class="path-line line-1"></div>
    <div class="path-line line-2"></div>
    <div class="path-line line-3"></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 max-w-4xl mx-auto text-center space-y-8 main-content-fade-in">
    
    <!-- Header Section -->
    <div class="space-y-6 header-slide-in">
      <!-- Travel Icon -->
      <div class="flex justify-center">
        <div class="w-24 h-24 bg-gradient-to-br from-emerald-400 via-teal-500 to-cyan-600 rounded-full flex items-center justify-center shadow-2xl icon-float relative overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent rounded-full"></div>
          <i class="fas fa-globe-americas text-white text-4xl icon-pulse relative z-10"></i>
        </div>
      </div>
      
      <!-- Title -->
      <div class="space-y-4">
        <h1 class="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-700 font-display title-glow">
          🌍 Wanderlust Moment
        </h1>
        <p class="text-xl md:text-2xl text-emerald-700 font-light subtitle-fade-in">
          {{ trigger.message }}
        </p>
        <div class="w-32 h-1 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-full mx-auto accent-line-grow"></div>
      </div>
    </div>

    <!-- Quote Card -->
    <div class="quote-card bg-white/80 backdrop-blur-lg border border-white/40 rounded-3xl p-8 md:p-12 shadow-2xl quote-slide-in relative overflow-hidden">
      <!-- Travel Background Effect -->
      <div class="absolute inset-0 bg-gradient-to-br from-emerald-200/30 via-teal-200/30 to-cyan-200/30 rounded-3xl"></div>
      
      <!-- Quote Content -->
      <div class="space-y-6 relative z-10" id="quote-content">
        <!-- Quote Text -->
        <blockquote class="text-2xl md:text-3xl lg:text-4xl font-light text-gray-800 leading-relaxed quote-text-fade-in">
          <i class="fas fa-quote-left text-emerald-600 text-2xl mr-4 opacity-60"></i>
          <span id="quote-text">{{ quote.quote }}</span>
          <i class="fas fa-quote-right text-emerald-600 text-2xl ml-4 opacity-60"></i>
        </blockquote>
        
        <!-- Author & Context -->
        <div class="space-y-3 author-info-slide-in">
          <div class="text-xl md:text-2xl font-semibold text-emerald-700" id="quote-author">
            — {{ quote.author }}
          </div>
          <div class="text-lg text-teal-600 opacity-80" id="quote-context">
            <i class="fas fa-map-marker-alt mr-2"></i>{{ quote.context }}
          </div>
          <div class="flex flex-wrap gap-2 justify-center">
            <div class="inline-flex items-center gap-2 bg-emerald-500/20 text-emerald-700 px-4 py-2 rounded-full text-sm font-medium" id="quote-theme">
              <i class="fas fa-compass"></i>
              {{ quote.theme }}
            </div>
            <div class="inline-flex items-center gap-2 bg-teal-500/20 text-teal-700 px-4 py-2 rounded-full text-sm font-medium" id="quote-category">
              <i class="fas fa-route"></i>
              {{ quote.category }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center buttons-slide-in">
      <!-- New Quote Button -->
      <button
        id="new-quote-btn"
        class="group bg-gradient-to-r from-emerald-500 to-teal-500 text-white font-bold py-4 px-8 rounded-2xl hover:from-emerald-600 hover:to-teal-600 focus:ring-4 focus:ring-emerald-500/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl"
      >
        <i class="fas fa-paper-plane mr-3 group-hover:rotate-12 transition-transform duration-500"></i>
        Next Adventure
      </button>
      
      <!-- Share Quote Button -->
      <button
        id="share-quote-btn"
        class="group bg-white/60 backdrop-blur-sm text-gray-700 font-bold py-4 px-8 rounded-2xl hover:bg-white/80 focus:ring-4 focus:ring-white/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl border border-white/40"
      >
        <i class="fas fa-share mr-3 group-hover:scale-110 transition-transform duration-300"></i>
        Share Journey
      </button>
      
      <!-- Return Button -->
      <a
        href="javascript:history.back()"
        class="group bg-gray-600/50 backdrop-blur-sm text-white font-bold py-4 px-8 rounded-2xl hover:bg-gray-700/50 focus:ring-4 focus:ring-gray-500/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl border border-white/20"
      >
        <i class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-300"></i>
        Return Home
      </a>
    </div>

    <!-- Easter Egg Discovery Message -->
    <div class="bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-400/30 rounded-2xl p-6 discovery-message-fade-in">
      <div class="flex items-center justify-center gap-3 text-emerald-700">
        <i class="fas fa-egg text-2xl text-emerald-600"></i>
        <div class="text-center">
          <p class="font-semibold">🎉 Congratulations! You've discovered a wanderlust easter egg!</p>
          <p class="text-sm opacity-80 mt-1">
            Your search for "{{ trigger.search_term }}" has unlocked this hidden feature. 
            The system appreciates adventurous spirits who dream of exploring the world.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  /* Travel Background Animations */
  .travel-icon {
    position: absolute;
    font-size: 2rem;
    animation: travelFloat 10s ease-in-out infinite;
    opacity: 0.6;
  }

  .icon-1 { top: 10%; left: 10%; animation-delay: 0s; }
  .icon-2 { top: 20%; right: 15%; animation-delay: 1s; }
  .icon-3 { top: 35%; left: 15%; animation-delay: 2s; }
  .icon-4 { top: 50%; right: 20%; animation-delay: 3s; }
  .icon-5 { top: 65%; left: 10%; animation-delay: 4s; }
  .icon-6 { top: 80%; right: 15%; animation-delay: 5s; }
  .icon-7 { top: 15%; left: 60%; animation-delay: 6s; }
  .icon-8 { top: 45%; right: 30%; animation-delay: 7s; }
  .icon-9 { top: 75%; left: 25%; animation-delay: 8s; }
  .icon-10 { top: 25%; right: 40%; animation-delay: 9s; }

  .cloud {
    position: absolute;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50px;
    animation: cloudDrift 15s linear infinite;
  }

  .cloud-1 {
    width: 100px;
    height: 40px;
    top: 15%;
    left: -100px;
    animation-delay: 0s;
  }

  .cloud-2 {
    width: 80px;
    height: 30px;
    top: 30%;
    left: -80px;
    animation-delay: 5s;
  }

  .cloud-3 {
    width: 120px;
    height: 50px;
    top: 60%;
    left: -120px;
    animation-delay: 10s;
  }

  .cloud-4 {
    width: 90px;
    height: 35px;
    top: 80%;
    left: -90px;
    animation-delay: 12s;
  }

  .path-line {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.4), transparent);
    animation: pathMove 8s ease-in-out infinite;
  }

  .line-1 {
    width: 200px;
    top: 25%;
    left: 20%;
    transform: rotate(15deg);
    animation-delay: 0s;
  }

  .line-2 {
    width: 150px;
    top: 55%;
    right: 25%;
    transform: rotate(-20deg);
    animation-delay: 2s;
  }

  .line-3 {
    width: 180px;
    bottom: 25%;
    left: 30%;
    transform: rotate(10deg);
    animation-delay: 4s;
  }

  /* Content Animations */
  .main-content-fade-in {
    opacity: 0;
    animation: mainContentFadeIn 1s ease-out 0.5s forwards;
  }

  .header-slide-in {
    opacity: 0;
    transform: translateY(-50px);
    animation: headerSlideIn 1s ease-out 0.8s forwards;
  }

  .icon-float {
    animation: iconFloat 4s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 3s ease-in-out infinite;
  }

  .title-glow {
    animation: titleGlow 3s ease-in-out infinite;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 1s ease-out 1.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 1s ease-out 1.5s forwards;
  }

  .quote-slide-in {
    opacity: 0;
    transform: translateY(50px);
    animation: quoteSlideIn 1s ease-out 1.8s forwards;
  }

  .quote-text-fade-in {
    opacity: 0;
    animation: quoteTextFadeIn 1s ease-out 2.2s forwards;
  }

  .author-info-slide-in {
    opacity: 0;
    transform: translateX(-30px);
    animation: authorInfoSlideIn 1s ease-out 2.5s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: buttonsSlideIn 1s ease-out 2.8s forwards;
  }

  .discovery-message-fade-in {
    opacity: 0;
    animation: discoveryMessageFadeIn 1s ease-out 3.2s forwards;
  }

  /* Keyframe Definitions */
  @keyframes travelFloat {
    0%, 100% { 
      transform: translateY(0px) rotate(0deg); 
      opacity: 0.6; 
    }
    25% { 
      transform: translateY(-25px) rotate(90deg); 
      opacity: 0.8; 
    }
    50% { 
      transform: translateY(-15px) rotate(180deg); 
      opacity: 1; 
    }
    75% { 
      transform: translateY(-20px) rotate(270deg); 
      opacity: 0.7; 
    }
  }

  @keyframes cloudDrift {
    from { transform: translateX(0); }
    to { transform: translateX(calc(100vw + 200px)); }
  }

  @keyframes pathMove {
    0%, 100% { opacity: 0; transform: scaleX(0); }
    50% { opacity: 1; transform: scaleX(1); }
  }

  @keyframes mainContentFadeIn {
    to { opacity: 1; }
  }

  @keyframes headerSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  @keyframes titleGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 8rem; }
  }

  @keyframes quoteSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes quoteTextFadeIn {
    to { opacity: 1; }
  }

  @keyframes authorInfoSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes buttonsSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes discoveryMessageFadeIn {
    to { opacity: 1; }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .travel-icon, .cloud, .path-line {
      display: none;
    }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const newQuoteBtn = document.getElementById('new-quote-btn');
    const shareQuoteBtn = document.getElementById('share-quote-btn');

    // New Quote functionality
    newQuoteBtn.addEventListener('click', function() {
        // Add loading state
        const originalText = this.innerHTML;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-3"></i>Loading...';
        this.disabled = true;

        // Fetch new quote
        fetch('{% url "core:get_new_travel_quote" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update quote content with animation
                const quoteContent = document.getElementById('quote-content');
                quoteContent.style.opacity = '0';
                quoteContent.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    document.getElementById('quote-text').textContent = data.quote.quote;
                    document.getElementById('quote-author').textContent = '— ' + data.quote.author;
                    document.getElementById('quote-context').innerHTML = '<i class="fas fa-map-marker-alt mr-2"></i>' + data.quote.context;
                    document.getElementById('quote-theme').innerHTML = '<i class="fas fa-compass"></i> ' + data.quote.theme;
                    document.getElementById('quote-category').innerHTML = '<i class="fas fa-route"></i> ' + data.quote.category;

                    quoteContent.style.opacity = '1';
                    quoteContent.style.transform = 'translateY(0)';
                }, 300);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        })
        .finally(() => {
            // Restore button
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 1000);
        });
    });

    // Share Quote functionality
    shareQuoteBtn.addEventListener('click', function() {
        const quote = document.getElementById('quote-text').textContent;
        const author = document.getElementById('quote-author').textContent;
        const shareText = `"${quote}" ${author}`;

        if (navigator.share) {
            navigator.share({
                title: 'Travel Inspiration',
                text: shareText,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareText).then(() => {
                // Show feedback
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check mr-3"></i>Copied!';
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            });
        }
    });
});
</script>

</body>
</html>
