# Feature Development Guide

## 🎯 Overview

This guide provides comprehensive instructions for developing new features in the Receipt Generator system, following established patterns and best practices.

## 🏗️ Architecture Overview

### Project Structure
```
receipt_gen/
├── academics/          # Academic records and assessments
├── accounts/           # User authentication and profiles
├── core/              # Shared utilities and base classes
├── finances/          # Financial management and receipts
├── students/          # Student management and enrollment
├── helpers/           # Utility functions and tools
└── templates/         # HTML templates with modern design
```

### Design Patterns
- **Model-View-Template (MVT)**: Django's standard pattern
- **Repository Pattern**: For complex data operations
- **Service Layer**: For business logic separation
- **Factory Pattern**: For object creation
- **Observer Pattern**: For model signals

## 🚀 Creating a New Feature

### 1. Planning Phase

#### Feature Requirements
- Define user stories and acceptance criteria
- Identify affected models and relationships
- Plan URL structure and navigation
- Design database schema changes
- Consider security and permissions

#### Documentation
- Update feature specifications
- Create wireframes or mockups
- Plan API endpoints (if applicable)
- Define test scenarios

### 2. Database Design

#### Creating Models
```python
# models.py
from django.db import models
from core.base_models import BaseModel
from helpers.strings import generate_unique_ids

class YourModel(BaseModel):
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)
    
    # Foreign key relationships
    related_model = models.ForeignKey(
        'app.RelatedModel', 
        on_delete=models.CASCADE,
        related_name='your_models'
    )
    
    class Meta:
        db_table = 'app_your_model'
        ordering = ['-created_at']
        verbose_name = 'Your Model'
        verbose_name_plural = 'Your Models'
    
    def generate_unique_id(self):
        prefix = "YM"
        self.unique_id = generate_unique_ids(self, prefix)
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        if not self.unique_id:
            self.generate_unique_id()
        super().save(*args, **kwargs)
    
    def __str__(self):
        return self.name
```

#### Migrations
```bash
# Create migration
python manage.py makemigrations app_name

# Review migration file
cat app_name/migrations/0001_initial.py

# Apply migration
python manage.py migrate

# Create data migration (if needed)
python manage.py makemigrations --empty app_name
```

### 3. Views Development

#### Function-Based Views
```python
# views.py
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator

from .models import YourModel
from .forms import YourModelForm

@login_required(login_url="accounts:login")
def your_model_list(request):
    """List view with pagination and search"""
    search_query = request.GET.get('search', '')
    your_models = YourModel.objects.filter(is_active=True)
    
    if search_query:
        your_models = your_models.filter(
            name__icontains=search_query
        )
    
    paginator = Paginator(your_models, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_count': your_models.count(),
    }
    return render(request, 'app/your_model_list.html', context)

@login_required(login_url="accounts:login")
def your_model_detail(request, slug):
    """Detail view"""
    your_model = get_object_or_404(YourModel, slug=slug, is_active=True)
    
    context = {
        'your_model': your_model,
    }
    return render(request, 'app/your_model_detail.html', context)

@login_required(login_url="accounts:login")
def your_model_create(request):
    """Create view"""
    if request.method == 'POST':
        form = YourModelForm(request.POST)
        if form.is_valid():
            your_model = form.save()
            messages.success(request, f'{your_model.name} created successfully!')
            return redirect('app:your_model_detail', slug=your_model.slug)
    else:
        form = YourModelForm()
    
    context = {
        'form': form,
        'title': 'Create New Item',
    }
    return render(request, 'app/your_model_form.html', context)
```

#### Class-Based Views
```python
# views.py
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy

class YourModelListView(LoginRequiredMixin, ListView):
    model = YourModel
    template_name = 'app/your_model_list.html'
    context_object_name = 'your_models'
    paginate_by = 25
    login_url = 'accounts:login'
    
    def get_queryset(self):
        queryset = super().get_queryset().filter(is_active=True)
        search_query = self.request.GET.get('search')
        if search_query:
            queryset = queryset.filter(name__icontains=search_query)
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_query'] = self.request.GET.get('search', '')
        return context

class YourModelCreateView(LoginRequiredMixin, CreateView):
    model = YourModel
    form_class = YourModelForm
    template_name = 'app/your_model_form.html'
    success_url = reverse_lazy('app:your_model_list')
    login_url = 'accounts:login'
    
    def form_valid(self, form):
        messages.success(self.request, 'Item created successfully!')
        return super().form_valid(form)
```

### 4. Forms Development

```python
# forms.py
from django import forms
from django.core.exceptions import ValidationError

from .models import YourModel

class YourModelForm(forms.ModelForm):
    class Meta:
        model = YourModel
        fields = ['name', 'description', 'related_model', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200',
                'placeholder': 'Enter name...'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200',
                'rows': 4,
                'placeholder': 'Enter description...'
            }),
            'related_model': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'w-5 h-5 text-[#7AB2D3] border-[#B9D8EB] rounded focus:ring-[#7AB2D3]'
            })
        }
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if YourModel.objects.filter(name__iexact=name).exists():
            raise ValidationError('A record with this name already exists.')
        return name
    
    def clean(self):
        cleaned_data = super().clean()
        # Add custom validation logic here
        return cleaned_data
```

### 5. URL Configuration

```python
# urls.py
from django.urls import path
from . import views

app_name = 'app'

urlpatterns = [
    path('', views.your_model_list, name='your_model_list'),
    path('create/', views.your_model_create, name='your_model_create'),
    path('<slug:slug>/', views.your_model_detail, name='your_model_detail'),
    path('<slug:slug>/edit/', views.your_model_update, name='your_model_update'),
    path('<slug:slug>/delete/', views.your_model_delete, name='your_model_delete'),
    
    # API endpoints
    path('api/search/', views.your_model_search_api, name='your_model_search_api'),
    path('api/<slug:slug>/', views.your_model_api, name='your_model_api'),
]
```

### 6. Template Development

#### Base Template Structure
```html
<!-- templates/app/your_model_list.html -->
{% extends 'base.html' %}
{% load static %}

{% block title %}Your Models | {% endblock %}

{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-6 space-y-6">
  <!-- Header Section -->
  <div class="bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
      <div class="space-y-2">
        <div class="flex items-center gap-4">
          <div class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg">
            <i class="fas fa-list text-white text-lg"></i>
          </div>
          <div>
            <h1 class="font-bold text-2xl md:text-3xl text-[#2C3E50] tracking-tight">
              Your Models
            </h1>
            <p class="text-[#40657F] text-lg font-medium">
              Manage your model records
            </p>
          </div>
        </div>
      </div>
      
      <div class="flex flex-col sm:flex-row gap-4">
        <a href="{% url 'app:your_model_create' %}" 
           class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group">
          <i class="fas fa-plus group-hover:rotate-90 transition-all duration-300"></i>
          <span>Add New</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Search Section -->
  <div class="bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-6">
    <form method="GET" class="flex gap-4">
      <div class="flex-1">
        <input type="text" 
               name="search" 
               value="{{ search_query }}"
               placeholder="Search records..."
               class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200">
      </div>
      <button type="submit" 
              class="px-6 py-3 bg-[#7AB2D3] text-white rounded-xl hover:bg-[#40657F] transition-colors duration-200">
        <i class="fas fa-search"></i>
      </button>
    </form>
  </div>

  <!-- Content Section -->
  <div class="bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 overflow-hidden">
    {% if page_obj %}
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
            <tr>
              <th class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider">Name</th>
              <th class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider">Description</th>
              <th class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider">Status</th>
              <th class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#E2F1F9]">
            {% for item in page_obj %}
              <tr class="hover:bg-[#E2F1F9]/50 transition-colors duration-200">
                <td class="px-6 py-4">
                  <div class="font-bold text-[#2C3E50]">{{ item.name }}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-[#40657F]">{{ item.description|truncatewords:10 }}</div>
                </td>
                <td class="px-6 py-4">
                  {% if item.is_active %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-[#74C69D]/20 text-[#74C69D]">
                      Active
                    </span>
                  {% else %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-[#F28C8C]/20 text-[#F28C8C]">
                      Inactive
                    </span>
                  {% endif %}
                </td>
                <td class="px-6 py-4">
                  <div class="flex items-center gap-2">
                    <a href="{% url 'app:your_model_detail' item.slug %}" 
                       class="text-[#7AB2D3] hover:text-[#40657F] transition-colors duration-200">
                      <i class="fas fa-eye"></i>
                    </a>
                    <a href="{% url 'app:your_model_update' item.slug %}" 
                       class="text-[#74C69D] hover:text-[#5fb085] transition-colors duration-200">
                      <i class="fas fa-edit"></i>
                    </a>
                  </div>
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      
      <!-- Pagination -->
      {% if page_obj.has_other_pages %}
        <div class="px-6 py-4 border-t border-[#E2F1F9] bg-[#F7FAFC]">
          <div class="flex items-center justify-between">
            <div class="text-sm text-[#40657F]">
              Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
            </div>
            <div class="flex gap-2">
              {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                   class="px-3 py-2 bg-[#7AB2D3] text-white rounded-lg hover:bg-[#40657F] transition-colors duration-200">
                  Previous
                </a>
              {% endif %}
              {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" 
                   class="px-3 py-2 bg-[#7AB2D3] text-white rounded-lg hover:bg-[#40657F] transition-colors duration-200">
                  Next
                </a>
              {% endif %}
            </div>
          </div>
        </div>
      {% endif %}
    {% else %}
      <div class="p-12 text-center">
        <div class="w-16 h-16 bg-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-inbox text-[#7AB2D3] text-2xl"></i>
        </div>
        <h3 class="text-lg font-bold text-[#2C3E50] mb-2">No records found</h3>
        <p class="text-[#40657F] mb-6">Get started by creating your first record.</p>
        <a href="{% url 'app:your_model_create' %}" 
           class="inline-flex items-center gap-2 bg-[#7AB2D3] text-white px-6 py-3 rounded-xl hover:bg-[#40657F] transition-colors duration-200">
          <i class="fas fa-plus"></i>
          <span>Add New Record</span>
        </a>
      </div>
    {% endif %}
  </div>
</section>
{% endblock %}
```

### 7. Testing

```python
# tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from .models import YourModel

User = get_user_model()

class YourModelTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client = Client()
        self.client.login(username='testuser', password='testpass123')
        
        self.your_model = YourModel.objects.create(
            name='Test Model',
            description='Test description'
        )
    
    def test_model_creation(self):
        self.assertEqual(self.your_model.name, 'Test Model')
        self.assertTrue(self.your_model.is_active)
        self.assertIsNotNone(self.your_model.slug)
    
    def test_list_view(self):
        response = self.client.get(reverse('app:your_model_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Model')
    
    def test_detail_view(self):
        response = self.client.get(
            reverse('app:your_model_detail', kwargs={'slug': self.your_model.slug})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.your_model.name)
    
    def test_create_view(self):
        data = {
            'name': 'New Model',
            'description': 'New description',
            'is_active': True
        }
        response = self.client.post(reverse('app:your_model_create'), data)
        self.assertEqual(response.status_code, 302)
        self.assertTrue(YourModel.objects.filter(name='New Model').exists())
```

## 🎨 Frontend Integration

### JavaScript Enhancement
```javascript
// static/js/your-feature.js
document.addEventListener('DOMContentLoaded', function() {
    // Initialize feature-specific functionality
    initializeSearch();
    initializeModals();
    initializeFormValidation();
});

function initializeSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        // Add debounced search functionality
        let timeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                // Implement live search
            }, 300);
        });
    }
}
```

### CSS Customization
```css
/* static/css/your-feature.css */
.your-feature-card {
    @apply bg-white rounded-2xl shadow-lg border border-blue-100 p-6 hover:shadow-xl transition-all duration-300;
}

.your-feature-button {
    @apply bg-gradient-to-r from-blue-400 to-blue-600 text-white font-bold py-3 px-6 rounded-xl hover:from-blue-600 hover:to-blue-400 transition-all duration-300;
}
```

## 📋 Best Practices

### Code Quality
- Follow PEP 8 style guidelines
- Use meaningful variable and function names
- Add docstrings to functions and classes
- Implement proper error handling
- Use type hints where appropriate

### Security
- Always validate user input
- Use Django's built-in security features
- Implement proper authentication and authorization
- Sanitize data before database operations
- Use CSRF protection for forms

### Performance
- Use database indexes for frequently queried fields
- Implement pagination for large datasets
- Use select_related() and prefetch_related() for queries
- Cache expensive operations
- Optimize static file delivery

### Testing
- Write unit tests for models and utilities
- Create integration tests for views
- Test form validation and error handling
- Use factory classes for test data
- Maintain good test coverage

## 🚀 Deployment Considerations

### Database Migrations
- Always create migrations for model changes
- Test migrations on staging environment
- Plan for data migrations when needed
- Consider migration rollback strategies

### Static Files
- Compile and minify CSS/JS for production
- Use CDN for static file delivery
- Implement proper caching headers
- Optimize images and assets

### Monitoring
- Add logging for important operations
- Monitor performance metrics
- Set up error tracking
- Implement health checks

---

This guide provides the foundation for developing robust features in the Receipt Generator system. Always refer to existing code patterns and maintain consistency with the established architecture.
