{% extends 'base.html' %}

<!--  -->
{% block title %}Add Expenditure | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-6xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex items-center gap-6 mb-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-xl icon-float"
      >
        <i class="fas fa-money-bill-wave text-white text-2xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
        >
          Add Expenditure
        </h1>
        <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
          Record new expense transaction
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
    <div
      class="bg-gradient-to-r from-[#F28C8C]/10 to-[#e07575]/10 rounded-xl p-6 border border-[#F28C8C]/20 description-fade-in"
    >
      <div class="flex items-center gap-3 mb-3">
        <i class="fas fa-info-circle text-[#F28C8C] text-lg"></i>
        <h3 class="font-bold text-[#2C3E50]">Instructions</h3>
      </div>
      <p class="text-[#40657F] leading-relaxed">
        Fill in the details below to add a new expenditure to the system. Make
        sure to include all expense line items for accurate record keeping.
      </p>
    </div>
  </div>
  <!-- Expenditure Form -->
  <form class="w-full space-y-8 form-fade-in" method="post" autocomplete="off">
    {% csrf_token %}

    <!-- Form Header -->
    <div class="text-center form-header-slide-in">
      <div
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white px-8 py-4 rounded-2xl shadow-lg"
      >
        <i class="fas fa-file-invoice-dollar text-xl"></i>
        <h2 class="text-2xl font-bold font-display">Expenditure Form</h2>
      </div>
    </div>

    <!-- Main Form Card -->
    <div
      class="bg-white rounded-2xl shadow-xl border border-[#B9D8EB]/50 overflow-hidden main-form-slide-in"
    >
      <!-- Header Section -->
      <div
        class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] p-8 border-b border-[#B9D8EB]/50"
      >
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg"
          >
            <i class="fas fa-clipboard-list text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
              Expenditure Details
            </h3>
            <p class="text-[#40657F] text-sm">
              Basic information about the expense
            </p>
          </div>
        </div>

        <div
          class="grid grid-cols-1 md:grid-cols-2 gap-6 header-fields-fade-in"
        >
          {% for field in form %}
          <div class="form-field-group">
            <label class="form-label" for="{{ field.auto_id }}">
              <i
                class="fas fa-{% if 'date' in field.name %}calendar{% elif 'description' in field.name %}file-alt{% elif 'category' in field.name %}tag{% else %}info-circle{% endif %} mr-2 text-[#7AB2D3]"
              ></i>
              {{ field.label }}
            </label>
            <div class="form-field-wrapper">
              {{ field }} {% if field.errors %}
              <div class="form-error">
                <i class="fas fa-exclamation-triangle mr-1"></i>
                {{ field.errors|striptags }}
              </div>
              {% endif %}
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
      <!-- Line Items Section -->
      {{ formset.management_form }}
      <div class="p-8">
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg"
          >
            <i class="fas fa-list text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
              Expense Line Items
            </h3>
            <p class="text-[#40657F] text-sm">
              Add individual expense items and amounts
            </p>
          </div>
        </div>

        <div
          class="bg-gradient-to-br from-[#F7FAFC] to-[#E2F1F9] rounded-2xl md:p-6 border border-[#B9D8EB]/30 line-items-fade-in"
        >
          <div class="space-y-6">
            {% for form in formset %}
            <div
              class="line-item-row bg-white rounded-xl p-8 border border-[#B9D8EB]/50 shadow-sm hover:shadow-md transition-all duration-300"
            >
              <div class="flex items-center gap-3 mb-6">
                <div
                  class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center text-white text-sm font-bold"
                >
                  {{ forloop.counter }}
                </div>
                <h4 class="font-bold text-[#2C3E50] text-lg">
                  Line Item {{ forloop.counter }}
                </h4>
              </div>
              <div class="formset-fields flex flex-col md:flex-row gap-8 my-5">{{ form.as_div }}</div>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div
      class="flex flex-col sm:flex-row justify-center items-center gap-4 w-full buttons-slide-in"
    >
      <button
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group w-full sm:w-auto"
        type="submit"
      >
        <i
          class="fas fa-save group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        ></i>
        <span>Save Expenditure</span>
      </button>
      <button
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group w-full sm:w-auto"
        type="reset"
      >
        <i
          class="fas fa-undo group-hover:scale-110 group-hover:rotate-180 transition-all duration-300"
        ></i>
        <span>Reset Form</span>
      </button>
    </div>
  </form>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .description-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: descriptionFadeIn 0.8s ease-out 0.8s forwards;
  }

  /* Form Animations */
  .form-fade-in {
    opacity: 0;
    animation: formFadeIn 0.8s ease-out 1s forwards;
  }

  .form-header-slide-in {
    opacity: 0;
    transform: translateY(-20px);
    animation: formHeaderSlideIn 0.8s ease-out 1.2s forwards;
  }

  .main-form-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: mainFormSlideIn 0.8s ease-out 1.4s forwards;
  }

  .header-fields-fade-in {
    opacity: 0;
    animation: headerFieldsFadeIn 0.8s ease-out 1.6s forwards;
  }

  .line-items-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: lineItemsFadeIn 0.8s ease-out 1.8s forwards;
  }

  .line-item-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: lineItemRowSlideIn 0.4s ease-out forwards;
  }

  .line-item-row:nth-child(1) {
    animation-delay: 2s;
  }
  .line-item-row:nth-child(2) {
    animation-delay: 2.1s;
  }
  .line-item-row:nth-child(3) {
    animation-delay: 2.2s;
  }
  .line-item-row:nth-child(4) {
    animation-delay: 2.3s;
  }
  .line-item-row:nth-child(5) {
    animation-delay: 2.4s;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: buttonsSlideIn 0.8s ease-out 2.5s forwards;
  }

  /* Form Field Styling */
  .form-field-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-label {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  .form-field-wrapper {
    position: relative;
  }

  .form-field-wrapper input,
  .form-field-wrapper select,
  .form-field-wrapper textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #b9d8eb;
    border-radius: 0.75rem;
    background-color: #f7fafc;
    color: #2c3e50;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .form-field-wrapper input:focus,
  .form-field-wrapper select:focus,
  .form-field-wrapper textarea:focus {
    outline: none;
    border-color: #7ab2d3;
    background-color: white;
    box-shadow: 0 0 0 4px rgba(122, 178, 211, 0.2);
  }

  .form-error {
    display: flex;
    align-items: center;
    color: #f28c8c;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.25rem;
  }

  /* Formset Field Styling */
  .formset-fields > div {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.5rem;
  }

  .formset-fields > div > div {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .formset-fields label {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #40657f;
    font-size: 0.875rem;
  }

  .formset-fields input,
  .formset-fields select,
  .formset-fields textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #b9d8eb;
    border-radius: 0.75rem;
    background-color: #f7fafc;
    color: #2c3e50;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .formset-fields input:focus,
  .formset-fields select:focus,
  .formset-fields textarea:focus {
    outline: none;
    border-color: #74c69d;
    background-color: white;
    box-shadow: 0 0 0 4px rgba(116, 198, 157, 0.2);
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes descriptionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes formFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes formHeaderSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes mainFormSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes headerFieldsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes lineItemsFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes lineItemRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes buttonsSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .line-item-row {
      animation-delay: 1.5s;
    }

    .line-item-row:nth-child(n) {
      animation-delay: calc(1.5s + 0.1s * var(--row-index, 1));
    }
  }
</style>

{% endblock %}
