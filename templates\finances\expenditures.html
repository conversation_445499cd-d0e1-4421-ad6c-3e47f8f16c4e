{% extends 'base.html' %} {% load humanize %} {% block title %}Finance Overview
| {% endblock%} {% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl"
        >
          <i class="fas fa-chart-line text-white text-lg sm:text-xl"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight"
          >
            Finance Overview
          </h1>
          <p class="text-[#40657F] text-base sm:text-lg font-medium mt-1">
            Track income, expenses and balance
          </p>
          <div
            class="w-16 sm:w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2"
          ></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3">
        <a
          href="{% url 'finances:add_expenditure'%}"
          class="bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-semibold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group"
        >
          <i
            class="fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300"
          ></i>
          Create Payment
        </a>
        <a
          href="{% url 'finances:reversal_list' %}"
          class="bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-semibold py-3 px-6 rounded-xl hover:from-[#e07575] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group"
        >
          <i
            class="fas fa-undo mr-2 group-hover:rotate-180 transition-transform duration-300"
          ></i>
          View Reversals
        </a>
        <button
          class="bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-semibold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group"
        >
          <i
            class="fas fa-download mr-2 group-hover:translate-y-[-2px] transition-transform duration-300"
          ></i>
          Export Report
        </button>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-[#B9D8EB]"
    >
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#40657F]">Finances</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Overview</span>
    </nav>
  </div>
  <!-- Finance Cards -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
    <!-- Total Income Card -->
    <div
      class="card-modern p-6 group hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 bg-gradient-to-br from-[#74C69D]/20 to-[#5fb085]/10 border-l-4 border-[#74C69D] relative overflow-hidden"
    >
      <!-- Background Pattern -->
      <div
        class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#74C69D]/20 to-[#5fb085]/10 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-700"
      ></div>

      <div class="flex items-center justify-between mb-4 relative z-10">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        >
          <i class="fas fa-arrow-up text-white text-lg"></i>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-[#74C69D] rounded-full animate-pulse"></div>
          <span class="text-xs text-[#74C69D] font-semibold">Active</span>
        </div>
      </div>

      <div class="relative z-10">
        <span
          class="text-xs font-bold text-[#2C3E50] uppercase tracking-wider mb-2 block"
          >Total Income</span
        >
        <span
          class="text-2xl md:text-3xl font-bold text-[#74C69D] mb-2 font-display block"
          >MWK {{ total_collected | intcomma}}</span
        >
        <span class="text-sm text-[#40657F] font-medium"
          >Amount collected this period</span
        >
      </div>

      <!-- Trend Indicator -->
      <div class="flex items-center gap-2 mt-4 relative z-10">
        <div
          class="flex items-center gap-1 text-[#74C69D] text-xs font-semibold"
        >
          <i class="fas fa-trending-up"></i>
          <span>+12.5%</span>
        </div>
        <span class="text-[#40657F] text-xs">vs last month</span>
      </div>
    </div>

    <!-- Total Spent Card -->
    <div
      class="card-modern p-6 group hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 bg-gradient-to-br from-[#F28C8C]/20 to-[#e07575]/10 border-l-4 border-[#F28C8C] relative overflow-hidden"
    >
      <!-- Background Pattern -->
      <div
        class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#F28C8C]/20 to-[#e07575]/10 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-700"
      ></div>

      <div class="flex items-center justify-between mb-4 relative z-10">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        >
          <i class="fas fa-arrow-down text-white text-lg"></i>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-[#F28C8C] rounded-full animate-pulse"></div>
          <span class="text-xs text-[#F28C8C] font-semibold">Tracking</span>
        </div>
      </div>

      <div class="relative z-10">
        <span
          class="text-xs font-bold text-[#2C3E50] uppercase tracking-wider mb-2 block"
          >Total Spent</span
        >
        <span
          class="text-2xl md:text-3xl font-bold text-[#F28C8C] mb-2 font-display block"
          >MWK {{ total_used | intcomma }}</span
        >
        <span class="text-sm text-[#40657F] font-medium"
          >Amount spent this period</span
        >
      </div>

      <!-- Trend Indicator -->
      <div class="flex items-center gap-2 mt-4 relative z-10">
        <div
          class="flex items-center gap-1 text-[#F28C8C] text-xs font-semibold"
        >
          <i class="fas fa-trending-down"></i>
          <span>-8.3%</span>
        </div>
        <span class="text-[#40657F] text-xs">vs last month</span>
      </div>
    </div>

    <!-- Available Balance Card -->
    <div
      class="card-modern p-6 group hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 border-l-4 border-[#7AB2D3] relative overflow-hidden"
    >
      <!-- Background Pattern -->
      <div
        class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-700"
      ></div>

      <div class="flex items-center justify-between mb-4 relative z-10">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        >
          <i class="fas fa-wallet text-white text-lg"></i>
        </div>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-[#7AB2D3] rounded-full animate-pulse"></div>
          <span class="text-xs text-[#7AB2D3] font-semibold">Available</span>
        </div>
      </div>

      <div class="relative z-10">
        <span
          class="text-xs font-bold text-[#2C3E50] uppercase tracking-wider mb-2 block"
          >Available Balance</span
        >
        <span
          class="text-2xl md:text-3xl font-bold text-[#7AB2D3] mb-2 font-display block"
          >MWK {{total_balance | intcomma }}</span
        >
        <span class="text-sm text-[#40657F] font-medium">Amount remaining</span>
      </div>

      <!-- Trend Indicator -->
      <div class="flex items-center gap-2 mt-4 relative z-10">
        <div
          class="flex items-center gap-1 text-[#7AB2D3] text-xs font-semibold"
        >
          <i class="fas fa-chart-line"></i>
          <span>Stable</span>
        </div>
        <span class="text-[#40657F] text-xs">healthy balance</span>
      </div>
    </div>
  </div>
  <!-- Payment History Table -->
  <div class="card-modern p-6">
    <div class="flex flex-col md:flex-row items-center gap-4 justify-between mb-6">
      <div class="flex items-center gap-3 w-full md:w-fit">
        <div
          class="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg"
        >
          <i class="fas fa-history text-white text-xs sm:text-sm"></i>
        </div>
        <div>
          <h2 class="text-lg sm:text-xl font-bold text-[#2C3E50] font-display">
            Payment History
          </h2>
          <p class="text-[#40657F] text-sm">Recent financial transactions</p>
        </div>
      </div>
      <!-- Search and Filter -->
      <div class="flex flex-col md:flex-row items-center gap-3 w-full md:w-fit">
        <div class="relative w-full md:w-fit">
          <input
            type="text"
            placeholder="Search payments..."
            class="pl-10 pr-4 py-2 border border-[#B9D8EB] rounded-lg focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm w-full md:w-fit text-[#2C3E50]"
          />
          <i
            class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-[#B9D8EB] text-sm"
          ></i>
        </div>
        <button
          class="bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white font-semibold py-2 px-4 rounded-lg hover:from-[#2C3E50] hover:to-[#40657F] transition-all duration-300 text-sm w-full md:w-fit"
        >
          <i class="fas fa-filter mr-2"></i>
          Filter
        </button>
      </div>
    </div>

    {% if expenditure_list %}
    <div class="overflow-x-auto">
      <div class="table-modern">
        <table class="min-w-full">
          <thead>
            <tr
              class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] border-b border-[#B9D8EB]"
            >
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Document No.
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Description
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Term
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Date
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Amount
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#B9D8EB] text-nowrap">
            {% for expense in expenditure_list %}
            <tr
              class="hover:bg-gradient-to-r hover:from-[#E2F1F9]/50 hover:to-[#B9D8EB]/30 transition-all duration-200 group"
            >
              <td class="py-4 px-6">
                <a
                  href="{% url 'finances:expenditure_detail' expense.slug %}"
                  class="inline-flex items-center gap-2 text-[#7AB2D3] hover:text-[#40657F] font-semibold transition-colors duration-300"
                >
                  <div
                    class="w-8 h-8 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center shadow-sm"
                  >
                    <span class="text-white text-xs font-bold"
                      >{{ expense.outflow_id|slice:":2" }}</span
                    >
                  </div>
                  {{expense.outflow_id}}
                </a>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div
                    class="w-10 h-10 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-full flex items-center justify-center shadow-sm"
                  >
                    <i class="fas fa-receipt text-white text-sm"></i>
                  </div>
                  <div>
                    <div class="font-medium text-[#2C3E50] capitalize">
                      {{expense.description}}
                    </div>
                    <div class="text-xs text-[#40657F]">Expenditure</div>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#E2F1F9] text-[#40657F]"
                >
                  {{expense.term.term_name}}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="text-[#2C3E50] font-medium">{{expense.date}}</div>
                <div class="text-xs text-[#40657F]">Transaction date</div>
              </td>
              <td class="py-4 px-6">
                <div class="text-xl font-bold text-[#F28C8C]">
                  MWK {{ expense.total_amount | intcomma}}
                </div>
                <div class="text-xs text-[#40657F]">Amount spent</div>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-2">
                  <a
                    href="{% url 'finances:expenditure_detail' expense.slug %}"
                    class="inline-flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white text-xs font-medium rounded-lg hover:from-[#40657F] hover:to-[#7AB2D3] transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    <i class="fas fa-eye"></i>
                    View
                  </a>
                  <button
                    class="inline-flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white text-xs font-medium rounded-lg hover:from-[#2C3E50] hover:to-[#40657F] transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    <i class="fas fa-edit"></i>
                    Edit
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-16">
      <div class="flex flex-col items-center gap-6">
        <div
          class="w-24 h-24 bg-gradient-to-br from-[#B9D8EB] to-[#E2F1F9] rounded-full flex items-center justify-center"
        >
          <i class="fas fa-receipt text-[#40657F] text-3xl"></i>
        </div>
        <div>
          <h3 class="font-display font-bold text-2xl text-[#2C3E50] mb-2">
            No Expenditures Found
          </h3>
          <p class="text-[#40657F] text-lg">
            No financial transactions have been recorded yet.
          </p>
          <p class="text-[#40657F] text-sm mt-2">
            Start by creating your first payment record.
          </p>
        </div>
        <a
          href="{% url 'finances:add_expenditure'%}"
          class="bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-semibold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-plus mr-2"></i>
          Create First Payment
        </a>
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Quick Stats Summary -->
  {% if expenditure_list %}
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <div class="card-modern p-6 text-center border-l-4 border-[#7AB2D3]">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-list text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Total Transactions
      </h3>
      <p class="text-3xl font-bold text-[#7AB2D3]">
        {{ expenditure_list|length }}
      </p>
      <p class="text-[#40657F] text-sm">This period</p>
    </div>

    <div class="card-modern p-6 text-center border-l-4 border-[#74C69D]">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-calendar text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        This Month
      </h3>
      <p class="text-3xl font-bold text-[#74C69D]">
        {{ expenditure_list|length }}
      </p>
      <p class="text-[#40657F] text-sm">Recent activity</p>
    </div>

    <div class="card-modern p-6 text-center border-l-4 border-[#F28C8C]">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-chart-bar text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Average Payment
      </h3>
      <p class="text-lg font-bold text-[#F28C8C]">MWK 25,000</p>
      <p class="text-[#40657F] text-sm">Per transaction</p>
    </div>

    <div class="card-modern p-6 text-center border-l-4 border-[#40657F]">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-trending-up text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Growth Rate
      </h3>
      <p class="text-lg font-bold text-[#40657F]">+15.2%</p>
      <p class="text-[#40657F] text-sm">vs last period</p>
    </div>
  </div>
  {% endif %}
</section>
{% endblock %}
