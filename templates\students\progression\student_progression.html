{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}Student Progression | {% endblock %}

{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg icon-float">
        <i class="fas fa-graduation-cap text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
          Student Progression Management
        </h1>
        <div class="w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"></div>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a href="{% url 'students:home' %}" class="text-[#7AB2D3] hover:text-[#40657F] transition-colors">Students</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Student Progression</span>
    </nav>
  </div>

  <!-- Current Academic Year Info -->
  <div class="card-modern p-6 student-info-fade-in">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="font-bold text-lg text-[#2C3E50] mb-2">Current Academic Year</h3>
        {% if current_year %}
        <p class="text-[#40657F]">{{ current_year.name }}</p>
        {% else %}
        <p class="text-[#F28C8C]">No active academic year set</p>
        {% endif %}
      </div>
      <div class="text-right">
        <p class="text-sm text-[#40657F]">Total Active Students</p>
        <p class="text-2xl font-bold text-[#7AB2D3]">{{ total_students }}</p>
      </div>
    </div>
  </div>

  <!-- Progression Form -->
  <div class="card-modern p-8 term-selection-slide-in">
    <h3 class="font-bold text-xl text-[#2C3E50] mb-6 flex items-center gap-3">
      <i class="fas fa-arrow-up text-[#7AB2D3]"></i>
      Progress Students to New Academic Year
    </h3>

    <form id="progressionForm" class="space-y-6">
      {% csrf_token %}
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label for="newAcademicYear" class="block text-sm font-medium text-[#2C3E50] mb-2">
            New Academic Year
          </label>
          <input 
            type="text" 
            id="newAcademicYear" 
            name="academic_year"
            placeholder="e.g., 2024-2025"
            class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl text-[#2C3E50] placeholder:text-[#2C3E50]/60 focus:ring-2 focus:ring-[#7AB2D3] focus:border-[#7AB2D3] transition-colors"
            required
          >
        </div>
        <div class="flex items-end">
          <button 
            type="submit" 
            id="progressBtn"
            class="w-full bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
          >
            <i class="fas fa-arrow-up mr-2"></i>
            Progress All Students
          </button>
        </div>
      </div>
    </form>
  </div>

  <!-- Progression Preview -->
  <div class="card-modern p-8 actions-slide-in">
    <h3 class="font-bold text-xl text-[#2C3E50] mb-6 flex items-center gap-3">
      <i class="fas fa-eye text-[#7AB2D3]"></i>
      Progression Preview
    </h3>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
      {% for level, students in students_by_level.items %}
      <div class="bg-gradient-to-br from-[#E2F1F9] to-[#B9D8EB] rounded-xl p-4 border border-[#B9D8EB]/50">
        <h4 class="font-bold text-[#2C3E50] mb-2">{{ level }}</h4>
        <p class="text-[#40657F] text-sm">{{ students|length }} student{{ students|length|pluralize }}</p>
        <div class="mt-2">
          {% for student in students|slice:":3" %}
          <p class="text-xs text-[#40657F]">• {{ student.name }}</p>
          {% endfor %}
          {% if students|length > 3 %}
          <p class="text-xs text-[#7AB2D3] font-medium">+ {{ students|length|add:"-3" }} more</p>
          {% endif %}
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- Progression Summary -->
    <div class="bg-gradient-to-r from-[#F7FAFC] to-[#E2F1F9] rounded-xl p-6 border border-[#B9D8EB]/50">
      <h4 class="font-bold text-[#2C3E50] mb-4">Expected Progression Results</h4>
      <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div class="text-center">
          <p class="text-2xl font-bold text-[#74C69D]">{{ progression_preview|length }}</p>
          <p class="text-sm text-[#40657F]">Total Students</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-[#7AB2D3]">
            {{passed_students}}
          </p>
          <p class="text-sm text-[#40657F]">Will Progress</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-[#F28C8C]">{{failed_students}}</p>
          <p class="text-sm text-[#40657F]">Will Repeat</p>
        </div>

      </div>
    </div>
  </div>

  <!-- Results Section (Hidden initially) -->
  <div id="resultsSection" class="card-modern p-8 hidden">
    <h3 class="font-bold text-xl text-[#2C3E50] mb-6 flex items-center gap-3">
      <i class="fas fa-check-circle text-[#74C69D]"></i>
      Progression Results
    </h3>
    <div id="resultsContent">
      <!-- Results will be populated here -->
    </div>
  </div>
</section>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
    <div class="text-center">
      <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-[#7AB2D3] mx-auto mb-4"></div>
      <h3 class="text-lg font-bold text-[#2C3E50] mb-2">Processing Student Progression</h3>
      <p class="text-[#40657F]">This may take a few moments...</p>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('progressionForm');
    const loadingModal = document.getElementById('loadingModal');
    const resultsSection = document.getElementById('resultsSection');
    const resultsContent = document.getElementById('resultsContent');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const academicYear = document.getElementById('newAcademicYear').value;
        
        if (!academicYear.trim()) {
            alert('Please enter an academic year');
            return;
        }
        
        // Show loading modal
        loadingModal.classList.remove('hidden');
        
        try {
            const response = await fetch('{% url "students:progress_students" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    academic_year: academicYear
                })
            });
            
            const data = await response.json();
            
            // Hide loading modal
            loadingModal.classList.add('hidden');
            
            if (data.success) {
                // Show results
                displayResults(data.results, data.details);
                resultsSection.classList.remove('hidden');
                resultsSection.scrollIntoView({ behavior: 'smooth' });
            } else {
                alert('Error: ' + data.message);
            }
            
        } catch (error) {
            loadingModal.classList.add('hidden');
            alert('Error: ' + error.message);
        }
    });
    
    function displayResults(results, details) {
        const html = `
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-[#74C69D]/10 rounded-xl p-4 text-center">
                    <p class="text-2xl font-bold text-[#74C69D]">${results.progressed}</p>
                    <p class="text-sm text-[#40657F]">Progressed</p>
                </div>
                <div class="bg-[#F28C8C]/10 rounded-xl p-4 text-center">
                    <p class="text-2xl font-bold text-[#F28C8C]">${results.repeated}</p>
                    <p class="text-sm text-[#40657F]">Repeated</p>
                </div>
                <div class="bg-[#7AB2D3]/10 rounded-xl p-4 text-center">
                    <p class="text-2xl font-bold text-[#7AB2D3]">${results.graduated}</p>
                    <p class="text-sm text-[#40657F]">Graduated</p>
                </div>
                <div class="bg-[#40657F]/10 rounded-xl p-4 text-center">
                    <p class="text-2xl font-bold text-[#40657F]">${results.fee_accounts_created}</p>
                    <p class="text-sm text-[#40657F]">Fee Accounts</p>
                </div>
            </div>
            <div class="bg-[#F7FAFC] rounded-xl p-4">
                <h4 class="font-bold text-[#2C3E50] mb-2">Academic Year: ${results.academic_year}</h4>
                <p class="text-[#40657F]">Total Students Processed: ${results.total_students}</p>
                ${results.progression_errors > 0 ? `<p class="text-[#F28C8C]">Errors: ${results.progression_errors}</p>` : ''}
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>

<style>
  /* Animation styles */
  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: breadcrumbSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  .student-info-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: studentInfoFadeIn 0.8s ease-out 0.8s forwards;
  }

  .term-selection-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: termSelectionSlideIn 0.8s ease-out 1.0s forwards;
  }

  .actions-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionsSlideIn 0.8s ease-out 1.2s forwards;
  }

  @keyframes breadcrumbSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes accentLineGrow {
    to { width: 5rem; }
  }

  @keyframes breadcrumbFadeIn {
    to { opacity: 1; }
  }

  @keyframes studentInfoFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes termSelectionSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes actionsSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }
</style>
{% endblock %}
