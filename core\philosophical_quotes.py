"""
Philosophical Easter Egg - Random Quote Generator
A delightful surprise for curious minds exploring the system
"""

import random

PHILOSOPHICAL_QUOTES = [
    # Immanuel Kant
    {
        "quote": "Two things awe me most, the starry sky above me and the moral law within me.",
        "author": "Immanu<PERSON> Kant",
        "context": "Critique of Practical Reason",
        "theme": "Wonder & Morality"
    },
    {
        "quote": "Act only according to that maxim whereby you can at the same time will that it should become a universal law.",
        "author": "Immanu<PERSON> Kant",
        "context": "Groundwork for the Metaphysics of Morals",
        "theme": "Categorical Imperative"
    },
    {
        "quote": "Enlightenment is man's emergence from his self-imposed nonage.",
        "author": "Immanu<PERSON> Kant",
        "context": "What is Enlightenment?",
        "theme": "Intellectual Freedom"
    },
    {
        "quote": "Happiness is not an ideal of reason, but of imagination.",
        "author": "Immanuel Kant",
        "context": "Groundwork for the Metaphysics of Morals",
        "theme": "Happiness & Reason"
    },
    
    # <PERSON>
    {
        "quote": "Beauty is no quality in things themselves: It exists merely in the mind which contemplates them.",
        "author": "<PERSON> Hume",
        "context": "Of the Standard of Taste",
        "theme": "Aesthetics & Perception"
    },
    {
        "quote": "Reason is, and ought only to be the slave of the passions.",
        "author": "David Hume",
        "context": "A Treatise of Human Nature",
        "theme": "Reason vs Emotion"
    },
    {
        "quote": "Custom, then, is the great guide of human life.",
        "author": "David Hume",
        "context": "An Enquiry Concerning Human Understanding",
        "theme": "Habit & Experience"
    },
    {
        "quote": "The life of man is of no greater importance to the universe than that of an oyster.",
        "author": "David Hume",
        "context": "On Suicide",
        "theme": "Human Significance"
    },
    
    # Life & Existence
    {
        "quote": "The unexamined life is not worth living.",
        "author": "Socrates",
        "context": "Plato's Apology",
        "theme": "Self-Knowledge"
    },
    {
        "quote": "Life must be understood backward. But it must be lived forward.",
        "author": "Søren Kierkegaard",
        "context": "Journals",
        "theme": "Time & Understanding"
    },
    {
        "quote": "Man is condemned to be free; because once thrown into the world, he is responsible for everything he does.",
        "author": "Jean-Paul Sartre",
        "context": "Being and Nothingness",
        "theme": "Freedom & Responsibility"
    },
    {
        "quote": "The meaning of life is to give life meaning.",
        "author": "Viktor Frankl",
        "context": "Man's Search for Meaning",
        "theme": "Purpose & Meaning"
    },
    {
        "quote": "We are what we repeatedly do. Excellence, then, is not an act, but a habit.",
        "author": "Aristotle",
        "context": "Nicomachean Ethics",
        "theme": "Character & Excellence"
    },
    {
        "quote": "The only true wisdom is in knowing you know nothing.",
        "author": "Socrates",
        "context": "Plato's Apology",
        "theme": "Wisdom & Humility"
    },
    {
        "quote": "I think, therefore I am.",
        "author": "René Descartes",
        "context": "Discourse on Method",
        "theme": "Existence & Consciousness"
    },
    {
        "quote": "Hell is other people.",
        "author": "Jean-Paul Sartre",
        "context": "No Exit",
        "theme": "Human Relations"
    },
    {
        "quote": "The cave you fear to enter holds the treasure you seek.",
        "author": "Joseph Campbell",
        "context": "The Hero with a Thousand Faces",
        "theme": "Courage & Growth"
    },
    {
        "quote": "What does not kill me makes me stronger.",
        "author": "Friedrich Nietzsche",
        "context": "Twilight of the Idols",
        "theme": "Resilience & Growth"
    },
    {
        "quote": "The greatest happiness of the greatest number is the foundation of morals and legislation.",
        "author": "Jeremy Bentham",
        "context": "Introduction to the Principles of Morals and Legislation",
        "theme": "Utilitarianism"
    },
    {
        "quote": "To be yourself in a world that is constantly trying to make you something else is the greatest accomplishment.",
        "author": "Ralph Waldo Emerson",
        "context": "Self-Reliance",
        "theme": "Authenticity"
    },
    {
        "quote": "The mind is everything. What you think you become.",
        "author": "Buddha",
        "context": "Dhammapada",
        "theme": "Consciousness & Reality"
    },
    {
        "quote": "In the depth of winter, I finally learned that there was in me an invincible summer.",
        "author": "Albert Camus",
        "context": "Return to Tipasa",
        "theme": "Inner Strength"
    },
    {
        "quote": "The only way to deal with an unfree world is to become so absolutely free that your very existence is an act of rebellion.",
        "author": "Albert Camus",
        "context": "The Myth of Sisyphus",
        "theme": "Freedom & Rebellion"
    },
    {
        "quote": "We suffer more often in imagination than in reality.",
        "author": "Seneca",
        "context": "Letters from a Stoic",
        "theme": "Suffering & Perception"
    },
    {
        "quote": "The good life is one inspired by love and guided by knowledge.",
        "author": "Bertrand Russell",
        "context": "What I Believe",
        "theme": "Love & Wisdom"
    },
    {
        "quote": "Everything we hear is an opinion, not a fact. Everything we see is perspective, not truth.",
        "author": "Marcus Aurelius",
        "context": "Meditations",
        "theme": "Perspective & Truth"
    },
    {
        "quote": "The journey of a thousand miles begins with one step.",
        "author": "Lao Tzu",
        "context": "Tao Te Ching",
        "theme": "Beginning & Progress"
    },
    {
        "quote": "Know thyself.",
        "author": "Ancient Greek Maxim",
        "context": "Inscribed at the Temple of Apollo at Delphi",
        "theme": "Self-Knowledge"
    },
    {
        "quote": "The only constant in life is change.",
        "author": "Heraclitus",
        "context": "Fragments",
        "theme": "Change & Impermanence"
    },
    {
        "quote": "Whereof one cannot speak, thereof one must be silent.",
        "author": "Ludwig Wittgenstein",
        "context": "Tractatus Logico-Philosophicus",
        "theme": "Language & Reality"
    }
]

# Easter egg trigger words
TRIGGER_WORDS = [
    'what is life',
    'kant',
    'hume',
    'philosophy',
    'meaning of life',
    'existence',
    'consciousness',
    'reality',
    'truth',
    'wisdom',
    'enlightenment',
    'categorical imperative',
    'empiricism',
    'rationalism'
]

def get_random_quote():
    """Get a random philosophical quote"""
    return random.choice(PHILOSOPHICAL_QUOTES)

def is_philosophical_search(search_term):
    """Check if search term contains philosophical trigger words"""
    if not search_term:
        return False
    
    search_lower = search_term.lower().strip()
    
    # Check for exact matches and partial matches
    for trigger in TRIGGER_WORDS:
        if trigger in search_lower:
            return True
    
    return False

def get_quote_by_theme(theme=None):
    """Get quotes filtered by theme"""
    if not theme:
        return get_random_quote()
    
    themed_quotes = [q for q in PHILOSOPHICAL_QUOTES if theme.lower() in q['theme'].lower()]
    return random.choice(themed_quotes) if themed_quotes else get_random_quote()

def get_quotes_by_author(author):
    """Get all quotes by a specific author"""
    return [q for q in PHILOSOPHICAL_QUOTES if author.lower() in q['author'].lower()]
