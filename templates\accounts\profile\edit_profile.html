{% extends 'base.html' %}
{% load static %}
{% load rbac_tags %}

{% block title %}Edit Profile | {% endblock %}

{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-2xl flex items-center justify-center shadow-lg icon-float">
        <i class="fas fa-user-edit text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
          Edit My Profile
        </h1>
        <div class="w-20 h-1 bg-gradient-to-r from-[#74C69D] to-[#5fb085] rounded-full accent-line-grow"></div>
        <p class="text-[#40657F] mt-2">Update your personal information</p>
      </div>
    </div>
    
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <a href="{% url 'accounts:user_profile' %}{% if request.GET.from == 'academics' %}?from=academics{% endif %}" class="text-[#40657F] hover:text-[#7AB2D3]">My Account</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Edit Profile</span>
    </nav>
  </div>

  <!-- Form -->
  <div class="card-modern p-8">
    <form method="post" class="space-y-8">
      {% csrf_token %}
      
      <!-- Personal Information -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-user text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">Personal Information</h3>
            <p class="text-[#40657F] text-sm">Update your personal details</p>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              First Name <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.first_name }}
            {% if form.first_name.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.first_name.errors.0 }}</div>
            {% endif %}
          </div>
          
          <div>
            <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Last Name <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.last_name }}
            {% if form.last_name.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.last_name.errors.0 }}</div>
            {% endif %}
          </div>
          
          <div>
            <label for="{{ form.email.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Email Address <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.email }}
            {% if form.email.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.email.errors.0 }}</div>
            {% endif %}
          </div>
          
          <div>
            <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Phone Number
            </label>
            {{ form.phone_number }}
            {% if form.phone_number.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.phone_number.errors.0 }}</div>
            {% endif %}
          </div>
          
          <div class="md:col-span-2">
            <label for="{{ form.gender.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Gender
            </label>
            {{ form.gender }}
            {% if form.gender.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.gender.errors.0 }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Account Information (Read-only) -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-[#9B59B6] to-[#8e44ad] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-lock text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">Account Information</h3>
            <p class="text-[#40657F] text-sm">These details cannot be changed</p>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Username</label>
            <div class="p-3 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#2C3E50] opacity-75">
              {{ user_obj.username }}
            </div>
            <div class="mt-1 text-xs text-[#40657F]">Username cannot be changed</div>
          </div>
          
          <div>
            <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Member Since</label>
            <div class="p-3 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#2C3E50] opacity-75">
              {{ user_obj.date_joined|date:"M d, Y" }}
            </div>
          </div>
        </div>
      </div>

      <!-- Current Roles Display -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-[#FFB84D] to-[#e6a43d] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-user-shield text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">Current Roles</h3>
            <p class="text-[#40657F] text-sm">Your current role assignments</p>
          </div>
        </div>
        
        {% if user_obj.get_active_roles %}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          {% for user_role in user_obj.get_active_roles %}
          <div class="p-4 border border-[#E2F1F9] rounded-xl bg-[#F7FAFC]">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-semibold text-[#2C3E50]">{{ user_role.role.display_name }}</div>
                <div class="text-sm text-[#40657F]">Level {{ user_role.role.level }}</div>
              </div>
              {% if user_role.expires_at %}
                <span class="px-2 py-1 bg-[#FFB84D] text-white rounded text-xs">
                  Expires {{ user_role.expires_at|date:"M d" }}
                </span>
              {% else %}
                <span class="px-2 py-1 bg-[#74C69D] text-white rounded text-xs">
                  Permanent
                </span>
              {% endif %}
            </div>
          </div>
          {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8 bg-[#F7FAFC] rounded-xl border border-[#E2F1F9]">
          <div class="w-16 h-16 bg-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-user-shield text-[#7AB2D3] text-xl"></i>
          </div>
          <h4 class="text-lg font-bold text-[#2C3E50] mb-2">No Active Roles</h4>
          <p class="text-[#40657F]">You have no active role assignments.</p>
        </div>
        {% endif %}
      </div>

      <!-- Security Notice -->
      <div class="p-6 bg-[#E2F1F9] rounded-xl border border-[#B9D8EB]">
        <div class="flex items-start gap-3">
          <i class="fas fa-shield-alt text-[#7AB2D3] mt-1"></i>
          <div>
            <h4 class="font-semibold text-[#2C3E50] mb-2">Security & Privacy</h4>
            <div class="text-sm text-[#40657F] space-y-1">
              <p>• Your profile information is visible to administrators and users with appropriate permissions.</p>
              <p>• To change your password, use the <a href="{% url 'accounts:change_password' %}" class="text-[#7AB2D3] hover:underline font-semibold">Change Password</a> feature.</p>
              <p>• Contact an administrator if you need to change your username or role assignments.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-between pt-6 border-t border-[#E2F1F9]">
        <a href="{% url 'accounts:user_profile' %}" 
           class="px-6 py-3 border border-[#B9D8EB] text-[#40657F] rounded-xl font-semibold hover:bg-[#F7FAFC] transition-colors">
          <i class="fas fa-arrow-left mr-2"></i>Cancel
        </a>
        
        <button type="submit" 
                class="px-8 py-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
          <i class="fas fa-save mr-2"></i>Save Changes
        </button>
      </div>
    </form>
  </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form validation feedback
    const form = document.querySelector('form');
    const submitButton = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving Changes...';
        submitButton.disabled = true;
    });
    
    // Add real-time validation for email
    const emailField = document.getElementById('{{ form.email.id_for_label }}');
    if (emailField) {
        emailField.addEventListener('blur', function() {
            const email = this.value;
            if (email && !email.includes('@')) {
                this.classList.add('border-[#F28C8C]');
            } else {
                this.classList.remove('border-[#F28C8C]');
            }
        });
    }
});
</script>

{% endblock %}
