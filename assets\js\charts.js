/**
 * Charts and Data Visualization JavaScript Module
 * Handles all chart creation and data visualization functionality
 */

class ChartManager {
    constructor() {
        this.charts = new Map();
        this.defaultColors = [
            '#40657F', '#7AB2D3', '#B9D8EB', '#E2F1F9',
            '#F28C8C', '#74C69D', '#2C3E50', '#F7FAFC'
        ];
        this.genderColors = {
            'Male': '#7AB2D3',
            'Female': '#F28C8C'
        };
        this.init();
    }

    init() {
        this.setupChartDefaults();
        this.createCharts();
        this.setupEventListeners();
    }

    setupChartDefaults() {
        if (typeof Chart !== 'undefined') {
            Chart.defaults.font.family = "'Inter', 'Roboto', sans-serif";
            Chart.defaults.color = '#6b7280';
            Chart.defaults.plugins.legend.labels.usePointStyle = true;
            Chart.defaults.plugins.legend.labels.padding = 20;
        }
    }

    createCharts() {
        // Auto-detect and create charts based on canvas elements
        const chartCanvases = document.querySelectorAll('canvas[data-chart-type]');
        
        chartCanvases.forEach(canvas => {
            const chartType = canvas.dataset.chartType;
            const chartId = canvas.id;
            
            if (chartType && chartId) {
                this.createChart(chartId, chartType);
            }
        });
    }

    createChart(canvasId, chartType, data = null, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas with id '${canvasId}' not found`);
            return null;
        }

        const ctx = canvas.getContext('2d');
        
        // Get data from data attributes or use provided data
        const chartData = data || this.getChartDataFromElement(canvas);
        const chartOptions = this.getChartOptions(chartType, options);

        try {
            const chart = new Chart(ctx, {
                type: chartType,
                data: chartData,
                options: chartOptions
            });

            this.charts.set(canvasId, chart);
            return chart;
        } catch (error) {
            console.error(`Error creating chart '${canvasId}':`, error);
            this.showChartError(canvas.parentElement, 'Failed to create chart');
            return null;
        }
    }

    getChartDataFromElement(canvas) {
        // Extract data from data attributes
        const labels = JSON.parse(canvas.dataset.labels || '[]');
        const data = JSON.parse(canvas.dataset.data || '[]');
        const label = canvas.dataset.label || 'Data';
        
        return {
            labels: labels,
            datasets: [{
                label: label,
                data: data,
                backgroundColor: this.getColorsForData(data.length),
                borderColor: this.defaultColors[0],
                borderWidth: 1
            }]
        };
    }

    getChartOptions(chartType, customOptions = {}) {
        const isMobile = window.innerWidth < 768;
        
        const baseOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: !isMobile || chartType === 'pie' || chartType === 'doughnut',
                    position: isMobile ? 'bottom' : 'top'
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#40657F',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: true
                }
            },
            interaction: {
                intersect: !isMobile,
                mode: isMobile ? 'nearest' : 'index'
            }
        };

        // Chart type specific options
        switch (chartType) {
            case 'bar':
            case 'column':
                baseOptions.scales = {
                    x: {
                        ticks: {
                            font: { size: isMobile ? 10 : 12 },
                            maxRotation: isMobile ? 45 : 0
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: { size: isMobile ? 10 : 12 }
                        }
                    }
                };
                break;
                
            case 'line':
                baseOptions.scales = {
                    x: {
                        ticks: {
                            font: { size: isMobile ? 10 : 12 }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: { size: isMobile ? 10 : 12 }
                        }
                    }
                };
                baseOptions.elements = {
                    line: {
                        tension: 0.4
                    },
                    point: {
                        radius: isMobile ? 3 : 4,
                        hoverRadius: isMobile ? 5 : 6
                    }
                };
                break;
                
            case 'pie':
            case 'doughnut':
                baseOptions.plugins.tooltip.callbacks = {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return `${context.label}: ${context.parsed} (${percentage}%)`;
                    }
                };
                break;
        }

        return this.mergeDeep(baseOptions, customOptions);
    }

    getColorsForData(count) {
        const colors = [];
        for (let i = 0; i < count; i++) {
            colors.push(this.defaultColors[i % this.defaultColors.length]);
        }
        return colors;
    }

    // Specific chart creation methods
    createStudentEnrollmentChart(canvasId, data) {
        return this.createChart(canvasId, 'bar', {
            labels: data.labels,
            datasets: [{
                label: 'Student Enrollment',
                data: data.data,
                backgroundColor: this.getColorsForData(data.data.length),
                borderColor: this.defaultColors[0],
                borderWidth: 1
            }]
        });
    }

    createGenderDistributionChart(canvasId, data) {
        return this.createChart(canvasId, 'pie', {
            labels: data.labels,
            datasets: [{
                data: data.data,
                backgroundColor: data.labels.map(label => this.genderColors[label] || this.defaultColors[0]),
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        });
    }

    createRevenueChart(canvasId, data) {
        return this.createChart(canvasId, 'line', {
            labels: data.labels,
            datasets: [{
                label: `Revenue (${data.currency || 'NGN'})`,
                data: data.data,
                borderColor: '#40657F',
                backgroundColor: 'rgba(64, 101, 127, 0.1)',
                fill: true,
                tension: 0.4
            }]
        }, {
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('en-NG', {
                                style: 'currency',
                                currency: data.currency || 'NGN'
                            }).format(value);
                        }
                    }
                }
            }
        });
    }

    createGradeDistributionChart(canvasId, data) {
        const gradeColors = {
            'A': '#74C69D',
            'B': '#7AB2D3', 
            'C': '#B9D8EB',
            'D': '#F28C8C',
            'F': '#FF6B6B'
        };

        return this.createChart(canvasId, 'bar', {
            labels: data.labels,
            datasets: [{
                label: 'Number of Students',
                data: data.data,
                backgroundColor: data.labels.map(label => gradeColors[label] || this.defaultColors[0]),
                borderColor: '#ffffff',
                borderWidth: 1
            }]
        });
    }

    // Chart update methods
    updateChart(chartId, newData) {
        const chart = this.charts.get(chartId);
        if (!chart) {
            console.error(`Chart '${chartId}' not found`);
            return;
        }

        chart.data = newData;
        chart.update('active');
    }

    updateChartData(chartId, newData, datasetIndex = 0) {
        const chart = this.charts.get(chartId);
        if (!chart) {
            console.error(`Chart '${chartId}' not found`);
            return;
        }

        if (chart.data.datasets[datasetIndex]) {
            chart.data.datasets[datasetIndex].data = newData;
            chart.update('active');
        }
    }

    // Chart export functionality
    exportChart(chartId, format = 'png') {
        const chart = this.charts.get(chartId);
        if (!chart) {
            console.error(`Chart '${chartId}' not found`);
            return;
        }

        const canvas = chart.canvas;
        const url = canvas.toDataURL(`image/${format}`);
        
        const link = document.createElement('a');
        link.download = `chart-${chartId}.${format}`;
        link.href = url;
        link.click();
    }

    // Error handling
    showChartError(container, message) {
        container.innerHTML = `
            <div class="chart-error">
                <div class="chart-error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="chart-error-message">${message}</div>
                <button class="chart-retry-button" onclick="location.reload()">
                    Retry
                </button>
            </div>
        `;
    }

    showChartLoading(container) {
        container.innerHTML = `
            <div class="chart-loading">
                <div class="chart-loading-spinner"></div>
                Loading chart...
            </div>
        `;
    }

    // Event listeners
    setupEventListeners() {
        // Chart export buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.chart-export-btn')) {
                const chartId = e.target.dataset.chartId;
                const format = e.target.dataset.format || 'png';
                this.exportChart(chartId, format);
            }
        });

        // Chart filter changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.chart-filter-select')) {
                const chartId = e.target.dataset.chartId;
                const filterValue = e.target.value;
                this.handleChartFilter(chartId, filterValue);
            }
        });

        // Window resize
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
    }

    handleChartFilter(chartId, filterValue) {
        // Implement chart filtering logic
        console.log(`Filtering chart ${chartId} with value: ${filterValue}`);
        // This would typically involve fetching new data and updating the chart
    }

    handleResize() {
        this.charts.forEach((chart, chartId) => {
            chart.resize();
        });
    }

    // Utility methods
    mergeDeep(target, source) {
        const output = Object.assign({}, target);
        if (this.isObject(target) && this.isObject(source)) {
            Object.keys(source).forEach(key => {
                if (this.isObject(source[key])) {
                    if (!(key in target))
                        Object.assign(output, { [key]: source[key] });
                    else
                        output[key] = this.mergeDeep(target[key], source[key]);
                } else {
                    Object.assign(output, { [key]: source[key] });
                }
            });
        }
        return output;
    }

    isObject(item) {
        return item && typeof item === 'object' && !Array.isArray(item);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Public API methods
    getChart(chartId) {
        return this.charts.get(chartId);
    }

    destroyChart(chartId) {
        const chart = this.charts.get(chartId);
        if (chart) {
            chart.destroy();
            this.charts.delete(chartId);
        }
    }

    destroyAllCharts() {
        this.charts.forEach((chart, chartId) => {
            chart.destroy();
        });
        this.charts.clear();
    }
}

// Dashboard statistics updater
class DashboardStats {
    constructor() {
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.updateStats();
        // Auto-refresh every 5 minutes
        this.updateInterval = setInterval(() => {
            this.updateStats();
        }, 5 * 60 * 1000);
    }

    async updateStats() {
        try {
            const response = await fetch('/api/dashboard-analytics/');
            const data = await response.json();
            
            if (data.statistics) {
                this.updateStatCards(data.statistics);
            }
            
            if (data.recent_activities) {
                this.updateRecentActivities(data.recent_activities);
            }
        } catch (error) {
            console.error('Failed to update dashboard stats:', error);
        }
    }

    updateStatCards(stats) {
        // Update total students
        const totalStudentsEl = document.getElementById('totalStudents');
        if (totalStudentsEl) {
            totalStudentsEl.textContent = stats.total_students.toLocaleString();
        }

        // Update total revenue
        const totalRevenueEl = document.getElementById('totalRevenue');
        if (totalRevenueEl) {
            totalRevenueEl.textContent = new Intl.NumberFormat('en-NG', {
                style: 'currency',
                currency: 'NGN'
            }).format(stats.total_revenue);
        }

        // Update outstanding balance
        const outstandingBalanceEl = document.getElementById('outstandingBalance');
        if (outstandingBalanceEl) {
            outstandingBalanceEl.textContent = new Intl.NumberFormat('en-NG', {
                style: 'currency',
                currency: 'NGN'
            }).format(stats.outstanding_balance);
        }

        // Update average performance
        const avgPerformanceEl = document.getElementById('avgPerformance');
        if (avgPerformanceEl) {
            avgPerformanceEl.textContent = `${stats.average_performance}%`;
        }
    }

    updateRecentActivities(activities) {
        // Update recent receipts
        const recentReceiptsEl = document.getElementById('recentReceipts');
        if (recentReceiptsEl && activities.receipts) {
            recentReceiptsEl.innerHTML = activities.receipts.map(receipt => `
                <div class="flex justify-between items-center py-2">
                    <div>
                        <div class="font-medium">${receipt.student}</div>
                        <div class="text-sm text-gray-500">${receipt.date}</div>
                    </div>
                    <div class="font-semibold">₦${receipt.amount.toLocaleString()}</div>
                </div>
            `).join('');
        }

        // Update recent admissions
        const recentAdmissionsEl = document.getElementById('recentAdmissions');
        if (recentAdmissionsEl && activities.admissions) {
            recentAdmissionsEl.innerHTML = activities.admissions.map(student => `
                <div class="flex justify-between items-center py-2">
                    <div>
                        <div class="font-medium">${student.name}</div>
                        <div class="text-sm text-gray-500">${student.level}</div>
                    </div>
                    <div class="text-sm text-gray-500">${student.student_id}</div>
                </div>
            `).join('');
        }
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize chart manager
    window.chartManager = new ChartManager();
    
    // Initialize dashboard stats if on dashboard page
    if (document.querySelector('.stats-grid')) {
        window.dashboardStats = new DashboardStats();
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ChartManager,
        DashboardStats
    };
}
