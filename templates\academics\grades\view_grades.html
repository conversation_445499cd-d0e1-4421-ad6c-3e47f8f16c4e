{% extends 'academics/base.html' %} {% load static %}

{% block title %}View Grades | {% endblock %}

{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6">
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl icon-float">
          <i class="fas fa-graduation-cap text-white text-lg sm:text-xl icon-pulse"></i>
        </div>
        <div>
          <h1 class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in">
            View Grades
          </h1>
          <p class="text-[#40657F] text-base sm:text-lg font-medium mt-1 subtitle-fade-in">
            Student performance overview by subject and assessment
          </p>
          <div class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2 accent-line-grow"></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3 action-buttons-slide-in">
        <a
          href="{% url 'academics:enter_by_level' %}"
          class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i class="fas fa-edit mr-2 group-hover:scale-110 transition-transform duration-300"></i>
          Enter Grades
        </a>
        <a
          href=""
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#7AB2D3] hover:text-white focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i class="fas fa-file-alt mr-2 group-hover:translate-y-[-2px] transition-transform duration-300"></i>
          Generate Reports
        </a>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200 breadcrumb-fade-in">
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#40657F]">Performance</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">View Grades</span>
    </nav>
  </div>

  <!-- Filter Section -->
  <div class="card-modern p-8 filter-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg">
        <i class="fas fa-filter text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">Grade Filters</h2>
        <p class="text-[#40657F] text-sm">Filter grades by assessment, class, and term</p>
      </div>
      <div class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"></div>
    </div>

    <form method="get" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Assessment Type Filter -->
        <div class="form-field space-y-3">
          <label for="assessment_type" class="flex items-center gap-3 font-bold text-[#2C3E50] text-sm">
            <div class="w-5 h-5 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-lg flex items-center justify-center shadow-sm">
              <i class="fas fa-clipboard-check text-white text-xs"></i>
            </div>
            Assessment Type
          </label>
          <select
            name="assessment_type"
            id="assessment_type"
            class="w-full pl-4 pr-10 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 bg-white text-[#2C3E50] shadow-sm hover:shadow-md"
          >
            <option value="">Select Assessment Type</option>
            {% for assessment in assessment_types %}
            <option value="{{ assessment.id }}" {% if assessment.id == selected_assessment_type %}selected{% endif %}>{{ assessment.name }}</option>
            {% endfor %}
          </select>
        </div>

        <!-- Class Level Filter -->
        <div class="form-field space-y-3">
          <label for="level" class="flex items-center gap-3 font-bold text-[#2C3E50] text-sm">
            <div class="w-5 h-5 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center shadow-sm">
              <i class="fas fa-school text-white text-xs"></i>
            </div>
            Class Level
          </label>
          <select
            name="level"
            id="level"
            class="w-full pl-4 pr-10 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 bg-white text-[#2C3E50] shadow-sm hover:shadow-md"
          >
            <option value="">Select Class Level</option>
            {% for level in levels %}
            <option value="{{ level.id }}" {% if level.id == selected_level %}selected{% endif %}>{{ level.level_name }}</option>
            {% endfor %}
          </select>
        </div>

        <!-- Term Filter -->
        <div class="form-field space-y-3">
          <label for="term" class="flex items-center gap-3 font-bold text-[#2C3E50] text-sm">
            <div class="w-5 h-5 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-lg flex items-center justify-center shadow-sm">
              <i class="fas fa-calendar text-white text-xs"></i>
            </div>
            Academic Term
          </label>
          <select
            name="term"
            id="term"
            class="w-full pl-4 pr-10 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 bg-white text-[#2C3E50] shadow-sm hover:shadow-md"
          >
            <option value="">Select Academic Term</option>
            {% for term in terms %}
            <option value="{{ term.id }}" {% if term.id == selected_term %}selected{% endif %}>{{ term }}</option>
            {% endfor %}
          </select>
        </div>
      </div>

      <!-- Filter Actions -->
      <div class="flex flex-col sm:flex-row gap-4 justify-between pt-6 border-t border-[#B9D8EB]">
        <div class="flex items-center gap-4">
          <span class="text-sm text-[#40657F] font-medium">
            {% if student_data %}
              Showing {{ student_data|length }} student{{ student_data|length|pluralize }} with grades
            {% else %}
              Use filters to view student grades
            {% endif %}
          </span>
        </div>

        <div class="flex gap-4">
          <button
            type="button"
            onclick="clearFilters()"
            class="bg-[#E2F1F9] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#B9D8EB] hover:text-[#2C3E50] focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
          >
            <i class="fas fa-times mr-2 group-hover:rotate-90 transition-transform duration-300"></i>
            Clear Filters
          </button>
          <button
            type="submit"
            class="bg-[#7AB2D3] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
          >
            <i class="fas fa-search mr-2 group-hover:scale-110 transition-transform duration-300"></i>
            Apply Filters
          </button>
        </div>
      </div>
    </form>
  </div>

  <!-- Results Section -->
  {% if student_data %}
  <div class="card-modern p-8 results-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg">
        <i class="fas fa-chart-line text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">Grades Overview</h2>
        <p class="text-[#40657F] text-sm">{{ student_data|length }} student{{ student_data|length|pluralize }} with performance data</p>
      </div>
      <div class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"></div>
    </div>

    <div class="overflow-x-auto">
      <div class="table-modern">
        <table class="min-w-full">
          <thead>
            <tr class="bg-[#E2F1F9] border-b border-[#B9D8EB]">
              <th class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm">#</th>
              <th class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm">Student</th>
              {% for subject in subjects %}
              <th class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm">
                <div class="flex flex-col items-center gap-1">
                  <span class="font-bold">{{ subject.abbrv }}</span>
                  <span class="text-xs text-[#40657F] font-normal">{{ subject.name|truncatechars:15 }}</span>
                </div>
              </th>
              {% endfor %}
              <th class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm">Average</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#B9D8EB]">
            {% for student_id, data in student_data.items %}
            <tr class="hover:bg-[#E2F1F9]/50 transition-all duration-200 group student-row">
              <td class="py-4 px-6">
                <div class="w-8 h-8 bg-[#7AB2D3] rounded-lg flex items-center justify-center shadow-sm">
                  <span class="text-white text-xs font-bold">{{ forloop.counter }}</span>
                </div>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-[#40657F] rounded-full flex items-center justify-center shadow-sm">
                    <span class="text-white text-sm font-bold">{{ data.student.name|first|upper }}</span>
                  </div>
                  <div>
                    <div class="font-medium text-[#2C3E50] capitalize">{{ data.student.name }}</div>
                    <div class="text-xs text-[#40657F]">Student ID: {{ data.student.student_id }}</div>
                  </div>
                </div>
              </td>
              {% for grade in data.grades_list %}
                <td class="py-4 px-6 text-center">
                  {% if grade.score %}
                    <div class="flex flex-col items-center gap-1">
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold
                        {% if grade.score >= 80 %}bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30
                        {% elif grade.score >= 60 %}bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30
                        {% else %}bg-[#F28C8C]/30 text-[#F28C8C] border border-[#F28C8C]/50{% endif %}">
                        {{ grade.score }}%
                      </span>
                      <div class="w-12 h-1 bg-gray-200 rounded-full overflow-hidden">
                        <div class="h-full rounded-full transition-all duration-500
                          {% if grade.score >= 80 %}bg-[#74C69D]
                          {% elif grade.score >= 60 %}bg-[#F28C8C]
                          {% else %}bg-[#F28C8C]{% endif %}"
                          style="width: {{ grade.score }}%">
                        </div>
                      </div>
                    </div>
                  {% else %}
                    <div class="flex flex-col items-center gap-1">
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-200 text-gray-600">
                        <i class="fas fa-minus text-xs"></i>
                      </span>
                      <div class="text-xs text-gray-400">No grade</div>
                    </div>
                  {% endif %}
                </td>
              {% endfor %}
              <td class="py-4 px-6 text-center">
                {% if data.average %}
                  <div class="flex flex-col items-center gap-1">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold
                      {% if data.average >= 80 %}bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30
                      {% elif data.average >= 60 %}bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30
                      {% else %}bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30{% endif %}">
                      {{ data.average|floatformat:1 }}%
                    </span>
                    <div class="text-xs text-[#40657F] font-medium">
                      {% if data.average >= 80 %}Excellent
                      {% elif data.average >= 60 %}Good
                      {% else %}Needs Improvement{% endif %}
                    </div>
                  </div>
                {% else %}
                  <span class="text-gray-400 text-sm">-</span>
                {% endif %}
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="{{ subjects|length|add:'3' }}" class="py-16 text-center">
                <div class="flex flex-col items-center gap-6">
                  <div class="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                    <i class="fas fa-graduation-cap text-gray-400 text-2xl"></i>
                  </div>
                  <div>
                    <h3 class="font-display font-bold text-xl text-gray-800 mb-2">No Grades Found</h3>
                    <p class="text-gray-600">No grades match your current filter criteria.</p>
                    <p class="text-gray-500 text-sm mt-2">Try adjusting your filters or enter grades first.</p>
                  </div>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Grade Statistics -->
    {% if student_data %}
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-gradient-to-r from-[#74C69D]/10 to-[#5fb085]/10 rounded-xl p-4 border border-[#74C69D]/20">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-[#74C69D] rounded-lg flex items-center justify-center">
            <i class="fas fa-trophy text-white text-sm"></i>
          </div>
          <div>
            <div class="text-sm text-[#40657F] font-medium">Excellent Performance</div>
            <div class="text-xl font-bold text-[#74C69D]">{{ excellent_count|default:0 }}</div>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-[#7AB2D3]/10 to-[#40657F]/10 rounded-xl p-4 border border-[#7AB2D3]/20">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-[#7AB2D3] rounded-lg flex items-center justify-center">
            <i class="fas fa-thumbs-up text-white text-sm"></i>
          </div>
          <div>
            <div class="text-sm text-[#40657F] font-medium">Good Performance</div>
            <div class="text-xl font-bold text-[#7AB2D3]">{{ good_count|default:0 }}</div>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-[#F28C8C]/10 to-[#e07575]/10 rounded-xl p-4 border border-[#F28C8C]/20">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-[#F28C8C] rounded-lg flex items-center justify-center">
            <i class="fas fa-chart-line text-white text-sm"></i>
          </div>
          <div>
            <div class="text-sm text-[#40657F] font-medium">Needs Improvement</div>
            <div class="text-xl font-bold text-[#F28C8C]">{{ needs_improvement_count|default:0 }}</div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
  {% else %}
  <div class="card-modern p-12 text-center empty-state-fade-in">
    <div class="flex flex-col items-center gap-6">
      <div class="w-24 h-24 bg-gradient-to-br from-[#B9D8EB] to-[#7AB2D3] rounded-full flex items-center justify-center">
        <i class="fas fa-search text-white text-3xl"></i>
      </div>
      <div>
        <h3 class="font-display font-bold text-2xl text-gray-800 mb-2">Ready to View Grades</h3>
        <p class="text-gray-600 text-lg">Select your filters above to display student grades.</p>
        <p class="text-gray-500 text-sm mt-2">Choose assessment type, class level, and term to get started.</p>
      </div>
      <div class="flex gap-4">
        <button
          onclick="document.getElementById('assessment_type').focus()"
          class="bg-[#7AB2D3] text-white font-semibold py-2 px-4 rounded-lg hover:bg-[#40657F] transition-all duration-300"
        >
          Start Filtering
        </button>
      </div>
    </div>
  </div>
  {% endif %}
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Section Animations */
  .filter-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: filterSectionFadeIn 0.8s ease-out 1.0s forwards;
  }

  .results-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: resultsSectionFadeIn 0.8s ease-out 1.2s forwards;
  }

  .empty-state-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: emptyStateFadeIn 0.8s ease-out 1.2s forwards;
  }

  .student-row {
    opacity: 0;
    transform: translateY(20px);
    animation: studentRowSlideIn 0.4s ease-out forwards;
  }

  .student-row:nth-child(1) { animation-delay: 1.4s; }
  .student-row:nth-child(2) { animation-delay: 1.5s; }
  .student-row:nth-child(3) { animation-delay: 1.6s; }
  .student-row:nth-child(4) { animation-delay: 1.7s; }
  .student-row:nth-child(5) { animation-delay: 1.8s; }
  .student-row:nth-child(6) { animation-delay: 1.9s; }
  .student-row:nth-child(7) { animation-delay: 2.0s; }
  .student-row:nth-child(8) { animation-delay: 2.1s; }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 5rem; }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes breadcrumbFadeIn {
    to { opacity: 1; }
  }

  @keyframes filterSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes resultsSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes emptyStateFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes studentRowSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .student-row {
      animation-delay: 1.2s;
    }

    .student-row:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--row-index, 1));
    }
  }
</style>

<script>
  function clearFilters() {
    document.getElementById('assessment_type').value = '';
    document.getElementById('level').value = '';
    document.getElementById('term').value = '';
  }
</script>

{% endblock %}
