{% extends 'base.html' %} {% load static %}
<!-- title -->
{% block title %}{{ title }} | {% endblock %}
<!-- body -->
{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex items-center gap-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-xl icon-float"
      >
        <i class="fas fa-undo text-white text-2xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
        >
          {{ title }}
        </h1>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
  </div>

  <!-- Warning Section -->
  <div
    class="bg-gradient-to-r from-[#F28C8C]/10 to-[#e07575]/10 border-l-4 border-[#F28C8C] rounded-xl p-6"
  >
    <div class="flex items-start gap-4">
      <div
        class="w-8 h-8 bg-[#F28C8C] rounded-full flex items-center justify-center flex-shrink-0"
      >
        <i class="fas fa-exclamation-triangle text-white text-sm"></i>
      </div>
      <div>
        <h3 class="font-bold text-[#2C3E50] mb-2">Important Notice</h3>
        <p class="text-[#40657F] mb-2">
          You are about to reverse receipt
          <strong>{{ receipt.receipt_number }}</strong>. This action will:
        </p>
        <ul class="text-[#40657F] space-y-1 ml-4">
          <li>• Create a reversal journal entry</li>
          <li>• Lock the original journal entry</li>
          <li>• Mark this receipt as reversed</li>
          <li>• Update student's fee account balance</li>
          <li>• This action cannot be undone</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Receipt Details -->
  <div class="card-modern p-8 receipt-details-fade-in">
    <h2 class="font-bold text-xl text-[#2C3E50] mb-6 flex items-center gap-3">
      <i class="fas fa-receipt text-[#7AB2D3] section-icon-float"></i>
      Receipt Details
    </h2>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="space-y-4">
        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Receipt Number</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            {{ receipt.receipt_number }}
          </p>
        </div>

        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Student</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            {{ receipt.student.name }}
          </p>
        </div>

        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Fee Category</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            {{ receipt.fee_account.category.name }}
          </p>
        </div>
      </div>

      <div class="space-y-4">
        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Date</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">{{ receipt.date }}</p>
        </div>

        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Amount Paid</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            MWK {{ receipt.amount_paid|floatformat:0 }}
          </p>
        </div>

        <div class="bg-[#E2F1F9] rounded-xl p-4">
          <label
            class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
            >Student ID</label
          >
          <p class="text-lg font-bold text-[#2C3E50]">
            {{ receipt.student.student_id }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Reversal Form -->
  <div class="card-modern p-8 form-slide-in">
    <h2 class="font-bold text-xl text-[#2C3E50] mb-6 flex items-center gap-3">
      <i class="fas fa-edit text-[#7AB2D3] section-icon-float"></i>
      Reversal Reason
    </h2>

    <form method="POST" class="space-y-6">
      {% csrf_token %}

      <div>
        <label
          for="{{ form.reason.id_for_label }}"
          class="block text-sm font-bold text-[#2C3E50] mb-2"
        >
          {{ form.reason.label }}
        </label>
        {{ form.reason }} {% if form.reason.help_text %}
        <p class="text-sm text-[#40657F] mt-2">{{ form.reason.help_text }}</p>
        {% endif %} {% if form.reason.errors %}
        <div class="text-[#F28C8C] text-sm mt-2">
          {% for error in form.reason.errors %}
          <p>{{ error }}</p>
          {% endfor %}
        </div>
        {% endif %}
      </div>

      <div class="flex gap-4 pt-6 buttons-slide-in">
        <button
          type="submit"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-bold py-4 px-8 rounded-xl hover:from-[#e07575] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl group"
        >
          <i
            class="fas fa-undo group-hover:rotate-180 transition-all duration-300"
          ></i>
          <span>Reverse Receipt</span>
        </button>

        <a
          href="{% url 'students:student_details' receipt.student.student_id %}"
          class="inline-flex items-center gap-3 bg-[#B9D8EB] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:bg-[#7AB2D3] hover:text-white focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl group"
        >
          <i
            class="fas fa-arrow-left group-hover:-translate-x-1 transition-all duration-300"
          ></i>
          <span>Cancel</span>
        </a>
      </div>
    </form>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .receipt-details-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: receiptDetailsFadeIn 0.8s ease-out 0.8s forwards;
  }

  .form-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: formSlideIn 0.8s ease-out 1s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: buttonsSlideIn 0.8s ease-out 1.2s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 3s ease-in-out infinite;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes receiptDetailsFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes formSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes buttonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes sectionIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(3deg);
    }
  }
</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Add confirmation before form submission
    const form = document.querySelector("form");
    form.addEventListener("submit", function (e) {
      const confirmed = confirm(
        "Are you sure you want to reverse this receipt? This action cannot be undone."
      );
      if (!confirmed) {
        e.preventDefault();
      }
    });
  });
</script>
{% endblock %}
