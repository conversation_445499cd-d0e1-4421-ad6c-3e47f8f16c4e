from functools import wraps
from django.http import HttpResponseForbidden, JsonResponse
from django.shortcuts import redirect
from django.urls import reverse
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied


def require_permission(*permissions):
    """
    Decorator that requires user to have at least one of the specified permissions.
    
    Usage:
        @require_permission('view_students', 'manage_students')
        def my_view(request):
            # View code here
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            # Superusers have all permissions
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # Check if user has any of the required permissions
            user_has_permission = any(
                request.user.has_permission(perm) for perm in permissions
            )
            
            if not user_has_permission:
                return _handle_permission_denied(
                    request, 
                    f"Requires one of these permissions: {', '.join(permissions)}"
                )
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def require_role(*roles):
    """
    Decorator that requires user to have at least one of the specified roles.
    
    Usage:
        @require_role('teacher', 'administrator')
        def my_view(request):
            # View code here
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            # Superusers bypass role checks
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # Check if user has any of the required roles
            user_has_role = any(
                request.user.has_role(role) for role in roles
            )
            
            if not user_has_role:
                return _handle_permission_denied(
                    request,
                    f"Requires one of these roles: {', '.join(roles)}"
                )
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def require_role_level(min_level):
    """
    Decorator that requires user to have a minimum role level.
    
    Usage:
        @require_role_level(3)  # Requires Academic Coordinator level or higher
        def my_view(request):
            # View code here
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            # Superusers bypass level checks
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            user_level = request.user.get_highest_role_level()
            
            if user_level < min_level:
                return _handle_permission_denied(
                    request,
                    f"Requires role level {min_level} or higher (you have level {user_level})"
                )
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def student_required(view_func):
    """
    Decorator that requires user to be a student.
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_student() and not request.user.is_superuser:
            return _handle_permission_denied(request, "Student access required")
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def teacher_required(view_func):
    """
    Decorator that requires user to be a teacher or higher.
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if not (request.user.is_teacher() or request.user.is_admin()) and not request.user.is_superuser:
            return _handle_permission_denied(request, "Teacher access required")
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def admin_required(view_func):
    """
    Decorator that requires user to be an administrator.
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_admin() and not request.user.is_superuser:
            return _handle_permission_denied(request, "Administrator access required")
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def staff_or_permission_required(*permissions):
    """
    Decorator that allows access if user is staff OR has specific permissions.
    This is useful for backward compatibility with existing is_staff checks.
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            # Allow if user is staff (backward compatibility)
            if request.user.is_staff or request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # Check permissions
            if permissions:
                user_has_permission = any(
                    request.user.has_permission(perm) for perm in permissions
                )
                if user_has_permission:
                    return view_func(request, *args, **kwargs)
            
            return _handle_permission_denied(
                request,
                f"Requires staff status or one of these permissions: {', '.join(permissions)}"
            )
        return _wrapped_view
    return decorator


def api_require_permission(*permissions):
    """
    Decorator for API views that requires specific permissions.
    Returns JSON responses instead of redirects.
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({
                    'error': 'Authentication required',
                    'code': 'AUTHENTICATION_REQUIRED'
                }, status=401)
            
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            user_has_permission = any(
                request.user.has_permission(perm) for perm in permissions
            )
            
            if not user_has_permission:
                return JsonResponse({
                    'error': f'Requires one of these permissions: {", ".join(permissions)}',
                    'code': 'INSUFFICIENT_PERMISSIONS'
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def _handle_permission_denied(request, message):
    """
    Handle permission denied scenarios.
    Returns appropriate response based on request type.
    """
    # For API requests, return JSON
    if request.path.startswith('/api/') or request.headers.get('Accept') == 'application/json':
        return JsonResponse({
            'error': message,
            'code': 'INSUFFICIENT_PERMISSIONS'
        }, status=403)
    
    # For web requests, add message and redirect
    messages.error(request, f"Access denied: {message}")
    
    # Redirect based on user's role level
    if hasattr(request.user, 'get_highest_role_level'):
        role_level = request.user.get_highest_role_level()
        if role_level >= 5:  # Administrator
            return redirect('students:home')
        elif role_level == 4 or request.user.has_permission('manage_finances'):  # Finance Manager
            return redirect('finances:expenditures')
        elif role_level >= 2:  # Teacher/Academic Coordinator
            return redirect('academics:dashboard')
        else:  # Student or lower
            return redirect('accounts:login')
    
    return redirect('accounts:login')


# Convenience decorators for common permission combinations
def can_manage_students(view_func):
    """Decorator for views that require student management permissions"""
    return require_permission('view_students', 'add_student', 'edit_student', 'manage_students')(view_func)


def can_manage_finances(view_func):
    """Decorator for views that require finance management permissions"""
    return require_permission('view_finances', 'manage_finances', 'manage_fee_collection')(view_func)


def can_manage_academics(view_func):
    """Decorator for views that require academic management permissions"""
    return require_permission('view_academics', 'manage_academics', 'manage_assessments')(view_func)


def can_view_reports(view_func):
    """Decorator for views that require report viewing permissions"""
    return require_permission('view_reports', 'view_financial_reports', 'view_academic_reports')(view_func)
