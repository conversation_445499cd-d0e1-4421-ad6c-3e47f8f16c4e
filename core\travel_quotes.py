"""
Travel/Adventure Easter Egg - Random Travel & Adventure Quote Generator
A delightful surprise for wanderers and adventure seekers
"""

import random

TRAVEL_QUOTES = [
    # Classic Travel Wisdom
    {
        "quote": "The world is a book and those who do not travel read only one page.",
        "author": "Saint Augustine",
        "context": "Ancient Philosopher",
        "theme": "Knowledge Through Travel",
        "category": "Classic Wisdom"
    },
    {
        "quote": "Not all those who wander are lost.",
        "author": "<PERSON><PERSON><PERSON><PERSON>",
        "context": "The Lord of the Rings",
        "theme": "Purpose in Wandering",
        "category": "Literary Adventure"
    },
    {
        "quote": "Travel is the only thing you buy that makes you richer.",
        "author": "Anonymous",
        "context": "Modern Travel Wisdom",
        "theme": "Wealth of Experience",
        "category": "Life Philosophy"
    },
    {
        "quote": "Adventure is worthwhile in itself.",
        "author": "Amelia <PERSON>hart",
        "context": "Aviation Pioneer",
        "theme": "Value of Adventure",
        "category": "Exploration"
    },
    {
        "quote": "To travel is to live.",
        "author": "<PERSON>",
        "context": "Danish Author",
        "theme": "Life Through Travel",
        "category": "Life Philosophy"
    },
    {
        "quote": "We travel, initially, to lose ourselves; and we travel, next, to find ourselves.",
        "author": "Pico Iyer",
        "context": "Travel Writer",
        "theme": "Self-Discovery",
        "category": "Personal Growth"
    },
    {
        "quote": "The journey not the arrival matters.",
        "author": "T.S. Eliot",
        "context": "Poet & Playwright",
        "theme": "Process Over Destination",
        "category": "Journey Philosophy"
    },
    {
        "quote": "Travel makes one modest. You see what a tiny place you occupy in the world.",
        "author": "Gustave Flaubert",
        "context": "French Novelist",
        "theme": "Humility & Perspective",
        "category": "Personal Growth"
    },
    {
        "quote": "Adventure is not outside man; it is within.",
        "author": "George Eliot",
        "context": "English Novelist",
        "theme": "Inner Adventure",
        "category": "Self-Discovery"
    },
    {
        "quote": "Life is either a daring adventure or nothing at all.",
        "author": "Helen Keller",
        "context": "Author & Activist",
        "theme": "Courage in Living",
        "category": "Life Philosophy"
    },
    {
        "quote": "The real voyage of discovery consists not in seeking new landscapes, but in having new eyes.",
        "author": "Marcel Proust",
        "context": "French Novelist",
        "theme": "Perspective & Vision",
        "category": "Discovery"
    },
    {
        "quote": "Travel far enough, you meet yourself.",
        "author": "David Mitchell",
        "context": "Cloud Atlas",
        "theme": "Self-Encounter",
        "category": "Self-Discovery"
    },
    {
        "quote": "Once a year, go someplace you've never been before.",
        "author": "Dalai Lama",
        "context": "Spiritual Leader",
        "theme": "Annual Adventure",
        "category": "Life Advice"
    },
    {
        "quote": "Travel is fatal to prejudice, bigotry, and narrow-mindedness.",
        "author": "Mark Twain",
        "context": "American Author",
        "theme": "Open-Mindedness",
        "category": "Social Understanding"
    },
    {
        "quote": "A journey is best measured in friends, rather than miles.",
        "author": "Tim Cahill",
        "context": "Travel Writer",
        "theme": "Relationships & Connection",
        "category": "Human Connection"
    },
    {
        "quote": "The world is big and I want to have a good look at it before it gets dark.",
        "author": "John Muir",
        "context": "Naturalist & Explorer",
        "theme": "Urgency of Exploration",
        "category": "Nature & Adventure"
    },
    {
        "quote": "Travel is about the gorgeous feeling of teetering in the unknown.",
        "author": "Anthony Bourdain",
        "context": "Chef & Travel Host",
        "theme": "Embracing Unknown",
        "category": "Adventure Spirit"
    },
    {
        "quote": "I haven't been everywhere, but it's on my list.",
        "author": "Susan Sontag",
        "context": "Writer & Filmmaker",
        "theme": "Wanderlust Dreams",
        "category": "Travel Aspirations"
    },
    {
        "quote": "Traveling – it leaves you speechless, then turns you into a storyteller.",
        "author": "Ibn Battuta",
        "context": "Medieval Explorer",
        "theme": "Stories & Experience",
        "category": "Cultural Exchange"
    },
    {
        "quote": "Adventure awaits, but only if you're willing to leave your comfort zone.",
        "author": "Anonymous",
        "context": "Modern Adventure Philosophy",
        "theme": "Comfort Zone Challenge",
        "category": "Personal Growth"
    },
    {
        "quote": "The gladdest moment in human life is a departure into unknown lands.",
        "author": "Sir Richard Burton",
        "context": "British Explorer",
        "theme": "Joy of Departure",
        "category": "Exploration"
    },
    {
        "quote": "Travel is the antidote to ignorance.",
        "author": "Anonymous",
        "context": "Modern Travel Wisdom",
        "theme": "Education Through Travel",
        "category": "Learning"
    },
    {
        "quote": "Every destination has a story. Every journey writes a new chapter.",
        "author": "Anonymous",
        "context": "Contemporary Travel Philosophy",
        "theme": "Stories & Narratives",
        "category": "Life Stories"
    },
    {
        "quote": "Wanderlust: a strong desire to travel and explore the world.",
        "author": "Dictionary Definition",
        "context": "German Origin",
        "theme": "Travel Desire",
        "category": "Wanderlust"
    },
    {
        "quote": "Take only memories, leave only footprints.",
        "author": "Chief Seattle",
        "context": "Native American Leader",
        "theme": "Responsible Travel",
        "category": "Environmental Wisdom"
    }
]

# Travel and adventure related trigger words
TRAVEL_TRIGGER_WORDS = [
    'travel',
    'traveling',
    'travelling',
    'trip',
    'journey',
    'adventure',
    'explore',
    'exploration',
    'vacation',
    'holiday',
    'wanderlust',
    'backpack',
    'backpacking',
    'nomad',
    'nomadic',
    'globe',
    'world',
    'passport',
    'visa',
    'flight',
    'airplane',
    'airport',
    'destination',
    'tourist',
    'tourism',
    'sightseeing',
    'expedition',
    'safari',
    'cruise',
    'road trip',
    'roadtrip',
    'hiking',
    'trekking',
    'camping',
    'hostel',
    'hotel',
    'resort',
    'beach',
    'mountain',
    'ocean',
    'sea',
    'island',
    'city break',
    'weekend getaway',
    'gap year',
    'study abroad',
    'exchange',
    'culture',
    'cultural',
    'heritage',
    'landmark',
    'monument',
    'museum',
    'gallery',
    'temple',
    'cathedral',
    'castle',
    'palace',
    'ruins',
    'ancient',
    'historic',
    'architecture',
    'local',
    'authentic',
    'traditional',
    'cuisine',
    'food',
    'street food',
    'market',
    'bazaar',
    'souvenir',
    'memento',
    'photo',
    'photography',
    'selfie',
    'instagram',
    'blog',
    'vlog',
    'travel blog',
    'itinerary',
    'bucket list',
    'wanderer',
    'explorer',
    'adventurer',
    'globetrotter',
    'jet lag',
    'time zone',
    'currency',
    'exchange rate',
    'language',
    'translation',
    'guide',
    'guidebook',
    'map',
    'gps',
    'navigation',
    'compass',
    'border',
    'customs',
    'immigration',
    'departure',
    'arrival',
    'layover',
    'connecting flight',
    'train',
    'bus',
    'taxi',
    'uber',
    'rental car',
    'motorcycle',
    'bicycle',
    'boat',
    'ferry',
    'subway',
    'metro',
    'public transport',
    'walk',
    'walking tour',
    'free tour',
    'guided tour',
    'self-guided',
    'solo travel',
    'group travel',
    'family vacation',
    'honeymoon',
    'business trip',
    'conference',
    'retreat',
    'pilgrimage',
    'spiritual journey',
    'wellness retreat',
    'spa',
    'relaxation',
    'escape',
    'getaway',
    'break',
    'time off',
    'sabbatical',
    'gap year',
    'retirement travel',
    'senior travel',
    'youth travel',
    'student travel',
    'budget travel',
    'luxury travel',
    'eco travel',
    'sustainable travel',
    'responsible travel',
    'volunteer travel',
    'voluntourism',
    'work travel',
    'digital nomad',
    'remote work',
    'coworking',
    'airbnb',
    'couchsurfing',
    'homestay',
    'bed and breakfast',
    'camping',
    'glamping',
    'rv',
    'motorhome',
    'caravan',
    'tent',
    'sleeping bag',
    'backpack',
    'suitcase',
    'luggage',
    'packing',
    'travel gear',
    'travel accessories',
    'travel insurance',
    'travel agent',
    'booking',
    'reservation',
    'check-in',
    'check-out',
    'concierge',
    'reception',
    'room service',
    'minibar',
    'wifi',
    'internet',
    'roaming',
    'sim card',
    'adapter',
    'converter',
    'plug',
    'voltage',
    'weather',
    'climate',
    'season',
    'peak season',
    'off season',
    'shoulder season',
    'festival',
    'event',
    'celebration',
    'carnival',
    'parade',
    'concert',
    'show',
    'performance',
    'theater',
    'opera',
    'ballet',
    'sports',
    'game',
    'match',
    'stadium',
    'arena',
    'park',
    'garden',
    'zoo',
    'aquarium',
    'planetarium',
    'observatory',
    'viewpoint',
    'lookout',
    'scenic',
    'panoramic',
    'vista',
    'landscape',
    'nature',
    'wildlife',
    'safari',
    'jungle',
    'forest',
    'desert',
    'glacier',
    'volcano',
    'waterfall',
    'river',
    'lake',
    'canyon',
    'valley',
    'cliff',
    'cave',
    'grotto',
    'hot spring',
    'geyser',
    'national park',
    'world heritage',
    'unesco',
    'wonder',
    'seven wonders',
    'bucket list',
    'must see',
    'hidden gem',
    'off the beaten path',
    'undiscovered',
    'remote',
    'exotic',
    'tropical',
    'paradise',
    'heaven',
    'dream destination',
    'once in a lifetime',
    'unforgettable',
    'memorable',
    'amazing',
    'incredible',
    'breathtaking',
    'stunning',
    'beautiful',
    'gorgeous',
    'spectacular',
    'magnificent',
    'awe-inspiring',
    'mind-blowing',
    'epic',
    'awesome',
    'fantastic',
    'wonderful',
    'marvelous',
    'extraordinary',
    'remarkable',
    'outstanding',
    'exceptional',
    'unique',
    'special',
    'magical',
    'enchanting',
    'captivating',
    'fascinating',
    'intriguing',
    'mysterious',
    'romantic',
    'peaceful',
    'serene',
    'tranquil',
    'calm',
    'relaxing',
    'rejuvenating',
    'refreshing',
    'invigorating',
    'energizing',
    'inspiring',
    'motivating',
    'life-changing',
    'transformative',
    'enlightening',
    'educational',
    'cultural immersion',
    'local experience',
    'authentic experience',
    'once in a lifetime experience'
]

def get_random_travel_quote():
    """Get a random travel/adventure quote"""
    return random.choice(TRAVEL_QUOTES)

def is_travel_search(search_term):
    """Check if search term contains travel/adventure trigger words"""
    if not search_term:
        return False
    
    search_lower = search_term.lower().strip()
    
    # Check for exact matches and partial matches
    for trigger in TRAVEL_TRIGGER_WORDS:
        if trigger in search_lower:
            return True
    
    return False

def get_travel_quote_by_category(category=None):
    """Get quotes filtered by category"""
    if not category:
        return get_random_travel_quote()
    
    category_quotes = [q for q in TRAVEL_QUOTES if category.lower() in q['category'].lower()]
    return random.choice(category_quotes) if category_quotes else get_random_travel_quote()

def get_travel_quotes_by_author(author):
    """Get all travel quotes by a specific author"""
    return [q for q in TRAVEL_QUOTES if author.lower() in q['author'].lower()]
