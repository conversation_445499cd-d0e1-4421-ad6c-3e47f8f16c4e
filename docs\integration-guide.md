# Integration Guide

## 🔌 Overview

This guide provides comprehensive instructions for integrating third-party services, external systems, and APIs with the Receipt Generator system, including authentication patterns, data synchronization, and webhook implementations.

## 🌐 External API Integration

### RESTful API Consumption

#### HTTP Client Setup
```python
# helpers/api_client.py
import requests
from django.conf import settings
from django.core.cache import cache
import logging

logger = logging.getLogger('api_integration')

class APIClient:
    def __init__(self, base_url, api_key=None, timeout=30):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.timeout = timeout
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'ReceiptGen/1.0'
        })
        
        if api_key:
            self.session.headers.update({
                'Authorization': f'Bearer {api_key}'
            })

    def get(self, endpoint, params=None, cache_key=None, cache_timeout=300):
        """GET request with optional caching"""
        if cache_key:
            cached_response = cache.get(cache_key)
            if cached_response:
                return cached_response
        
        try:
            url = f"{self.base_url}/{endpoint.lstrip('/')}"
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if cache_key:
                cache.set(cache_key, data, cache_timeout)
            
            logger.info(f"API GET success: {url}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API GET failed: {url} - {str(e)}")
            raise APIException(f"API request failed: {str(e)}")

    def post(self, endpoint, data=None, json_data=None):
        """POST request"""
        try:
            url = f"{self.base_url}/{endpoint.lstrip('/')}"
            
            if json_data:
                response = self.session.post(url, json=json_data, timeout=self.timeout)
            else:
                response = self.session.post(url, data=data, timeout=self.timeout)
            
            response.raise_for_status()
            logger.info(f"API POST success: {url}")
            return response.json() if response.content else {}
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API POST failed: {url} - {str(e)}")
            raise APIException(f"API request failed: {str(e)}")

    def put(self, endpoint, data=None, json_data=None):
        """PUT request"""
        try:
            url = f"{self.base_url}/{endpoint.lstrip('/')}"
            
            if json_data:
                response = self.session.put(url, json=json_data, timeout=self.timeout)
            else:
                response = self.session.put(url, data=data, timeout=self.timeout)
            
            response.raise_for_status()
            logger.info(f"API PUT success: {url}")
            return response.json() if response.content else {}
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API PUT failed: {url} - {str(e)}")
            raise APIException(f"API request failed: {str(e)}")

    def delete(self, endpoint):
        """DELETE request"""
        try:
            url = f"{self.base_url}/{endpoint.lstrip('/')}"
            response = self.session.delete(url, timeout=self.timeout)
            response.raise_for_status()
            logger.info(f"API DELETE success: {url}")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API DELETE failed: {url} - {str(e)}")
            raise APIException(f"API request failed: {str(e)}")

class APIException(Exception):
    """Custom exception for API errors"""
    pass
```

### Payment Gateway Integration

#### Payment Service Implementation
```python
# finances/services/payment_service.py
from helpers.api_client import APIClient, APIException
from django.conf import settings

class PaymentGatewayService:
    def __init__(self):
        self.client = APIClient(
            base_url=settings.PAYMENT_GATEWAY_URL,
            api_key=settings.PAYMENT_GATEWAY_API_KEY
        )

    def initialize_payment(self, amount, student_id, fee_account_id, callback_url):
        """Initialize payment transaction"""
        payment_data = {
            'amount': float(amount),
            'currency': 'NGN',
            'reference': f"FEE_{fee_account_id}_{student_id}",
            'callback_url': callback_url,
            'metadata': {
                'student_id': student_id,
                'fee_account_id': fee_account_id,
                'system': 'receipt_generator'
            }
        }
        
        try:
            response = self.client.post('transactions/initialize', json_data=payment_data)
            return {
                'success': True,
                'payment_url': response.get('data', {}).get('authorization_url'),
                'reference': response.get('data', {}).get('reference'),
                'access_code': response.get('data', {}).get('access_code')
            }
        except APIException as e:
            return {
                'success': False,
                'error': str(e)
            }

    def verify_payment(self, reference):
        """Verify payment status"""
        try:
            response = self.client.get(f'transactions/verify/{reference}')
            
            if response.get('status') and response.get('data', {}).get('status') == 'success':
                return {
                    'success': True,
                    'amount': response['data']['amount'] / 100,  # Convert from kobo
                    'reference': response['data']['reference'],
                    'paid_at': response['data']['paid_at'],
                    'metadata': response['data']['metadata']
                }
            else:
                return {
                    'success': False,
                    'status': response.get('data', {}).get('status', 'failed')
                }
        except APIException as e:
            return {
                'success': False,
                'error': str(e)
            }

    def process_webhook(self, webhook_data):
        """Process payment webhook"""
        event = webhook_data.get('event')
        data = webhook_data.get('data', {})
        
        if event == 'charge.success':
            return self.handle_successful_payment(data)
        elif event == 'charge.failed':
            return self.handle_failed_payment(data)
        
        return {'processed': False}

    def handle_successful_payment(self, payment_data):
        """Handle successful payment webhook"""
        from finances.fee_management.models import FeeAccount, Receipt
        
        reference = payment_data.get('reference')
        amount = payment_data.get('amount', 0) / 100  # Convert from kobo
        metadata = payment_data.get('metadata', {})
        
        try:
            fee_account_id = metadata.get('fee_account_id')
            fee_account = FeeAccount.objects.get(id=fee_account_id)
            
            # Create receipt
            receipt = Receipt.objects.create(
                fee_account=fee_account,
                amount=amount,
                payment_method='online',
                reference_number=reference,
                notes=f"Online payment via gateway - {reference}"
            )
            
            logger.info(f"Payment processed successfully: {reference}")
            return {'processed': True, 'receipt_id': receipt.id}
            
        except FeeAccount.DoesNotExist:
            logger.error(f"Fee account not found for payment: {reference}")
            return {'processed': False, 'error': 'Fee account not found'}
        except Exception as e:
            logger.error(f"Error processing payment {reference}: {str(e)}")
            return {'processed': False, 'error': str(e)}
```

## 📧 Email Service Integration

### Email Service Provider Setup

#### Email Service Implementation
```python
# core/services/email_service.py
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
import logging

logger = logging.getLogger('email_service')

class EmailService:
    def __init__(self):
        self.from_email = settings.DEFAULT_FROM_EMAIL
        self.reply_to = settings.DEFAULT_REPLY_TO_EMAIL

    def send_receipt_email(self, receipt, recipient_email):
        """Send receipt via email"""
        try:
            context = {
                'receipt': receipt,
                'student': receipt.fee_account.student,
                'school_name': settings.SCHOOL_NAME,
                'school_logo': settings.SCHOOL_LOGO_URL
            }
            
            # Render email templates
            subject = f"Payment Receipt - {receipt.receipt_number}"
            text_content = render_to_string('emails/receipt_email.txt', context)
            html_content = render_to_string('emails/receipt_email.html', context)
            
            # Create email message
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=self.from_email,
                to=[recipient_email],
                reply_to=[self.reply_to]
            )
            
            email.attach_alternative(html_content, "text/html")
            
            # Send email
            email.send()
            
            logger.info(f"Receipt email sent successfully to {recipient_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send receipt email: {str(e)}")
            return False

    def send_bulk_notification(self, recipients, subject, template_name, context):
        """Send bulk notification emails"""
        successful_sends = 0
        failed_sends = 0
        
        for recipient in recipients:
            try:
                # Personalize context for each recipient
                personalized_context = {**context, 'recipient': recipient}
                
                text_content = render_to_string(f'emails/{template_name}.txt', personalized_context)
                html_content = render_to_string(f'emails/{template_name}.html', personalized_context)
                
                email = EmailMultiAlternatives(
                    subject=subject,
                    body=text_content,
                    from_email=self.from_email,
                    to=[recipient.email],
                    reply_to=[self.reply_to]
                )
                
                email.attach_alternative(html_content, "text/html")
                email.send()
                
                successful_sends += 1
                
            except Exception as e:
                logger.error(f"Failed to send email to {recipient.email}: {str(e)}")
                failed_sends += 1
        
        return {
            'successful': successful_sends,
            'failed': failed_sends,
            'total': len(recipients)
        }
```

## 🔗 Webhook Implementation

### Webhook Receiver System

#### Webhook Handler
```python
# core/views/webhook_handler.py
from django.http import HttpResponse, HttpResponseBadRequest
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
import json
import hmac
import hashlib
import logging

logger = logging.getLogger('webhooks')

@method_decorator(csrf_exempt, name='dispatch')
class WebhookHandler(View):
    def __init__(self):
        super().__init__()
        self.webhook_secret = settings.WEBHOOK_SECRET

    def verify_signature(self, request):
        """Verify webhook signature"""
        signature = request.META.get('HTTP_X_SIGNATURE')
        if not signature:
            return False
        
        expected_signature = hmac.new(
            self.webhook_secret.encode('utf-8'),
            request.body,
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(signature, expected_signature)

    def post(self, request):
        """Handle webhook POST requests"""
        if not self.verify_signature(request):
            logger.warning("Webhook signature verification failed")
            return HttpResponseBadRequest("Invalid signature")
        
        try:
            payload = json.loads(request.body)
            event_type = payload.get('event')
            
            # Route to appropriate handler
            if event_type == 'payment.success':
                return self.handle_payment_success(payload)
            elif event_type == 'payment.failed':
                return self.handle_payment_failed(payload)
            elif event_type == 'student.updated':
                return self.handle_student_update(payload)
            else:
                logger.warning(f"Unknown webhook event type: {event_type}")
                return HttpResponseBadRequest("Unknown event type")
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON in webhook payload")
            return HttpResponseBadRequest("Invalid JSON")
        except Exception as e:
            logger.error(f"Webhook processing error: {str(e)}")
            return HttpResponseBadRequest("Processing error")

    def handle_payment_success(self, payload):
        """Handle successful payment webhook"""
        from finances.services.payment_service import PaymentGatewayService
        
        payment_service = PaymentGatewayService()
        result = payment_service.process_webhook(payload)
        
        if result.get('processed'):
            logger.info(f"Payment webhook processed successfully")
            return HttpResponse("OK")
        else:
            logger.error(f"Payment webhook processing failed: {result.get('error')}")
            return HttpResponseBadRequest("Processing failed")

    def handle_payment_failed(self, payload):
        """Handle failed payment webhook"""
        reference = payload.get('data', {}).get('reference')
        logger.info(f"Payment failed webhook received for: {reference}")
        
        # Log failed payment for manual review
        # Could also send notification to admin
        
        return HttpResponse("OK")

    def handle_student_update(self, payload):
        """Handle student update webhook from external system"""
        try:
            student_data = payload.get('data', {})
            external_id = student_data.get('external_id')
            
            # Find student by external ID
            student = Student.objects.get(external_id=external_id)
            
            # Update student information
            if 'name' in student_data:
                student.name = student_data['name']
            if 'level' in student_data:
                level = Level.objects.get(external_id=student_data['level'])
                student.level = level
            
            student.save()
            
            logger.info(f"Student updated via webhook: {student.name}")
            return HttpResponse("OK")
            
        except Student.DoesNotExist:
            logger.error(f"Student not found for external ID: {external_id}")
            return HttpResponseBadRequest("Student not found")
        except Exception as e:
            logger.error(f"Student update webhook error: {str(e)}")
            return HttpResponseBadRequest("Update failed")
```

## 📱 SMS Integration

### SMS Service Implementation

#### SMS Gateway Integration
```python
# core/services/sms_service.py
from helpers.api_client import APIClient, APIException
from django.conf import settings
import logging

logger = logging.getLogger('sms_service')

class SMSService:
    def __init__(self):
        self.client = APIClient(
            base_url=settings.SMS_GATEWAY_URL,
            api_key=settings.SMS_GATEWAY_API_KEY
        )

    def send_sms(self, phone_number, message, sender_id=None):
        """Send SMS message"""
        try:
            # Clean phone number
            phone = self.clean_phone_number(phone_number)
            
            sms_data = {
                'to': phone,
                'message': message,
                'from': sender_id or settings.SMS_SENDER_ID
            }
            
            response = self.client.post('sms/send', json_data=sms_data)
            
            if response.get('status') == 'success':
                logger.info(f"SMS sent successfully to {phone}")
                return {
                    'success': True,
                    'message_id': response.get('message_id'),
                    'cost': response.get('cost', 0)
                }
            else:
                logger.error(f"SMS sending failed: {response.get('message')}")
                return {
                    'success': False,
                    'error': response.get('message', 'Unknown error')
                }
                
        except APIException as e:
            logger.error(f"SMS API error: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def send_payment_reminder(self, student, fee_account):
        """Send payment reminder SMS"""
        message = f"Dear Parent, payment reminder for {student.name} " \
                 f"({student.student_id}). Outstanding balance: " \
                 f"₦{fee_account.balance:,.2f}. Please visit school to pay. " \
                 f"Thank you."
        
        # Get parent phone number (assuming it's stored in student model)
        phone_number = getattr(student, 'parent_phone', None)
        
        if phone_number:
            return self.send_sms(phone_number, message)
        else:
            logger.warning(f"No phone number found for student: {student.name}")
            return {'success': False, 'error': 'No phone number available'}

    def send_bulk_sms(self, recipients, message_template, context=None):
        """Send bulk SMS messages"""
        results = {
            'successful': 0,
            'failed': 0,
            'total': len(recipients),
            'errors': []
        }
        
        for recipient in recipients:
            try:
                # Personalize message
                personalized_context = {**(context or {}), 'recipient': recipient}
                message = message_template.format(**personalized_context)
                
                result = self.send_sms(recipient.phone_number, message)
                
                if result['success']:
                    results['successful'] += 1
                else:
                    results['failed'] += 1
                    results['errors'].append({
                        'recipient': recipient.name,
                        'error': result['error']
                    })
                    
            except Exception as e:
                results['failed'] += 1
                results['errors'].append({
                    'recipient': getattr(recipient, 'name', 'Unknown'),
                    'error': str(e)
                })
        
        return results

    def clean_phone_number(self, phone):
        """Clean and format phone number"""
        # Remove all non-digit characters
        cleaned = ''.join(filter(str.isdigit, phone))
        
        # Handle Nigerian phone numbers
        if cleaned.startswith('0'):
            cleaned = '234' + cleaned[1:]  # Replace leading 0 with 234
        elif not cleaned.startswith('234'):
            cleaned = '234' + cleaned  # Add country code
        
        return '+' + cleaned
```

## 🔄 Data Synchronization

### Sync Service Implementation

#### Two-Way Data Sync
```python
# core/services/sync_service.py
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger('sync_service')

class DataSyncService:
    def __init__(self, external_api_client):
        self.api_client = external_api_client
        self.last_sync_key = 'last_sync_timestamp'

    def sync_students(self, full_sync=False):
        """Sync student data with external system"""
        try:
            if full_sync:
                # Full synchronization
                external_students = self.api_client.get('students')
                local_students = Student.objects.all()
                
                sync_results = self.perform_full_student_sync(external_students, local_students)
            else:
                # Incremental synchronization
                last_sync = cache.get(self.last_sync_key)
                if not last_sync:
                    last_sync = timezone.now() - timedelta(days=1)
                
                # Get changes since last sync
                params = {'modified_since': last_sync.isoformat()}
                external_changes = self.api_client.get('students/changes', params=params)
                
                sync_results = self.perform_incremental_student_sync(external_changes)
            
            # Update last sync timestamp
            cache.set(self.last_sync_key, timezone.now(), 86400)  # 24 hours
            
            logger.info(f"Student sync completed: {sync_results}")
            return sync_results
            
        except Exception as e:
            logger.error(f"Student sync failed: {str(e)}")
            return {'success': False, 'error': str(e)}

    def perform_full_student_sync(self, external_students, local_students):
        """Perform full student synchronization"""
        results = {
            'created': 0,
            'updated': 0,
            'deleted': 0,
            'errors': []
        }
        
        # Create lookup for local students
        local_lookup = {s.external_id: s for s in local_students if s.external_id}
        external_ids = set()
        
        for ext_student in external_students:
            external_id = ext_student.get('id')
            external_ids.add(external_id)
            
            try:
                if external_id in local_lookup:
                    # Update existing student
                    local_student = local_lookup[external_id]
                    if self.update_student_from_external(local_student, ext_student):
                        results['updated'] += 1
                else:
                    # Create new student
                    if self.create_student_from_external(ext_student):
                        results['created'] += 1
                        
            except Exception as e:
                results['errors'].append({
                    'external_id': external_id,
                    'error': str(e)
                })
        
        # Handle deleted students (present locally but not externally)
        for local_student in local_students:
            if local_student.external_id and local_student.external_id not in external_ids:
                local_student.is_active = False
                local_student.save()
                results['deleted'] += 1
        
        return results

    def update_student_from_external(self, local_student, external_data):
        """Update local student with external data"""
        updated = False
        
        if local_student.name != external_data.get('name'):
            local_student.name = external_data.get('name')
            updated = True
        
        if local_student.gender != external_data.get('gender'):
            local_student.gender = external_data.get('gender')
            updated = True
        
        # Handle level mapping
        external_level = external_data.get('level')
        if external_level:
            try:
                level = Level.objects.get(external_id=external_level)
                if local_student.level != level:
                    local_student.level = level
                    updated = True
            except Level.DoesNotExist:
                logger.warning(f"Level not found for external ID: {external_level}")
        
        if updated:
            local_student.save()
        
        return updated

    def create_student_from_external(self, external_data):
        """Create new student from external data"""
        try:
            # Map external level to local level
            external_level = external_data.get('level')
            level = Level.objects.get(external_id=external_level)
            
            student = Student.objects.create(
                name=external_data.get('name'),
                gender=external_data.get('gender', 'Male'),
                level=level,
                external_id=external_data.get('id'),
                is_active=True
            )
            
            return True
            
        except Level.DoesNotExist:
            logger.error(f"Cannot create student: Level not found for external ID: {external_level}")
            return False
        except Exception as e:
            logger.error(f"Error creating student: {str(e)}")
            return False
```

## 🔧 Best Practices

### Integration Guidelines
1. **Always implement proper authentication** for external APIs
2. **Use retry mechanisms** with exponential backoff for failed requests
3. **Implement comprehensive logging** for all integration activities
4. **Cache API responses** where appropriate to reduce external calls
5. **Handle rate limiting** gracefully with proper delays
6. **Validate all incoming data** from external sources
7. **Use webhooks** for real-time updates instead of frequent polling
8. **Implement proper error handling** and fallback mechanisms

### Security Considerations
- Store API keys and secrets in environment variables
- Use HTTPS for all external communications
- Implement signature verification for webhooks
- Validate and sanitize all external data
- Use proper authentication mechanisms (OAuth, API keys)
- Implement request rate limiting to prevent abuse
- Log security events and monitor for suspicious activity

### Performance Optimization
- Use connection pooling for HTTP clients
- Implement caching strategies for frequently accessed data
- Use background tasks for long-running sync operations
- Batch API requests where possible
- Monitor API usage and costs
- Implement circuit breakers for unreliable services

This guide provides the foundation for robust integration with external services while maintaining security and performance standards.
