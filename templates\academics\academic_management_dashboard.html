{% extends 'academics/base.html' %} {% load humanize %}
<!--  -->
{% block title %}Academic Management | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-graduation-cap text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Academic Management
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Manage subjects, assessment types, and academic activities
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 action-buttons-slide-in">
        <a
          href="{% url 'academics:add_subject' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-plus group-hover:rotate-90 transition-all duration-300"
          ></i>
          <span>Add Subject</span>
        </a>
        <a
          href="{% url 'academics:add_assessment_type' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-clipboard-check group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Add Assessment</span>
        </a>
        <a
          href="{% url 'academics:add_activity' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] text-white font-bold py-4 px-8 rounded-xl hover:from-[#e74c3c] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-calendar-plus group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Add Activity</span>
        </a>
        <a
          href="{% url 'academics:activity_type_management' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white font-bold py-4 px-8 rounded-xl hover:from-[#2C3E50] hover:to-[#40657F] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-tags group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Manage Activity Types</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Statistics Section -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 stats-cards-fade-in">
    <!-- Total Subjects -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5 border-l-4 border-[#7AB2D3]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#7AB2D3] rounded-xl flex items-center justify-center">
          <i class="fas fa-book text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Total Subjects</h3>
          <p class="text-[#40657F] font-medium text-2xl">{{ stats.total_subjects }}</p>
        </div>
      </div>
    </div>

    <!-- Assessment Types -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5 border-l-4 border-[#74C69D]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#74C69D] rounded-xl flex items-center justify-center">
          <i class="fas fa-clipboard-check text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Assessment Types</h3>
          <p class="text-[#40657F] font-medium text-2xl">{{ stats.total_assessment_types }}</p>
        </div>
      </div>
    </div>

    <!-- Activities -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#F28C8C]/5 to-[#e74c3c]/5 border-l-4 border-[#F28C8C]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#F28C8C] rounded-xl flex items-center justify-center">
          <i class="fas fa-calendar-alt text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Activities</h3>
          <p class="text-[#40657F] font-medium text-2xl">{{ stats.total_activities }}</p>
        </div>
      </div>
    </div>

    <!-- Activity Types -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#40657F]/5 to-[#2C3E50]/5 border-l-4 border-[#40657F]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#40657F] rounded-xl flex items-center justify-center">
          <i class="fas fa-tags text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Activity Types</h3>
          <p class="text-[#40657F] font-medium text-2xl">{{ stats.total_activity_types }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Subjects Section -->
  <div class="card-modern p-8 subjects-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
      >
        <i class="fas fa-book text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Subjects
        </h3>
        <p class="text-[#40657F] text-sm">
          {% if subject_search %}
            Search results for "{{ subject_search }}"
          {% else %}
            All subjects in the curriculum
          {% endif %}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div class="flex items-center gap-2">
        <div
          class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30"
        >
          <i class="fas fa-book text-sm"></i>
          <span>{{ subject_total_count }} subject{{ subject_total_count|pluralize }}</span>
        </div>
        <a
          href="{% url 'academics:bulk_delete_subjects' %}"
          class="bg-[#F28C8C] text-white px-4 py-2 rounded-lg hover:bg-[#e74c3c] transition-colors duration-200 text-sm font-medium"
          title="Bulk Delete"
        >
          <i class="fas fa-trash"></i>
        </a>
      </div>
    </div>

    <!-- Subject Search -->
    <form method="GET" class="mb-6">
      <div class="flex gap-4">
        <div class="relative flex-1">
          <input
            type="text"
            name="subject_search"
            value="{{ subject_search }}"
            placeholder="Search subjects... (try 'what is life' 🧠)"
            class="w-full px-4 py-3 pl-12 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]/60"
          />
          <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#7AB2D3]"></i>
        </div>
        <div class="flex gap-2">
          <button
            type="submit"
            class="bg-[#7AB2D3] text-white px-6 py-3 rounded-xl hover:bg-[#40657F] transition-colors duration-200 font-medium"
          >
            <i class="fas fa-search mr-2"></i>
            Search
          </button>
          {% if subject_search %}
          <a
            href="{% url 'academics:academic_management_dashboard' %}"
            class="bg-[#B9D8EB] text-[#40657F] px-4 py-3 rounded-xl hover:bg-[#E2F1F9] transition-colors duration-200 font-medium"
          >
            <i class="fas fa-times"></i>
          </a>
          {% endif %}
        </div>
      </div>
    </form>

    <!-- Subjects Table -->
    <div
      class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg table-fade-in"
    >
      <table class="min-w-full bg-white">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-book text-[#7AB2D3]"></i>
                Subject Name
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-tag text-[#40657F]"></i>
                Abbreviation
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-layer-group text-[#74C69D]"></i>
                Category
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-graduation-cap text-[#F28C8C]"></i>
                Level
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-cog text-[#7AB2D3]"></i>
                Actions
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#B9D8EB]/30">
          {% for subject in subjects %}
          <tr
            class="subject-row hover:bg-[#E2F1F9]/50 transition-colors duration-200"
          >
            <td class="px-6 py-4">
              <a
                href="{% url 'academics:subject_details' subject.pk %}"
                class="font-bold text-[#2C3E50] hover:text-[#7AB2D3] transition-colors duration-200"
              >
                {{ subject.name }}
              </a>
            </td>
            <td class="px-6 py-4 text-center">
              <span class="font-mono text-[#40657F] font-bold bg-[#E2F1F9] px-2 py-1 rounded">
                {{ subject.abbrv }}
              </span>
            </td>
            <td class="px-6 py-4 text-center">
              <span class="text-[#2C3E50] font-medium">{{ subject.category|default:"—" }}</span>
            </td>
            <td class="px-6 py-4 text-center">
              <div class="flex flex-wrap justify-center gap-1">
                {% for level in subject.levels.all %}
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30"
                  >
                    {{ level.level_name }}
                  </span>
                {% empty %}
                  <span class="text-gray-400 text-sm">No levels assigned</span>
                {% endfor %}
              </div>
            </td>
            <td class="px-6 py-4 text-center">
              <div class="flex items-center justify-center gap-2">
                <a
                  href="{% url 'academics:subject_details' subject.pk %}"
                  class="bg-[#7AB2D3] text-white px-3 py-2 rounded-lg hover:bg-[#40657F] transition-colors duration-200 text-sm"
                  title="View Details"
                >
                  <i class="fas fa-eye"></i>
                </a>
                <a
                  href="{% url 'academics:edit_subject' subject.pk %}"
                  class="bg-[#74C69D] text-white px-3 py-2 rounded-lg hover:bg-[#5fb085] transition-colors duration-200 text-sm"
                  title="Edit"
                >
                  <i class="fas fa-edit"></i>
                </a>
              </div>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="5" class="text-center py-12">
              <div class="flex flex-col items-center gap-4">
                <div
                  class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
                >
                  <i class="fas fa-book text-[#B9D8EB] text-2xl"></i>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
                    No Subjects Found
                  </h3>
                  <p class="text-[#40657F]">
                    {% if subject_search %}
                      No subjects match your search criteria.
                    {% else %}
                      Get started by creating your first subject.
                    {% endif %}
                  </p>
                </div>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Subject Pagination -->
    {% if subjects.has_other_pages %}
    <div class="flex justify-center items-center gap-4 mt-6">
      <div class="flex items-center gap-2">
        {% if subjects.has_previous %}
        <a
          href="?subject_page=1{% if subject_search %}&subject_search={{ subject_search }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-left"></i>
        </a>
        <a
          href="?subject_page={{ subjects.previous_page_number }}{% if subject_search %}&subject_search={{ subject_search }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-left"></i>
        </a>
        {% endif %}

        <span class="px-4 py-2 bg-[#7AB2D3] text-white rounded-lg font-semibold">
          Page {{ subjects.number }} of {{ subjects.paginator.num_pages }}
        </span>

        {% if subjects.has_next %}
        <a
          href="?subject_page={{ subjects.next_page_number }}{% if subject_search %}&subject_search={{ subject_search }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-right"></i>
        </a>
        <a
          href="?subject_page={{ subjects.paginator.num_pages }}{% if subject_search %}&subject_search={{ subject_search }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-right"></i>
        </a>
        {% endif %}
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Assessment Types Section -->
  <div class="card-modern p-8 assessments-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg assessment-icon-float"
      >
        <i class="fas fa-clipboard-check text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Assessment Types
        </h3>
        <p class="text-[#40657F] text-sm">
          {% if assessment_search %}
            Search results for "{{ assessment_search }}"
          {% else %}
            All assessment types in the system
          {% endif %}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div class="flex items-center gap-2">
        <div
          class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30"
        >
          <i class="fas fa-clipboard-check text-sm"></i>
          <span>{{ assessment_total_count }} assessment{{ assessment_total_count|pluralize }}</span>
        </div>
        <a
          href="{% url 'academics:bulk_delete_assessment_types' %}"
          class="bg-[#F28C8C] text-white px-4 py-2 rounded-lg hover:bg-[#e74c3c] transition-colors duration-200 text-sm font-medium"
          title="Bulk Delete"
        >
          <i class="fas fa-trash"></i>
        </a>
      </div>
    </div>

    <!-- Assessment Types Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {% for assessment_type in assessment_types %}
      <div
        class="assessment-card bg-gradient-to-br from-white via-[#F7FAFC] to-[#E2F1F9] border-2 border-[#B9D8EB] hover:border-[#74C69D] hover:shadow-xl hover:-translate-y-2 transition-all duration-300 p-6 rounded-2xl relative overflow-hidden group"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-12 h-12 bg-gradient-to-br from-[#74C69D]/20 to-[#5fb085]/10 rounded-full -translate-y-6 translate-x-6 group-hover:scale-125 transition-transform duration-500"
        ></div>

        <!-- Assessment Header -->
        <div class="relative z-10 mb-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-lg font-bold text-[#2C3E50]">
              {{ assessment_type.name }}
            </h4>
            {% if assessment_type.is_final %}
            <span class="text-xs bg-[#F28C8C]/20 text-[#F28C8C] px-2 py-1 rounded-full font-medium">
              Final
            </span>
            {% endif %}
          </div>
          <div class="flex items-center gap-4">
            <span class="text-sm text-[#40657F]">
              Weight: <strong>{{ assessment_type.weight }}%</strong>
            </span>
            <span class="text-sm text-[#40657F]">
              Sequence: <strong>{{ assessment_type.sequence }}</strong>
            </span>
          </div>
        </div>

        <!-- Assessment Details -->
        <div class="relative z-10 mb-4">
          {% if assessment_type.description %}
          <p class="text-sm text-[#40657F] line-clamp-2">
            {{ assessment_type.description }}
          </p>
          {% endif %}
        </div>

        <!-- Assessment Actions -->
        <div class="relative z-10 flex gap-2">
          <a
            href="{% url 'academics:edit_assessment_type' assessment_type.pk %}"
            class="flex-1 bg-[#74C69D] text-white text-center py-2 px-3 rounded-lg hover:bg-[#5fb085] transition-colors duration-200 text-sm font-medium"
          >
            <i class="fas fa-edit mr-1"></i>
            Edit
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#74C69D]/5 to-[#5fb085]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>
      {% empty %}
      <div class="col-span-full text-center py-12">
        <div class="flex flex-col items-center gap-4">
          <div
            class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-clipboard-check text-[#B9D8EB] text-2xl"></i>
          </div>
          <div>
            <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
              No Assessment Types Found
            </h3>
            <p class="text-[#40657F]">
              {% if assessment_search %}
                No assessment types match your search criteria.
              {% else %}
                Get started by creating your first assessment type.
              {% endif %}
            </p>
          </div>
          <a
            href="{% url 'academics:add_assessment_type' %}"
            class="inline-flex items-center gap-2 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
          >
            <i class="fas fa-plus"></i>
            <span>Add First Assessment Type</span>
          </a>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>

  <!-- Activities Section -->
  <div class="card-modern p-8 activities-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e74c3c] rounded-xl flex items-center justify-center shadow-lg activity-icon-float"
      >
        <i class="fas fa-calendar-alt text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Recent Activities
        </h3>
        <p class="text-[#40657F] text-sm">
          {% if activity_search %}
            Search results for "{{ activity_search }}"
          {% else %}
            Latest academic activities
          {% endif %}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div class="flex items-center gap-2">
        <div
          class="flex items-center gap-2 bg-[#F28C8C]/20 text-[#F28C8C] px-4 py-2 rounded-full font-bold border border-[#F28C8C]/30"
        >
          <i class="fas fa-calendar-alt text-sm"></i>
          <span>{{ activity_total_count }} activit{{ activity_total_count|pluralize:"y,ies" }}</span>
        </div>
        <a
          href="{% url 'academics:bulk_delete_activities' %}"
          class="bg-[#F28C8C] text-white px-4 py-2 rounded-lg hover:bg-[#e74c3c] transition-colors duration-200 text-sm font-medium"
          title="Bulk Delete"
        >
          <i class="fas fa-trash"></i>
        </a>
      </div>
    </div>

    <!-- Activities List -->
    <div class="space-y-4">
      {% for activity in activities %}
      <div
        class="activity-card bg-gradient-to-r from-white to-[#F7FAFC] border border-[#B9D8EB] hover:border-[#F28C8C] hover:shadow-lg transition-all duration-300 p-6 rounded-xl group"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <div class="w-12 h-12 bg-[#F28C8C] rounded-xl flex items-center justify-center">
              <i class="fas fa-calendar-day text-white"></i>
            </div>
            <div>
              <h4 class="font-bold text-[#2C3E50] text-lg">
                {{ activity.activity_type.name }}
              </h4>
              <div class="flex items-center gap-4 text-sm text-[#40657F]">
                <span>
                  <i class="fas fa-calendar mr-1"></i>
                  {{ activity.date }}
                </span>
                {% if activity.subject %}
                <span>
                  <i class="fas fa-book mr-1"></i>
                  {{ activity.subject.name }}
                </span>
                {% endif %}
                {% if activity.class_assigned %}
                <span>
                  <i class="fas fa-users mr-1"></i>
                  {{ activity.class_assigned.level_name }}
                </span>
                {% endif %}
              </div>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <a
              href="{% url 'academics:activity_details' activity.pk %}"
              class="bg-[#7AB2D3] text-white px-3 py-2 rounded-lg hover:bg-[#40657F] transition-colors duration-200 text-sm"
              title="View Details"
            >
              <i class="fas fa-eye"></i>
            </a>
            <a
              href="{% url 'academics:edit_activity' activity.pk %}"
              class="bg-[#F28C8C] text-white px-3 py-2 rounded-lg hover:bg-[#e74c3c] transition-colors duration-200 text-sm"
              title="Edit"
            >
              <i class="fas fa-edit"></i>
            </a>
          </div>
        </div>
        {% if activity.notes %}
        <div class="mt-4 pt-4 border-t border-[#B9D8EB]/30">
          <p class="text-sm text-[#40657F]">{{ activity.notes }}</p>
        </div>
        {% endif %}
      </div>
      {% empty %}
      <div class="text-center py-12">
        <div class="flex flex-col items-center gap-4">
          <div
            class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-calendar-alt text-[#B9D8EB] text-2xl"></i>
          </div>
          <div>
            <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
              No Activities Found
            </h3>
            <p class="text-[#40657F]">
              {% if activity_search %}
                No activities match your search criteria.
              {% else %}
                Get started by creating your first activity.
              {% endif %}
            </p>
          </div>
          <a
            href="{% url 'academics:add_activity' %}"
            class="inline-flex items-center gap-2 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] text-white font-bold py-3 px-6 rounded-xl hover:from-[#e74c3c] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
          >
            <i class="fas fa-plus"></i>
            <span>Add First Activity</span>
          </a>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Stats Cards Animation */
  .stats-cards-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: statsCardsFadeIn 0.8s ease-out 1s forwards;
  }

  /* Subjects Section Animation */
  .subjects-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: subjectsSectionFadeIn 0.8s ease-out 1.2s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  .table-fade-in {
    opacity: 0;
    animation: tableFadeIn 0.8s ease-out 1.4s forwards;
  }

  .subject-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: subjectRowSlideIn 0.4s ease-out forwards;
  }

  .subject-row:nth-child(1) { animation-delay: 1.6s; }
  .subject-row:nth-child(2) { animation-delay: 1.7s; }
  .subject-row:nth-child(3) { animation-delay: 1.8s; }
  .subject-row:nth-child(4) { animation-delay: 1.9s; }
  .subject-row:nth-child(5) { animation-delay: 2s; }

  /* Assessment Types Section Animation */
  .assessments-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: assessmentsSectionFadeIn 0.8s ease-out 2.2s forwards;
  }

  .assessment-icon-float {
    animation: assessmentIconFloat 4s ease-in-out infinite;
  }

  .assessment-card {
    opacity: 0;
    transform: translateY(20px);
    animation: assessmentCardSlideIn 0.4s ease-out forwards;
  }

  .assessment-card:nth-child(1) { animation-delay: 2.4s; }
  .assessment-card:nth-child(2) { animation-delay: 2.5s; }
  .assessment-card:nth-child(3) { animation-delay: 2.6s; }

  /* Activities Section Animation */
  .activities-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: activitiesSectionFadeIn 0.8s ease-out 2.8s forwards;
  }

  .activity-icon-float {
    animation: activityIconFloat 4s ease-in-out infinite;
  }

  .activity-card {
    opacity: 0;
    transform: translateX(-20px);
    animation: activityCardSlideIn 0.4s ease-out forwards;
  }

  .activity-card:nth-child(1) { animation-delay: 3s; }
  .activity-card:nth-child(2) { animation-delay: 3.1s; }
  .activity-card:nth-child(3) { animation-delay: 3.2s; }
  .activity-card:nth-child(4) { animation-delay: 3.3s; }
  .activity-card:nth-child(5) { animation-delay: 3.4s; }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes actionButtonsSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes statsCardsFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes subjectsSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes tableIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes tableFadeIn {
    to { opacity: 1; }
  }

  @keyframes subjectRowSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes assessmentsSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes assessmentIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(3deg); }
  }

  @keyframes assessmentCardSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes activitiesSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes activityIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes activityCardSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .subject-row, .assessment-card, .activity-card {
      animation-delay: 1.2s;
    }

    .subject-row:nth-child(n), .assessment-card:nth-child(n), .activity-card:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--item-index, 1));
    }
  }
</style>

{% endblock %}
