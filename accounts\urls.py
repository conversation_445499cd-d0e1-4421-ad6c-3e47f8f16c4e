from django.urls import path
from .views import (
    login_view, logout_view,
    add_assignment, add_teacher, edit_teacher, remove_assignment, teachers, teacher_details,
    user_management_dashboard, create_user, user_detail, edit_user,
    assign_role, remove_role, toggle_user_status, 
    user_profile, edit_profile, change_password
)


app_name = 'accounts'

urlpatterns = [
    # Authentication
    path('login/', login_view, name='login'),
    path('logout/', logout_view, name='logout'),

    # Teacher Management
    path('teachers/', teachers, name="teachers"),
    path('teacher-details/<int:id>', teacher_details, name="teacher_details"),
    path('add-teacher/', add_teacher, name="add_teacher"),
    path('edit-teacher/<int:pk>', edit_teacher, name='edit_teacher'),
    path('add-assignment/<int:pk>', add_assignment, name="add_assignment"),
    path('remove-assignment/<int:pk>', remove_assignment, name="remove_assignment"),

    # User Management
    path('users/', user_management_dashboard, name='user_management'),
    path('users/create/', create_user, name='create_user'),
    path('users/<int:user_id>/', user_detail, name='user_detail'),
    path('users/<int:user_id>/edit/', edit_user, name='edit_user'),
    path('users/<int:user_id>/assign-role/', assign_role, name='assign_role'),
    path('users/<int:user_id>/remove-role/<int:role_id>/', remove_role, name='remove_role'),
    path('users/<int:user_id>/toggle-status/', toggle_user_status, name='toggle_user_status'),

    # User Profile
    path('profile/', user_profile, name='user_profile'),
    path('profile/edit/', edit_profile, name='edit_profile'),
    path('profile/change-password/', change_password, name='change_password'),
]
