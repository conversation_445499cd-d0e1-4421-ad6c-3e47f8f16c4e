
from django import forms
from django.forms import inlineformset_factory
from django.core.exceptions import ValidationError

from finances.book_keeping import Outflow, OutflowLine


class OutflowForm(forms.ModelForm):
    class Meta:
        model = Outflow
        fields = ["date", "payee", "description"]
        widgets = {
            "date": forms.DateInput(attrs={
                "type": "date",
                "class": "django-form"

            }),
            "payee": forms.TextInput(attrs={
                "placeholder": "Payee",
                "class": "django-form"

            }),
            "description": forms.TextInput(attrs={
                "placeholder": "Description",
                "class": "django-form"

            }),
            "paid_from": forms.Select(attrs={
                "class": "django-form"
            }),
        }


class OutFlowLineForm(forms.ModelForm):
    class Meta:
        model = OutflowLine
        fields = ["account", "amount", "description"]
        widgets = {
            "account": forms.Select(attrs={
                "class": "django-form"
            }),
            "amount": forms.TextInput(attrs={
                "placeholder": "Amount",
                "class": "django-form"
            }),
            "description": forms.TextInput(attrs={
                "placeholder": "Description",
                "class": "django-form"
            }),
        }


OutFlowLineFormSet = inlineformset_factory(
    Outflow,
    OutflowLine,
    form=OutFlowLineForm,
    extra=10,
    can_delete=True,
)


class ReversalReasonForm(forms.Form):
    """Form for capturing reversal reason"""
    reason = forms.CharField(
        max_length=500,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]',
            'rows': 4,
            'placeholder': 'Please provide a detailed reason for this reversal...',
            'required': True
        }),
        help_text='Explain why this transaction needs to be reversed.'
    )

    def clean_reason(self):
        reason = self.cleaned_data.get('reason')
        if not reason or len(reason.strip()) < 10:
            raise ValidationError(
                'Please provide a detailed reason (at least 10 characters).')
        return reason.strip()
