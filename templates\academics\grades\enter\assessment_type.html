{% extends "academics/base.html" %} {% load static %}
<!-- title -->
{% block title %}Assessment Type | {% endblock %}
<!-- title -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i
            class="fas fa-list-check text-white text-lg sm:text-xl icon-pulse"
          ></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Assessment Types
          </h1>
          <p
            class="text-[#40657F] text-base sm:text-lg font-medium mt-1 subtitle-fade-in"
          >
            Choose assessment type for {{level.level_name}} grade entry
          </p>
          <div
            class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3 action-buttons-slide-in">
        <a
          href=""
          class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300"
          ></i>
          Manage Types
        </a>
        <a
          href="{% url 'academics:enter_by_level' %}"
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#7AB2D3] hover:text-white focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-arrow-left mr-2 group-hover:translate-x-[-2px] transition-transform duration-300"
          ></i>
          Back to Classes
        </a>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200 breadcrumb-fade-in"
    >
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a href="" class="text-[#40657F] hover:text-[#7AB2D3] transition-colors"
        >Classes</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold"
        >{{level.level_name}} Assessments</span
      >
    </nav>
  </div>

  <!-- Assessment Types Grid -->
  <div class="assessments-grid-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-tasks text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Available Assessment Types
        </h2>
        <p class="text-[#40657F] text-sm">
          {{ assessment_types|length }} assessment
          type{{assessment_types|length|pluralize }} for {{level.level_name}}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
    >
      {% for assessment_type in assessment_types %}
      <a
        href="{% url 'academics:enter_by_subjects' level_slug assessment_type.slug %}"
        class="assessment-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-6 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45 group-hover:from-[#7AB2D3]/20 group-hover:to-[#40657F]/20"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10 rounded-full translate-y-8 -translate-x-8 transition-all duration-700 group-hover:scale-150 group-hover:-rotate-45 group-hover:from-[#74C69D]/25 group-hover:to-[#5fb085]/25"
        ></div>

        <!-- Assessment Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div class="flex items-center gap-3">
            <div
              class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
            >
              <i class="fas fa-clipboard-check text-white text-lg"></i>
            </div>
            <div>
              <h3
                class="font-bold text-lg text-[#2C3E50] font-display group-hover:text-[#7AB2D3] transition-colors duration-300"
              >
                {{ assessment_type.name }}
              </h3>
              <p class="text-sm text-[#40657F] font-medium">Assessment</p>
            </div>
          </div>
          <div
            class="opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1"
          >
            <i class="fas fa-arrow-right text-[#7AB2D3] text-lg"></i>
          </div>
        </div>

        <!-- Assessment Description -->
        <div class="space-y-4 relative z-10">
          <div
            class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]/30 rounded-lg p-3 min-h-[60px] flex items-center"
          >
            <p class="text-sm text-[#40657F] font-medium line-clamp-3">
              assessment for evaluating performance.
            </p>
          </div>

          <!-- Assessment Features -->
          <div class="flex items-center gap-2">
            <i class="fas fa-check-circle text-[#74C69D] text-sm"></i>
            <span
              class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
              >Grade Entry Ready</span
            >
          </div>

          <div class="flex items-center justify-between">
            <span
              class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30"
            >
              <i class="fas fa-edit mr-1"></i>
              {{level.level_name}} Level
            </span>
            <span
              class="text-xs text-[#40657F] group-hover:text-[#7AB2D3] transition-colors duration-300 font-medium"
            >
              Select subjects →
            </span>
          </div>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#74C69D]/5 to-[#7AB2D3]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </a>
      {% empty %}
      <div class="col-span-full text-center py-16">
        <div class="flex flex-col items-center gap-6">
          <div
            class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-clipboard-list text-gray-400 text-3xl"></i>
          </div>
          <div>
            <h3 class="font-display font-bold text-2xl text-gray-800 mb-2">
              No Assessment Types Found
            </h3>
            <p class="text-gray-600 text-lg">
              No assessment types have been created for {{level.level_name}}
              yet.
            </p>
            <p class="text-gray-500 text-sm mt-2">
              Create assessment types to start entering grades.
            </p>
          </div>
          <a
            href=""
            class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg"
          >
            <i class="fas fa-plus mr-2"></i>
            Create Assessment Types
          </a>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Assessments Grid Animations */
  .assessments-grid-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: assessmentsGridFadeIn 0.8s ease-out 1s forwards;
  }

  .assessment-card {
    opacity: 0;
    transform: translateY(30px);
    animation: assessmentCardSlideIn 0.6s ease-out forwards;
  }

  .assessment-card:nth-child(1) {
    animation-delay: 1.2s;
  }
  .assessment-card:nth-child(2) {
    animation-delay: 1.3s;
  }
  .assessment-card:nth-child(3) {
    animation-delay: 1.4s;
  }
  .assessment-card:nth-child(4) {
    animation-delay: 1.5s;
  }
  .assessment-card:nth-child(5) {
    animation-delay: 1.6s;
  }
  .assessment-card:nth-child(6) {
    animation-delay: 1.7s;
  }
  .assessment-card:nth-child(7) {
    animation-delay: 1.8s;
  }
  .assessment-card:nth-child(8) {
    animation-delay: 1.9s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes assessmentsGridFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes assessmentCardSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .assessment-card {
      animation-delay: 1s;
    }

    .assessment-card:nth-child(n) {
      animation-delay: calc(1s + 0.1s * var(--card-index, 1));
    }
  }
</style>

{% endblock %}
