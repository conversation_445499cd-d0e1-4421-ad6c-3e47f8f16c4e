from django.shortcuts import redirect, render
from django.contrib.auth.decorators import login_required


from academics.models import Subject, Assessment, AssessmentType, Enrollment
from accounts.models import TeacherAssignment

from students.models import Student, Level


# ?enter grades view cluster


@login_required(login_url="accounts:login")
def enter_grades(request,  level_slug, type_slug, subject_slug):
    subject = Subject.objects.get(slug=subject_slug)
    level = Level.objects.get(slug=level_slug)

    # Check if teacher has permission to enter grades for this subject and level
    if not (request.user.is_staff or request.user.is_superuser):
        teacher_assignment = TeacherAssignment.objects.filter(
            teacher=request.user,
            subject=subject,
            class_assigned=level,
            term__is_active=True
        ).first()

        if not teacher_assignment:
            from django.contrib import messages
            from django.shortcuts import redirect
            messages.error(request, "You don't have permission to enter grades for this subject and class.")
            return redirect('academics:enter_by_level')

    students = Student.objects.filter(
        is_active=True, level__slug=level_slug).order_by('name')
    assess_type = AssessmentType.objects.get(slug=type_slug)

    if request.method == "POST":

        max_marks = int(request.POST['max_marks'])
        for key, value in request.POST.items():
            if key == "csrfmiddlewaretoken" or key == "max_marks":
                continue

            if value:
                value = int(value)
                student = Student.objects.get(id=key)
                enroll = Enrollment.objects.get(
                    student=student, subject=subject,
                    term__is_active=True
                )

                assessment, _ = Assessment.objects.get_or_create(
                    enrollment=enroll, assessment_type=assess_type
                )
                assessment.score = value
                assessment.save()

        return redirect('academics:enter_grades', level_slug, type_slug, subject_slug)

    context = {
        'students': students,
        'level_slug': level_slug,
        'type_slug': type_slug,
        'subject': subject,
    }
    return render(request, 'academics/grades/enter_grades.html', context)


@login_required(login_url="accounts:login")
def assessment_type(request, level_slug):
    assessment_types = AssessmentType.objects.all().order_by('sequence')
    context = {
        'assessment_types': assessment_types,
        'level_slug': level_slug,
    }
    return render(request, 'academics/grades/enter/assessment_type.html', context)


@login_required(login_url="accounts:login")
def enter_by_levels(request):
    # If user is staff/admin, show all levels
    if request.user.is_staff or request.user.is_superuser:
        levels = Level.objects.all()
    else:
        # For teachers, only show levels they are assigned to
        teacher_assignments = TeacherAssignment.objects.filter(
            teacher=request.user,
            term__is_active=True
        ).select_related('class_assigned')

        # Get unique levels from assignments
        level_ids = teacher_assignments.values_list('class_assigned_id', flat=True).distinct()
        levels = Level.objects.filter(id__in=level_ids)

    context = {
        'levels': levels,
    }
    return render(request, 'academics/grades/enter/by_levels.html', context)


@login_required(login_url="accounts:login")
def enter_by_subjects(request, level_slug, type_slug):
    level = Level.objects.get(slug=level_slug)

    # If user is staff/admin, show all subjects for this level
    if request.user.is_staff or request.user.is_superuser:
        subjects = Subject.objects.filter(levels=level)
    else:
        # For teachers, only show subjects they are assigned to teach for this specific level
        teacher_assignments = TeacherAssignment.objects.filter(
            teacher=request.user,
            class_assigned=level,
            term__is_active=True
        ).select_related('subject')

        # Get subjects from assignments that are also taught in this level
        subject_ids = teacher_assignments.values_list('subject_id', flat=True)
        subjects = Subject.objects.filter(id__in=subject_ids, levels=level)

    context = {
        'subjects': subjects,
        'level_slug': level_slug,
        'type_slug': type_slug,
    }
    return render(request, 'academics/grades/enter/by_subjects.html', context)


# ?View Grades cluster
@login_required(login_url="accounts:login")
def view_grades(request):
    from students.models import Term

    # Filter data based on user permissions
    if request.user.is_staff or request.user.is_superuser:
        levels = Level.objects.all()
        subjects = Subject.objects.all()
    else:
        # For teachers, only show levels and subjects they are assigned to
        teacher_assignments = TeacherAssignment.objects.filter(
            teacher=request.user,
            term__is_active=True
        ).select_related('class_assigned', 'subject')

        # Get unique levels and subjects from assignments
        level_ids = teacher_assignments.values_list('class_assigned_id', flat=True).distinct()
        subject_ids = teacher_assignments.values_list('subject_id', flat=True).distinct()

        levels = Level.objects.filter(id__in=level_ids)
        subjects = Subject.objects.filter(id__in=subject_ids)

    assessment_types = AssessmentType.objects.all().order_by('sequence')
    terms = Term.objects.all()

    # Get filter values from query params
    level_id = request.GET.get('level')
    type_id = request.GET.get('assessment_type')
    term_id = request.GET.get('term')

    student_data = None  # Will hold the final structured data

    if level_id and type_id and term_id:
        level = Level.objects.get(id=level_id)
        assessment_type = AssessmentType.objects.get(id=type_id)

        # Filter subjects based on the selected level
        if request.user.is_staff or request.user.is_superuser:
            subjects = Subject.objects.filter(levels=level)
        else:
            # For teachers, filter by both level and their assignments
            teacher_assignments = TeacherAssignment.objects.filter(
                teacher=request.user,
                class_assigned=level,
                term__is_active=True
            ).select_related('subject')

            subject_ids = teacher_assignments.values_list('subject_id', flat=True)
            subjects = Subject.objects.filter(id__in=subject_ids, levels=level)

        enrollments = Enrollment.objects.filter(
            term__is_active=True,
            student__level=level
        ).select_related('student', 'subject')

        student_data = {}

        # Pre-fill each student's grade row
        for enrollment in enrollments:
            student = enrollment.student
            subject = enrollment.subject

            assessment = Assessment.objects.filter(
                enrollment=enrollment,
                assessment_type=assessment_type
            ).first()

            if student.id not in student_data:
                student_data[student.id] = {
                    'student': student,
                    'grades_list': [
                        {'subject_id': subj.id, 'score': "-"}
                        for subj in subjects
                    ]
                }

            # Find the right subject slot and update the score
            for grade in student_data[student.id]['grades_list']:
                if grade['subject_id'] == subject.id:
                    grade['score'] = assessment.score if assessment else "-"
                    break

    context = {
        "levels": levels,
        "assessment_types": assessment_types,
        "terms": terms,
        "subjects": subjects,
        "student_data": student_data,
        "selected_level": int(level_id) if level_id else None,
        "selected_assessment_type": int(type_id) if type_id else None,
        "selected_term": int(term_id) if term_id else None,
    }

    return render(request, "academics/grades/view_grades.html", context)
