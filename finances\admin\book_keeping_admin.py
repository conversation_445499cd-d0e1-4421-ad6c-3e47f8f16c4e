from django.contrib import admin

from finances.book_keeping import Budget, BudgetLine, Outflow, Ledger, JournalEntry,  JournalLine, OutflowLine

admin.site.register(Ledger)


class JournalLineInline(admin.TabularInline):
    model = JournalLine
    extra = 3


class JournalEntryAdmin(admin.ModelAdmin):
    list_display = ("journal_number", "date", "description", "voucher",
                    )
    list_filter = ["term",]
    search_fields = ["journal_number", "description", "voucher"]
    inlines = [JournalLineInline]


admin.site.register(JournalEntry, JournalEntryAdmin)


@admin.register(JournalLine)
class JournalLineAdmin(admin.ModelAdmin):
    list_display = ("journal_entry", "account",
                    "description", "amount", "line_type")
    list_filter = ["journal_entry__term", "account"]
    search_fields = ["journal_entry__journal_number", "description"]


class OutflowInline(admin.TabularInline):
    model = OutflowLine
    extra = 3


class OutflowAdmin(admin.ModelAdmin):
    list_display = ("outflow_id", "date", "description", "payee")
    list_filter = ["term"]
    search_fields = ["outflow_id", "description"]
    inlines = [OutflowInline]


admin.site.register(Outflow, OutflowAdmin)


class BudgetLineInline(admin.TabularInline):
    model = BudgetLine
    extra = 3


class BudgetAdmin(admin.ModelAdmin):
    list_display = ("term", "description")
    list_filter = ["term"]
    search_fields = ["term", "description"]
    inlines = [BudgetLineInline]


admin.site.register(Budget, BudgetAdmin)
