from django.db import models
from django.utils.text import slugify


class Level(models.Model):
    level_name_choices = [
        ('Baby Class', 'Baby Class'),
        ('Reception', 'Reception'),
        ('Toddler Class', 'Toddler Class'),
        ('Primary 1', 'Primary 1'),
        ('Primary 2', 'Primary 2'),
        ('Primary 3', 'Primary 3'),
        ('Primary 4', 'Primary 4'),
    ]
    level_name = models.CharField(max_length=20, choices=level_name_choices)
    abbrv = models.CharField(max_length=10)
    education_stage = models.CharField(
        max_length=200,
        choices=[
            ('Primary', 'Primary'),
            ('Nursery', 'Nursery')
        ], default="Primary"
    )
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.level_name)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.level_name
