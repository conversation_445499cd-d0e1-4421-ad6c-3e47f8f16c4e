{% extends 'base.html' %} {% load humanize %}
<!--  -->
{% block title %}Academic Year Management | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-calendar-alt text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Academic Year Management
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Manage academic years, terms, and academic periods
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 action-buttons-slide-in">
        <a
          href="{% url 'students:add_academic_year' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-plus group-hover:rotate-90 transition-all duration-300"
          ></i>
          <span>Add Academic Year</span>
        </a>
        <a
          href="{% url 'students:add_term' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-calendar-plus group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Add Term</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Current Status Section -->
  <div
    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 status-cards-fade-in"
  >
    <!-- Active Academic Year -->
    <div
      class="card-modern p-6 bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5 border-l-4 border-[#7AB2D3]"
    >
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 bg-[#7AB2D3] rounded-xl flex items-center justify-center"
        >
          <i class="fas fa-calendar text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Active Academic Year</h3>
          {% if active_academic_year %}
          <p class="text-[#40657F] font-medium">
            {{ active_academic_year.name }}
          </p>
          <p class="text-sm text-[#40657F]/70">
            {{ active_academic_year.start_date }} -
            {{active_academic_year.end_date}}
          </p>
          {% else %}
          <p class="text-[#F28C8C] font-medium">No active academic year</p>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Active Term -->
    <div
      class="card-modern p-6 bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5 border-l-4 border-[#74C69D]"
    >
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 bg-[#74C69D] rounded-xl flex items-center justify-center"
        >
          <i class="fas fa-clock text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Active Term</h3>
          {% if active_term %}
          <p class="text-[#40657F] font-medium">{{ active_term.term_name }}</p>
          <p class="text-sm text-[#40657F]/70">
            {{ active_term.start_date }} - {{ active_term.end_date }}
          </p>
          {% else %}
          <p class="text-[#F28C8C] font-medium">No active term</p>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div
      class="card-modern p-6 bg-gradient-to-br from-[#F28C8C]/5 to-[#e74c3c]/5 border-l-4 border-[#F28C8C]"
    >
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 bg-[#F28C8C] rounded-xl flex items-center justify-center"
        >
          <i class="fas fa-cogs text-white"></i>
        </div>
        <div class="flex-1">
          <h3 class="font-bold text-[#2C3E50] text-lg mb-3">Quick Actions</h3>
          <div class="flex flex-col gap-2">
            <a
              href="{% url 'students:activate_term' %}"
              class="text-sm bg-[#7AB2D3] text-white px-3 py-1 rounded-lg hover:bg-[#40657F] transition-colors duration-200"
            >
              Activate Term
            </a>
            <a
              href="{% url 'students:close_academic_year' %}"
              class="text-sm bg-[#F28C8C] text-white px-3 py-1 rounded-lg hover:bg-[#e74c3c] transition-colors duration-200"
            >
              Close Academic Year
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Search Section -->
  <div class="card-modern p-6 search-fade-in">
    <form method="GET" class="flex flex-col lg:flex-row gap-4">
      <div class="flex-1">
        <div class="relative">
          <input
            type="text"
            name="search"
            value="{{ search_query }}"
            placeholder="Search academic years by name..."
            class="w-full px-4 py-3 pl-12 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]/60"
          />
          <i
            class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#7AB2D3]"
          ></i>
        </div>
      </div>
      <div class="flex gap-3">
        <button
          type="submit"
          class="px-6 py-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] transition-all duration-200 font-semibold"
        >
          <i class="fas fa-search mr-2"></i>
          Search
        </button>
        {% if search_query %}
        <a
          href="{% url 'students:academic_year_management' %}"
          class="px-6 py-3 bg-[#B9D8EB] text-[#40657F] rounded-xl hover:bg-[#E2F1F9] transition-all duration-200 font-semibold"
        >
          <i class="fas fa-times mr-2"></i>
          Clear
        </a>
        {% endif %}
      </div>
    </form>
  </div>

  <!-- Academic Years List -->
  <div class="card-modern p-8 table-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
      >
        <i class="fas fa-list text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Academic Years
        </h3>
        <p class="text-[#40657F] text-sm">
          {% if search_query %} Search results for "{{ search_query }}" 
          {%else%} All academic years and their terms {% endif %}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div
        class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30"
      >
        <i class="fas fa-calendar text-sm"></i>
        <span>{{ total_count }} academic year{{ total_count|pluralize }}</span>
      </div>
    </div>

    <div class="space-y-6">
      {% for academic_year in academic_years %}
      <div
        class="academic-year-card bg-white border border-[#B9D8EB]/50 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 {% if academic_year.is_active %}ring-2 ring-[#7AB2D3]/30 bg-gradient-to-r from-[#7AB2D3]/5 to-[#40657F]/5{% endif %}"
      >
        <!-- Academic Year Header -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center gap-4">
            <div
              class="w-12 h-12 {% if academic_year.is_active %}bg-gradient-to-br from-[#7AB2D3] to-[#40657F]{% else %}bg-gradient-to-br from-[#B9D8EB] to-[#E2F1F9]{% endif %} rounded-xl flex items-center justify-center shadow-lg"
            >
              <i class="fas fa-calendar-alt text-white text-lg"></i>
            </div>
            <div>
              <h4
                class="text-xl font-bold text-[#2C3E50] flex items-center gap-2"
              >
                {{ academic_year.name }} {% if academic_year.is_active %}
                <span
                  class="bg-[#74C69D] text-white text-xs px-2 py-1 rounded-full"
                  >Active</span
                >
                {% endif %}
              </h4>
              <p class="text-[#40657F] text-sm">
                {{ academic_year.start_date }} - {{ academic_year.end_date }}
              </p>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <a
              href="{% url 'students:academic_year_details' academic_year.pk %}"
              class="bg-[#7AB2D3] text-white px-3 py-2 rounded-lg hover:bg-[#40657F] transition-colors duration-200 text-sm"
              title="View Details"
            >
              <i class="fas fa-eye"></i>
            </a>
            <a
              href="{% url 'students:edit_academic_year' academic_year.pk %}"
              class="bg-[#74C69D] text-white px-3 py-2 rounded-lg hover:bg-[#5fb085] transition-colors duration-200 text-sm"
              title="Edit"
            >
              <i class="fas fa-edit"></i>
            </a>
          </div>
        </div>

        <!-- Terms -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          {% for term in academic_year.academic_year.all %}
          <div
            class="term-card bg-gradient-to-br from-[#F7FAFC] to-[#E2F1F9] border border-[#B9D8EB]/30 rounded-xl p-4 {% if term.is_active %}ring-2 ring-[#74C69D]/50 bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10{% endif %}"
          >
            <div class="flex items-center justify-between mb-2">
              <h5 class="font-bold text-[#2C3E50] flex items-center gap-2">
                {{ term.term_name }} {% if term.is_active %}
                <span
                  class="bg-[#74C69D] text-white text-xs px-2 py-1 rounded-full"
                  >Active</span
                >
                {% endif %}
              </h5>
              <a
                href="{% url 'students:edit_term' term.pk %}"
                class="text-[#7AB2D3] hover:text-[#40657F] transition-colors duration-200"
                title="Edit Term"
              >
                <i class="fas fa-edit text-sm"></i>
              </a>
            </div>
            <p class="text-[#40657F] text-sm">
              {{ term.start_date }} - {{ term.end_date }}
            </p>
          </div>
          {% empty %}
          <div class="col-span-3 text-center py-4">
            <p class="text-[#40657F] text-sm">
              No terms created for this academic year
            </p>
            <a
              href="{% url 'students:add_term' %}"
              class="text-[#7AB2D3] hover:text-[#40657F] text-sm font-medium"
            >
              Add first term
            </a>
          </div>
          {% endfor %}
        </div>
      </div>
      {% empty %}
      <div class="text-center py-12">
        <div class="flex flex-col items-center gap-4">
          <div
            class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-calendar text-[#B9D8EB] text-2xl"></i>
          </div>
          <div>
            <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
              No Academic Years Found
            </h3>
            <p class="text-[#40657F]">
              {% if search_query %} No academic years match your search
              criteria. {% else %} Get started by creating your first academic
              year. {% endif %}
            </p>
          </div>
          <a
            href="{% url 'students:add_academic_year' %}"
            class="inline-flex items-center gap-2 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
          >
            <i class="fas fa-plus"></i>
            <span>Add First Academic Year</span>
          </a>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex justify-center items-center gap-4 mt-8 pagination-fade-in">
      <div class="flex items-center gap-2">
        {% if page_obj.has_previous %}
        <a
          href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-left"></i>
        </a>
        <a
          href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-left"></i>
        </a>
        {% endif %}

        <span
          class="px-4 py-2 bg-[#7AB2D3] text-white rounded-lg font-semibold"
        >
          Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
        </span>

        {% if page_obj.has_next %}
        <a
          href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-right"></i>
        </a>
        <a
          href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-right"></i>
        </a>
        {% endif %}
      </div>
    </div>
    {% endif %}
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Status Cards Animation */
  .status-cards-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: statusCardsFadeIn 0.8s ease-out 1s forwards;
  }

  /* Search Section Animation */
  .search-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: searchFadeIn 0.8s ease-out 1.2s forwards;
  }

  /* Table Section Animation */
  .table-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: tableSectionFadeIn 0.8s ease-out 1.4s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  /* Academic Year Cards Animation */
  .academic-year-card {
    opacity: 0;
    transform: translateX(-20px);
    animation: academicYearCardSlideIn 0.4s ease-out forwards;
  }

  .academic-year-card:nth-child(1) {
    animation-delay: 1.6s;
  }
  .academic-year-card:nth-child(2) {
    animation-delay: 1.7s;
  }
  .academic-year-card:nth-child(3) {
    animation-delay: 1.8s;
  }
  .academic-year-card:nth-child(4) {
    animation-delay: 1.9s;
  }
  .academic-year-card:nth-child(5) {
    animation-delay: 2s;
  }

  .pagination-fade-in {
    opacity: 0;
    animation: paginationFadeIn 0.8s ease-out 2.2s forwards;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes statusCardsFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes searchFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes tableSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes tableIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(-3deg);
    }
  }

  @keyframes academicYearCardSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes paginationFadeIn {
    to {
      opacity: 1;
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .academic-year-card {
      animation-delay: 1.2s;
    }

    .academic-year-card:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--card-index, 1));
    }
  }
</style>

{% endblock %}
