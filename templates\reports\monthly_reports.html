{% extends 'base.html' %} {% load static %} {% load humanize %}
<!--  -->

{% block content %}
<section class="flex gap-4 flex-col w-full">
  <div class="flex items-center justify-between w-full">
    <h1 class="text-2xl font-semibold text-[#7AB2D3]">
      Income Statement For {{month}}
    </h1>
    <a
      href="{% url 'finances:monthly_reports' month  %}"
      class="text-lg font-semibold border-2 border-[#7AB2D3] px-4 py-1 text-gray-500 hover:bg-[#7AB2D3] hover:text-white rounded-md transition-all duration-300 ease-in-out"
      >Download</a
    >
  </div>
  <table class="border border-gray-400 w-full">
    <thead>
      <tr class="bg-gray-300 py-2">
        <th class="border border-black py-2">Revenue</th>
        <th class="border border-black">{{month}}</th>
      </tr>
    </thead>
    <tbody>
      {% for revenue in revenue_ledgers %}
      <tr>
        <td class="border border-gray-400 py-2 px-4">
          {{revenue.account__name}}
        </td>
        <td class="border border-gray-400 py-2 px-4 text-right">
          MWK {{revenue.total_amount | floatformat:2|intcomma}}
        </td>
      </tr>
      {% endfor %}
      <tr>
        <td class="border border-gray-400 py-2 px-4 font-bold">Total:</td>
        <td class="border border-gray-400 py-2 px-4 text-right">
          MWK {{revenue_total |floatformat:2|intcomma}}
        </td>
      </tr>
    </tbody>
  </table>
</section>

<section class="flex gap-4 flex-col w-full">
  <div class="flex flex-col text-left items-center justify-center gap-8 w-full">
    <div class="flex flex-col gap-2 w-full">
      <table class="table-fixed border border-gray-400 w-full">
        <thead>
          <tr class="bg-gray-300">
            <th class="border border-black py-2 px-4">Expense</th>
            <th class="border border-black py-2 px-4"></th>
          </tr>
        </thead>
        <tbody>
          {% for expense in expense_ledgers %}
          <tr>
            <td class="border border-gray-400 py-2 px-4">
              {{expense.account__name}}
            </td>
            <td class="border border-gray-400 py-2 px-4 text-right">
              MWK {{expense_total |floatformat:2|intcomma}}
            </td>
          </tr>
          {% endfor %}
          <tr class="border border-gray-400 font-semibold">
            <td class="border border-gray-400 py-2 px-4">Sub Total</td>
            <td class="border border-gray-400 py-2 px-4 text-right">
              MWK {{expense_total |floatformat:2|intcomma}}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>
{% endblock %}
