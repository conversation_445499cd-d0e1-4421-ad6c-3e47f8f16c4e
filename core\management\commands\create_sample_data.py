from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from django.contrib.auth import get_user_model

from core.models import (
    NotificationType, Notification, 
    EventCategory, CalendarEvent
)
from students.models import Student, Term, Level
from finances.fee_management.models import FeeAccount

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample notifications and calendar events for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create notification types
        notification_types = [
            {
                'name': 'Fee Reminder',
                'icon': 'fas fa-dollar-sign',
                'color': '#F28C8C'
            },
            {
                'name': 'Academic Update',
                'icon': 'fas fa-graduation-cap',
                'color': '#7AB2D3'
            },
            {
                'name': 'System Alert',
                'icon': 'fas fa-exclamation-triangle',
                'color': '#e07575'
            },
            {
                'name': 'General Info',
                'icon': 'fas fa-info-circle',
                'color': '#74C69D'
            }
        ]
        
        for nt_data in notification_types:
            nt, created = NotificationType.objects.get_or_create(
                name=nt_data['name'],
                defaults={
                    'icon': nt_data['icon'],
                    'color': nt_data['color']
                }
            )
            if created:
                self.stdout.write(f'Created notification type: {nt.name}')
        
        # Create event categories
        event_categories = [
            {
                'name': 'Academic',
                'color': '#7AB2D3',
                'icon': 'fas fa-graduation-cap'
            },
            {
                'name': 'Administrative',
                'color': '#40657F',
                'icon': 'fas fa-cog'
            },
            {
                'name': 'Holiday',
                'color': '#74C69D',
                'icon': 'fas fa-calendar-day'
            },
            {
                'name': 'Examination',
                'color': '#F28C8C',
                'icon': 'fas fa-clipboard-check'
            },
            {
                'name': 'Meeting',
                'color': '#B9D8EB',
                'icon': 'fas fa-users'
            }
        ]
        
        for ec_data in event_categories:
            ec, created = EventCategory.objects.get_or_create(
                name=ec_data['name'],
                defaults={
                    'color': ec_data['color'],
                    'icon': ec_data['icon']
                }
            )
            if created:
                self.stdout.write(f'Created event category: {ec.name}')
        
        # Get some existing data for relationships
        try:
            active_term = Term.objects.get_active()
        except:
            active_term = None
            
        students = Student.objects.filter(is_active=True)[:5]
        fee_accounts = FeeAccount.objects.all()[:3]
        
        # Create sample notifications
        fee_reminder_type = NotificationType.objects.get(name='Fee Reminder')
        academic_type = NotificationType.objects.get(name='Academic Update')
        system_type = NotificationType.objects.get(name='System Alert')
        info_type = NotificationType.objects.get(name='General Info')
        
        sample_notifications = [
            {
                'title': 'Outstanding Fee Payment',
                'message': 'Several students have outstanding fee payments for this term. Please review and follow up.',
                'notification_type': fee_reminder_type,
                'priority': 'high',
                'related_student': students[0] if students else None,
                'related_fee_account': fee_accounts[0] if fee_accounts else None,
            },
            {
                'title': 'Term Assessment Deadline',
                'message': 'Reminder: Term assessment submissions are due in 3 days. Please ensure all assessments are completed.',
                'notification_type': academic_type,
                'priority': 'medium',
            },
            {
                'title': 'System Maintenance Scheduled',
                'message': 'System maintenance is scheduled for this weekend. The system may be unavailable for 2 hours.',
                'notification_type': system_type,
                'priority': 'low',
                'expires_at': timezone.now() + timedelta(days=7),
            },
            {
                'title': 'New Academic Year Planning',
                'message': 'Planning for the new academic year has begun. Please review the proposed calendar and provide feedback.',
                'notification_type': info_type,
                'priority': 'medium',
            },
            {
                'title': 'Student Registration Update',
                'message': 'New student registrations are now open for the next term. Please update the admission requirements.',
                'notification_type': info_type,
                'priority': 'low',
            }
        ]
        
        for notif_data in sample_notifications:
            notification, created = Notification.objects.get_or_create(
                title=notif_data['title'],
                defaults=notif_data
            )
            if created:
                self.stdout.write(f'Created notification: {notification.title}')
        
        # Create sample calendar events
        academic_cat = EventCategory.objects.get(name='Academic')
        admin_cat = EventCategory.objects.get(name='Administrative')
        holiday_cat = EventCategory.objects.get(name='Holiday')
        exam_cat = EventCategory.objects.get(name='Examination')
        meeting_cat = EventCategory.objects.get(name='Meeting')
        
        # Get first user for created_by field
        try:
            first_user = User.objects.first()
        except:
            self.stdout.write(self.style.ERROR('No users found. Please create a user first.'))
            return
        
        today = timezone.now().date()
        
        sample_events = [
            {
                'title': 'Term 1 Begins',
                'description': 'First day of Term 1 for all students',
                'event_type': 'academic',
                'category': academic_cat,
                'start_date': today + timedelta(days=1),
                'end_date': today + timedelta(days=1),
                'is_all_day': True,
                'created_by': first_user,
                'related_term': active_term,
            },
            {
                'title': 'Staff Meeting',
                'description': 'Monthly staff meeting to discuss academic progress and administrative matters',
                'event_type': 'meeting',
                'category': meeting_cat,
                'start_date': today + timedelta(days=3),
                'end_date': today + timedelta(days=3),
                'start_time': datetime.strptime('09:00', '%H:%M').time(),
                'end_time': datetime.strptime('11:00', '%H:%M').time(),
                'location': 'Conference Room',
                'created_by': first_user,
            },
            {
                'title': 'Mid-Term Examinations',
                'description': 'Mid-term examinations for all levels',
                'event_type': 'exam',
                'category': exam_cat,
                'start_date': today + timedelta(days=5),
                'end_date': today + timedelta(days=7),
                'is_all_day': True,
                'created_by': first_user,
                'related_term': active_term,
            },
            {
                'title': 'Parent-Teacher Conference',
                'description': 'Scheduled meetings with parents to discuss student progress',
                'event_type': 'meeting',
                'category': meeting_cat,
                'start_date': today + timedelta(days=10),
                'end_date': today + timedelta(days=10),
                'start_time': datetime.strptime('14:00', '%H:%M').time(),
                'end_time': datetime.strptime('17:00', '%H:%M').time(),
                'location': 'School Hall',
                'created_by': first_user,
            },
            {
                'title': 'Independence Day Holiday',
                'description': 'National holiday - school closed',
                'event_type': 'holiday',
                'category': holiday_cat,
                'start_date': today + timedelta(days=15),
                'end_date': today + timedelta(days=15),
                'is_all_day': True,
                'created_by': first_user,
            },
            {
                'title': 'Fee Collection Deadline',
                'description': 'Final deadline for term fee payments',
                'event_type': 'administrative',
                'category': admin_cat,
                'start_date': today + timedelta(days=20),
                'end_date': today + timedelta(days=20),
                'is_all_day': True,
                'created_by': first_user,
                'related_term': active_term,
            }
        ]
        
        for event_data in sample_events:
            event, created = CalendarEvent.objects.get_or_create(
                title=event_data['title'],
                start_date=event_data['start_date'],
                defaults=event_data
            )
            if created:
                self.stdout.write(f'Created calendar event: {event.title}')
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created sample data!')
        )
