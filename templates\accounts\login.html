{% load static %}

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'img/favicon.ico' %}"
    />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css"
      integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Main CSS -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />

    <style>
      /* Custom animations and effects that can't be replicated with Tailwind utilities */

      /* Enhanced floating animation */

      /* Enhanced floating animation */
      @keyframes float {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        33% {
          transform: translateY(-15px) rotate(1deg);
        }
        66% {
          transform: translateY(-5px) rotate(-1deg);
        }
      }

      @keyframes pulse-glow {
        0%,
        100% {
          box-shadow: 0 0 20px rgba(122, 178, 211, 0.3);
        }
        50% {
          box-shadow: 0 0 40px rgba(122, 178, 211, 0.6);
        }
      }

      .float-animation {
        animation: float 8s ease-in-out infinite;
      }

      .pulse-glow {
        animation: pulse-glow 3s ease-in-out infinite;
      }

      /* Grain texture effect */
      .grain-texture {
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      }

      /* Button hover effects */
      .btn-modern {
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
      }

      .btn-modern::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .btn-modern:hover::before {
        left: 100%;
      }

      /* Particle animation */
      @keyframes particle-float {
        0% {
          transform: translateY(100vh) rotate(0deg);
          opacity: 0;
        }
        10% {
          opacity: 1;
        }
        90% {
          opacity: 1;
        }
        100% {
          transform: translateY(-100vh) rotate(360deg);
          opacity: 0;
        }
      }

      .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        animation: particle-float 15s linear infinite;
      }

      .particle:nth-child(1) {
        left: 10%;
        animation-delay: 0s;
      }
      .particle:nth-child(2) {
        left: 20%;
        animation-delay: 2s;
      }
      .particle:nth-child(3) {
        left: 30%;
        animation-delay: 4s;
      }
      .particle:nth-child(4) {
        left: 40%;
        animation-delay: 6s;
      }
      .particle:nth-child(5) {
        left: 50%;
        animation-delay: 8s;
      }
      .particle:nth-child(6) {
        left: 60%;
        animation-delay: 10s;
      }
      .particle:nth-child(7) {
        left: 70%;
        animation-delay: 12s;
      }
      .particle:nth-child(8) {
        left: 80%;
        animation-delay: 14s;
      }
      .particle:nth-child(9) {
        left: 90%;
        animation-delay: 16s;
      }

      body {
        font-family: Roboto;
      }
    </style>

    <title>Login - Tiny Feet Academy</title>
  </head>
  <body
    class="min-h-screen bg-gradient-to-br from-[#7AB2D3] via-[#40657F] to-[#2C3E50] flex items-center justify-center p-4 font-['Roboto'] relative"
  >
    <!-- Background texture overlay -->
    <div
      class="absolute inset-0 bg-black/5 opacity-30 pointer-events-none grain-texture"
    ></div>

    <!-- Enhanced background decorative elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- Floating particles -->
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>

      <!-- Floating geometric shapes -->
      <div
        class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full float-animation blur-sm"
      ></div>
      <div
        class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-5 rounded-full float-animation blur-sm"
        style="animation-delay: -3s"
      ></div>
      <div
        class="absolute top-1/2 left-1/4 w-32 h-32 bg-white opacity-10 rounded-full float-animation blur-sm"
        style="animation-delay: -1s"
      ></div>
      <div
        class="absolute top-1/4 right-1/4 w-24 h-24 bg-gradient-to-br from-white to-transparent opacity-15 rounded-lg float-animation transform rotate-45"
        style="animation-delay: -2s"
      ></div>
      <div
        class="absolute bottom-1/4 left-1/3 w-16 h-16 bg-gradient-to-tr from-white to-transparent opacity-20 rounded-full float-animation"
        style="animation-delay: -4s"
      ></div>
    </div>

    <main class="relative z-10 w-full max-w-md">
      <!-- Login Card -->
      <div
        class="bg-white/95 backdrop-blur-xl border border-white/30 rounded-3xl shadow-2xl p-6 space-y-6 transform hover:scale-[1.02] transition-all duration-500 glass-card"
      >
        <!-- Header Section -->
        <div class="text-center space-y-4">
          <!-- Logo/Icon -->
          <div
            class="mx-auto w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-xl pulse-glow relative overflow-hidden"
          >
            <div
              class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-3xl"
            ></div>
            <img
              src="{% static 'img/tinyfeet.jpg' %}"
              alt="Tiny Feet Academy"
              class="w-10 h-10 rounded-xl object-cover relative z-10 shadow-lg"
              onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
            />
            <i
              class="fas fa-graduation-cap text-white text-xl hidden relative z-10"
            ></i>
          </div>

          <!-- Title -->
          <div class="space-y-2">
            <h1
              class="text-2xl font-bold text-gray-800 font-['Roboto'] tracking-tight"
            >
              Welcome Back
            </h1>
            <p class="text-gray-600 font-medium text-base">
              Sign in to
              <span
                class="font-bold bg-gradient-to-r from-[#7AB2D3] to-[#40657F] bg-clip-text text-transparent"
                >{{request.tenant.name|default:"Tiny Feet Academy"}}</span
              >
            </p>
            <div
              class="w-12 h-0.5 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mx-auto"
            ></div>
          </div>
        </div>

        <!-- Login Form -->
        <form action="" method="post" class="space-y-3">
          {% csrf_token %}

          <!-- Username Field -->
          <div class="space-y-2">
            <label
              for="username"
              class="flex items-center gap-2 text-sm font-bold text-gray-700 font-['Roboto']"
            >
              <div
                class="w-4 h-4 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center"
              >
                <i class="fas fa-user text-white text-xs"></i>
              </div>
              Username
            </label>
            <div class="relative">
              <input
                type="text"
                id="username"
                name="username"
                placeholder="Enter your username"
                required
                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-[#7AB2D3] focus:ring-2 focus:ring-[#7AB2D3]/20 outline-none transition-all duration-300 bg-white/90 text-gray-800 font-medium placeholder-gray-400 shadow-inner hover:shadow-lg focus:shadow-xl font-['Roboto']"
              />
              <div
                class="absolute inset-0 rounded-xl bg-gradient-to-r from-[#7AB2D3]/5 to-[#40657F]/5 opacity-0 transition-opacity duration-300 pointer-events-none focus-within:opacity-100"
              ></div>
            </div>
          </div>

          <!-- Password Field -->
          <div class="space-y-2">
            <label
              for="password"
              class="flex items-center gap-2 text-sm font-bold text-gray-700 font-['Roboto']"
            >
              <div
                class="w-4 h-4 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center"
              >
                <i class="fas fa-lock text-white text-xs"></i>
              </div>
              Password
            </label>
            <div class="relative">
              <input
                type="password"
                id="password"
                name="password"
                placeholder="Enter your password"
                required
                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-[#7AB2D3] focus:ring-2 focus:ring-[#7AB2D3]/20 outline-none transition-all duration-300 bg-white/90 text-gray-800 font-medium placeholder-gray-400 shadow-inner pr-12 hover:shadow-lg focus:shadow-xl font-['Roboto']"
              />
              <div
                class="absolute inset-0 rounded-xl bg-gradient-to-r from-[#7AB2D3]/5 to-[#40657F]/5 opacity-0 transition-opacity duration-300 pointer-events-none focus-within:opacity-100"
              ></div>
              <button
                type="button"
                onclick="togglePassword()"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-[#7AB2D3] transition-all duration-300 p-1 rounded-lg hover:bg-gray-100/50"
              >
                <i id="password-toggle-icon" class="fas fa-eye text-sm"></i>
              </button>
            </div>
          </div>

          <!-- Error Message -->
          {% if message %}
          <div
            class="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200/50 rounded-xl p-3 flex items-center space-x-3 shadow-lg backdrop-blur-sm"
          >
            <div
              class="w-6 h-6 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg"
            >
              <i class="fas fa-exclamation-triangle text-white text-xs"></i>
            </div>
            <div class="flex-1">
              <p class="text-red-700 font-semibold text-xs font-['Roboto']">
                Authentication Error
              </p>
              <p class="text-red-600 text-xs font-['Roboto']">{{message}}</p>
            </div>
          </div>
          {% endif %}

          <!-- Submit Button -->
          <button
            type="submit"
            class="w-full bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-2 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl btn-modern relative overflow-hidden"
          >
            <span
              class="relative z-10 flex items-center justify-center gap-2 font-['Roboto']"
            >
              <i class="fas fa-sign-in-alt text-sm"></i>
              <span class="font-['Poppins'] tracking-wide">Sign In</span>
            </span>
          </button>
        </form>

        <!-- Footer -->
        <div class="text-center pt-4 border-t border-gray-200/50">
          <div class="flex items-center justify-center gap-1 mb-1">
            <div
              class="w-1.5 h-1.5 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full"
            ></div>
            <div class="w-1 h-1 bg-gray-300 rounded-full"></div>
            <div
              class="w-1.5 h-1.5 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full"
            ></div>
          </div>
          <p class="text-xs text-gray-500 font-medium font-['Roboto']">
            Secure login powered by
            <span
              class="font-bold bg-gradient-to-r from-[#7AB2D3] to-[#40657F] bg-clip-text text-transparent"
              >Mahara RND</span
            >
          </p>
        </div>
      </div>

      <!-- Additional Info -->
      <footer class="mt-8 text-center space-y-4">
        <!-- Main Security Message -->
        <div
          class="flex items-center justify-center gap-3 text-white text-sm font-medium font-['Roboto'] bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-4 border border-white/20 shadow-lg"
        >
          <div
            class="w-8 h-8 bg-white/30 rounded-xl flex items-center justify-center backdrop-blur-sm shadow-inner"
          >
            <i class="fas fa-shield-alt text-sm text-[#2C3E50]"></i>
          </div>
          <span class="text-white font-semibold"
            >Your data is protected with enterprise-grade security</span
          >
        </div>

        <!-- Security Features -->
        <div
          class="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 text-white text-xs font-['Roboto'] bg-white/5 backdrop-blur-sm rounded-xl px-4 py-3 border border-white/10"
        >
          <div class="flex items-center gap-2 bg-white/10 rounded-lg px-3 py-2">
            <i class="fas fa-lock text-[#74C69D]"></i>
            <span class="text-white font-medium">SSL Encrypted</span>
          </div>
          <div class="flex items-center gap-2 bg-white/10 rounded-lg px-3 py-2">
            <i class="fas fa-server text-[#7AB2D3]"></i>
            <span class="text-white font-medium">Secure Servers</span>
          </div>
          <div class="flex items-center gap-2 bg-white/10 rounded-lg px-3 py-2">
            <i class="fas fa-user-shield text-[#B9D8EB]"></i>
            <span class="text-white font-medium">Privacy Protected</span>
          </div>
        </div>
      </footer>
    </main>

    <!-- JavaScript for password toggle -->
    <script>
      function togglePassword() {
        const passwordInput = document.getElementById("password");
        const toggleIcon = document.getElementById("password-toggle-icon");

        if (passwordInput.type === "password") {
          passwordInput.type = "text";
          toggleIcon.classList.remove("fa-eye");
          toggleIcon.classList.add("fa-eye-slash");
        } else {
          passwordInput.type = "password";
          toggleIcon.classList.remove("fa-eye-slash");
          toggleIcon.classList.add("fa-eye");
        }
      }

      // Add subtle animations on load
      document.addEventListener("DOMContentLoaded", function () {
        const card = document.querySelector(".glass-card");
        card.style.opacity = "0";
        card.style.transform = "translateY(20px)";

        setTimeout(() => {
          card.style.transition = "all 0.6s ease-out";
          card.style.opacity = "1";
          card.style.transform = "translateY(0)";
        }, 100);
      });
    </script>
  </body>
</html>
