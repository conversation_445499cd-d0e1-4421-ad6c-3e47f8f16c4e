from django.db import models

from academics.maps import get_grade
from core.base_models import NameSlugModels
from students.models import Level, Student


class Subject(NameSlugModels):
    student = models.ManyToManyField(Student, through="Enrollment")
    abbrv = models.CharField(max_length=100)
    category = models.CharField(max_length=50)
    levels = models.ManyToManyField(
        Level,
        related_name='subjects',
        help_text="Classes where this subject is taught"
    )


class Enrollment(models.Model):
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE)
    term = models.ForeignKey('students.Term', on_delete=models.CASCADE)

    @property
    def final_grade(self):
        assessments = self.assessments.filter(assessment_type__weight__gt=0)
        total_score = sum(assessment.score for assessment in assessments)

        return total_score

    @property
    def grade(self):
        return get_grade(self.final_grade)

    @property
    def student_count(self):
        return self.subject.student.filter(level=self.student.level, is_active=True).count()

    @property
    def has_passed(self):
        if self.final_grade >= 40:
            return True
        return False

    @property
    def position(self):
        enrollments = Enrollment.objects.filter(
            subject=self.subject,
            term=self.term,
            student__level=self.student.level
        )

        sorted_enrollments = sorted(
            enrollments,
            key=lambda enrollment: enrollment.final_grade if enrollment.final_grade is not None else 0,
            reverse=True
        )

        for index, enrollment in enumerate(sorted_enrollments, start=1):
            if enrollment.id == self.id:
                return index
        return None

    def save(self, *args, **kwargs):
        from students.models import Term
        self.term = Term.objects.get_active()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.student.name} | {self.subject.name} | {self.term}"

    class Meta:
        unique_together = ('student', 'subject', 'term')
