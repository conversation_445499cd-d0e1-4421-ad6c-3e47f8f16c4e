# Generated by Django 5.1.2 on 2025-06-26 19:34

import datetime
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ExpenseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'finances_expense_category',
            },
        ),
        migrations.CreateModel(
            name='Ledger',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('ledger_type', models.CharField(choices=[('Asset', 'Asset'), ('Liability', 'Liability'), ('Equity', 'Equity'), ('Revenue', 'Revenue'), ('Expense', 'Expense')], max_length=50)),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
            ],
            options={
                'db_table': 'finances_ledger',
            },
        ),
        migrations.CreateModel(
            name='Budget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, null=True)),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.term')),
            ],
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=datetime.date.today)),
                ('receipt_number', models.CharField(max_length=100)),
                ('amount', models.IntegerField(default=0)),
                ('description', models.TextField(blank=True, null=True)),
                ('responsibility', models.CharField(max_length=100)),
                ('payment_method', models.CharField(choices=[('Cash', 'Cash'), ('Bank Transfer', 'Bank Transfer'), ('Cheque', 'Cheque'), ('Mobile Money', 'Mobile Money')], max_length=50)),
                ('document', models.FileField(blank=True, null=True, upload_to='documents/')),
                ('document_number', models.CharField(blank=True, max_length=100, null=True)),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.term')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expenditures', to='finances.expensecategory')),
            ],
            options={
                'db_table': 'finances_expense',
            },
        ),
        migrations.CreateModel(
            name='ExpenseTotal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_total', models.IntegerField(default=0)),
                ('total_spent', models.IntegerField(default=0)),
                ('balance', models.IntegerField(default=0)),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.term')),
            ],
            options={
                'db_table': 'finances_expense_total',
            },
        ),
        migrations.CreateModel(
            name='FeeCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('amount', models.IntegerField()),
            ],
            options={
                'verbose_name_plural': 'Fee Categories',
                'db_table': 'receipts_fee_category',
                'unique_together': {('name', 'amount')},
            },
        ),
        migrations.CreateModel(
            name='FeeAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_due', models.IntegerField(default=0)),
                ('amount_paid', models.IntegerField(default=0)),
                ('is_paid', models.BooleanField(default=False)),
                ('carry_on', models.IntegerField(default=0)),
                ('balance', models.IntegerField(default=0)),
                ('billing_cycle', models.CharField(blank=True, choices=[('Monthly', 'Monthly'), ('Termly', 'Termly')], max_length=100, null=True)),
                ('month', models.DateField(blank=True, null=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.student')),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.term')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finances.feecategory')),
            ],
            options={
                'db_table': 'receipts_fee_account',
                'unique_together': {('student', 'category', 'term', 'month')},
            },
        ),
        migrations.CreateModel(
            name='IncomeTotal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_total', models.IntegerField(default=0)),
                ('total_collected', models.IntegerField(default=0)),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.term')),
            ],
            options={
                'db_table': 'finances_income_total',
            },
        ),
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('journal_number', models.CharField(blank=True, max_length=100, null=True)),
                ('date', models.DateField(default=datetime.date.today)),
                ('voucher', models.CharField(blank=True, max_length=100, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.term')),
            ],
            options={
                'db_table': 'finances_journal_entry',
                'ordering': ['-date'],
                'unique_together': {('journal_number', 'voucher')},
            },
        ),
        migrations.CreateModel(
            name='JournalLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, null=True)),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=20)),
                ('line_type', models.CharField(choices=[('Debit', 'Debit'), ('Credit', 'Credit')], max_length=50)),
                ('journal_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finances.journalentry')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='journal_line', to='finances.ledger')),
            ],
            options={
                'db_table': 'finances_journal_line',
            },
        ),
        migrations.CreateModel(
            name='Outflow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('outflow_id', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('date', models.DateField()),
                ('payee', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='outflows', to=settings.AUTH_USER_MODEL)),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='students.term')),
            ],
            options={
                'db_table': 'finances_outflow',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='OutflowLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('jounal_line_created', models.BooleanField(default=False)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finances.ledger')),
                ('outflow', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outflow_lines', to='finances.outflow')),
            ],
            options={
                'db_table': 'finances_outflow_line',
            },
        ),
        migrations.CreateModel(
            name='Receipt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('receipt_number', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('amount_paid', models.IntegerField()),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
                ('fee_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finances.feeaccount')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.student')),
            ],
            options={
                'db_table': 'receipts_receipt',
            },
        ),
        migrations.CreateModel(
            name='Expenditure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('targeted', models.IntegerField(default=1000000)),
                ('actual', models.IntegerField(default=0)),
                ('balance', models.IntegerField(default=0)),
                ('month_year', models.DateField(default=django.utils.timezone.now)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finances.expensecategory')),
            ],
            options={
                'db_table': 'reports_expenditure',
                'unique_together': {('category', 'month_year')},
            },
        ),
        migrations.CreateModel(
            name='FeesWaiver',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('waiver_id', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
                ('amount_waived', models.IntegerField(default=0)),
                ('waived_by', models.CharField(max_length=100)),
                ('date_waived', models.DateField(default=datetime.datetime.today)),
                ('reason', models.CharField(max_length=255)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finances.feeaccount')),
            ],
            options={
                'db_table': 'receipts_fees_waiver',
                'unique_together': {('account', 'date_waived', 'amount_waived')},
            },
        ),
        migrations.CreateModel(
            name='Income',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('targeted', models.IntegerField(default=0)),
                ('actual', models.IntegerField(default=0)),
                ('balance', models.IntegerField(default=0)),
                ('month_year', models.DateField(default=django.utils.timezone.now)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finances.feecategory')),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.term')),
            ],
            options={
                'db_table': 'reports_income',
                'unique_together': {('category', 'month_year')},
            },
        ),
        migrations.CreateModel(
            name='BudgetLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=20)),
                ('budget', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='budget_lines', to='finances.budget')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finances.ledger')),
            ],
            options={
                'ordering': ['budget'],
                'unique_together': {('budget', 'account')},
            },
        ),
    ]
