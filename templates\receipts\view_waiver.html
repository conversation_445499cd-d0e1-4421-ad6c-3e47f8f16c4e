{% extends 'base.html' %} {% load static %} {% load humanize %}
<!--  -->
{% block title %}View Waiver - {{ waiver.waiver_id }} | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-6xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-lg icon-float"
      >
        <i class="fas fa-receipt text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in"
        >
          Fee Waiver Details
        </h1>
        <div
          class="w-20 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <a
        href="{% url 'students:home' %}"
        class="text-[#40657F] hover:text-[#2C3E50] transition-colors"
        >Home</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'finances:waiver_list' %}"
        class="text-[#40657F] hover:text-[#2C3E50] transition-colors"
        >Waivers</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#F28C8C] font-semibold">{{ waiver.waiver_id }}</span>
    </nav>
  </div>

  <!-- Main Content -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Waiver Details Card -->
    <div class="lg:col-span-2 space-y-8">
      <!-- Waiver Information -->
      <div class="card-modern p-8 form-fade-in">
        <div class="flex items-center gap-4 mb-8">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg"
          >
            <i class="fas fa-info-circle text-white text-lg"></i>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
              Waiver Information
            </h2>
            <p class="text-[#40657F] text-sm">
              Complete details of the fee waiver
            </p>
          </div>
          <div
            class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
          ></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div
            class="bg-gradient-to-br from-[#E2F1F9] to-[#B9D8EB]/50 rounded-2xl p-6 border border-[#B9D8EB]/50"
          >
            <p
              class="text-xs font-bold text-[#40657F] uppercase tracking-wider mb-2"
            >
              Waiver ID
            </p>
            <p class="text-[#2C3E50] font-bold text-xl">
              {{ waiver.waiver_id }}
            </p>
          </div>

          <div
            class="bg-gradient-to-br from-[#F28C8C]/10 to-[#e07575]/10 rounded-2xl p-6 border border-[#F28C8C]/20"
          >
            <p
              class="text-xs font-bold text-[#40657F] uppercase tracking-wider mb-2"
            >
              Amount Waived
            </p>
            <p class="text-[#F28C8C] font-bold text-xl">
              K {{ waiver.amount_waived|intcomma }}
            </p>
          </div>

          <div
            class="bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10 rounded-2xl p-6 border border-[#74C69D]/20"
          >
            <p
              class="text-xs font-bold text-[#40657F] uppercase tracking-wider mb-2"
            >
              Date Waived
            </p>
            <p class="text-[#74C69D] font-bold text-lg">
              {{ waiver.date_waived|date:"M d, Y" }}
            </p>
          </div>

          <div
            class="bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 rounded-2xl p-6 border border-[#7AB2D3]/20"
          >
            <p
              class="text-xs font-bold text-[#40657F] uppercase tracking-wider mb-2"
            >
              Waived By
            </p>
            <p class="text-[#7AB2D3] font-bold text-lg">
              {{ waiver.waived_by }}
            </p>
          </div>
        </div>

        <!-- Reason Section -->
        <div class="mt-8">
          <div
            class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]/30 rounded-2xl p-6 border border-[#B9D8EB]/50"
          >
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-8 h-8 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-full flex items-center justify-center"
              >
                <i class="fas fa-comment text-white text-sm"></i>
              </div>
              <h3 class="font-bold text-[#2C3E50] font-display text-lg">
                Reason for Waiver
              </h3>
            </div>
            <p class="text-[#40657F] leading-relaxed">{{ waiver.reason }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Student & Account Information -->
    <div class="space-y-8">
      <!-- Student Information -->
      <div class="card-modern p-6 form-fade-in">
        <div class="flex items-center gap-3 mb-6">
          <div
            class="w-10 h-10 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center"
          >
            <i class="fas fa-user text-white text-sm"></i>
          </div>
          <h3 class="text-xl font-bold text-[#2C3E50] font-display">
            Student Details
          </h3>
        </div>

        <div class="space-y-4">
          <div
            class="flex justify-between items-center py-3 border-b border-[#B9D8EB]/30"
          >
            <span class="text-[#40657F] font-medium">Name</span>
            <span class="text-[#2C3E50] font-bold"
              >{{ waiver.account.student.name }}</span
            >
          </div>
          <div
            class="flex justify-between items-center py-3 border-b border-[#B9D8EB]/30"
          >
            <span class="text-[#40657F] font-medium">Student ID</span>
            <span class="text-[#2C3E50] font-bold"
              >{{ waiver.account.student.student_id }}</span
            >
          </div>
          <div
            class="flex justify-between items-center py-3 border-b border-[#B9D8EB]/30"
          >
            <span class="text-[#40657F] font-medium">Class</span>
            <span class="text-[#2C3E50] font-bold"
              >{{ waiver.account.student.level.level_name }}</span
            >
          </div>
        </div>

        <div class="mt-6">
          <a
            href="{% url 'students:student_details' waiver.account.student.student_id %}"
            class="inline-flex items-center gap-2 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <i class="fas fa-eye"></i>
            <span>View Student</span>
          </a>
        </div>
      </div>

      <!-- Fee Account Information -->
      <div class="card-modern p-6 form-fade-in">
        <div class="flex items-center gap-3 mb-6">
          <div
            class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center"
          >
            <i class="fas fa-credit-card text-white text-sm"></i>
          </div>
          <h3 class="text-xl font-bold text-[#2C3E50] font-display">
            Fee Account
          </h3>
        </div>

        <div class="space-y-4">
          <div
            class="flex justify-between items-center py-3 border-b border-[#B9D8EB]/30"
          >
            <span class="text-[#40657F] font-medium">Category</span>
            <span class="text-[#2C3E50] font-bold"
              >{{ waiver.account.category.name }}</span
            >
          </div>
          <div
            class="flex justify-between items-center py-3 border-b border-[#B9D8EB]/30"
          >
            <span class="text-[#40657F] font-medium">Term</span>
            <span class="text-[#2C3E50] font-bold"
              >{{ waiver.account.term }}</span
            >
          </div>
          <div
            class="flex justify-between items-center py-3 border-b border-[#B9D8EB]/30"
          >
            <span class="text-[#40657F] font-medium">Total Due</span>
            <span class="text-[#2C3E50] font-bold"
              >K {{ waiver.account.total_due|intcomma }}</span
            >
          </div>
          <div class="flex justify-between items-center py-3">
            <span class="text-[#40657F] font-medium">Current Balance</span>
            <span class="text-[#2C3E50] font-bold"
              >K {{ waiver.account.current_balance|intcomma }}</span
            >
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="card-modern p-6 form-fade-in">
        <div class="flex items-center gap-3 mb-6">
          <div
            class="w-10 h-10 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center"
          >
            <i class="fas fa-cogs text-white text-sm"></i>
          </div>
          <h3 class="text-xl font-bold text-[#2C3E50] font-display">Actions</h3>
        </div>

        <div class="space-y-3">
          <a
            href="{% url 'finances:waiver_list' %}"
            class="w-full inline-flex items-center justify-center gap-2 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <i class="fas fa-list"></i>
            <span>All Waivers</span>
          </a>

          <button
            onclick="window.print()"
            class="w-full inline-flex items-center justify-center gap-2 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-3 px-6 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <i class="fas fa-print"></i>
            <span>Print Waiver</span>
          </button>

          {% if user.is_superuser %}
          <button
            onclick="deleteWaiver('{{ waiver.waiver_id }}', '{{ waiver.account.student.name }}', '{{ waiver.amount_waived }}')"
            class="w-full inline-flex items-center justify-center gap-2 bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-bold py-3 px-6 rounded-xl hover:from-[#e07575] hover:to-[#F28C8C] transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <i class="fas fa-trash"></i>
            <span>Delete Waiver</span>
          </button>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: breadcrumbSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Form Animations */
  .form-fade-in {
    opacity: 0;
    animation: formFadeIn 0.8s ease-out 0.8s forwards;
  }

  /* Keyframes */
  @keyframes breadcrumbSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 80px;
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes formFadeIn {
    to {
      opacity: 1;
    }
  }

  @media print {
    .no-print {
      display: none !important;
    }

    body {
      background: white !important;
    }

    .card-modern {
      box-shadow: none !important;
      border: 1px solid #ddd !important;
    }
  }
</style>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
  <div
    class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 transform scale-95 transition-all duration-300"
    id="deleteModalContent"
  >
    <div class="text-center">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-full flex items-center justify-center mx-auto mb-6"
      >
        <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
      </div>
      <h3 class="text-2xl font-bold text-[#2C3E50] font-display mb-4">
        Delete Waiver
      </h3>
      <p class="text-[#40657F] mb-6" id="deleteMessage">
        Are you sure you want to delete this waiver? This action cannot be
        undone.
      </p>
      <div class="flex gap-4 justify-center">
        <button
          onclick="closeDeleteModal()"
          class="inline-flex items-center gap-2 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-3 px-6 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] transition-all duration-300"
        >
          <i class="fas fa-times"></i>
          <span>Cancel</span>
        </button>
        <button
          onclick="confirmDelete()"
          class="inline-flex items-center gap-2 bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-bold py-3 px-6 rounded-xl hover:from-[#e07575] hover:to-[#F28C8C] transition-all duration-300"
          id="confirmDeleteBtn"
        >
          <i class="fas fa-trash"></i>
          <span>Delete Waiver</span>
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  let currentWaiverId = null;

  function deleteWaiver(waiverId, studentName, amount) {
    currentWaiverId = waiverId;
    const message = `Are you sure you want to delete waiver ${waiverId} for ${studentName} (K ${parseInt(
      amount
    ).toLocaleString()})? This action cannot be undone.`;
    document.getElementById("deleteMessage").textContent = message;

    const modal = document.getElementById("deleteModal");
    const modalContent = document.getElementById("deleteModalContent");

    modal.classList.remove("hidden");
    modal.classList.add("flex", "items-center", "justify-center");
    setTimeout(() => {
      modalContent.classList.remove("scale-95");
      modalContent.classList.add("scale-100");
    }, 10);
  }

  function closeDeleteModal() {
    const modal = document.getElementById("deleteModal");
    const modalContent = document.getElementById("deleteModalContent");

    modalContent.classList.remove("scale-100");
    modalContent.classList.add("scale-95");

    setTimeout(() => {
      modal.classList.add("hidden");
      modal.classList.remove("flex", "items-center", "justify-center");
      currentWaiverId = null;
    }, 300);
  }

  function confirmDelete() {
    if (!currentWaiverId) return;

    const confirmBtn = document.getElementById("confirmDeleteBtn");
    const originalContent = confirmBtn.innerHTML;

    // Show loading state
    confirmBtn.innerHTML =
      '<i class="fas fa-spinner fa-spin"></i><span>Deleting...</span>';
    confirmBtn.disabled = true;

    // Create form and submit
    const form = document.createElement("form");
    form.method = "POST";
    form.action = `/finances/waiver/${currentWaiverId}/delete/`;

    // Add CSRF token
    const csrfToken = document.querySelector("[name=csrfmiddlewaretoken]");
    if (csrfToken) {
      const csrfInput = document.createElement("input");
      csrfInput.type = "hidden";
      csrfInput.name = "csrfmiddlewaretoken";
      csrfInput.value = csrfToken.value;
      form.appendChild(csrfInput);
    }

    document.body.appendChild(form);
    form.submit();
  }

  // Close modal when clicking outside
  document
    .getElementById("deleteModal")
    .addEventListener("click", function (e) {
      if (e.target === this) {
        closeDeleteModal();
      }
    });

  // Close modal with Escape key
  document.addEventListener("keydown", function (e) {
    if (e.key === "Escape") {
      closeDeleteModal();
    }
  });
</script>
{% endblock %}
