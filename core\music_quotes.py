"""
Music Easter Egg - Random Music Quote Generator
A delightful surprise for music lovers exploring the system
"""

import random

MUSIC_QUOTES = [
    # Classical Composers
    {
        "quote": "Music is the universal language of mankind.",
        "author": "<PERSON>fellow",
        "context": "Outre-Mer: A Pilgrimage Beyond the Sea",
        "theme": "Universal Language",
        "genre": "Poetry"
    },
    {
        "quote": "Music can change the world because it can change people.",
        "author": "Bono",
        "context": "U2 Lead Singer",
        "theme": "Transformation",
        "genre": "Rock"
    },
    {
        "quote": "Without music, life would be a mistake.",
        "author": "Friedrich <PERSON>",
        "context": "Twilight of the Idols",
        "theme": "Life's Essence",
        "genre": "Philosophy"
    },
    {
        "quote": "Music is my religion.",
        "author": "Jimi Hendrix",
        "context": "Guitar Legend",
        "theme": "Spiritual Connection",
        "genre": "Rock"
    },
    {
        "quote": "Where words fail, music speaks.",
        "author": "<PERSON>",
        "context": "Danish Author",
        "theme": "Expression",
        "genre": "Literature"
    },
    {
        "quote": "Music is the healing force of the universe.",
        "author": "<PERSON>",
        "context": "Jazz Saxophonist",
        "theme": "Healing",
        "genre": "<PERSON>"
    },
    {
        "quote": "One good thing about music, when it hits you, you feel no pain.",
        "author": "Bob Marley",
        "context": "Reggae Legend",
        "theme": "Comfort",
        "genre": "Reggae"
    },
    {
        "quote": "Music is the wine which inspires one to new generative processes, and I am Bacchus who presses out this glorious wine for mankind and makes them spiritually drunken.",
        "author": "<PERSON> van Beethoven",
        "context": "Classical Composer",
        "theme": "Inspiration",
        "genre": "Classical"
    },
    {
        "quote": "Jazz is not just music, it's a way of life, it's a way of being, a way of thinking.",
        "author": "Nina Simone",
        "context": "Jazz Pianist & Singer",
        "theme": "Lifestyle",
        "genre": "Jazz"
    },
    {
        "quote": "Music is the soundtrack of your life.",
        "author": "Dick Clark",
        "context": "American Bandstand Host",
        "theme": "Life Journey",
        "genre": "Pop Culture"
    },
    {
        "quote": "The good music is out there, but you have to look for it.",
        "author": "Questlove",
        "context": "The Roots Drummer",
        "theme": "Discovery",
        "genre": "Hip-Hop"
    },
    {
        "quote": "Music is the great uniter. An incredible force. Something that people who differ on everything and anything else can have in common.",
        "author": "Sarah Dessen",
        "context": "Author",
        "theme": "Unity",
        "genre": "Literature"
    },
    {
        "quote": "Music is what feelings sound like.",
        "author": "Anonymous",
        "context": "Popular Saying",
        "theme": "Emotion",
        "genre": "Folk Wisdom"
    },
    {
        "quote": "The only truth is music.",
        "author": "Jack Kerouac",
        "context": "On the Road Author",
        "theme": "Truth",
        "genre": "Literature"
    },
    {
        "quote": "Music is the art of thinking with sounds.",
        "author": "Jules Combarieu",
        "context": "French Musicologist",
        "theme": "Intellectual Art",
        "genre": "Academic"
    },
    {
        "quote": "Music is the movement of sound to reach the soul for the education of its virtue.",
        "author": "Plato",
        "context": "Ancient Greek Philosopher",
        "theme": "Education",
        "genre": "Philosophy"
    },
    {
        "quote": "Music is the literature of the heart; it commences where speech ends.",
        "author": "Alphonse de Lamartine",
        "context": "French Poet",
        "theme": "Heart's Language",
        "genre": "Poetry"
    },
    {
        "quote": "Music is the divine way to tell beautiful, poetic things to the heart.",
        "author": "Pablo Casals",
        "context": "Cellist & Conductor",
        "theme": "Divine Beauty",
        "genre": "Classical"
    },
    {
        "quote": "Music is my higher power.",
        "author": "Oliver James",
        "context": "Contemporary Artist",
        "theme": "Spiritual Power",
        "genre": "Contemporary"
    },
    {
        "quote": "Music is the strongest form of magic.",
        "author": "Marilyn Manson",
        "context": "Rock Artist",
        "theme": "Magic",
        "genre": "Rock"
    },
    {
        "quote": "Music is the space between the notes.",
        "author": "Claude Debussy",
        "context": "French Composer",
        "theme": "Silence & Space",
        "genre": "Classical"
    },
    {
        "quote": "Music is the cup that holds the wine of silence.",
        "author": "Robert Fripp",
        "context": "King Crimson Guitarist",
        "theme": "Silence",
        "genre": "Progressive Rock"
    },
    {
        "quote": "Music is the emotional life of most people.",
        "author": "Leonard Cohen",
        "context": "Singer-Songwriter",
        "theme": "Emotional Life",
        "genre": "Folk"
    },
    {
        "quote": "Music is the key to the female heart.",
        "author": "Johann Georg Hamann",
        "context": "German Philosopher",
        "theme": "Romance",
        "genre": "Philosophy"
    },
    {
        "quote": "Music is the medicine of the mind.",
        "author": "John A. Logan",
        "context": "American Politician",
        "theme": "Mental Health",
        "genre": "Political"
    }
]

# Easter egg trigger words for music
MUSIC_TRIGGER_WORDS = [
    'music',
    'song',
    'melody',
    'rhythm',
    'beat',
    'harmony',
    'symphony',
    'jazz',
    'rock',
    'classical',
    'blues',
    'hip hop',
    'rap',
    'pop',
    'country',
    'folk',
    'reggae',
    'electronic',
    'dance',
    'opera',
    'concert',
    'album',
    'artist',
    'musician',
    'composer',
    'singer',
    'band',
    'guitar',
    'piano',
    'drums',
    'violin',
    'saxophone',
    'trumpet',
    'bass',
    'vocals',
    'lyrics',
    'sound',
    'audio',
    'tune',
    'note',
    'chord',
    'scale',
    'tempo',
    'genre',
    'playlist',
    'soundtrack',
    'beethoven',
    'mozart',
    'bach',
    'chopin',
    'elvis',
    'beatles',
    'bob marley',
    'jimi hendrix',
    'miles davis',
    'john coltrane',
    'nina simone',
    'aretha franklin',
    'michael jackson',
    'prince',
    'david bowie',
    'radiohead',
    'pink floyd',
    'led zeppelin',
    'queen',
    'the rolling stones',
    'bob dylan',
    'johnny cash',
    'louis armstrong',
    'ella fitzgerald',
    'billie holiday',
    'frank sinatra',
    'stevie wonder',
    'marvin gaye',
    'whitney houston',
    'adele',
    'beyonce',
    'taylor swift',
    'kanye west',
    'jay-z',
    'eminem',
    'tupac',
    'biggie',
    'nas',
    'kendrick lamar',
    'drake',
    'rihanna',
    'bruno mars',
    'ed sheeran',
    'coldplay',
    'u2',
    'green day',
    'nirvana',
    'metallica',
    'ac/dc',
    'guns n roses',
    'red hot chili peppers',
    'foo fighters',
    'pearl jam',
    'soundgarden',
    'alice in chains',
    'black sabbath',
    'iron maiden',
    'judas priest',
    'deep purple',
    'the who',
    'the doors',
    'the kinks',
    'the clash',
    'sex pistols',
    'ramones',
    'velvet underground',
    'sonic youth',
    'pixies',
    'r.e.m.',
    'depeche mode',
    'new order',
    'joy division',
    'the cure',
    'smiths',
    'morrissey',
    'blur',
    'oasis',
    'arctic monkeys',
    'muse',
    'linkin park',
    'system of a down',
    'tool',
    'nine inch nails',
    'marilyn manson',
    'korn',
    'limp bizkit',
    'rage against the machine',
    'red hot chili peppers'
]

def get_random_music_quote():
    """Get a random music quote"""
    return random.choice(MUSIC_QUOTES)

def is_music_search(search_term):
    """Check if search term contains music trigger words"""
    if not search_term:
        return False
    
    search_lower = search_term.lower().strip()
    
    # Check for exact matches and partial matches
    for trigger in MUSIC_TRIGGER_WORDS:
        if trigger in search_lower:
            return True
    
    return False

def get_music_quote_by_genre(genre=None):
    """Get quotes filtered by genre"""
    if not genre:
        return get_random_music_quote()
    
    genre_quotes = [q for q in MUSIC_QUOTES if genre.lower() in q['genre'].lower()]
    return random.choice(genre_quotes) if genre_quotes else get_random_music_quote()

def get_music_quotes_by_author(author):
    """Get all music quotes by a specific author"""
    return [q for q in MUSIC_QUOTES if author.lower() in q['author'].lower()]
