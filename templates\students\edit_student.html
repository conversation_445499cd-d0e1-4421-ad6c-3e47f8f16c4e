{% extends 'base.html' %} {% load static %} {% block title %}Edit {{
student.name }} | {% endblock %} {% block content %}
<section class="w-full max-w-6xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-lg icon-float"
      >
        <i class="fas fa-user-edit text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in"
        >
          Edit Student
        </h1>
        <div
          class="w-20 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'students:students' %}"
        class="text-[#7AB2D3] hover:text-[#40657F] transition-colors"
        >Students</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'students:student_details' student.student_id %}"
        class="text-[#7AB2D3] hover:text-[#40657F] transition-colors"
        >{{ student.name }}</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#F28C8C] font-semibold">Edit</span>
    </nav>
  </div>

  <!-- Student Edit Form -->
  <form class="w-full space-y-8 form-fade-in" method="post" autocomplete="off">
    {% csrf_token %}

    <!-- Form Header -->
    <div class="text-center form-header-slide-in">
      <div
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white px-8 py-4 rounded-2xl shadow-lg"
      >
        <i class="fas fa-user-graduate text-xl"></i>
        <h2 class="text-2xl font-bold font-display">
          Student Information Form
        </h2>
      </div>
    </div>

    <!-- Main Form Card -->
    <div
      class="bg-white rounded-2xl shadow-xl border border-[#B9D8EB]/50 overflow-hidden main-form-slide-in"
    >
      <!-- Current Student Info -->
      <div
        class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] p-8 border-b border-[#B9D8EB]/50"
      >
        <div class="flex items-center gap-6 mb-6">
          <div
            class="w-16 h-16 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg"
          >
            {{ student.name|first }}
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
              {{ student.name }}
            </h3>
            <p class="text-[#40657F] font-medium">
              Student ID: {{ student.student_id }}
            </p>
            <p class="text-[#40657F] text-sm">
              Currently enrolled in {{ student.level }}
            </p>
          </div>
        </div>

        <div
          class="bg-gradient-to-r from-[#F28C8C]/10 to-[#e07575]/10 rounded-xl p-6 border border-[#F28C8C]/20"
        >
          <div class="flex items-center gap-3 mb-3">
            <i class="fas fa-info-circle text-[#F28C8C] text-lg"></i>
            <h4 class="font-bold text-[#2C3E50]">Edit Instructions</h4>
          </div>
          <p class="text-[#40657F] leading-relaxed">
            Update the student's information below. All changes will be saved to
            the student's record immediately upon submission.
          </p>
        </div>
      </div>

      <!-- Form Fields -->
      <div class="p-8">
        <div class="flex items-center gap-4 mb-8">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg"
          >
            <i class="fas fa-edit text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
              Student Details
            </h3>
            <p class="text-[#40657F] text-sm">Update student information</p>
          </div>
          <div
            class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
          ></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 form-fields-fade-in">
          {% for field in form %} {% if field.name != 'is_active' %}
          <div
            class="form-field-group {% if field.name == 'student_id' %}md:col-span-2{% endif %}"
          >
            <label class="form-label" for="{{ field.auto_id }}">
              <i
                class="fas fa-{% if field.name == 'student_id' %}hashtag{% elif field.name == 'name' %}user{% elif field.name == 'gender' %}venus-mars{% elif field.name == 'level' %}graduation-cap{% elif field.name == 'date_of_birth' %}birthday-cake{% elif field.name == 'address' %}map-marker-alt{% elif field.name == 'phone' %}phone{% elif field.name == 'email' %}envelope{% else %}info-circle{% endif %} mr-2 text-[#7AB2D3]"
              ></i>
              {{ field.label }}
            </label>
            <div class="form-field-wrapper">
              {{ field }} {% if field.errors %}
              <div class="form-error">
                <i class="fas fa-exclamation-triangle mr-1"></i>
                {{ field.errors|striptags }}
              </div>
              {% endif %}
            </div>
          </div>
          {% endif %} {% endfor %}

          <!-- Student Status Toggle -->
          {% for field in form %} {% if field.name == 'is_active' %}
          <div class="form-field-group">
            <label class="form-label">
              <i class="fas fa-toggle-on mr-2 text-[#7AB2D3]"></i>
              Student Status
            </label>
            <div class="form-field-wrapper">
              <div
                class="flex items-center gap-4 p-6 bg-gradient-to-br from-[#F7FAFC] to-[#E2F1F9] rounded-xl border-2 border-[#B9D8EB]/50"
              >
                <div class="flex items-center gap-4">
                  <div class="relative">
                    <div class="hidden">{{ field }}</div>
                    <div
                      class="toggle-bg w-16 h-9 {% if field.value %}bg-[#74C69D]{% else %}bg-[#F28C8C]{% endif %} rounded-full shadow-inner cursor-pointer transition-all duration-300"
                      onclick="toggleStatus()"
                    ></div>
                    <div
                      class="toggle-dot absolute w-7 h-7 bg-white rounded-full shadow top-1 left-1 transition-all duration-300 transform {% if field.value %}translate-x-7{% else %}translate-x-0{% endif %}"
                    ></div>
                  </div>
                  <div>
                    <span
                      class="status-text font-bold {% if field.value %}text-[#74C69D]{% else %}text-[#F28C8C]{% endif %}"
                    >
                      {% if field.value %}Active Student{% else %}Inactive
                      Student{% endif %}
                    </span>
                    <p
                      class="status-description text-sm {% if field.value %}text-[#74C69D]{% else %}text-[#F28C8C]{% endif %}"
                    >
                      {% if field.value %}Student can access all academy
                      services{% else %}Student access is suspended{% endif %}
                    </p>
                  </div>
                </div>
                <div class="ml-auto">
                  <div
                    class="status-icon w-12 h-12 {% if field.value %}bg-[#74C69D]/20{% else %}bg-[#F28C8C]/20{% endif %} rounded-full flex items-center justify-center"
                  >
                    <i
                      class="fas fa-{% if field.value %}check{% else %}times{% endif %} {% if field.value %}text-[#74C69D]{% else %}text-[#F28C8C]{% endif %} text-lg"
                    ></i>
                  </div>
                </div>
              </div>
              {% if field.errors %}
              <div class="form-error">
                <i class="fas fa-exclamation-triangle mr-1"></i>
                {{ field.errors|striptags }}
              </div>
              {% endif %}
            </div>
          </div>
          {% endif %} {% endfor %}
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div
      class="flex flex-col sm:flex-row justify-center items-center gap-4 w-full buttons-slide-in"
    >
      <button
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group w-full sm:w-auto"
        type="submit"
      >
        <i
          class="fas fa-save group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        ></i>
        <span>Update Student</span>
      </button>
      <a
        href="{% url 'students:student_details' student.student_id %}"
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group w-full sm:w-auto"
      >
        <i
          class="fas fa-times group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        ></i>
        <span>Cancel</span>
      </a>
    </div>
  </form>
</section>

<style>
  /* Header Animations */
  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: breadcrumbSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Form Animations */
  .form-fade-in {
    opacity: 0;
    animation: formFadeIn 0.8s ease-out 0.8s forwards;
  }

  .form-header-slide-in {
    opacity: 0;
    transform: translateY(-20px);
    animation: formHeaderSlideIn 0.8s ease-out 1s forwards;
  }

  .main-form-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: mainFormSlideIn 0.8s ease-out 1.2s forwards;
  }

  .form-fields-fade-in {
    opacity: 0;
    animation: formFieldsFadeIn 0.8s ease-out 1.4s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: buttonsSlideIn 0.8s ease-out 1.6s forwards;
  }

  /* Form Field Styling */
  .form-field-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-label {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  .form-field-wrapper {
    position: relative;
  }

  .form-field-wrapper input,
  .form-field-wrapper select,
  .form-field-wrapper textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #b9d8eb;
    border-radius: 0.75rem;
    background-color: #f7fafc;
    color: #2c3e50;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .form-field-wrapper input:focus,
  .form-field-wrapper select:focus,
  .form-field-wrapper textarea:focus {
    outline: none;
    border-color: #7ab2d3;
    background-color: white;
    box-shadow: 0 0 0 4px rgba(122, 178, 211, 0.2);
  }

  .form-error {
    display: flex;
    align-items: center;
    color: #f28c8c;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.25rem;
  }

  /* Keyframe Definitions */
  @keyframes breadcrumbSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes formFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes formHeaderSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes mainFormSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes formFieldsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes buttonsSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Toggle Switch Styling */
  .toggle-bg.inactive {
    background-color: #f28c8c !important;
  }

  .toggle-dot.inactive {
    transform: translateX(0) !important;
  }

  .status-text.inactive {
    color: #f28c8c !important;
  }

  .status-description.inactive {
    color: #f28c8c !important;
  }

  .status-icon.inactive {
    background-color: rgba(242, 140, 140, 0.2) !important;
  }

  .status-icon.inactive i {
    color: #f28c8c !important;
  }

  .toggle-bg.active {
    background-color: #74c69d !important;
  }

  .toggle-dot.active {
    transform: translateX(1.75rem) !important;
  }

  .status-text.active {
    color: #74c69d !important;
  }

  .status-description.active {
    color: #74c69d !important;
  }

  .status-icon.active {
    background-color: rgba(116, 198, 157, 0.2) !important;
  }

  .status-icon.active i {
    color: #74c69d !important;
  }

  /* Hide the actual checkbox */
  input[type="checkbox"].toggle-input {
    position: absolute;
    opacity: 0;
    pointer-events: none;
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .form-fields-fade-in {
      animation-delay: 1s;
    }
  }
</style>

<script>
  function toggleStatus() {
    const checkbox = document.querySelector('input[name="is_active"]');
    const toggleBg = document.querySelector(".toggle-bg");
    const toggleDot = document.querySelector(".toggle-dot");
    const statusText = document.querySelector(".status-text");
    const statusDescription = document.querySelector(".status-description");
    const statusIcon = document.querySelector(".status-icon");
    const statusIconI = document.querySelector(".status-icon i");

    // Toggle the checkbox
    checkbox.checked = !checkbox.checked;

    if (checkbox.checked) {
      // Active state
      toggleBg.classList.remove("inactive");
      toggleBg.classList.add("active");
      toggleDot.classList.remove("inactive");
      toggleDot.classList.add("active");
      statusText.classList.remove("inactive");
      statusText.classList.add("active");
      statusDescription.classList.remove("inactive");
      statusDescription.classList.add("active");
      statusIcon.classList.remove("inactive");
      statusIcon.classList.add("active");

      statusText.textContent = "Active Student";
      statusDescription.textContent = "Student can access all academy services";
      statusIconI.className = "fas fa-check text-[#74C69D] text-lg";
    } else {
      // Inactive state
      toggleBg.classList.remove("active");
      toggleBg.classList.add("inactive");
      toggleDot.classList.remove("active");
      toggleDot.classList.add("inactive");
      statusText.classList.remove("active");
      statusText.classList.add("inactive");
      statusDescription.classList.remove("active");
      statusDescription.classList.add("inactive");
      statusIcon.classList.remove("active");
      statusIcon.classList.add("inactive");

      statusText.textContent = "Inactive Student";
      statusDescription.textContent = "Student access is suspended";
      statusIconI.className = "fas fa-times text-[#F28C8C] text-lg";
    }
  }
</script>

{% endblock %}
