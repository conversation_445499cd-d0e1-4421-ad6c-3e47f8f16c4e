from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django import forms


from accounts.models import CustomUser
from accounts.models import TeacherAssignment


class RegisterTeacherForm(UserCreationForm):
    class Meta:
        model = CustomUser
        fields = ['username', 'first_name', 'last_name',
                  'email', 'gender', 'phone_number', 'password1', 'password2']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': 'django-form'})


class UpdateTeacherForm(UserChangeForm):
    class Meta:
        model = CustomUser
        fields = ['username', 'first_name', 'last_name',
                  'email', 'gender', 'phone_number']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': 'django-form'})


class AddAssignmentForm(forms.ModelForm):
    class Meta:
        model = TeacherAssignment
        fields = ['subject', 'class_assigned', 'term']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': 'django-form'})
