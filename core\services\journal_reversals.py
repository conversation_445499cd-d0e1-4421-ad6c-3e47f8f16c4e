from django.http import HttpResponseBadRequest
from django.db import transaction

from finances.book_keeping import JournalEntry, JournalLine


@transaction.atomic
def reverse_transaction(instance, reason, user):
    # Sanity Checks
    if hasattr(instance, "receipt_number"):
        journal = JournalEntry.objects.get(
            voucher=instance.receipt_number, is_reversal=False)
    else:
        journal = JournalEntry.objects.get(
            voucher=instance.outflow_id, is_reversal=False)

    if getattr(instance, 'is_reversed', False):
        raise HttpResponseBadRequest("Transaction already reversed")

    journal.is_locked = True
    journal.save(update_fields=["is_locked"])

    # create reversal journal
    reversal_journal = JournalEntry.objects.create(
        term=journal.term,
        date=journal.date,
        description=f"Reversal of {str(instance)}",
        voucher=journal.voucher,
        is_reversal=True,
        reverses=journal,
        reason=reason,
        created_by=user,
    )

    # Reverse all lines
    for line in journal.journalline_set.all():
        JournalLine.objects.create(
            journal_entry=reversal_journal,
            account=line.account,
            description=f"Revesal of {line.description or 'No desc'}",
            amount=line.amount,
            line_type="Credit" if line.line_type == "Debit" else "Debit"
        )
    instance.is_reversed = True

    if hasattr(instance, "reversal_entry"):
        instance.reversal_entry = reversal_journal

    instance.save(
        update_fields=["is_reversed", "reversal_entry"] if hasattr(
            instance, "reversal_entry") else ["is_reversed"]
    )

    # Update fee account amount_paid if this is a receipt reversal
    if hasattr(instance, "receipt_number") and hasattr(instance, "fee_account"):
        instance.update_fee_account_on_reversal()

    print("Reversal created successfully")

    return reversal_journal
