from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class EventCategory(models.Model):
    """
    Categories for calendar events (e.g., Academic, Administrative, Holiday)
    """
    name = models.CharField(max_length=100)
    color = models.CharField(max_length=20, default='#7AB2D3',
                           help_text="Color for events in this category")
    icon = models.CharField(max_length=50, default='fas fa-calendar',
                           help_text="FontAwesome icon class")
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = "Event Category"
        verbose_name_plural = "Event Categories"


class CalendarEvent(models.Model):
    """
    Calendar events for the dashboard calendar
    """
    EVENT_TYPE_CHOICES = [
        ('academic', 'Academic'),
        ('administrative', 'Administrative'),
        ('holiday', 'Holiday'),
        ('exam', 'Examination'),
        ('meeting', 'Meeting'),
        ('deadline', 'Deadline'),
        ('other', 'Other'),
    ]
    
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    event_type = models.CharField(max_length=20, choices=EVENT_TYPE_CHOICES, default='other')
    category = models.ForeignKey(
        EventCategory,
        on_delete=models.CASCADE,
        related_name='events'
    )
    
    # Date and time fields
    start_date = models.DateField()
    end_date = models.DateField()
    start_time = models.TimeField(null=True, blank=True)
    end_time = models.TimeField(null=True, blank=True)
    is_all_day = models.BooleanField(default=False)
    
    # Location and additional info
    location = models.CharField(max_length=200, blank=True, null=True)
    is_recurring = models.BooleanField(default=False)
    recurrence_pattern = models.CharField(
        max_length=50, 
        blank=True, 
        null=True,
        choices=[
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly'),
            ('yearly', 'Yearly'),
        ]
    )
    
    # Status and visibility
    is_active = models.BooleanField(default=True)
    is_public = models.BooleanField(default=True, 
                                   help_text="Whether this event is visible to all users")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_events')
    
    # Optional: Link to related objects
    related_term = models.ForeignKey(
        'students.Term',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='events'
    )
    related_level = models.ForeignKey(
        'students.Level',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='events'
    )
    related_subject = models.ForeignKey(
        'academics.Subject',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='events'
    )
    
    def __str__(self):
        return f"{self.title} - {self.start_date}"
    
    @property
    def duration_days(self):
        return (self.end_date - self.start_date).days + 1
    
    @property
    def is_today(self):
        today = timezone.now().date()
        return self.start_date <= today <= self.end_date
    
    @property
    def is_upcoming(self):
        today = timezone.now().date()
        return self.start_date > today
    
    @property
    def is_past(self):
        today = timezone.now().date()
        return self.end_date < today
    
    @property
    def status_display(self):
        if self.is_today:
            return "Today"
        elif self.is_upcoming:
            return "Upcoming"
        else:
            return "Past"
    
    class Meta:
        verbose_name = "Calendar Event"
        verbose_name_plural = "Calendar Events"
        ordering = ['start_date', 'start_time']


class EventAttendee(models.Model):
    """
    Track who is attending which events
    """
    event = models.ForeignKey(CalendarEvent, on_delete=models.CASCADE, related_name='attendees')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    status = models.CharField(
        max_length=20,
        choices=[
            ('invited', 'Invited'),
            ('accepted', 'Accepted'),
            ('declined', 'Declined'),
            ('tentative', 'Tentative'),
        ],
        default='invited'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('event', 'user')
        verbose_name = "Event Attendee"
        verbose_name_plural = "Event Attendees"
