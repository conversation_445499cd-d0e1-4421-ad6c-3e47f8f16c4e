{% extends 'base.html' %} {% load static %} {% load humanize %}
<!-- title -->
{% block title %}Report Card - {{ student.name }} | {% endblock %}
<!-- body -->
{% block content %}
<section class="w-full max-w-6xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg icon-float"
      >
        <i class="fas fa-file-alt text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in"
        >
          Academic Report Card
        </h1>
        <div
          class="w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'academics:dashboard' %}"
        class="text-[#7AB2D3] hover:text-[#40657F] transition-colors"
        >Academics</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'academics:student_details' student.slug %}"
        class="text-[#7AB2D3] hover:text-[#40657F] transition-colors"
        >{{ student.name }}</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Report Card</span>
    </nav>
  </div>

  <!-- Student Information -->
  <div class="card-modern p-8 student-info-fade-in">
    <div class="flex items-center gap-6 mb-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg"
      >
        {{ student.name|first }}
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          {{ student.name }}
        </h2>
        <p class="text-[#40657F] font-medium">
          Student ID: {{ student.student_id }}
        </p>
        <p class="text-[#40657F] text-sm">Class: {{ student.level }}</p>
      </div>
    </div>

    <div
      class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] rounded-xl p-6 border border-[#B9D8EB]/50"
    >
      <div class="flex items-center gap-3 mb-3">
        <i class="fas fa-info-circle text-[#7AB2D3] text-lg"></i>
        <h3 class="font-bold text-[#2C3E50]">Report Card Information</h3>
      </div>
      <p class="text-[#40657F] leading-relaxed">
        Generate and download the academic report card for {{ student.name }}.
        The report card includes all subjects, grades, and performance summary
        for the current term.
      </p>
    </div>
  </div>

  <!-- Term Selection -->
  <div class="card-modern p-8 term-selection-slide-in">
    <h3 class="font-bold text-xl text-[#2C3E50] mb-6 flex items-center gap-3">
      <i class="fas fa-calendar-alt text-[#7AB2D3]"></i>
      Select Term
    </h3>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {% for term in terms %}
      <a
        href="{% url 'academics:generate_report_card_term' student.student_id term.id %}"
        class="term-card group relative overflow-hidden bg-gradient-to-br from-white via-[#F7FAFC] to-[#E2F1F9] border-2 {% if term.is_active %}border-[#7AB2D3]{% else %}border-[#B9D8EB]{% endif %} hover:border-[#7AB2D3] hover:shadow-xl hover:-translate-y-2 transition-all duration-300 p-6 rounded-2xl"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-12 h-12 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 rounded-full -translate-y-6 translate-x-6 group-hover:scale-125 transition-transform duration-500"
        ></div>

        <div class="relative z-10">
          <div class="flex items-center gap-3 mb-3">
            <div
              class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg"
            >
              <i class="fas fa-calendar text-white text-sm"></i>
            </div>
            <div>
              <h4
                class="font-bold text-[#2C3E50] group-hover:text-[#7AB2D3] transition-colors"
              >
                {{ term.term_name }}
              </h4>
              {% if term.is_active %}
              <span
                class="inline-flex items-center gap-1 text-xs font-medium text-[#74C69D]"
              >
                <i class="fas fa-circle text-[4px]"></i>
                Active Term
              </span>
              {% endif %}
            </div>
          </div>

          <p class="text-[#40657F] text-sm mb-4">{{ term.academic_year }}</p>

          <div
            class="flex items-center gap-2 text-[#7AB2D3] font-medium text-sm group-hover:text-[#40657F] transition-colors"
          >
            <i class="fas fa-download"></i>
            <span>Generate Report Card</span>
          </div>
        </div>
      </a>
      {% empty %}
      <div class="col-span-full text-center py-8">
        <i class="fas fa-calendar-times text-[#B9D8EB] text-4xl mb-4"></i>
        <p class="text-[#40657F] font-medium">No terms available</p>
      </div>
      {% endfor %}
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="flex gap-4 actions-slide-in">
    <a
      href="{% url 'academics:student_details' student.slug %}"
      class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl group"
    >
      <i
        class="fas fa-arrow-left group-hover:-translate-x-1 transition-all duration-300"
      ></i>
      <span>Back to Student</span>
    </a>

    {% if terms %}
    <a
      href="{% url 'academics:generate_report_card' student.student_id %}"
      class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl group"
    >
      <i
        class="fas fa-file-alt group-hover:scale-110 transition-transform duration-300"
      ></i>
      <span>Current Term Report</span>
    </a>
    {% endif %}
  </div>
</section>

<style>
  /* Header Animations */
  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: breadcrumbSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  .student-info-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: studentInfoFadeIn 0.8s ease-out 0.8s forwards;
  }

  .term-selection-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: termSelectionSlideIn 0.8s ease-out 1s forwards;
  }

  .actions-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionsSlideIn 0.8s ease-out 1.2s forwards;
  }

  /* Term card animations */
  .term-card {
    opacity: 0;
    transform: translateY(20px);
    animation: termCardSlideIn 0.6s ease-out forwards;
  }

  .term-card:nth-child(1) {
    animation-delay: 1.2s;
  }
  .term-card:nth-child(2) {
    animation-delay: 1.3s;
  }
  .term-card:nth-child(3) {
    animation-delay: 1.4s;
  }

  /* Keyframe Definitions */
  @keyframes breadcrumbSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes studentInfoFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes termSelectionSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes actionsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes termCardSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
{% endblock %}
