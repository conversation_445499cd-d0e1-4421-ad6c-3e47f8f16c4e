# Discrepancy Checks Guide

This guide explains how to run and interpret discrepancy checks in the Tiny Feet Academy financial system to ensure data integrity and identify potential issues.

## Overview

The system uses a modern centralized accounting approach with Budget → BudgetLines → Ledgers → JournalEntries. Discrepancy checks help verify that all financial calculations are consistent across different parts of the system.

## Web Interface

The system provides a user-friendly web interface for running discrepancy checks without requiring command-line access. Navigate to **Administration → System Discrepancy Check** to access:

### Available Analysis Types

1. **Comprehensive Analysis** - Full system check across all financial data
2. **Category Analysis** - Focus on specific fee categories with manual input
3. **Dynamic Amount Analysis** - Debug payment distribution with dropdown category selection

### Features

- **Real-time Results**: Analysis runs in the browser with live progress indicators
- **Category Dropdown**: Pre-populated list of active fee categories for dynamic amount analysis
- **Formatted Output**: Clean, readable results with color-coded status indicators
- **Error Handling**: Clear error messages and troubleshooting guidance
- **Permission-based Access**: Requires `manage_system` permission for security

## Available Commands

### 1. Comprehensive Ledger Discrepancy Check

```bash
python manage.py diagnose_ledger_discrepancies
```

**What it checks:**
- Ledger total_amount vs Receipt totals
- Journal lines vs Ledger calculations
- Dynamic amount calculations vs Receipt totals
- Orphaned journal entries (including reversal journals)
- Missing journal entries for receipts
- Detailed breakdown of reversal, locked, and regular orphaned entries
- Cross-validates all financial data sources using modern accounting system

**Example Output:**
```
============================================================
ANALYZING CATEGORY: Primary Tuition Fees
============================================================
📊 Ledger total_amount: 5,307,500.00
🧾 Receipts total: 5,307,500
💰 Fee accounts total_due: 10,512,500
🔄 Dynamic amount total: 5,307,500
📝 Journal lines total (active): 5,307,500.00
📊 Income models removed - using modern budget/ledger system

🔍 DISCREPANCY ANALYSIS:
✅ All amounts match!

🔬 DETAILED BREAKDOWN:
⚠️  Orphaned journal entries: 125,000
   🔄 Reversal journals: 75,000 (3 entries)
   🔒 Locked journals: 50,000 (2 entries)
   📝 Regular orphaned: 0 (0 entries)
```

### 2. Specific Category Analysis

```bash
python manage.py diagnose_ledger_discrepancies --category "Primary Tuition"
```

**Use when:** You want to focus on a specific fee category.

### 3. Specific Term Analysis

```bash
python manage.py diagnose_ledger_discrepancies --term-id 5
```

**Use when:** You want to analyze a specific academic term.

### 4. Dynamic Amount Debugging

```bash
python manage.py debug_dynamic_amount --category "Primary Food Fees"
```

**What it does:**
- Analyzes dynamic amount calculation discrepancies
- Shows student-by-student breakdown
- Identifies overpayments and distribution issues

**Web Interface:**
The Dynamic Amount Analysis is also available through the web interface at **Administration → System Discrepancy Check**. This provides:
- Dropdown selection of active fee categories
- Real-time analysis results
- User-friendly output formatting
- No command-line access required

**Example Output:**
```
🔍 George Mgawa: Receipts=187,000, Dynamic=114,000, Expected=114,000, Discrepancy=73,000
   Account 1481: due=38,000, amount=38,000
   Account 1482: due=38,000, amount=38,000
   Account 1483: due=38,000, amount=38,000
```

## Understanding the Results

### ✅ Healthy System Indicators

1. **Ledger = Receipts**: Core accounting system is accurate
2. **Journal Lines = Ledger**: Double-entry bookkeeping is consistent
3. **All amounts match**: No discrepancies found

### ❌ Common Discrepancy Types

#### 1. Data Entry Errors
**Symptoms:**
- Large discrepancies in specific students
- Overpayments not properly distributed
- Duplicate receipt entries

**Example:**
```
❌ Receipts vs Dynamic Amount: 73,000
```

**Solution:** Review individual student payment records for errors.

#### 2. Legacy Model References (Deprecated)
**Note:** Income and Expense models have been removed from the system.

**Previous Issues (Now Resolved):**
- Income model calculation discrepancies
- Expense model inconsistencies

**Current Solution:** All financial tracking now uses the modern double-entry bookkeeping system:
- Budget/BudgetLine for planning
- Ledger for account tracking
- JournalEntry/JournalLine for transactions

#### 3. Missing Ledger Accounts
**Symptoms:**
- "No ledger found for category" messages
- Fee categories without corresponding ledger accounts

**Example:**
```
❌ No ledger found for category: PTA
```

**Solution:** Create missing ledger accounts in the admin interface.

#### 4. Orphaned Journal Entries
**Symptoms:**
- Journal entries that don't have corresponding active receipts
- Includes reversal journals, locked entries, and truly orphaned entries

**Types of Orphaned Journals:**
- **🔄 Reversal journals**: Entries that reverse/cancel previous transactions
- **🔒 Locked journals**: Original entries that have been reversed (marked as locked)
- **📝 Regular orphaned**: Journal entries without corresponding receipts (potential data issues)

**Example:**
```
⚠️  Orphaned journal entries: 125,000
   🔄 Reversal journals: 75,000 (3 entries)
   🔒 Locked journals: 50,000 (2 entries)
   📝 Regular orphaned: 0 (0 entries)
```

**Analysis:**
- Reversal and locked journals are normal and expected
- Regular orphaned entries may indicate data synchronization issues
- Total orphaned amount should be investigated if it's significant

## Troubleshooting Workflow

### Step 1: Run Comprehensive Check
```bash
python manage.py diagnose_ledger_discrepancies
```

### Step 2: Identify Problem Categories
Look for ❌ symbols in the output and note which categories have issues.

### Step 3: Deep Dive into Specific Issues

**For Dynamic Amount Issues:**
```bash
python manage.py debug_dynamic_amount --category "Category Name"
```

**For Specific Categories:**
```bash
python manage.py diagnose_ledger_discrepancies --category "Category Name"
```

### Step 4: Investigate Individual Records

Use Django shell to examine specific students or transactions:

```python
# Example: Investigate a student with discrepancies
from finances.fee_management.models import FeeAccount, Receipt
from finances.book_keeping import Ledger, JournalLine
from students.models import Student

student = Student.objects.get(name__icontains='Student Name')
accounts = FeeAccount.objects.filter(student=student, category__name='Category')
receipts = Receipt.objects.filter(fee_account__student=student, fee_account__category__name='Category')

print(f"Total Due: {sum(acc.total_due for acc in accounts):,}")
print(f"Dynamic Amount: {sum(acc.amount for acc in accounts):,}")
print(f"Total Receipts: {sum(r.amount_paid for r in receipts):,}")

# Check ledger entries
ledger = Ledger.objects.get(name='Category Name')
print(f"Ledger Total: {ledger.total_amount:,}")
```

## Best Practices

### Regular Monitoring
- Run discrepancy checks weekly
- Always check after bulk data imports
- Verify after major system changes

### Data Quality
- Ensure all fee categories have corresponding ledger accounts
- Validate payment entries before processing
- Review overpayments and refunds carefully

### System Health
- The core ledger system should always match receipts
- Journal entries should always balance
- Any discrepancies indicate data issues, not system problems
- Modern double-entry bookkeeping ensures data integrity

## Quick Reference

| Command | Purpose | When to Use |
|---------|---------|-------------|
| `diagnose_ledger_discrepancies` | Full system check | Weekly monitoring |
| `diagnose_ledger_discrepancies --category "Name"` | Category-specific | Investigating specific issues |
| `diagnose_ledger_discrepancies --term-id X` | Term-specific | Historical analysis |
| `debug_dynamic_amount --category "Name"` | Payment distribution | Overpayment issues |

## Expected Results

In a healthy system, you should see:
- ✅ All amounts match across all categories
- No missing ledger accounts
- Consistent calculations between ledger and receipt systems
- Proper double-entry bookkeeping balance

Any ❌ indicators suggest data entry errors or missing configuration that should be investigated and corrected.

## System Architecture Changes

### Deprecated Models (Removed)
- **Income Model**: Replaced by Budget/BudgetLine + Ledger system
- **IncomeTotal Model**: Replaced by Budget/BudgetLine aggregations
- **Expense Model**: Replaced by Outflow + Ledger system
- **ExpenseTotal Model**: Replaced by Ledger aggregations

### Modern System Components
- **Budget/BudgetLine**: Financial planning and allocations
- **Ledger**: Account tracking with proper chart of accounts
- **JournalEntry/JournalLine**: Double-entry bookkeeping transactions
- **Outflow**: Modern expense transaction recording
