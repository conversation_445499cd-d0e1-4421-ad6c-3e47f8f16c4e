# Dynamic Properties Usage Examples

This document shows how to use the new dynamic properties in the FeeAccount model.

## Basic Usage

### Checking Payment Status

```python
# OLD WAY (deprecated)
if fee_account.is_paid:
    print("Account is paid")

# NEW WAY (recommended)
if fee_account.is_fully_paid:
    print("Account is paid")
```

### Getting Balance Information

```python
# OLD WAY (deprecated)
remaining_balance = fee_account.balance

# NEW WAY (recommended)
remaining_balance = fee_account.current_balance

# Handle payment status
if fee_account.current_balance > 0:
    print(f"Still owes: {fee_account.current_balance}")
else:
    print("Fully paid")

# Note: Individual accounts don't show overpayments
# Overpayments are distributed to other accounts automatically
```

### Getting Payment Amount

```python
# OLD WAY (deprecated)
amount_paid = fee_account.amount_paid

# NEW WAY (recommended)
amount_paid = fee_account.amount
```

## Advanced Usage

### Filtering Paid/Unpaid Accounts

```python
# Using dynamic properties in queries (requires database functions)
from django.db.models import F, Case, When, IntegerField

# Get accounts with outstanding balances
unpaid_accounts = FeeAccount.objects.annotate(
    dynamic_balance=F('total_due') - F('amount')  # This won't work directly
).filter(dynamic_balance__gt=0)

# Better approach: Use the stored fields (updated by our system)
unpaid_accounts = FeeAccount.objects.filter(is_paid=False)

# Or filter in Python for small datasets
all_accounts = FeeAccount.objects.all()
unpaid_accounts = [acc for acc in all_accounts if not acc.is_fully_paid]
```

### Bulk Status Checking

```python
# Check payment status for multiple accounts efficiently
def check_payment_status(student, category, term):
    accounts = FeeAccount.objects.filter(
        student=student,
        category=category,
        term=term
    )
    
    total_due = sum(acc.total_due for acc in accounts)
    total_paid = sum(acc.amount for acc in accounts)
    
    return {
        'total_due': total_due,
        'total_paid': total_paid,
        'balance': total_due - total_paid,
        'is_fully_paid': total_paid >= total_due,
        'accounts': [
            {
                'id': acc.id,
                'due': acc.total_due,
                'paid': acc.amount,
                'balance': acc.current_balance,
                'is_paid': acc.is_fully_paid
            }
            for acc in accounts
        ]
    }
```

### Template Usage

```django
<!-- In Django templates -->
{% for account in fee_accounts %}
    <tr>
        <td>{{ account.category.name }}</td>
        <td>{{ account.total_due }}</td>
        <td>{{ account.amount }}</td>
        <td>{{ account.current_balance }}</td>
        <td>
            {% if account.is_fully_paid %}
                <span class="badge badge-success">Paid</span>
            {% else %}
                <span class="badge badge-warning">Pending</span>
            {% endif %}
        </td>
    </tr>
{% endfor %}
```

### API Serialization

```python
# In Django REST Framework serializers
class FeeAccountSerializer(serializers.ModelSerializer):
    amount_paid = serializers.ReadOnlyField(source='amount')
    balance = serializers.ReadOnlyField(source='current_balance')
    is_paid = serializers.ReadOnlyField(source='is_fully_paid')

    class Meta:
        model = FeeAccount
        fields = ['id', 'total_due', 'amount_paid', 'balance', 'is_paid']
```

## Migration Patterns

### Gradual Migration

```python
# Validate dynamic calculations
def validate_calculations(fee_account):
    """Validate dynamic calculations work correctly"""
    return {
        'dynamic_amount': fee_account.amount,
        'dynamic_balance': fee_account.current_balance,
        'stored_is_paid': fee_account.is_paid,
        'dynamic_is_paid': fee_account.is_fully_paid,
        'status_match': fee_account.is_paid == fee_account.is_fully_paid,
        'is_fully_paid': fee_account.current_balance <= 0,
    }
```

### Safe Property Access

```python
# Helper functions for safe property access
def get_payment_amount(fee_account):
    """Get payment amount safely"""
    try:
        return fee_account.amount  # Dynamic property
    except Exception as e:
        # Log the error and return 0 as fallback
        print(f"Error calculating amount for {fee_account}: {e}")
        return 0

def get_balance(fee_account):
    """Get balance safely"""
    try:
        return fee_account.current_balance  # Dynamic property
    except Exception as e:
        # Log the error and return total_due as fallback
        print(f"Error calculating balance for {fee_account}: {e}")
        return fee_account.total_due
```

## Performance Considerations

### When to Use Dynamic Properties

- ✅ **Use for**: Real-time calculations, accurate status checking
- ✅ **Use for**: Small datasets, individual account operations
- ✅ **Use for**: API responses, template rendering

### When to Use Stored Fields

- ✅ **Use for**: Database queries, filtering, ordering
- ✅ **Use for**: Large dataset operations
- ✅ **Use for**: Reporting queries

### Best Practices

```python
# Good: Use stored fields for database operations
recent_payments = FeeAccount.objects.filter(
    is_paid=True,
    updated_at__gte=last_week
)

# Good: Use dynamic properties for business logic
for account in recent_payments:
    if account.is_fully_paid:
        mark_as_complete(account)

# Avoid: Using dynamic properties in database queries
# This won't work as expected:
# FeeAccount.objects.filter(current_balance__gt=0)  # Error!
```

## Common Pitfalls

1. **Don't use dynamic properties in database queries** - They're Python properties, not database fields
2. **Update stored fields when needed** - Some legacy code might still depend on them
3. **Test thoroughly** - Ensure calculations match between old and new methods
4. **Monitor performance** - Dynamic properties involve database queries

## Conclusion

The dynamic properties provide accurate, real-time calculations while maintaining backward compatibility. Use them for business logic and display, but continue using stored fields for database operations during the migration period.
