<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} | Tiny Feet MIS</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        .font-display { font-family: 'Orbitron', monospace; }
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="overflow-x-hidden">

<section class="w-full min-h-screen flex items-center justify-center px-4 py-8 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
  <!-- Animated Background Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <!-- Stars -->
    <div class="star star-1">✦</div>
    <div class="star star-2">✧</div>
    <div class="star star-3">✦</div>
    <div class="star star-4">✧</div>
    <div class="star star-5">✦</div>
    <div class="star star-6">✧</div>
    <div class="star star-7">✦</div>
    <div class="star star-8">✧</div>
    <div class="star star-9">✦</div>
    <div class="star star-10">✧</div>
    
    <!-- Planets -->
    <div class="planet planet-1"></div>
    <div class="planet planet-2"></div>
    <div class="planet planet-3"></div>
    
    <!-- Shooting Stars -->
    <div class="shooting-star shooting-1"></div>
    <div class="shooting-star shooting-2"></div>
    
    <!-- Nebula Effects -->
    <div class="nebula nebula-1"></div>
    <div class="nebula nebula-2"></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 max-w-4xl mx-auto text-center space-y-8 main-content-fade-in">
    
    <!-- Header Section -->
    <div class="space-y-6 header-slide-in">
      <!-- Space Icon -->
      <div class="flex justify-center">
        <div class="w-24 h-24 bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-2xl icon-float relative overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-full"></div>
          <i class="fas fa-rocket text-white text-4xl icon-pulse relative z-10"></i>
        </div>
      </div>
      
      <!-- Title -->
      <div class="space-y-4">
        <h1 class="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-600 font-display title-glow">
          🚀 Cosmic Moment
        </h1>
        <p class="text-xl md:text-2xl text-blue-200 font-light subtitle-fade-in">
          {{ trigger.message }}
        </p>
        <div class="w-32 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mx-auto accent-line-grow"></div>
      </div>
    </div>

    <!-- Quote Card -->
    <div class="quote-card bg-white/10 backdrop-blur-lg border border-white/20 rounded-3xl p-8 md:p-12 shadow-2xl quote-slide-in relative overflow-hidden">
      <!-- Cosmic Background Effect -->
      <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-indigo-500/10 to-purple-500/10 rounded-3xl"></div>
      
      <!-- Quote Content -->
      <div class="space-y-6 relative z-10" id="quote-content">
        <!-- Quote Text -->
        <blockquote class="text-2xl md:text-3xl lg:text-4xl font-light text-white leading-relaxed quote-text-fade-in">
          <i class="fas fa-quote-left text-blue-400 text-2xl mr-4 opacity-60"></i>
          <span id="quote-text">{{ quote.quote }}</span>
          <i class="fas fa-quote-right text-blue-400 text-2xl ml-4 opacity-60"></i>
        </blockquote>
        
        <!-- Author & Context -->
        <div class="space-y-3 author-info-slide-in">
          <div class="text-xl md:text-2xl font-semibold text-blue-300" id="quote-author">
            — {{ quote.author }}
          </div>
          <div class="text-lg text-indigo-200 opacity-80" id="quote-context">
            <i class="fas fa-telescope mr-2"></i>{{ quote.context }}
          </div>
          <div class="flex flex-wrap gap-2 justify-center">
            <div class="inline-flex items-center gap-2 bg-blue-500/20 text-blue-200 px-4 py-2 rounded-full text-sm font-medium" id="quote-theme">
              <i class="fas fa-star"></i>
              {{ quote.theme }}
            </div>
            <div class="inline-flex items-center gap-2 bg-indigo-500/20 text-indigo-200 px-4 py-2 rounded-full text-sm font-medium" id="quote-category">
              <i class="fas fa-atom"></i>
              {{ quote.category }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center buttons-slide-in">
      <!-- New Quote Button -->
      <button
        id="new-quote-btn"
        class="group bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-bold py-4 px-8 rounded-2xl hover:from-blue-600 hover:to-indigo-600 focus:ring-4 focus:ring-blue-500/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl"
      >
        <i class="fas fa-satellite mr-3 group-hover:rotate-180 transition-transform duration-500"></i>
        Launch Another
      </button>
      
      <!-- Share Quote Button -->
      <button
        id="share-quote-btn"
        class="group bg-white/10 backdrop-blur-sm text-white font-bold py-4 px-8 rounded-2xl hover:bg-white/20 focus:ring-4 focus:ring-white/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl border border-white/20"
      >
        <i class="fas fa-share mr-3 group-hover:scale-110 transition-transform duration-300"></i>
        Share Discovery
      </button>
      
      <!-- Return Button -->
      <a
        href="javascript:history.back()"
        class="group bg-slate-700/50 backdrop-blur-sm text-white font-bold py-4 px-8 rounded-2xl hover:bg-slate-600/50 focus:ring-4 focus:ring-slate-500/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl border border-white/10"
      >
        <i class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-300"></i>
        Return to Earth
      </a>
    </div>

    <!-- Easter Egg Discovery Message -->
    <div class="bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border border-blue-400/30 rounded-2xl p-6 discovery-message-fade-in">
      <div class="flex items-center justify-center gap-3 text-blue-200">
        <i class="fas fa-egg text-2xl text-blue-400"></i>
        <div class="text-center">
          <p class="font-semibold">🎉 Congratulations! You've discovered a cosmic easter egg!</p>
          <p class="text-sm opacity-80 mt-1">
            Your search for "{{ trigger.search_term }}" has unlocked this hidden feature. 
            The system appreciates curious minds who reach for the stars.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  /* Space Background Animations */
  .star {
    position: absolute;
    color: rgba(147, 197, 253, 0.8);
    animation: starTwinkle 4s ease-in-out infinite;
    font-size: 1.5rem;
  }

  .star-1 { top: 10%; left: 10%; animation-delay: 0s; }
  .star-2 { top: 20%; right: 15%; animation-delay: 0.5s; }
  .star-3 { top: 30%; left: 20%; animation-delay: 1s; }
  .star-4 { top: 40%; right: 25%; animation-delay: 1.5s; }
  .star-5 { top: 60%; left: 15%; animation-delay: 2s; }
  .star-6 { top: 70%; right: 20%; animation-delay: 2.5s; }
  .star-7 { top: 80%; left: 25%; animation-delay: 3s; }
  .star-8 { top: 15%; left: 60%; animation-delay: 3.5s; }
  .star-9 { top: 50%; right: 10%; animation-delay: 4s; }
  .star-10 { top: 85%; right: 30%; animation-delay: 4.5s; }

  .planet {
    position: absolute;
    border-radius: 50%;
    animation: planetOrbit 20s linear infinite;
  }

  .planet-1 {
    width: 60px;
    height: 60px;
    background: radial-gradient(circle at 30% 30%, #fbbf24, #f59e0b, #d97706);
    top: 15%;
    right: 10%;
    animation-delay: 0s;
  }

  .planet-2 {
    width: 40px;
    height: 40px;
    background: radial-gradient(circle at 30% 30%, #3b82f6, #1d4ed8, #1e40af);
    bottom: 20%;
    left: 10%;
    animation-delay: -7s;
  }

  .planet-3 {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle at 30% 30%, #ef4444, #dc2626, #b91c1c);
    top: 50%;
    left: 5%;
    animation-delay: -14s;
  }

  .shooting-star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: white;
    border-radius: 50%;
    animation: shootingStar 3s linear infinite;
  }

  .shooting-1 {
    top: 20%;
    left: -10px;
    animation-delay: 0s;
  }

  .shooting-2 {
    top: 60%;
    left: -10px;
    animation-delay: 1.5s;
  }

  .nebula {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    animation: nebulaGlow 8s ease-in-out infinite;
  }

  .nebula-1 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.3), transparent);
    top: 10%;
    left: 20%;
    animation-delay: 0s;
  }

  .nebula-2 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.2), transparent);
    bottom: 20%;
    right: 30%;
    animation-delay: -4s;
  }

  /* Content Animations */
  .main-content-fade-in {
    opacity: 0;
    animation: mainContentFadeIn 1s ease-out 0.5s forwards;
  }

  .header-slide-in {
    opacity: 0;
    transform: translateY(-50px);
    animation: headerSlideIn 1s ease-out 0.8s forwards;
  }

  .icon-float {
    animation: iconFloat 4s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 3s ease-in-out infinite;
  }

  .title-glow {
    animation: titleGlow 3s ease-in-out infinite;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 1s ease-out 1.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 1s ease-out 1.5s forwards;
  }

  .quote-slide-in {
    opacity: 0;
    transform: translateY(50px);
    animation: quoteSlideIn 1s ease-out 1.8s forwards;
  }

  .quote-text-fade-in {
    opacity: 0;
    animation: quoteTextFadeIn 1s ease-out 2.2s forwards;
  }

  .author-info-slide-in {
    opacity: 0;
    transform: translateX(-30px);
    animation: authorInfoSlideIn 1s ease-out 2.5s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: buttonsSlideIn 1s ease-out 2.8s forwards;
  }

  .discovery-message-fade-in {
    opacity: 0;
    animation: discoveryMessageFadeIn 1s ease-out 3.2s forwards;
  }

  /* Keyframe Definitions */
  @keyframes starTwinkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
  }

  @keyframes planetOrbit {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes shootingStar {
    0% { 
      transform: translateX(0) translateY(0); 
      opacity: 1; 
    }
    70% { 
      opacity: 1; 
    }
    100% { 
      transform: translateX(300px) translateY(100px); 
      opacity: 0; 
    }
  }

  @keyframes nebulaGlow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.1); }
  }

  @keyframes mainContentFadeIn {
    to { opacity: 1; }
  }

  @keyframes headerSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  @keyframes titleGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 8rem; }
  }

  @keyframes quoteSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes quoteTextFadeIn {
    to { opacity: 1; }
  }

  @keyframes authorInfoSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes buttonsSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes discoveryMessageFadeIn {
    to { opacity: 1; }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .star, .planet, .shooting-star, .nebula {
      display: none;
    }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const newQuoteBtn = document.getElementById('new-quote-btn');
    const shareQuoteBtn = document.getElementById('share-quote-btn');

    // New Quote functionality
    newQuoteBtn.addEventListener('click', function() {
        // Add loading state
        const originalText = this.innerHTML;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-3"></i>Loading...';
        this.disabled = true;

        // Fetch new quote
        fetch('{% url "core:get_new_space_quote" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update quote content with animation
                const quoteContent = document.getElementById('quote-content');
                quoteContent.style.opacity = '0';
                quoteContent.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    document.getElementById('quote-text').textContent = data.quote.quote;
                    document.getElementById('quote-author').textContent = '— ' + data.quote.author;
                    document.getElementById('quote-context').innerHTML = '<i class="fas fa-telescope mr-2"></i>' + data.quote.context;
                    document.getElementById('quote-theme').innerHTML = '<i class="fas fa-star"></i> ' + data.quote.theme;
                    document.getElementById('quote-category').innerHTML = '<i class="fas fa-atom"></i> ' + data.quote.category;

                    quoteContent.style.opacity = '1';
                    quoteContent.style.transform = 'translateY(0)';
                }, 300);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        })
        .finally(() => {
            // Restore button
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 1000);
        });
    });

    // Share Quote functionality
    shareQuoteBtn.addEventListener('click', function() {
        const quote = document.getElementById('quote-text').textContent;
        const author = document.getElementById('quote-author').textContent;
        const shareText = `"${quote}" ${author}`;

        if (navigator.share) {
            navigator.share({
                title: 'Cosmic Wisdom',
                text: shareText,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareText).then(() => {
                // Show feedback
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check mr-3"></i>Copied!';
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            });
        }
    });
});
</script>

</body>
</html>
