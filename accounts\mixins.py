from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.http import JsonResponse
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse_lazy


class PermissionRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires user to have specific permissions.
    
    Usage:
        class MyView(PermissionRequiredMixin, View):
            required_permissions = ['view_students', 'edit_students']
    """
    required_permissions = []
    permission_denied_message = "You don't have permission to access this page."
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not self.has_permission():
            return self.handle_permission_denied()
        
        return super().dispatch(request, *args, **kwargs)
    
    def has_permission(self):
        """Check if user has required permissions"""
        if self.request.user.is_superuser:
            return True
        
        if not self.required_permissions:
            return True
        
        return any(
            self.request.user.has_permission(perm) 
            for perm in self.required_permissions
        )
    
    def handle_permission_denied(self):
        """Handle permission denied"""
        if self.request.path.startswith('/api/'):
            return JsonResponse({
                'error': self.permission_denied_message,
                'code': 'INSUFFICIENT_PERMISSIONS'
            }, status=403)
        
        messages.error(self.request, self.permission_denied_message)
        return self._redirect_based_on_role()


class RoleRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires user to have specific roles.
    
    Usage:
        class MyView(RoleRequiredMixin, View):
            required_roles = ['teacher', 'administrator']
    """
    required_roles = []
    role_denied_message = "You don't have the required role to access this page."
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not self.has_role():
            return self.handle_role_denied()
        
        return super().dispatch(request, *args, **kwargs)
    
    def has_role(self):
        """Check if user has required roles"""
        if self.request.user.is_superuser:
            return True
        
        if not self.required_roles:
            return True
        
        return any(
            self.request.user.has_role(role) 
            for role in self.required_roles
        )
    
    def handle_role_denied(self):
        """Handle role denied"""
        if self.request.path.startswith('/api/'):
            return JsonResponse({
                'error': self.role_denied_message,
                'code': 'INSUFFICIENT_ROLE'
            }, status=403)
        
        messages.error(self.request, self.role_denied_message)
        return self._redirect_based_on_role()


class RoleLevelRequiredMixin(LoginRequiredMixin):
    """
    Mixin that requires user to have minimum role level.
    
    Usage:
        class MyView(RoleLevelRequiredMixin, View):
            required_role_level = 3  # Academic Coordinator or higher
    """
    required_role_level = 1
    level_denied_message = "You don't have sufficient role level to access this page."
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not self.has_role_level():
            return self.handle_level_denied()
        
        return super().dispatch(request, *args, **kwargs)
    
    def has_role_level(self):
        """Check if user has required role level"""
        if self.request.user.is_superuser:
            return True
        
        user_level = self.request.user.get_highest_role_level()
        return user_level >= self.required_role_level
    
    def handle_level_denied(self):
        """Handle level denied"""
        if self.request.path.startswith('/api/'):
            return JsonResponse({
                'error': self.level_denied_message,
                'code': 'INSUFFICIENT_LEVEL'
            }, status=403)
        
        messages.error(self.request, self.level_denied_message)
        return self._redirect_based_on_role()


class StudentRequiredMixin(LoginRequiredMixin):
    """Mixin that requires user to be a student"""
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not request.user.is_student() and not request.user.is_superuser:
            messages.error(request, "Student access required.")
            return self._redirect_based_on_role()
        
        return super().dispatch(request, *args, **kwargs)


class TeacherRequiredMixin(LoginRequiredMixin):
    """Mixin that requires user to be a teacher or higher"""
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not (request.user.is_teacher() or request.user.is_admin()) and not request.user.is_superuser:
            messages.error(request, "Teacher access required.")
            return self._redirect_based_on_role()
        
        return super().dispatch(request, *args, **kwargs)


class AdminRequiredMixin(LoginRequiredMixin):
    """Mixin that requires user to be an administrator"""
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        if not request.user.is_admin() and not request.user.is_superuser:
            messages.error(request, "Administrator access required.")
            return self._redirect_based_on_role()
        
        return super().dispatch(request, *args, **kwargs)


class StaffOrPermissionRequiredMixin(LoginRequiredMixin):
    """
    Mixin that allows access if user is staff OR has specific permissions.
    Useful for backward compatibility.
    """
    required_permissions = []
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        
        # Allow if user is staff (backward compatibility)
        if request.user.is_staff or request.user.is_superuser:
            return super().dispatch(request, *args, **kwargs)
        
        # Check permissions
        if self.required_permissions:
            user_has_permission = any(
                request.user.has_permission(perm) 
                for perm in self.required_permissions
            )
            if user_has_permission:
                return super().dispatch(request, *args, **kwargs)
        
        messages.error(request, "Insufficient permissions.")
        return self._redirect_based_on_role()


class APIPermissionMixin:
    """
    Mixin for API views that handles permission checking with JSON responses.
    """
    required_permissions = []
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return JsonResponse({
                'error': 'Authentication required',
                'code': 'AUTHENTICATION_REQUIRED'
            }, status=401)
        
        if not self.has_permission():
            return JsonResponse({
                'error': 'Insufficient permissions',
                'code': 'INSUFFICIENT_PERMISSIONS',
                'required_permissions': self.required_permissions
            }, status=403)
        
        return super().dispatch(request, *args, **kwargs)
    
    def has_permission(self):
        """Check if user has required permissions"""
        if self.request.user.is_superuser:
            return True
        
        if not self.required_permissions:
            return True
        
        return any(
            self.request.user.has_permission(perm) 
            for perm in self.required_permissions
        )


# Convenience mixins for common permission combinations
class StudentManagementMixin(PermissionRequiredMixin):
    """Mixin for views that require student management permissions"""
    required_permissions = ['view_students', 'manage_students']


class FinanceManagementMixin(PermissionRequiredMixin):
    """Mixin for views that require finance management permissions"""
    required_permissions = ['view_finances', 'manage_finances']


class AcademicManagementMixin(PermissionRequiredMixin):
    """Mixin for views that require academic management permissions"""
    required_permissions = ['view_academics', 'manage_academics']


class ReportViewMixin(PermissionRequiredMixin):
    """Mixin for views that require report viewing permissions"""
    required_permissions = ['view_reports']


# Base mixin with common functionality
class RBACMixin:
    """Base mixin with common RBAC functionality"""
    
    def _redirect_based_on_role(self):
        """Redirect user based on their role level"""
        if hasattr(self.request.user, 'get_highest_role_level'):
            role_level = self.request.user.get_highest_role_level()
            if role_level >= 5:  # Administrator
                return redirect('students:home')
            elif role_level == 4 or self.request.user.has_permission('manage_finances'):  # Finance Manager
                return redirect('finances:expenditures')
            elif role_level >= 2:  # Teacher/Academic Coordinator
                return redirect('academics:dashboard')
            else:  # Student or lower
                return redirect('accounts:login')

        return redirect('accounts:login')
    
    def get_context_data(self, **kwargs):
        """Add RBAC context to template"""
        context = super().get_context_data(**kwargs) if hasattr(super(), 'get_context_data') else {}
        
        if self.request.user.is_authenticated:
            context.update({
                'user_roles': self.request.user.get_active_roles(),
                'user_permissions': self.request.user.get_all_permissions(),
                'user_role_level': self.request.user.get_highest_role_level(),
                'is_student': self.request.user.is_student(),
                'is_teacher': self.request.user.is_teacher(),
                'is_admin': self.request.user.is_admin(),
                'can_manage_students': self.request.user.can_manage_students(),
                'can_manage_finances': self.request.user.can_manage_finances(),
                'can_manage_academics': self.request.user.can_manage_academics(),
            })
        
        return context


# Apply RBACMixin to all other mixins
for mixin_class in [PermissionRequiredMixin, RoleRequiredMixin, RoleLevelRequiredMixin,
                   StudentRequiredMixin, TeacherRequiredMixin, AdminRequiredMixin,
                   StaffOrPermissionRequiredMixin]:
    if not issubclass(mixin_class, RBACMixin):
        mixin_class.__bases__ = (RBACMixin,) + mixin_class.__bases__
