{% extends 'base.html' %} {% load humanize %} {% block content %}
<section
  class="flex flex-col items-center gap-12 px-4 py-8 w-full max-w-7xl mx-auto"
>
  <!-- Section Header -->
  <section
    class="flex flex-col md:flex-row justify-between items-center w-full gap-8 card-modern p-8 header-animation"
  >
    <div class="flex flex-col space-y-4">
      <div class="flex items-center gap-4">
        <div
          class="w-14 h-14 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-chart-line text-white text-xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl md:text-3xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Dashboard
          </h1>
          <p class="font-medium text-[#40657F] text-lg subtitle-fade-in">
            Welcome back, <PERSON>
          </p>
        </div>
      </div>
      <div
        class="w-24 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full accent-line-grow"
      ></div>
    </div>

    <!-- Enhanced Tab Navigation -->
    <div
      class="flex justify-center items-center font-semibold text-[#40657F] gap-1 bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] rounded-2xl p-2 text-sm mt-4 md:mt-0 shadow-lg border border-[#B9D8EB]/50 backdrop-blur-sm tabs-slide-in"
    >
      <button
        id="overview-btn"
        type="button"
        class="bg-white px-4 py-2 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 text-[#7AB2D3] font-bold tab-button"
      >
        <i class="fas fa-chart-pie mr-2 tab-icon"></i>Overview
      </button>
      <button
        id="notifications-btn"
        type="button"
        class="px-4 py-2 rounded-xl transition-all duration-300 hover:bg-white/80 hover:shadow-md transform hover:scale-105 tab-button relative"
      >
        <i class="fas fa-bell mr-2 tab-icon"></i>Notifications
        <!-- Notification Badge -->
        <span
          id="notification-badge"
          class="absolute -top-1 -right-1 bg-[#F28C8C] text-white text-xs font-bold rounded-full h-5 w-5 items-center justify-center shadow-lg animate-pulse hidden"
          title="Unread notifications"
        >
          <span id="notification-count">0</span>
        </span>
        <!-- Notification Dot for smaller counts -->
        <span
          id="notification-dot"
          class="absolute -top-1 -right-1 bg-[#F28C8C] rounded-full h-3 w-3 shadow-lg animate-pulse hidden"
          title="New notifications available"
        ></span>
      </button>
      <button
        id="calendar-btn"
        type="button"
        class="px-4 py-2 rounded-xl transition-all duration-300 hover:bg-white/80 hover:shadow-md transform hover:scale-105 tab-button"
      >
        <i class="fas fa-calendar mr-2 tab-icon"></i>Calendar
      </button>
    </div>
  </section>

  <section
    id="overview"
    class="flex flex-col justify-center items-center w-full gap-10"
  >
    <section class="flex flex-col gap-8 w-full">
      <section
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full stats-grid"
      >
        <!-- Overall Total Card -->
        <div
          class="dashboard-card group relative overflow-hidden bg-gradient-to-br from-[#7AB2D3]/20 via-white to-[#E2F1F9] border-l-4 border-[#7AB2D3] hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 p-4"
        >
          <!-- Background Pattern -->
          <div
            class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-700"
          ></div>

          <div class="flex items-center justify-between mb-6 relative z-10">
            <div
              class="w-14 h-14 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
            >
              <i class="fas fa-dollar-sign text-white text-xl"></i>
            </div>
            <div class="flex items-center gap-2">
              <div
                class="w-3 h-3 bg-[#74C69D] rounded-full animate-pulse"
              ></div>
              <span class="text-xs text-[#74C69D] font-semibold">Active</span>
            </div>
          </div>

          <div class="relative z-10">
            <h2 class="font-bold text-[#2C3E50] mb-3 text-lg">Overall Total</h2>
            <p
              class="text-2xl md:text-3xl font-bold text-[#7AB2D3] mb-3 font-display"
            >
              ${{overall_total|intcomma}}
            </p>
            <span
              class="text-sm text-[#40657F] font-medium bg-white/80 rounded-lg px-3 py-2 backdrop-blur-sm"
            >
              Total based on enrolled students
            </span>
          </div>
        </div>

        <!-- Collected Total Card -->
        <div
          class="dashboard-card group relative overflow-hidden bg-gradient-to-br from-[#74C69D]/20 via-white to-[#E2F1F9] border-l-4 border-[#74C69D] hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 p-4"
        >
          <!-- Background Pattern -->
          <div
            class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#74C69D]/20 to-[#5fb085]/10 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-700"
          ></div>

          <div class="flex items-center justify-between mb-6 relative z-10">
            <div
              class="w-14 h-14 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
            >
              <i class="fas fa-coins text-white text-xl"></i>
            </div>
            <div class="flex items-center gap-2">
              <div
                class="w-3 h-3 bg-[#74C69D] rounded-full animate-pulse"
              ></div>
              <span class="text-xs text-[#74C69D] font-semibold"
                >Collected</span
              >
            </div>
          </div>

          <div class="relative z-10">
            <h2 class="font-bold text-[#2C3E50] mb-3 text-lg">
              Collected Total
            </h2>
            <p
              class="text-2xl md:text-3xl font-bold text-[#74C69D] mb-3 font-display"
            >
              ${{total_collected|intcomma}}
            </p>
            <div class="flex items-center gap-2">
              <span
                class="bg-[#F28C8C]/20 text-[#F28C8C] font-bold rounded-lg px-3 py-2 text-sm border border-[#F28C8C]/30"
              >
                ${{fee_receivables|intcomma}}
              </span>
              <span class="text-sm text-[#40657F] font-medium">remaining</span>
            </div>
          </div>
        </div>

        <!-- Total Expenditure Card -->
        <div
          class="dashboard-card group relative overflow-hidden bg-gradient-to-br from-[#F28C8C]/20 via-white to-[#E2F1F9] border-l-4 border-[#F28C8C] hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 p-4"
        >
          <!-- Background Pattern -->
          <div
            class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#F28C8C]/20 to-[#e07575]/10 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-700"
          ></div>

          <div class="flex items-center justify-between mb-6 relative z-10">
            <div
              class="w-14 h-14 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
            >
              <i class="fas fa-chart-line text-white text-xl"></i>
            </div>
            <div class="flex items-center gap-2">
              <div
                class="w-3 h-3 bg-[#F28C8C] rounded-full animate-pulse"
              ></div>
              <span class="text-xs text-[#F28C8C] font-semibold">Tracking</span>
            </div>
          </div>

          <div class="relative z-10">
            <h2 class="font-bold text-[#2C3E50] mb-3 text-lg">
              Total Expenditure
            </h2>
            <p
              class="text-2xl md:text-3xl font-bold text-[#F28C8C] mb-3 font-display"
            >
              ${{expenditure_total|intcomma}}
            </p>
            <div class="flex items-center gap-2">
              <span
                class="bg-[#74C69D]/20 text-[#74C69D] font-bold rounded-lg px-3 py-2 text-sm border border-[#74C69D]/30"
              >
                +10.1%
              </span>
              <span class="text-sm text-[#40657F] font-medium"
                >from last term</span
              >
            </div>
          </div>
        </div>

        <!-- Total Students Card -->
        <div
          class="dashboard-card group relative overflow-hidden bg-gradient-to-br from-[#40657F]/20 via-white to-[#E2F1F9] border-l-4 border-[#40657F] hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 p-4"
        >
          <!-- Background Pattern -->
          <div
            class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#40657F]/20 to-[#2C3E50]/10 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-700"
          ></div>

          <div class="flex items-center justify-between mb-6 relative z-10">
            <div
              class="w-14 h-14 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
            >
              <i class="fas fa-users text-white text-xl"></i>
            </div>
            <div class="flex items-center gap-2">
              <div
                class="w-3 h-3 bg-[#7AB2D3] rounded-full animate-pulse"
              ></div>
              <span class="text-xs text-[#7AB2D3] font-semibold">Enrolled</span>
            </div>
          </div>

          <div class="relative z-10">
            <h2 class="font-bold text-[#2C3E50] mb-3 text-lg">
              Total Students
            </h2>
            <p
              class="text-2xl md:text-3xl font-bold text-[#40657F] mb-3 font-display"
            >
              {{ students_count }}
            </p>
            <div class="flex gap-2 text-sm font-bold">
              <span
                class="bg-[#7AB2D3]/20 text-[#40657F] rounded-lg px-3 py-2 border border-[#7AB2D3]/30"
              >
                {{ male_students_count }} Males
              </span>
              <span
                class="bg-[#F28C8C]/20 text-[#F28C8C] rounded-lg px-3 py-2 border border-[#F28C8C]/30"
              >
                {{ female_students_count }} Females
              </span>
            </div>
          </div>
        </div>
      </section>

      <!-- Fee Breakdown Section -->
      <div class="w-full section-fade-in">
        <div class="flex items-center gap-4 mb-8">
          <div
            class="w-10 h-10 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg section-icon-float"
          >
            <i class="fas fa-chart-bar text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
              Fee Breakdown
            </h3>
            <p class="text-[#40657F] text-sm">Detailed financial analysis</p>
          </div>
          <div
            class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
          ></div>
        </div>

        <section
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full fee-breakdown-grid"
        >
          <!-- Tuition Expected Card -->
          <div
            class="fee-card group relative overflow-hidden bg-gradient-to-br from-[#7AB2D3]/15 via-white to-[#E2F1F9] border-l-4 border-[#7AB2D3] hover:shadow-xl hover:-translate-y-1 transition-all duration-400 p-4"
          >
            <div
              class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 rounded-full -translate-y-8 translate-x-8 group-hover:scale-125 transition-transform duration-500"
            ></div>

            <div class="flex items-center justify-between mb-4 relative z-10">
              <div
                class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 group-hover:rotate-6 transition-all duration-300"
              >
                <i class="fas fa-graduation-cap text-white text-lg"></i>
              </div>
              <span
                class="text-xs font-bold text-[#7AB2D3] bg-[#7AB2D3]/20 px-3 py-1 rounded-full border border-[#7AB2D3]/30"
              >
                EXPECTED
              </span>
            </div>

            <div class="relative z-10">
              <h2 class="font-bold text-[#2C3E50] mb-3 text-lg">
                Tuition Expected
              </h2>
              <p
                class="text-2xl md:text-3xl font-bold text-[#7AB2D3] mb-3 font-display counter-animate"
              >
                ${{tuition_fees_total|intcomma}}
              </p>
              <span
                class="text-xs text-[#40657F] font-medium bg-white/80 rounded-lg px-3 py-2 backdrop-blur-sm"
              >
                Based on enrolled students
              </span>
            </div>
          </div>

          <!-- Tuition Collected Card -->
          <div
            class="fee-card group relative overflow-hidden bg-gradient-to-br from-[#74C69D]/15 via-white to-[#E2F1F9] border-l-4 border-[#74C69D] hover:shadow-xl hover:-translate-y-1 transition-all duration-400 p-4"
          >
            <div
              class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-[#74C69D]/20 to-[#5fb085]/10 rounded-full -translate-y-8 translate-x-8 group-hover:scale-125 transition-transform duration-500"
            ></div>

            <div class="flex items-center justify-between mb-4 relative z-10">
              <div
                class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 group-hover:rotate-6 transition-all duration-300"
              >
                <i class="fas fa-check-circle text-white text-lg"></i>
              </div>
              <span
                class="text-xs font-bold text-[#74C69D] bg-[#74C69D]/20 px-3 py-1 rounded-full border border-[#74C69D]/30"
              >
                ACTUAL
              </span>
            </div>

            <div class="relative z-10">
              <h2 class="font-bold text-[#2C3E50] mb-3 text-lg">
                Tuition Collected
              </h2>
              <p
                class="text-2xl md:text-3xl font-bold text-[#74C69D] mb-3 font-display counter-animate"
              >
                ${{tuition_collection|intcomma}}
              </p>
              <div class="flex items-center gap-2">
                <span
                  class="bg-[#F28C8C]/20 text-[#F28C8C] font-bold rounded-lg px-3 py-2 text-xs border border-[#F28C8C]/30"
                >
                  ${{tuition_balance|intcomma}}
                </span>
                <span class="text-xs text-[#40657F] font-medium"
                  >remaining</span
                >
              </div>
            </div>
          </div>

          <!-- Food Expected Card -->
          <div
            class="fee-card group relative overflow-hidden bg-gradient-to-br from-[#F28C8C]/15 via-white to-[#E2F1F9] border-l-4 border-[#F28C8C] hover:shadow-xl hover:-translate-y-1 transition-all duration-400 p-4"
          >
            <div
              class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-[#F28C8C]/20 to-[#e07575]/10 rounded-full -translate-y-8 translate-x-8 group-hover:scale-125 transition-transform duration-500"
            ></div>

            <div class="flex items-center justify-between mb-4 relative z-10">
              <div
                class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 group-hover:rotate-6 transition-all duration-300"
              >
                <i class="fas fa-utensils text-white text-lg"></i>
              </div>
              <span
                class="text-xs font-bold text-[#F28C8C] bg-[#F28C8C]/20 px-3 py-1 rounded-full border border-[#F28C8C]/30"
              >
                EXPECTED
              </span>
            </div>

            <div class="relative z-10">
              <h2 class="font-bold text-[#2C3E50] mb-3 text-lg">
                Food Expected
              </h2>
              <p
                class="text-2xl md:text-3xl font-bold text-[#F28C8C] mb-3 font-display counter-animate"
              >
                ${{food_fees_total|intcomma}}
              </p>
              <span
                class="text-xs text-[#40657F] font-medium bg-white/80 rounded-lg px-3 py-2 backdrop-blur-sm"
              >
                Based on enrolled students
              </span>
            </div>
          </div>

          <!-- Food Collected Card -->
          <div
            class="fee-card group relative overflow-hidden bg-gradient-to-br from-[#40657F]/15 via-white to-[#E2F1F9] border-l-4 border-[#40657F] hover:shadow-xl hover:-translate-y-1 transition-all duration-400 p-4"
          >
            <div
              class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-[#40657F]/20 to-[#2C3E50]/10 rounded-full -translate-y-8 translate-x-8 group-hover:scale-125 transition-transform duration-500"
            ></div>

            <div class="flex items-center justify-between mb-4 relative z-10">
              <div
                class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 group-hover:rotate-6 transition-all duration-300"
              >
                <i class="fas fa-receipt text-white text-lg"></i>
              </div>
              <span
                class="text-xs font-bold text-[#40657F] bg-[#40657F]/20 px-3 py-1 rounded-full border border-[#40657F]/30"
              >
                ACTUAL
              </span>
            </div>

            <div class="relative z-10">
              <h2 class="font-bold text-[#2C3E50] mb-3 text-lg">
                Food Collected
              </h2>
              <p
                class="text-2xl md:text-3xl font-bold text-[#40657F] mb-3 font-display counter-animate"
              >
                ${{food_collection|intcomma}}
              </p>
              <div class="flex items-center gap-2">
                <span
                  class="bg-[#F28C8C]/20 text-[#F28C8C] font-bold rounded-lg px-3 py-2 text-xs border border-[#F28C8C]/30"
                >
                  ${{food_balance|intcomma}}
                </span>
                <span class="text-xs text-[#40657F] font-medium"
                  >remaining</span
                >
              </div>
            </div>
          </div>
        </section>
      </div>
    </section>
    <!-- end of breakdown -->

    <section
      class="grid grid-cols-1 lg:grid-cols-5 gap-8 w-full justify-center items-stretch"
    >
      <div
        class="flex flex-col col-span-1 lg:col-span-3 justify-between w-full card-modern p-6"
      >
        <div class="flex items-center gap-3 mb-6">
          <div
            class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center"
          >
            <i class="fas fa-chart-area text-white text-sm"></i>
          </div>
          <h3 class="text-xl font-bold text-gray-800 font-display">
            Monthly Financial Overview
          </h3>
          <div
            class="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"
          ></div>
        </div>
        <div class="relative">
          <canvas id="monthly-data" class="w-full h-80"></canvas>
          <div class="absolute top-4 right-4 flex gap-2">
            <div
              class="flex items-center gap-2 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1 shadow-sm"
            >
              <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span class="text-xs font-medium text-gray-700">Income</span>
            </div>
            <div
              class="flex items-center gap-2 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1 shadow-sm"
            >
              <div class="w-3 h-3 bg-red-500 rounded-full"></div>
              <span class="text-xs font-medium text-gray-700">Expenses</span>
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex flex-col col-span-1 lg:col-span-2 items-center justify-center w-full"
      >
        <div class="w-full card-modern p-6">
          <div class="flex items-center gap-3 mb-6">
            <div
              class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center"
            >
              <i class="fas fa-calendar-alt text-white text-sm"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 font-display">
              Calendar
            </h3>
          </div>
          <div id="calendar" class="calendar-modern"></div>
        </div>
      </div>
    </section>
  </section>

  <!-- Notifications Section -->
  <section
    id="notifications"
    class="hidden flex-col justify-center items-center w-full gap-6"
  >
    <div class="w-full card-modern p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center gap-3">
          <div
            class="w-8 h-8 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center"
          >
            <i class="fas fa-bell text-white text-sm"></i>
          </div>
          <h3 class="text-xl font-bold text-[#2C3E50] font-display">
            Recent Notifications
          </h3>
        </div>
        <div class="flex gap-2">
          <button
            onclick="window.open('/admin/core/notification/add/', '_blank')"
            class="px-3 py-2 bg-[#F28C8C] text-white rounded-lg hover:bg-[#e07575] transition-colors duration-300 text-sm font-medium flex items-center gap-1"
            title="Add New Notification"
          >
            <i class="fas fa-plus text-xs"></i>
            Add
          </button>
          <button
            id="mark-all-read-btn"
            class="px-4 py-2 bg-[#7AB2D3] text-white rounded-lg hover:bg-[#40657F] transition-colors duration-300 text-sm font-medium"
          >
            Mark All Read
          </button>
        </div>
      </div>

      <!-- Notifications Loading State -->
      <div id="notifications-loading" class="text-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#7AB2D3] mx-auto"></div>
        <p class="text-[#40657F] mt-2">Loading notifications...</p>
      </div>

      <!-- Notifications Container -->
      <div id="notifications-container" class="space-y-4 hidden">
        <!-- Notifications will be loaded here -->
      </div>

      <!-- Empty State -->
      <div id="notifications-empty" class="text-center py-8 hidden">
        <div class="w-16 h-16 bg-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-bell-slash text-[#7AB2D3] text-xl"></i>
        </div>
        <h4 class="text-lg font-semibold text-[#2C3E50] mb-2">No Notifications</h4>
        <p class="text-[#40657F]">You're all caught up! No new notifications at this time.</p>
      </div>
    </div>
  </section>

  <!-- Calendar Section -->
  <section
    id="calendar-tab"
    class="hidden flex-col justify-center items-center w-full gap-6"
  >
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 w-full">
      <!-- Full Calendar -->
      <div class="lg:col-span-2 card-modern p-6">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center gap-3">
            <div
              class="w-8 h-8 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center"
            >
              <i class="fas fa-calendar-alt text-white text-sm"></i>
            </div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">
              Academic Calendar
            </h3>
          </div>
          <button
            onclick="window.open('/admin/core/calendarevent/add/', '_blank')"
            class="px-3 py-2 bg-[#7AB2D3] text-white rounded-lg hover:bg-[#40657F] transition-colors duration-300 text-sm font-medium flex items-center gap-1"
            title="Add New Event"
          >
            <i class="fas fa-plus text-xs"></i>
            Add Event
          </button>
        </div>
        <div id="full-calendar" class="calendar-modern"></div>
      </div>

      <!-- Upcoming Events -->
      <div class="card-modern p-6">
        <div class="flex items-center gap-3 mb-6">
          <div
            class="w-8 h-8 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center"
          >
            <i class="fas fa-clock text-white text-sm"></i>
          </div>
          <h3 class="text-xl font-bold text-[#2C3E50] font-display">
            Upcoming Events
          </h3>
        </div>

        <!-- Upcoming Events Loading State -->
        <div id="upcoming-events-loading" class="text-center py-8">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-[#7AB2D3] mx-auto"></div>
          <p class="text-[#40657F] mt-2 text-sm">Loading events...</p>
        </div>

        <!-- Upcoming Events Container -->
        <div id="upcoming-events-container" class="space-y-4 hidden">
          <!-- Events will be loaded here -->
        </div>

        <!-- Quick Add Event Button -->
        <div class="mt-6 pt-4 border-t border-gray-200">
          <button
            onclick="window.open('/admin/core/calendarevent/add/', '_blank')"
            class="w-full bg-[#7AB2D3] hover:bg-[#40657F] text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-300 flex items-center justify-center gap-2 text-sm"
          >
            <i class="fas fa-plus"></i>
            Quick Add Event
          </button>
        </div>

        <!-- Empty State -->
        <div id="upcoming-events-empty" class="text-center py-8 hidden">
          <div class="w-12 h-12 bg-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-3">
            <i class="fas fa-calendar-times text-[#7AB2D3] text-lg"></i>
          </div>
          <h4 class="text-base font-semibold text-[#2C3E50] mb-2">No Upcoming Events</h4>
          <p class="text-[#40657F] text-sm">No events scheduled for the next 7 days.</p>
        </div>
      </div>
    </div>
  </section>
</section>

<!-- Event Details Modal -->
<div id="event-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 p-4" style="align-items: center; justify-content: center;">
  <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
    <div class="p-6">
      <!-- Modal Header -->
      <div class="flex items-center justify-between mb-4">
        <h3 id="modal-title" class="text-xl font-bold text-[#2C3E50]"></h3>
        <button id="close-modal" class="text-gray-400 hover:text-gray-600 transition-colors">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <!-- Modal Content -->
      <div id="modal-content" class="space-y-4">
        <!-- Content will be populated by JavaScript -->
      </div>

      <!-- Modal Footer -->
      <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
        <button id="close-modal-btn" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
          Close
        </button>
        <button id="edit-event-btn" class="px-4 py-2 bg-[#7AB2D3] text-white rounded-lg hover:bg-[#40657F] transition-colors hidden">
          Edit Event
        </button>
      </div>
    </div>
  </div>
</div>

{% endblock %}

<!-- js -->
{% block scripts %}

<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.17/index.global.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Initialize notification badge check
    checkUnreadNotifications();

    const calendarEl = document.getElementById("calendar");
    const calendar = new FullCalendar.Calendar(calendarEl, {
      initialView: "dayGridMonth",
      headerToolbar: {
        left: '',
        center: 'title',
        right: 'prev,next'
      },
      height: 300,
      dayMaxEvents: 2,
      displayEventTime: false,
      events: function(fetchInfo, successCallback, failureCallback) {
        fetch(`/core/api/calendar/events/?start=${fetchInfo.startStr}&end=${fetchInfo.endStr}`)
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              successCallback(data.events);
            } else {
              failureCallback(data.error);
            }
          })
          .catch(error => {
            console.error('Error fetching calendar events:', error);
            failureCallback(error);
          });
      },
      eventClick: function(info) {
        const event = info.event;
        const props = event.extendedProps;

        showEventModal(event, props);
      }
    });
    calendar.render();

    // Initialize full calendar for the calendar tab
    const fullCalendarEl = document.getElementById("full-calendar");
    const fullCalendar = new FullCalendar.Calendar(fullCalendarEl, {
      initialView: "dayGridMonth",
      headerToolbar: {
        left: 'prev,next today',
        center: 'title',
        right: 'dayGridMonth,timeGridWeek,timeGridDay'
      },
      themeSystem: 'standard',
      height: 'auto',
      dayMaxEvents: 3,
      moreLinkClick: 'popover',
      eventDisplay: 'block',
      displayEventTime: false,
      events: function(fetchInfo, successCallback, failureCallback) {
        fetch(`/core/api/calendar/events/?start=${fetchInfo.startStr}&end=${fetchInfo.endStr}`)
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              successCallback(data.events);
            } else {
              failureCallback(data.error);
            }
          })
          .catch(error => {
            console.error('Error fetching calendar events:', error);
            failureCallback(error);
          });
      },
      eventClick: function(info) {
        // Show event details in modal
        const event = info.event;
        const props = event.extendedProps;

        showEventModal(event, props);
      },
      dayCellClassNames: function(info) {
        // Add custom class for styling
        return ['custom-day-cell'];
      },
      eventClassNames: function(info) {
        // Add custom classes based on event type
        return ['custom-event', `event-${info.event.extendedProps.event_type}`];
      },
      dayCellDidMount: function(info) {
        // Add upcoming event indicators
        const today = new Date();
        const cellDate = info.date;
        const daysDiff = Math.ceil((cellDate - today) / (1000 * 60 * 60 * 24));

        if (daysDiff >= 0 && daysDiff <= 7) {
          // Check if this day has events
          fetch(`/core/api/calendar/events/?start=${cellDate.toISOString().split('T')[0]}&end=${cellDate.toISOString().split('T')[0]}`)
            .then(response => response.json())
            .then(data => {
              if (data.success && data.events.length > 0) {
                // Add upcoming event indicator
                const indicator = document.createElement('div');
                indicator.className = 'upcoming-event-indicator';
                indicator.innerHTML = `<i class="fas fa-star"></i>`;
                info.el.appendChild(indicator);
              }
            })
            .catch(error => console.error('Error checking events:', error));
        }
      }
    });

    // Check for unread notifications and update badge
    function checkUnreadNotifications() {
      fetch('/core/api/notifications/?unread_only=true&limit=50')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            updateNotificationBadge(data.count);
          }
        })
        .catch(error => {
          console.error('Error checking unread notifications:', error);
        });
    }

    // Update notification badge display
    function updateNotificationBadge(count) {
      const badge = document.getElementById('notification-badge');
      const dot = document.getElementById('notification-dot');
      const countElement = document.getElementById('notification-count');

      if (count > 0) {
        if (count > 9) {
          // Show badge with count for 10+ notifications
          countElement.textContent = '9+';
          badge.classList.remove('hidden');
          badge.classList.add('flex');
          dot.classList.add('hidden');
        } else if (count > 0) {
          // Show badge with exact count for 1-9 notifications
          countElement.textContent = count;
          badge.classList.remove('hidden');
          badge.classList.add('flex');
          dot.classList.add('hidden');
        }
      } else {
        // Hide both badge and dot when no unread notifications
        badge.classList.add('hidden');
        badge.classList.remove('flex');
        dot.classList.add('hidden');
      }
    }

    // Load notifications when notifications tab is clicked
    function loadNotifications() {
      const container = document.getElementById('notifications-container');
      const loading = document.getElementById('notifications-loading');
      const empty = document.getElementById('notifications-empty');

      loading.classList.remove('hidden');
      container.classList.add('hidden');
      empty.classList.add('hidden');

      fetch('/core/api/notifications/?limit=10')
        .then(response => response.json())
        .then(data => {
          loading.classList.add('hidden');

          if (data.success && data.notifications.length > 0) {
            container.innerHTML = '';
            data.notifications.forEach(notification => {
              const notificationEl = createNotificationElement(notification);
              container.appendChild(notificationEl);
            });
            container.classList.remove('hidden');
          } else {
            empty.classList.remove('hidden');
          }
        })
        .catch(error => {
          console.error('Error loading notifications:', error);
          loading.classList.add('hidden');
          empty.classList.remove('hidden');
        });
    }

    // Load upcoming events
    function loadUpcomingEvents() {
      const container = document.getElementById('upcoming-events-container');
      const loading = document.getElementById('upcoming-events-loading');
      const empty = document.getElementById('upcoming-events-empty');

      loading.classList.remove('hidden');
      container.classList.add('hidden');
      empty.classList.add('hidden');

      fetch('/core/api/calendar/upcoming/')
        .then(response => response.json())
        .then(data => {
          loading.classList.add('hidden');

          if (data.success && data.events.length > 0) {
            container.innerHTML = '';
            data.events.forEach(event => {
              const eventEl = createUpcomingEventElement(event);
              container.appendChild(eventEl);
            });
            container.classList.remove('hidden');
          } else {
            empty.classList.remove('hidden');
          }
        })
        .catch(error => {
          console.error('Error loading upcoming events:', error);
          loading.classList.add('hidden');
          empty.classList.remove('hidden');
        });
    }

    // Create notification element
    function createNotificationElement(notification) {
      const div = document.createElement('div');
      div.className = `notification-item p-4 rounded-lg border-l-4 transition-all duration-300 hover:shadow-md ${
        notification.is_read ? 'bg-gray-50 border-gray-300' : 'bg-white border-' + getPriorityBorderColor(notification.priority)
      }`;

      div.innerHTML = `
        <div class="flex items-start justify-between">
          <div class="flex items-start gap-3 flex-1">
            <div class="w-8 h-8 rounded-full flex items-center justify-center" style="background-color: ${notification.type.color}20;">
              <i class="${notification.type.icon} text-sm" style="color: ${notification.type.color};"></i>
            </div>
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-1">
                <h4 class="font-semibold text-[#2C3E50] text-sm">${notification.title}</h4>
                <span class="px-2 py-1 rounded-full text-xs font-medium" style="background-color: ${notification.priority_color}20; color: ${notification.priority_color};">
                  ${notification.priority.toUpperCase()}
                </span>
              </div>
              <p class="text-[#40657F] text-sm mb-2">${notification.message}</p>
              <div class="flex items-center gap-4 text-xs text-gray-500">
                <span><i class="fas fa-clock mr-1"></i>${notification.time_ago}</span>
                ${notification.related_student ? `<span><i class="fas fa-user mr-1"></i>${notification.related_student}</span>` : ''}
              </div>
            </div>
          </div>
          ${!notification.is_read ? `
            <button class="mark-read-btn text-[#7AB2D3] hover:text-[#40657F] transition-colors" data-notification-id="${notification.id}">
              <i class="fas fa-check text-sm"></i>
            </button>
          ` : ''}
        </div>
      `;

      // Add click handler for mark as read
      const markReadBtn = div.querySelector('.mark-read-btn');
      if (markReadBtn) {
        markReadBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          markNotificationRead(notification.id, div);
        });
      }

      return div;
    }

    // Create upcoming event element
    function createUpcomingEventElement(event) {
      const div = document.createElement('div');
      div.className = 'event-item p-4 rounded-lg bg-white border-l-4 hover:shadow-md transition-all duration-300 cursor-pointer';
      div.style.borderLeftColor = event.category.color;

      const daysText = event.days_until === 0 ? 'Today' :
                      event.days_until === 1 ? 'Tomorrow' :
                      `In ${event.days_until} days`;

      const urgencyClass = event.days_until === 0 ? 'text-red-600 font-bold' :
                          event.days_until === 1 ? 'text-orange-600 font-semibold' :
                          'text-[#40657F] font-medium';

      div.innerHTML = `
        <div class="flex items-start justify-between">
          <div class="flex items-start gap-3 flex-1">
            <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" style="background-color: ${event.category.color}20;">
              <i class="${event.category.icon} text-sm" style="color: ${event.category.color};"></i>
            </div>
            <div class="flex-1 min-w-0">
              <h4 class="font-semibold text-[#2C3E50] text-sm mb-1">${event.title}</h4>
              <div class="flex items-center gap-2 mb-2">
                <span class="px-2 py-1 rounded-full text-xs ${urgencyClass}" style="background-color: ${event.category.color}15;">
                  ${daysText}
                </span>
                ${event.start_time && !event.is_all_day ? `<span class="text-xs text-[#40657F]">• ${event.start_time}</span>` : ''}
              </div>
              ${event.location ? `<p class="text-xs text-gray-500 flex items-center gap-1"><i class="fas fa-map-marker-alt"></i>${event.location}</p>` : ''}
            </div>
          </div>
          <div class="flex-shrink-0">
            <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
          </div>
        </div>
      `;

      // Add click handler to show more details
      div.addEventListener('click', () => {
        // Create a mock FullCalendar event object for the modal
        const mockEvent = {
          title: event.title,
          start: new Date(event.start_date + (event.start_time ? `T${event.start_time}` : '')),
          allDay: event.is_all_day
        };

        const mockProps = {
          description: `Upcoming event in ${event.days_until === 0 ? 'today' : event.days_until === 1 ? 'tomorrow' : `${event.days_until} days`}`,
          location: event.location,
          category: event.category.name,
          event_type: 'upcoming'
        };

        showEventModal(mockEvent, mockProps);
      });

      return div;
    }

    // Helper function to get priority border color
    function getPriorityBorderColor(priority) {
      const colors = {
        'low': '[#74C69D]',
        'medium': '[#7AB2D3]',
        'high': '[#F28C8C]',
        'urgent': '[#e07575]'
      };
      return colors[priority] || '[#7AB2D3]';
    }

    // Mark notification as read
    function markNotificationRead(notificationId, element) {
      fetch('/core/api/notifications/mark-read/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notification_id: notificationId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          element.classList.add('bg-gray-50', 'border-gray-300');
          element.classList.remove('bg-white');
          const markReadBtn = element.querySelector('.mark-read-btn');
          if (markReadBtn) {
            markReadBtn.remove();
          }
          // Update notification badge
          checkUnreadNotifications();
        }
      })
      .catch(error => {
        console.error('Error marking notification as read:', error);
      });
    }

    // Mark all notifications as read
    document.getElementById('mark-all-read-btn').addEventListener('click', function() {
      const unreadNotifications = document.querySelectorAll('.notification-item .mark-read-btn');
      unreadNotifications.forEach(btn => {
        const notificationId = btn.getAttribute('data-notification-id');
        markNotificationRead(notificationId, btn.closest('.notification-item'));
      });

      // Update badge after marking all as read
      setTimeout(() => {
        checkUnreadNotifications();
      }, 500);
    });

    // Define tabs configuration
    const tabs = [
      { btn: "overview-btn", section: "overview" },
      { btn: "notifications-btn", section: "notifications" },
      { btn: "calendar-btn", section: "calendar-tab" },
    ];

    // Load data when tabs are switched
    tabs.forEach(({ btn, section }) => {
      const buttonElement = document.getElementById(btn);
      if (!buttonElement) {
        console.error(`Button with ID ${btn} not found`);
        return;
      }

      buttonElement.addEventListener("click", () => {
        console.log(`Tab clicked: ${btn} -> ${section}`);
        // Original tab switching logic
        tabs.forEach(({ btn: otherBtn, section: otherSection }) => {
          const btnElement = document.getElementById(otherBtn);
          const sectionElement = document.getElementById(otherSection);

          if (otherBtn === btn) {
            // Active tab styling
            btnElement.classList.add(
              "bg-white",
              "text-[#7AB2D3]",
              "font-bold",
              "shadow-md"
            );
            btnElement.classList.remove("hover:bg-white/80");
            sectionElement.classList.remove("hidden");
            sectionElement.classList.add("flex");

            // Load data based on active tab
            if (section === 'notifications') {
              loadNotifications();
              // Hide notification badge when notifications tab is opened
              updateNotificationBadge(0);
            } else if (section === 'calendar-tab') {
              setTimeout(() => {
                fullCalendar.render();
                loadUpcomingEvents();
              }, 100);
            }
          } else {
            // Inactive tab styling
            btnElement.classList.remove(
              "bg-white",
              "text-[#7AB2D3]",
              "font-bold",
              "shadow-md"
            );
            btnElement.classList.add("hover:bg-white/80");
            sectionElement.classList.add("hidden");
            sectionElement.classList.remove("flex");
          }
        });
      });
    });

    // Modal functionality
    function showEventModal(event, props) {
      const modal = document.getElementById('event-modal');
      const modalTitle = document.getElementById('modal-title');
      const modalContent = document.getElementById('modal-content');
      const editBtn = document.getElementById('edit-event-btn');

      // Set modal title
      modalTitle.textContent = event.title;

      // Build modal content
      let contentHTML = '';

      // Date and time
      if (event.start) {
        const startDate = event.start.toLocaleDateString();
        const startTime = event.allDay ? 'All Day' : event.start.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

        contentHTML += `
          <div class="flex items-center gap-3 p-3 bg-[#E2F1F9] rounded-lg">
            <div class="w-8 h-8 bg-[#7AB2D3] rounded-full flex items-center justify-center">
              <i class="fas fa-calendar text-white text-sm"></i>
            </div>
            <div>
              <p class="font-semibold text-[#2C3E50]">${startDate}</p>
              <p class="text-sm text-[#40657F]">${startTime}</p>
            </div>
          </div>
        `;
      }

      // Description
      if (props.description) {
        contentHTML += `
          <div class="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
            <div class="w-8 h-8 bg-[#40657F] rounded-full flex items-center justify-center">
              <i class="fas fa-info-circle text-white text-sm"></i>
            </div>
            <div>
              <p class="font-semibold text-[#2C3E50] mb-1">Description</p>
              <p class="text-sm text-[#40657F]">${props.description}</p>
            </div>
          </div>
        `;
      }

      // Location
      if (props.location) {
        contentHTML += `
          <div class="flex items-center gap-3 p-3 bg-[#F7FAFC] rounded-lg">
            <div class="w-8 h-8 bg-[#74C69D] rounded-full flex items-center justify-center">
              <i class="fas fa-map-marker-alt text-white text-sm"></i>
            </div>
            <div>
              <p class="font-semibold text-[#2C3E50]">Location</p>
              <p class="text-sm text-[#40657F]">${props.location}</p>
            </div>
          </div>
        `;
      }

      // Category
      if (props.category) {
        contentHTML += `
          <div class="flex items-center gap-3 p-3 bg-[#B9D8EB] bg-opacity-30 rounded-lg">
            <div class="w-8 h-8 bg-[#7AB2D3] rounded-full flex items-center justify-center">
              <i class="fas fa-tag text-white text-sm"></i>
            </div>
            <div>
              <p class="font-semibold text-[#2C3E50]">Category</p>
              <p class="text-sm text-[#40657F]">${props.category}</p>
            </div>
          </div>
        `;
      }

      modalContent.innerHTML = contentHTML;

      // Show/hide edit button
      if (props.event_type !== 'upcoming') {
        editBtn.classList.remove('hidden');
        editBtn.onclick = () => {
          window.open('/admin/core/calendarevent/', '_blank');
        };
      } else {
        editBtn.classList.add('hidden');
      }

      // Show modal
      modal.style.display = 'flex';
      modal.classList.remove('hidden');
    }

    function hideEventModal() {
      const modal = document.getElementById('event-modal');
      modal.style.display = 'none';
      modal.classList.add('hidden');
    }

    // Modal event listeners
    document.getElementById('close-modal').addEventListener('click', hideEventModal);
    document.getElementById('close-modal-btn').addEventListener('click', hideEventModal);

    // Close modal when clicking outside
    document.getElementById('event-modal').addEventListener('click', function(e) {
      if (e.target === this) {
        hideEventModal();
      }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        hideEventModal();
      }
    });

    // Periodically check for new notifications (every 2 minutes)
    setInterval(checkUnreadNotifications, 120000);
  });
</script>

<script>
  const ctx = document.getElementById("monthly-data");
  const incomeData = JSON.parse("{{income_data|escapejs}}");

  const months = incomeData.map((item) => item.month);
  const income = incomeData.map((item) => item.income);
  const expense = incomeData.map((item) => item.expense);
  console.log(incomeData);

  const data = {
    labels: months,
    datasets: [
      {
        label: "Income",
        data: income,
        backgroundColor: "rgba(59, 130, 246, 0.6)", // Tailwind blue-500
      },
      {
        label: "Expenses",
        data: expense,
        backgroundColor: "rgba(239, 68, 68, 0.6)", // Tailwind red-500
      },
    ],
  };

  const config = {
    type: "bar",
    data: data,
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: "top",
        },
        tooltip: {
          mode: "index",
          intersect: false,
        },
      },
      scales: {
        x: {
          stacked: false,
        },
        y: {
          beginAtZero: true,
        },
      },
    },
  };

  new Chart(ctx, config);
</script>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    // Add loading animation to cards
    const cards = document.querySelectorAll(".card-modern");
    cards.forEach((card, index) => {
      card.style.opacity = "0";
      card.style.transform = "translateY(20px)";
      setTimeout(() => {
        card.style.transition = "all 0.6s ease-out";
        card.style.opacity = "1";
        card.style.transform = "translateY(0)";
      }, index * 100);
    });
  });
</script>

{% endblock %}

<!--  -->
{% block style %}
<style>
  /* Notification Badge Styles */
  #notification-badge {
    font-size: 10px;
    line-height: 1;
    min-width: 20px;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(242, 140, 140, 0.4);
    z-index: 10;
  }

  #notification-dot {
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(242, 140, 140, 0.4);
    z-index: 10;
  }

  /* Enhanced notification badge animation */
  @keyframes notificationPulse {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
  }

  #notification-badge.animate-pulse,
  #notification-dot.animate-pulse {
    animation: notificationPulse 2s ease-in-out infinite;
  }

  /* Tab button hover effect with badge */
  #notifications-btn:hover #notification-badge,
  #notifications-btn:hover #notification-dot {
    animation-duration: 1s;
  }

  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .tabs-slide-in {
    opacity: 0;
    transform: translateX(50px);
    animation: tabsSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Dashboard Card Animations */
  .stats-grid {
    opacity: 0;
    animation: statsGridFadeIn 0.8s ease-out 1s forwards;
  }

  .dashboard-card {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
    animation: dashboardCardSlideUp 0.8s ease-out forwards;
  }

  .dashboard-card:nth-child(1) {
    animation-delay: 1.1s;
  }
  .dashboard-card:nth-child(2) {
    animation-delay: 1.2s;
  }
  .dashboard-card:nth-child(3) {
    animation-delay: 1.3s;
  }
  .dashboard-card:nth-child(4) {
    animation-delay: 1.4s;
  }

  .counter-animate {
    opacity: 0;
    animation: counterFadeIn 1s ease-out 1.8s forwards;
  }

  /* Section Animations */
  .section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: sectionFadeIn 0.8s ease-out 1.6s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 4s ease-in-out infinite;
  }

  /* Fee Breakdown Animations */
  .fee-breakdown-grid {
    opacity: 0;
    animation: feeBreakdownFadeIn 0.8s ease-out 1.8s forwards;
  }

  .fee-card {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    animation: feeCardSlideUp 0.6s ease-out forwards;
  }

  .fee-card:nth-child(1) {
    animation-delay: 1.9s;
  }
  .fee-card:nth-child(2) {
    animation-delay: 2s;
  }
  .fee-card:nth-child(3) {
    animation-delay: 2.1s;
  }
  .fee-card:nth-child(4) {
    animation-delay: 2.2s;
  }

  /* Tab Button Animations */
  .tab-button {
    position: relative;
    overflow: hidden;
  }

  .tab-button::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transition: left 0.5s;
  }

  .tab-button:hover::before {
    left: 100%;
  }

  .tab-icon {
    transition: transform 0.3s ease;
  }

  .tab-button:hover .tab-icon {
    transform: scale(1.1) rotate(5deg);
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes tabsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes statsGridFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes dashboardCardSlideUp {
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes counterFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes sectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes sectionIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(3deg);
    }
  }

  @keyframes feeBreakdownFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes feeCardSlideUp {
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* Hover Enhancements */
  .dashboard-card:hover .counter-animate {
    animation: counterPulse 0.6s ease-in-out;
    opacity: 1 !important; /* Ensure numbers stay visible on hover */
  }

  @keyframes counterPulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1; /* Keep opacity at 1 throughout pulse */
    }
    50% {
      transform: scale(1.05);
      opacity: 1; /* Keep opacity at 1 throughout pulse */
    }
  }

  /* Loading States */
  .dashboard-card::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.8s;
  }

  .dashboard-card:hover::after {
    left: 100%;
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .dashboard-card {
      animation-delay: 0.5s;
    }

    .dashboard-card:nth-child(n) {
      animation-delay: calc(0.5s + 0.1s * var(--card-index, 1));
    }

    .fee-card {
      animation-delay: 1s;
    }

    .fee-card:nth-child(n) {
      animation-delay: calc(1s + 0.1s * var(--card-index, 1));
    }
  }

  /* Chart Container Animation */
  .chart-container {
    opacity: 0;
    transform: translateY(20px);
    animation: chartFadeIn 0.8s ease-out 2.5s forwards;
  }

  @keyframes chartFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Calendar Animation */
  .calendar-modern {
    opacity: 0;
    transform: scale(0.95);
    animation: calendarZoomIn 0.8s ease-out 2.7s forwards;
  }

  @keyframes calendarZoomIn {
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* FullCalendar Custom Styling */
  .fc {
    font-family: inherit;
  }

  .fc-theme-standard .fc-scrollgrid {
    border: 1px solid #E2F1F9;
  }

  .fc-theme-standard td, .fc-theme-standard th {
    border: 1px solid #E2F1F9;
  }

  .fc-theme-standard .fc-col-header-cell {
    border: 1px solid #40657F !important;
  }

  .fc-col-header-cell {
    background-color: #2C3E50;
    color: white;
    font-weight: 600;
    padding: 8px 4px;
    border-color: #40657F !important;
  }

  .fc-daygrid-day {
    background-color: white;
    color: #2C3E50;
    border-color: #E2F1F9 !important;
  }

  .fc-daygrid-day-number {
    color: #2C3E50 !important;
    font-weight: 500;
    padding: 4px;
  }

  .fc-day-today {
    background-color: #E2F1F9 !important;
  }

  .fc-day-today .fc-daygrid-day-number {
    background-color: #7AB2D3;
    color: white !important;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 2px;
  }

  .fc-day-other {
    background-color: #F7FAFC;
    color: #9CA3AF;
    border-color: #E2F1F9 !important;
  }

  .fc-day-other .fc-daygrid-day-number {
    color: #9CA3AF !important;
  }

  /* Event Styling */
  .fc-event {
    border: none !important;
    border-radius: 4px !important;
    padding: 2px 6px !important;
    margin: 1px 2px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
  }

  .fc-event-title {
    color: white !important;
    font-weight: 500 !important;
  }

  .fc-event:hover {
    opacity: 0.8;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  /* Event type specific styling */
  .event-academic {
    background-color: #7AB2D3 !important;
    border-left: 3px solid #40657F !important;
  }

  .event-administrative {
    background-color: #40657F !important;
    border-left: 3px solid #2C3E50 !important;
  }

  .event-holiday {
    background-color: #74C69D !important;
    border-left: 3px solid #5fb085 !important;
  }

  .event-exam {
    background-color: #F28C8C !important;
    border-left: 3px solid #e07575 !important;
  }

  .event-meeting {
    background-color: #B9D8EB !important;
    border-left: 3px solid #7AB2D3 !important;
    color: #2C3E50 !important;
  }

  .event-meeting .fc-event-title {
    color: #2C3E50 !important;
  }

  /* Header styling */
  .fc-toolbar {
    margin-bottom: 1rem !important;
  }

  .fc-toolbar-title {
    color: #2C3E50 !important;
    font-weight: 700 !important;
    font-size: 1.25rem !important;
  }

  .fc-button {
    background-color: #2C3E50 !important;
    border-color: #2C3E50 !important;
    color: white !important;
    font-weight: 600 !important;
    border-radius: 0 !important;
    padding: 6px 16px !important;
    border: none !important;
    box-shadow: none !important;
  }

  .fc-button:hover {
    background-color: #1a252f !important;
    border-color: #1a252f !important;
    box-shadow: none !important;
  }

  .fc-button:focus {
    box-shadow: 0 0 0 2px rgba(44, 62, 80, 0.3) !important;
    outline: none !important;
  }

  .fc-button-active {
    background-color: #1a252f !important;
    border-color: #1a252f !important;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.2) !important;
  }

  .fc-button:disabled {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    opacity: 0.6 !important;
  }

  /* More link styling */
  .fc-more-link {
    color: #7AB2D3 !important;
    font-weight: 500 !important;
  }

  .fc-more-link:hover {
    color: #40657F !important;
  }

  /* Popover styling */
  .fc-popover {
    border: 1px solid #E2F1F9 !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  }

  .fc-popover-header {
    background-color: #7AB2D3 !important;
    color: white !important;
    font-weight: 600 !important;
  }

  /* Upcoming Event Indicators */
  .upcoming-event-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    background-color: #F28C8C;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    z-index: 10;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Custom day cell positioning */
  .fc-daygrid-day-frame {
    position: relative;
  }
</style>

{% endblock %}
