# Generated by Django 5.1.2 on 2025-06-26 19:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AcademicYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=False)),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
            ],
            options={
                'db_table': 'students_academic_year',
            },
        ),
        migrations.CreateModel(
            name='Level',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level_name', models.CharField(choices=[('Baby Class', 'Baby Class'), ('Reception', 'Reception'), ('Toddler Class', 'Toddler Class'), ('Primary 1', 'Primary 1'), ('Primary 2', 'Primary 2'), ('Primary 3', 'Primary 3'), ('Primary 4', 'Primary 4')], max_length=20)),
                ('abbrv', models.CharField(max_length=10)),
                ('education_stage', models.CharField(choices=[('Primary', 'Primary'), ('Nursery', 'Nursery')], default='Primary', max_length=200)),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
            ],
            options={
                'db_table': 'students_level',
            },
        ),
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('student_id', models.SlugField(blank=True, null=True, unique=True)),
                ('gender', models.CharField(choices=[('Male', 'Male'), ('Female', 'Female')], default='Male', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.level')),
            ],
            options={
                'db_table': 'students_student',
            },
        ),
        migrations.CreateModel(
            name='Term',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('term_name', models.CharField(choices=[('Term 1', 'Term 1'), ('Term 2', 'Term 2'), ('Term 3', 'Term 3')], max_length=50)),
                ('slug', models.SlugField(blank=True, max_length=200, null=True, unique=True)),
                ('is_active', models.BooleanField(default=False)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='academic_year', to='students.academicyear')),
            ],
            options={
                'db_table': 'students_term',
                'unique_together': {('term_name', 'academic_year')},
            },
        ),
    ]
