{% extends 'academics/base.html' %} {% load static %}
<!--  -->
{% block title %}{{teacher.first_name}} {{teacher.last_name}} | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl"
        >
          <i class="fas fa-user-tie text-white text-lg sm:text-xl"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight"
          >
            {{teacher.first_name}} {{teacher.last_name}}
          </h1>
          <p class="text-[#40657F] text-base sm:text-lg font-medium mt-1">
            Teacher profile and assignment details
          </p>
          <div class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2"></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3">
        <a
          href="{% url 'accounts:edit_teacher' teacher.id %}"
          class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-edit mr-2 group-hover:rotate-12 transition-transform duration-300"
          ></i>
          Edit Profile
        </a>
        <a
          href="{% url 'accounts:teachers' %}"
          class="bg-[#40657F] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#2C3E50] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-arrow-left mr-2 group-hover:translate-x-[-2px] transition-transform duration-300"
          ></i>
          Back to Teachers
        </a>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200"
    >
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'accounts:teachers' %}"
        class="text-[#40657F] hover:text-[#7AB2D3] transition-colors"
        >Teachers</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold"
        >{{teacher.first_name}} {{teacher.last_name}}</span
      >
    </nav>
  </div>

  <!-- Search Section -->
  <div class="card-modern p-6">
    <div class="flex items-center gap-3 mb-6">
      <div
        class="w-8 h-8 sm:w-10 sm:h-10 bg-[#7AB2D3] rounded-lg sm:rounded-xl flex items-center justify-center shadow-md"
      >
        <i class="fas fa-search text-white text-xs sm:text-sm"></i>
      </div>
      <div>
        <h3 class="text-lg sm:text-xl font-bold text-[#2C3E50] font-display">
          Teacher Search
        </h3>
        <p class="text-[#40657F] text-sm">Find other teachers by name</p>
      </div>
    </div>

    <form
      method="GET"
      name="q"
      action=""
      class="flex flex-col sm:flex-row gap-4"
    >
      <div class="flex-1 relative">
        <input
          class="w-full pl-12 pr-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm bg-white"
          type="text"
          name="q"
          id="q"
          placeholder="Search by Teacher Name..."
          required
        />
        <i
          class="fas fa-user-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#B9D8EB] text-sm"
        ></i>
      </div>
      <button
        class="bg-[#7AB2D3] text-white font-semibold py-3 px-8 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        type="submit"
      >
        <i
          class="fas fa-search mr-2 group-hover:scale-110 transition-transform duration-300"
        ></i>
        Search Teacher
      </button>
    </form>
  </div>

  <!-- Teacher Profile Section -->
  <div class="card-modern p-8">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-id-card text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Teacher Profile
        </h2>
        <p class="text-[#40657F] text-sm">
          Complete teacher information and details
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <!-- Teacher Information Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Teacher Photo -->
      <div class="lg:col-span-1">
        <div
          class="bg-gradient-to-br from-[#E2F1F9] to-[#B9D8EB] rounded-2xl p-6 text-center"
        >
          <div class="relative inline-block">
            {% if teacher.profile_picture %}
            <img
              src="{{ teacher.profile_picture.url }}"
              alt="{{teacher.first_name}} {{teacher.last_name}}"
              class="rounded-2xl shadow-lg border-4 border-white object-cover w-48 h-48 mx-auto"
            />
            {% else %}
            <img
              src="{% static 'img/student1.jpg' %}"
              alt="{{teacher.first_name}} {{teacher.last_name}}"
              class="rounded-2xl shadow-lg border-4 border-white object-cover w-48 h-48 mx-auto"
            />
            {% endif %}
            <div
              class="absolute -bottom-2 -right-2 w-8 h-8 bg-[#74C69D] rounded-full flex items-center justify-center border-4 border-white"
            >
              {% if teacher.is_staff %}
              <i class="fas fa-crown text-white text-sm"></i>
              {% else %}
              <i class="fas fa-chalkboard-teacher text-white text-sm"></i>
              {% endif %}
            </div>
          </div>
          <h3 class="text-xl font-bold text-[#2C3E50] mt-4 font-display">
            {{teacher.first_name}} {{teacher.last_name}}
          </h3>
          {% if teacher.is_staff %}
          <span
            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30 mt-2"
          >
            <i class="fas fa-user-shield mr-1"></i>
            Administrator
          </span>
          {% else %}
          <span
            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30 mt-2"
          >
            <i class="fas fa-chalkboard-teacher mr-1"></i>
            Teacher
          </span>
          {% endif %}
        </div>
      </div>

      <!-- Teacher Details -->
      <div class="lg:col-span-2">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Personal Information -->
          <div class="space-y-4">
            <h4
              class="text-lg font-bold text-[#2C3E50] font-display mb-4 flex items-center gap-2"
            >
              <i class="fas fa-user text-[#7AB2D3]"></i>
              Personal Information
            </h4>

            <div class="space-y-3">
              <div
                class="flex items-center justify-between p-3 bg-[#E2F1F9]/50 rounded-lg"
              >
                <span class="text-[#40657F] font-medium">Teacher ID</span>
                <span class="text-[#2C3E50] font-bold font-mono"
                  >{{teacher.id}}</span
                >
              </div>

              <div
                class="flex items-center justify-between p-3 bg-white rounded-lg border border-[#B9D8EB]/30"
              >
                <span class="text-[#40657F] font-medium">Full Name</span>
                <span class="text-[#2C3E50] font-bold capitalize"
                  >{{teacher.first_name}} {{teacher.last_name}}</span
                >
              </div>

              <div
                class="flex items-center justify-between p-3 bg-[#E2F1F9]/50 rounded-lg"
              >
                <span class="text-[#40657F] font-medium">Email Address</span>
                <a
                  href="mailto:{{teacher.email}}"
                  class="text-[#7AB2D3] font-bold hover:text-[#40657F] transition-colors"
                  >{{teacher.email}}</a
                >
              </div>

              <div
                class="flex items-center justify-between p-3 bg-white rounded-lg border border-[#B9D8EB]/30"
              >
                <span class="text-[#40657F] font-medium">Gender</span>
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold {% if teacher.gender == 'Male' %}bg-[#7AB2D3]/20 text-[#7AB2D3]{% else %}bg-[#F28C8C]/20 text-[#F28C8C]{% endif %}"
                >
                  <i
                    class="fas {% if teacher.gender == 'Male' %}fa-mars{% else %}fa-venus{% endif %} mr-1"
                  ></i>
                  {{teacher.gender}}
                </span>
              </div>

              <div
                class="flex items-center justify-between p-3 bg-[#E2F1F9]/50 rounded-lg"
              >
                <span class="text-[#40657F] font-medium">Phone Number</span>
                <a
                  href="tel:{{teacher.phone_number}}"
                  class="text-[#7AB2D3] font-bold hover:text-[#40657F] transition-colors"
                  >{{teacher.phone_number}}</a
                >
              </div>
            </div>
          </div>

          <!-- Subject Assignments -->
          <div class="space-y-4">
            <h4
              class="text-lg font-bold text-[#2C3E50] font-display mb-4 flex items-center gap-2"
            >
              <i class="fas fa-book text-[#74C69D]"></i>
              Subject Assignments
            </h4>

            <div class="space-y-3">
              {% for subject in teacher.subject_assignments.all %}
              <div
                class="p-3 bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]/30 rounded-lg border border-[#B9D8EB]/30"
              >
                <div class="flex items-center gap-3">
                  <div
                    class="w-8 h-8 bg-[#7AB2D3] rounded-lg flex items-center justify-center shadow-sm"
                  >
                    <i class="fas fa-graduation-cap text-white text-xs"></i>
                  </div>
                  <div class="flex-1">
                    <div class="font-bold text-[#2C3E50]">
                      {{subject.subject}}
                    </div>
                    <div class="text-xs text-[#40657F]">
                      {{subject.class_assigned}}
                    </div>
                  </div>
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-semibold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30"
                  >
                    {{subject.subject.category}}
                  </span>
                </div>
              </div>
              {% empty %}
              <div class="text-center py-8">
                <div
                  class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center mx-auto mb-4"
                >
                  <i class="fas fa-book text-[#B9D8EB] text-xl"></i>
                </div>
                <p class="text-[#40657F] font-medium">
                  No subjects assigned yet
                </p>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Teacher Assignments Section -->
  <div class="card-modern p-8">
    <div class="flex items-center justify-between mb-8">
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg"
        >
          <i class="fas fa-clipboard-list text-white text-lg"></i>
        </div>
        <div>
          <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
            Subject Assignments
          </h2>
          <p class="text-[#40657F] text-sm">
            Manage teacher's subject assignments and classes
          </p>
        </div>
      </div>

      <a
        href="{% url 'accounts:add_assignment' teacher.id %}"
        class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
      >
        <i
          class="fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300"
        ></i>
        Add Assignment
      </a>
    </div>

    {% if teacher.subject_assignments.all %}
    <div class="overflow-x-auto">
      <div class="table-modern">
        <table class="min-w-full">
          <thead>
            <tr class="bg-[#E2F1F9] border-b border-[#B9D8EB]">
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Subject
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Class Assigned
              </th>
              <th
                class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm"
              >
                Category
              </th>
              <th
                class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#B9D8EB]">
            {% for assignment in teacher.subject_assignments.all %}
            <tr class="hover:bg-[#E2F1F9]/50 transition-all duration-200 group">
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div
                    class="w-10 h-10 bg-[#7AB2D3] rounded-lg flex items-center justify-center shadow-sm"
                  >
                    <i class="fas fa-book text-white text-sm"></i>
                  </div>
                  <div>
                    <div class="font-bold text-[#2C3E50]">
                      {{assignment.subject}}
                    </div>
                    <div class="text-xs text-[#40657F]">Subject</div>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30"
                >
                  <i class="fas fa-chalkboard mr-1"></i>
                  {{assignment.class_assigned}}
                </span>
              </td>
              <td class="py-4 px-6 text-center">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#40657F]/20 text-[#40657F] border border-[#40657F]/30"
                >
                  <i class="fas fa-tag mr-1"></i>
                  {{assignment.subject.category}}
                </span>
              </td>
              <td class="py-4 px-6 text-center">
                <a
                  href="{% url 'accounts:remove_assignment' assignment.id %}"
                  class="inline-flex items-center gap-2 px-3 py-2 bg-[#F28C8C] text-white text-xs font-medium rounded-lg hover:bg-[#e07575] transition-all duration-300 shadow-sm hover:shadow-md group/btn"
                  onclick="return confirm('Are you sure you want to remove this assignment?')"
                >
                  <i
                    class="fas fa-trash group-hover/btn:scale-110 transition-transform duration-300"
                  ></i>
                  Remove
                </a>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-16">
      <div class="flex flex-col items-center gap-6">
        <div
          class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center"
        >
          <i class="fas fa-clipboard-list text-gray-400 text-3xl"></i>
        </div>
        <div>
          <h3 class="font-display font-bold text-2xl text-gray-800 mb-2">
            No Assignments Found
          </h3>
          <p class="text-gray-600 text-lg">
            This teacher doesn't have any subject assignments yet.
          </p>
          <p class="text-gray-500 text-sm mt-2">
            Add assignments to get started with subject management.
          </p>
        </div>
        <a
          href="{% url 'accounts:add_assignment' teacher.id %}"
          class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg"
        >
          <i class="fas fa-plus mr-2"></i>
          Add First Assignment
        </a>
      </div>
    </div>
    {% endif %}
  </div>
</section>
{% endblock %}
