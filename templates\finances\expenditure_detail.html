{% extends 'base.html' %} {% load humanize %}
<!--  -->
{% block title %}{{expenditure.outflow_id}} Details | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-6xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-lg icon-float"
      >
        <i class="fas fa-receipt text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in"
        >
          Expenditure Details
        </h1>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'finances:expenditures' %}"
        class="text-[#7AB2D3] hover:text-[#40657F] transition-colors"
        >Expenditures</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#F28C8C] font-semibold"
        >{{ expenditure.outflow_id }}</span
      >
    </nav>
  </div>

  <!-- Header Section -->
  <div
    class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6 header-info-slide-in"
  >
    <div class="flex items-center gap-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-2xl flex items-center justify-center shadow-xl"
      >
        <i class="fas fa-file-invoice-dollar text-white text-2xl"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          {{ expenditure.outflow_id }}
        </h2>
        <p class="text-[#40657F] font-medium">Expenditure Record</p>
      </div>
    </div>
    <div class="flex items-center gap-3">
      {% if expenditure.is_reversed %}
      <span
        class="inline-flex items-center gap-2 bg-[#F28C8C]/20 text-[#F28C8C] px-4 py-2 rounded-full font-bold border border-[#F28C8C]/30"
      >
        <i class="fas fa-undo text-sm"></i>
        <span>Reversed</span>
      </span>
      {% else %}
      <span
        class="inline-flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30"
      >
        <i class="fas fa-check-circle text-sm"></i>
        <span>Processed</span>
      </span>

      <a
        href="{% url 'finances:reverse_outflow' expenditure.slug %}"
        class="inline-flex items-center gap-2 bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-bold py-2 px-4 rounded-xl hover:from-[#e07575] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl group"
        onclick="return confirm('Are you sure you want to reverse this outflow? This action cannot be undone.')"
      >
        <i
          class="fas fa-undo group-hover:rotate-180 transition-all duration-300"
        ></i>
        <span>Reverse</span>
      </a>
      {% endif %}
    </div>
  </div>
  <!-- Expenditure Summary Card -->
  <div class="card-modern p-8 summary-card-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-info-circle text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Expenditure Summary
        </h3>
        <p class="text-[#40657F] text-sm">Complete expenditure information</p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Total Amount -->
      <div
        class="detail-item bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10 rounded-xl p-6 border border-[#74C69D]/20"
      >
        <div class="flex items-center gap-3 mb-3">
          <div
            class="w-8 h-8 bg-[#74C69D] rounded-lg flex items-center justify-center"
          >
            <i class="fas fa-money-bill text-white text-sm"></i>
          </div>
          <dt class="text-[#40657F] text-sm font-bold uppercase tracking-wider">
            Total Amount
          </dt>
        </div>
        <dd class="text-3xl font-bold text-[#74C69D] font-display">
          MWK {{ expenditure.total_amount | intcomma }}
        </dd>
      </div>

      <!-- Supplier -->
      <div
        class="detail-item bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 rounded-xl p-6 border border-[#7AB2D3]/20"
      >
        <div class="flex items-center gap-3 mb-3">
          <div
            class="w-8 h-8 bg-[#7AB2D3] rounded-lg flex items-center justify-center"
          >
            <i class="fas fa-building text-white text-sm"></i>
          </div>
          <dt class="text-[#40657F] text-sm font-bold uppercase tracking-wider">
            Supplier
          </dt>
        </div>
        <dd class="text-xl font-bold text-[#2C3E50] capitalize">
          {{ expenditure.payee }}
        </dd>
      </div>

      <!-- Entered By -->
      <div
        class="detail-item bg-gradient-to-br from-[#F28C8C]/10 to-[#e07575]/10 rounded-xl p-6 border border-[#F28C8C]/20"
      >
        <div class="flex items-center gap-3 mb-3">
          <div
            class="w-8 h-8 bg-[#F28C8C] rounded-lg flex items-center justify-center"
          >
            <i class="fas fa-user text-white text-sm"></i>
          </div>
          <dt class="text-[#40657F] text-sm font-bold uppercase tracking-wider">
            Entered By
          </dt>
        </div>
        <dd class="text-xl font-bold text-[#2C3E50]">
          {{ expenditure.created_by.first_name }}
          <!--  -->
          {{expenditure.created_by.last_name }}
        </dd>
      </div>

      <!-- Description -->
      <div
        class="detail-item bg-gradient-to-br from-[#B9D8EB]/20 to-[#E2F1F9]/20 rounded-xl p-6 border border-[#B9D8EB]/30"
      >
        <div class="flex items-center gap-3 mb-3">
          <div
            class="w-8 h-8 bg-[#40657F] rounded-lg flex items-center justify-center"
          >
            <i class="fas fa-file-alt text-white text-sm"></i>
          </div>
          <dt class="text-[#40657F] text-sm font-bold uppercase tracking-wider">
            Description
          </dt>
        </div>
        <dd class="text-lg text-[#2C3E50] leading-relaxed">
          {{ expenditure.description }}
        </dd>
      </div>
    </div>
  </div>
  <!-- Line Items Table -->
  <div class="card-modern p-8 table-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
      >
        <i class="fas fa-list text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Line Items
        </h3>
        <p class="text-[#40657F] text-sm">
          Detailed breakdown of expenditure items
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div
        class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30"
      >
        <i class="fas fa-list-ul text-sm"></i>
        <span>
          {{ expenditure.outflow_lines.all|length }}
          <!--  -->
          item{{ expenditure.outflow_lines.all|length|pluralize }}</span
        >
      </div>
    </div>

    <div
      class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg table-fade-in"
    >
      <table class="min-w-full bg-white">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-file-alt text-[#7AB2D3]"></i>
                Description
              </div>
            </th>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-book text-[#40657F]"></i>
                Ledger
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-hashtag text-[#F28C8C]"></i>
                Code
              </div>
            </th>
            <th
              class="px-6 py-4 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-end gap-2">
                <i class="fas fa-money-bill text-[#74C69D]"></i>
                Amount
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#B9D8EB]/30">
          {% for line in expenditure.outflow_lines.all %}
          <tr
            class="line-item-row hover:bg-[#E2F1F9]/50 transition-colors duration-200"
          >
            <td class="px-6 py-4">
              <div class="flex items-center gap-3">
                <div
                  class="w-8 h-8 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center text-white text-sm font-bold"
                >
                  {{ forloop.counter }}
                </div>
                <span class="font-bold text-[#2C3E50] capitalize"
                  >{{ line.description }}</span
                >
              </div>
            </td>
            <td class="px-6 py-4">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#40657F]/20 text-[#40657F] border border-[#40657F]/30 capitalize"
              >
                <i class="fas fa-book mr-1"></i>
                {{ line.account.name }}
              </span>
            </td>
            <td class="px-6 py-4 text-center">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30"
              >
                <i class="fas fa-hashtag mr-1"></i>
                {{ line.account.code }}
              </span>
            </td>
            <td class="px-6 py-4 text-right">
              <div class="flex items-center justify-end gap-2">
                <i class="fas fa-coins text-[#74C69D]"></i>
                <span class="font-bold text-[#74C69D] text-xl font-display">
                  MWK {{ line.amount | intcomma }}
                </span>
              </div>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="4" class="text-center py-12">
              <div class="flex flex-col items-center gap-4">
                <div
                  class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
                >
                  <i class="fas fa-list text-[#B9D8EB] text-2xl"></i>
                </div>
                <div>
                  <h4 class="text-lg font-bold text-[#2C3E50] mb-2">
                    No Line Items Found
                  </h4>
                  <p class="text-[#40657F]">
                    This expenditure has no detailed line items.
                  </p>
                </div>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: breadcrumbSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  .header-info-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: headerInfoSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Content Animations */
  .summary-card-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: summaryCardFadeIn 0.8s ease-out 1s forwards;
  }

  .detail-item {
    opacity: 0;
    transform: translateX(-20px);
    animation: detailItemSlideIn 0.4s ease-out forwards;
  }

  .detail-item:nth-child(1) {
    animation-delay: 1.2s;
  }
  .detail-item:nth-child(2) {
    animation-delay: 1.3s;
  }
  .detail-item:nth-child(3) {
    animation-delay: 1.4s;
  }
  .detail-item:nth-child(4) {
    animation-delay: 1.5s;
  }

  /* Table Section Animations */
  .table-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: tableSectionFadeIn 0.8s ease-out 1.6s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  .table-fade-in {
    opacity: 0;
    animation: tableFadeIn 0.8s ease-out 1.8s forwards;
  }

  .line-item-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: lineItemRowSlideIn 0.4s ease-out forwards;
  }

  .line-item-row:nth-child(1) {
    animation-delay: 2s;
  }
  .line-item-row:nth-child(2) {
    animation-delay: 2.1s;
  }
  .line-item-row:nth-child(3) {
    animation-delay: 2.2s;
  }
  .line-item-row:nth-child(4) {
    animation-delay: 2.3s;
  }
  .line-item-row:nth-child(5) {
    animation-delay: 2.4s;
  }

  /* Keyframe Definitions */
  @keyframes breadcrumbSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes headerInfoSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes summaryCardFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes detailItemSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes tableSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes tableIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(-3deg);
    }
  }

  @keyframes tableFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes lineItemRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .detail-item {
      animation-delay: 1s;
    }

    .detail-item:nth-child(n) {
      animation-delay: calc(1s + 0.1s * var(--item-index, 1));
    }

    .line-item-row {
      animation-delay: 1.5s;
    }

    .line-item-row:nth-child(n) {
      animation-delay: calc(1.5s + 0.1s * var(--row-index, 1));
    }
  }
</style>

{% endblock %}
