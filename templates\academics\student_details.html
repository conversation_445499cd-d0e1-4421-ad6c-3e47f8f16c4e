{% extends 'academics/base.html' %} {% load static %}
<!--  -->
{% block title %}{{student.name}} | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl"
        >
          <i class="fas fa-user-graduate text-white text-lg sm:text-xl"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight"
          >
            {{student.name}}
          </h1>
          <p class="text-[#40657F] text-base sm:text-lg font-medium mt-1">
            Student academic profile and performance
          </p>
          <div class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2"></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3">
        <a
          href="{% url 'academics:report_card_selection' student.student_id %}"
          class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-file-pdf mr-2 group-hover:rotate-12 transition-transform duration-300"
          ></i>
          Report Card
        </a>
        <button
          class="bg-[#40657F] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#2C3E50] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-edit mr-2 group-hover:translate-y-[-2px] transition-transform duration-300"
          ></i>
          Edit Profile
        </button>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200"
    >
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'academics:levels' %}"
        class="text-[#40657F] hover:text-[#7AB2D3] transition-colors"
        >Classes</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'academics:level_details' student.level.slug %}"
        class="text-[#40657F] hover:text-[#7AB2D3] transition-colors"
        >{{student.level.level_name}}</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">{{student.name}}</span>
    </nav>
  </div>

  <!-- Enhanced Search Section -->
  <div class="card-modern p-6">
    <div class="flex items-center gap-3 mb-6">
      <div
        class="w-8 h-8 sm:w-10 sm:h-10 bg-[#7AB2D3] rounded-lg sm:rounded-xl flex items-center justify-center shadow-md"
      >
        <i class="fas fa-search text-white text-xs sm:text-sm"></i>
      </div>
      <div>
        <h3 class="text-lg sm:text-xl font-bold text-[#2C3E50] font-display">
          Student Search
        </h3>
        <p class="text-[#40657F] text-sm">
          Find students by ID, name, or class level
        </p>
      </div>
    </div>

    <form
      method="GET"
      name="q"
      action="{% url 'academics:students' %}"
      class="flex flex-col sm:flex-row gap-4"
    >
      <div class="flex-1 relative">
        <input
          class="w-full pl-12 pr-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm bg-white"
          type="text"
          name="q"
          value="{{ request.GET.q }}"
          placeholder="Search by Student ID, name, or class level..."
        />
        <i
          class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#B9D8EB] text-sm"
        ></i>
      </div>
      <div class="flex gap-3">
        <button
          class="bg-[#7AB2D3] text-white font-semibold py-3 px-8 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
          type="submit"
        >
          <i
            class="fas fa-search mr-2 group-hover:scale-110 transition-transform duration-300"
          ></i>
          Search Students
        </button>
        {% if request.GET.q %}
        <a
          href="{% url 'academics:student_details' student.slug %}"
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#E2F1F9] focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern"
        >
          <i class="fas fa-times mr-2"></i>
          Clear
        </a>
        {% endif %}
      </div>
    </form>
  </div>

  {% if messages %}
  <!-- Messages -->
  {% for message in messages %}
  <div class="card-modern p-4 border-l-4 border-[#F28C8C] bg-[#F28C8C]/10">
    <div class="flex items-center gap-3">
      <i class="fas fa-exclamation-triangle text-[#F28C8C]"></i>
      <p class="text-[#F28C8C] font-medium">{{ message }}</p>
    </div>
  </div>
  {% endfor %} {% endif %}

  <!-- Student Profile Section -->
  <div class="card-modern p-8">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-id-card text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Student Profile
        </h2>
        <p class="text-[#40657F] text-sm">
          Complete academic and personal information
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <!-- Student Information Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Student Photo -->
      <div class="lg:col-span-1">
        <div
          class="bg-gradient-to-br from-[#E2F1F9] to-[#B9D8EB] rounded-2xl p-6 text-center"
        >
          <div class="relative inline-block">
            <img
              width="200"
              height="200"
              src="{% static 'img/student1.jpg' %}"
              alt="{{student.name}}"
              class="rounded-2xl shadow-lg border-4 border-white object-cover"
            />
            <div
              class="absolute -bottom-2 -right-2 w-8 h-8 bg-[#74C69D] rounded-full flex items-center justify-center border-4 border-white"
            >
              <i class="fas fa-check text-white text-sm"></i>
            </div>
          </div>
          <h3 class="text-xl font-bold text-[#2C3E50] mt-4 font-display">
            {{student.name}}
          </h3>
          <p class="text-[#40657F] font-medium">
            Student ID: {{student.student_id}}
          </p>
        </div>
      </div>

      <!-- Student Details -->
      <div class="lg:col-span-2">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Personal Information -->
          <div class="space-y-4">
            <h4
              class="text-lg font-bold text-[#2C3E50] font-display mb-4 flex items-center gap-2"
            >
              <i class="fas fa-user text-[#7AB2D3]"></i>
              Personal Information
            </h4>

            <div class="space-y-3">
              <div
                class="flex items-center justify-between p-3 bg-[#E2F1F9]/50 rounded-lg"
              >
                <span class="text-[#40657F] font-medium">Full Name</span>
                <span class="text-[#2C3E50] font-bold">{{student.name}}</span>
              </div>

              <div
                class="flex items-center justify-between p-3 bg-white rounded-lg border border-[#B9D8EB]/30"
              >
                <span class="text-[#40657F] font-medium">Gender</span>
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold {% if student.gender == 'Male' %}bg-[#7AB2D3]/20 text-[#7AB2D3]{% else %}bg-[#F28C8C]/20 text-[#F28C8C]{% endif %}"
                >
                  <i
                    class="fas {% if student.gender == 'Male' %}fa-mars{% else %}fa-venus{% endif %} mr-1"
                  ></i>
                  {{student.gender}}
                </span>
              </div>

              <div
                class="flex items-center justify-between p-3 bg-[#E2F1F9]/50 rounded-lg"
              >
                <span class="text-[#40657F] font-medium">Student ID</span>
                <span class="text-[#2C3E50] font-bold font-mono"
                  >{{student.student_id}}</span
                >
              </div>
            </div>
          </div>

          <!-- Academic Information -->
          <div class="space-y-4">
            <h4
              class="text-lg font-bold text-[#2C3E50] font-display mb-4 flex items-center gap-2"
            >
              <i class="fas fa-graduation-cap text-[#74C69D]"></i>
              Academic Information
            </h4>

            <div class="space-y-3">
              <div
                class="flex items-center justify-between p-3 bg-[#E2F1F9]/50 rounded-lg"
              >
                <span class="text-[#40657F] font-medium">Class Level</span>
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30"
                >
                  <i class="fas fa-chalkboard mr-1"></i>
                  {{student.level.level_name}}
                </span>
              </div>

              <div
                class="flex items-center justify-between p-3 bg-white rounded-lg border border-[#B9D8EB]/30"
              >
                <span class="text-[#40657F] font-medium">Class Position</span>
                <span class="text-[#2C3E50] font-bold"
                  >#{{student.position}}</span
                >
              </div>

              <div
                class="flex items-center justify-between p-3 bg-[#E2F1F9]/50 rounded-lg"
              >
                <span class="text-[#40657F] font-medium">Academic Status</span>
                {% if student.has_passed %}
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30"
                >
                  <i class="fas fa-check-circle mr-1"></i>
                  Passed
                </span>
                {% else %}
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30"
                >
                  <i class="fas fa-times-circle mr-1"></i>
                  Failed
                </span>
                {% endif %}
              </div>

              <div
                class="flex items-center justify-between p-3 bg-white rounded-lg border border-[#B9D8EB]/30"
              >
                <span class="text-[#40657F] font-medium">Average Marks</span>
                <span class="text-[#2C3E50] font-bold text-lg"
                  >{{student.average_marks}}%</span
                >
              </div>

              <div
                class="flex items-center justify-between p-3 bg-[#E2F1F9]/50 rounded-lg"
              >
                <span class="text-[#40657F] font-medium">Class Size</span>
                <span class="text-[#2C3E50] font-bold"
                  >{{student_count}} students</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Academic Performance Section -->
  <div class="card-modern p-8">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-chart-bar text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Academic Performance
        </h2>
        <p class="text-[#40657F] text-sm">
          Subject-wise grades and teacher information
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    {% if enrollments %}
    <div class="overflow-x-auto">
      <div class="table-modern">
        <table class="min-w-full">
          <thead>
            <tr class="bg-[#E2F1F9] border-b border-[#B9D8EB]">
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Subject
              </th>
              <th
                class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm"
              >
                Marks
              </th>
              <th
                class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm"
              >
                Grade
              </th>
              <th
                class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm"
              >
                Position
              </th>
              <th
                class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm"
              >
                Status
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Teacher
              </th>
              <th
                class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm"
              >
                Contact
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#B9D8EB]">
            {% for enroll in enrollments %}
            <tr class="hover:bg-[#E2F1F9]/50 transition-all duration-200 group">
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div
                    class="w-10 h-10 bg-[#7AB2D3] rounded-lg flex items-center justify-center shadow-sm"
                  >
                    <i class="fas fa-book text-white text-sm"></i>
                  </div>
                  <div>
                    <div class="font-bold text-[#2C3E50]">
                      {{enroll.subject}}
                    </div>
                    <div class="text-xs text-[#40657F]">Subject</div>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6 text-center">
                <span class="text-2xl font-bold text-[#40657F]"
                  >{{enroll.final_grade}}</span
                >
                <span class="text-sm text-[#40657F]">%</span>
              </td>
              <td class="py-4 px-6 text-center">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold {% if enroll.grade == 'A' %}bg-[#74C69D]/20 text-[#74C69D] {% elif enroll.grade == 'B' %}bg-[#7AB2D3]/20 text-[#7AB2D3] {% elif enroll.grade == 'C' %}bg-[#40657F]/20 text-[#40657F] {% elif enroll.grade == 'D' %}bg-[#F28C8C]/20 text-[#F28C8C] {% else %}bg-gray-200 text-gray-600{% endif %}"
                >
                  {{enroll.grade}}
                </span>
              </td>
              <td class="py-4 px-6 text-center">
                <div class="text-lg font-bold text-[#2C3E50]">
                  #{{enroll.position}}
                </div>
                <div class="text-xs text-[#40657F]">
                  of {{enroll.student_count}}
                </div>
              </td>
              <td class="py-4 px-6 text-center">
                {% if enroll.has_passed %}
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30"
                >
                  <i class="fas fa-check mr-1"></i>
                  Pass
                </span>
                {% else %}
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30"
                >
                  <i class="fas fa-times mr-1"></i>
                  Fail
                </span>
                {% endif %}
              </td>
              <td class="py-4 px-6">
                {% for teacher in teachers %}
                <!--  -->
                {% if teacher.subject == enroll.subject %}
                <div class="flex items-center gap-3">
                  <div
                    class="w-8 h-8 bg-[#40657F] rounded-full flex items-center justify-center shadow-sm"
                  >
                    <span class="text-white text-xs font-bold"
                      >{{teacher.teacher.first_name|first}}{{teacher.teacher.last_name|first}}</span
                    >
                  </div>
                  <div>
                    <div class="font-medium text-[#2C3E50]">
                      {{teacher.teacher.first_name}}
                      {{teacher.teacher.last_name}}
                    </div>
                    <div class="text-xs text-[#40657F]">Subject Teacher</div>
                  </div>
                </div>
                {% endif %} {% endfor %}
              </td>
              <td class="py-4 px-6 text-center">
                {% for teacher in teachers %}
                <!--  -->
                {% if teacher.subject == enroll.subject %}
                <a
                  href="tel:{{teacher.teacher.phone_number}}"
                  class="inline-flex items-center gap-2 px-3 py-2 bg-[#7AB2D3] text-white text-xs font-medium rounded-lg hover:bg-[#40657F] transition-all duration-300 shadow-sm hover:shadow-md"
                >
                  <i class="fas fa-phone"></i>
                  {{teacher.teacher.phone_number}}
                </a>
                {% endif %} {% endfor %}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-16">
      <div class="flex flex-col items-center gap-6">
        <div
          class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center"
        >
          <i class="fas fa-chart-bar text-gray-400 text-3xl"></i>
        </div>
        <div>
          <h3 class="font-display font-bold text-2xl text-gray-800 mb-2">
            No Academic Records
          </h3>
          <p class="text-gray-600 text-lg">
            No subject enrollments found for this student.
          </p>
          <p class="text-gray-500 text-sm mt-2">
            Academic performance data will appear here once available.
          </p>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</section>
{% endblock %}
