"""
Space/Science Easter Egg - Random Space & Science Quote Generator
A delightful surprise for curious minds exploring the cosmos
"""

import random

SPACE_QUOTES = [
    # Space Exploration
    {
        "quote": "That's one small step for man, one giant leap for mankind.",
        "author": "<PERSON>",
        "context": "First words on the Moon, July 20, 1969",
        "theme": "Historic Achievement",
        "category": "Space Exploration"
    },
    {
        "quote": "The Earth is the cradle of humanity, but mankind cannot stay in the cradle forever.",
        "author": "<PERSON>",
        "context": "Father of Astronautics",
        "theme": "Human Destiny",
        "category": "Space Philosophy"
    },
    {
        "quote": "Space exploration is a force of nature unto itself that no other force in society can rival.",
        "author": "<PERSON>",
        "context": "Astrophysicist & Science Communicator",
        "theme": "Exploration Drive",
        "category": "Modern Science"
    },
    {
        "quote": "The universe is not only stranger than we imagine, it is stranger than we can imagine.",
        "author": "J.B.S. Haldane",
        "context": "British Scientist",
        "theme": "Cosmic Mystery",
        "category": "Scientific Wonder"
    },
    {
        "quote": "We are all made of star stuff.",
        "author": "<PERSON> Sagan",
        "context": "Cosmos",
        "theme": "Cosmic Connection",
        "category": "Astrophysics"
    },
    {
        "quote": "The cosmos is within us. We are made of star-stuff. We are a way for the universe to know itself.",
        "author": "Carl Sagan",
        "context": "Cosmos",
        "theme": "Self-Awareness",
        "category": "Philosophy of Science"
    },
    {
        "quote": "Space is big. You just won't believe how vastly, hugely, mind-bogglingly big it is.",
        "author": "Douglas Adams",
        "context": "The Hitchhiker's Guide to the Galaxy",
        "theme": "Scale of Universe",
        "category": "Science Fiction"
    },
    {
        "quote": "The important thing is not to stop questioning. Curiosity has its own reason for existing.",
        "author": "Albert Einstein",
        "context": "Theoretical Physicist",
        "theme": "Scientific Curiosity",
        "category": "Physics"
    },
    {
        "quote": "Somewhere, something incredible is waiting to be known.",
        "author": "Carl Sagan",
        "context": "Contact",
        "theme": "Discovery",
        "category": "Scientific Wonder"
    },
    {
        "quote": "The nitrogen in our DNA, the calcium in our teeth, the iron in our blood, the carbon in our apple pies were made in the interiors of collapsing stars.",
        "author": "Carl Sagan",
        "context": "Cosmos",
        "theme": "Stellar Origins",
        "category": "Astrophysics"
    },
    {
        "quote": "I don't think the human race will survive the next thousand years, unless we spread into space.",
        "author": "Stephen Hawking",
        "context": "Theoretical Physicist",
        "theme": "Human Survival",
        "category": "Future of Humanity"
    },
    {
        "quote": "The Earth is a very small stage in a vast cosmic arena.",
        "author": "Carl Sagan",
        "context": "Pale Blue Dot",
        "theme": "Cosmic Perspective",
        "category": "Philosophy"
    },
    {
        "quote": "Science is not only a disciple of reason but also one of romance and passion.",
        "author": "Stephen Hawking",
        "context": "A Brief History of Time",
        "theme": "Love of Science",
        "category": "Scientific Method"
    },
    {
        "quote": "The universe is under no obligation to make sense to you.",
        "author": "Neil deGrasse Tyson",
        "context": "Astrophysicist",
        "theme": "Cosmic Humility",
        "category": "Scientific Reality"
    },
    {
        "quote": "We choose to go to the Moon not because it is easy, but because it is hard.",
        "author": "John F. Kennedy",
        "context": "Moon Speech, 1962",
        "theme": "Challenge & Achievement",
        "category": "Space Race"
    },
    {
        "quote": "The sky calls to us. If we do not destroy ourselves, we will one day venture to the stars.",
        "author": "Carl Sagan",
        "context": "Cosmos",
        "theme": "Future Exploration",
        "category": "Space Dreams"
    },
    {
        "quote": "In space, no one can hear you scream... but they can hear you wonder.",
        "author": "Anonymous",
        "context": "Modern Space Philosophy",
        "theme": "Wonder & Isolation",
        "category": "Space Philosophy"
    },
    {
        "quote": "The universe is not only queerer than we suppose, but queerer than we can suppose.",
        "author": "J.B.S. Haldane",
        "context": "Possible Worlds",
        "theme": "Infinite Mystery",
        "category": "Scientific Wonder"
    },
    {
        "quote": "Space exploration is the ultimate expression of human curiosity.",
        "author": "Mae Jemison",
        "context": "First African American Woman in Space",
        "theme": "Human Nature",
        "category": "Space Exploration"
    },
    {
        "quote": "The stars are not so far away as they seem.",
        "author": "Anonymous",
        "context": "Astronomical Inspiration",
        "theme": "Accessibility of Wonder",
        "category": "Inspiration"
    },
    {
        "quote": "Every atom in your body came from a star that exploded.",
        "author": "Lawrence Krauss",
        "context": "Theoretical Physicist",
        "theme": "Stellar Heritage",
        "category": "Cosmology"
    },
    {
        "quote": "The universe is a pretty big place. If it's just us, seems like an awful waste of space.",
        "author": "Carl Sagan",
        "context": "Contact",
        "theme": "Extraterrestrial Life",
        "category": "SETI"
    },
    {
        "quote": "Science is the poetry of reality.",
        "author": "Richard Dawkins",
        "context": "Evolutionary Biologist",
        "theme": "Beauty of Science",
        "category": "Scientific Wonder"
    },
    {
        "quote": "The most beautiful thing we can experience is the mysterious.",
        "author": "Albert Einstein",
        "context": "The World As I See It",
        "theme": "Mystery & Beauty",
        "category": "Scientific Philosophy"
    },
    {
        "quote": "We are the universe becoming aware of itself.",
        "author": "Carl Sagan",
        "context": "Cosmos",
        "theme": "Consciousness",
        "category": "Philosophy of Science"
    }
]

# Space and science related trigger words
SPACE_TRIGGER_WORDS = [
    'space',
    'nasa',
    'rocket',
    'astronaut',
    'galaxy',
    'universe',
    'mars',
    'moon',
    'earth',
    'planet',
    'solar system',
    'milky way',
    'black hole',
    'star',
    'constellation',
    'nebula',
    'comet',
    'asteroid',
    'meteor',
    'satellite',
    'spacecraft',
    'shuttle',
    'apollo',
    'hubble',
    'telescope',
    'observatory',
    'cosmos',
    'cosmic',
    'interstellar',
    'intergalactic',
    'orbit',
    'gravity',
    'weightless',
    'zero gravity',
    'space station',
    'iss',
    'international space station',
    'spacex',
    'blue origin',
    'virgin galactic',
    'science',
    'physics',
    'chemistry',
    'biology',
    'astronomy',
    'astrophysics',
    'cosmology',
    'quantum',
    'relativity',
    'einstein',
    'newton',
    'galileo',
    'copernicus',
    'kepler',
    'hawking',
    'sagan',
    'tyson',
    'neil armstrong',
    'buzz aldrin',
    'yuri gagarin',
    'john glenn',
    'sally ride',
    'mae jemison',
    'discovery',
    'exploration',
    'research',
    'experiment',
    'laboratory',
    'scientific method',
    'hypothesis',
    'theory',
    'law of physics',
    'speed of light',
    'big bang',
    'evolution',
    'dna',
    'atom',
    'molecule',
    'element',
    'periodic table',
    'electron',
    'proton',
    'neutron',
    'nuclear',
    'radiation',
    'energy',
    'matter',
    'antimatter',
    'dark matter',
    'dark energy',
    'wormhole',
    'time travel',
    'dimension',
    'parallel universe',
    'multiverse',
    'alien',
    'extraterrestrial',
    'ufo',
    'seti',
    'exoplanet',
    'habitable zone',
    'goldilocks zone',
    'life on mars',
    'europa',
    'titan',
    'enceladus',
    'curiosity rover',
    'perseverance',
    'opportunity',
    'spirit',
    'voyager',
    'pioneer',
    'cassini',
    'juno',
    'new horizons',
    'james webb',
    'kepler telescope',
    'spitzer',
    'chandra'
]

def get_random_space_quote():
    """Get a random space/science quote"""
    return random.choice(SPACE_QUOTES)

def is_space_search(search_term):
    """Check if search term contains space/science trigger words"""
    if not search_term:
        return False
    
    search_lower = search_term.lower().strip()
    
    # Check for exact matches and partial matches
    for trigger in SPACE_TRIGGER_WORDS:
        if trigger in search_lower:
            return True
    
    return False

def get_space_quote_by_category(category=None):
    """Get quotes filtered by category"""
    if not category:
        return get_random_space_quote()
    
    category_quotes = [q for q in SPACE_QUOTES if category.lower() in q['category'].lower()]
    return random.choice(category_quotes) if category_quotes else get_random_space_quote()

def get_space_quotes_by_author(author):
    """Get all space quotes by a specific author"""
    return [q for q in SPACE_QUOTES if author.lower() in q['author'].lower()]
