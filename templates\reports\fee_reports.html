{% extends 'base.html' %} {% load static %} {% load humanize %}
<!--  -->
{% block title %}Fee Payment Reports | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-chart-bar text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Fee Payment Reports
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Generate comprehensive monthly and term fee reports
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <a
        href="{% url 'finances:fee_term_report' %}"
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group button-slide-in"
      >
        <i
          class="fas fa-calendar-alt text-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        ></i>
        <span>Term Fee Report</span>
      </a>
    </div>
  </div>

  <!-- Monthly Reports Grid -->
  <div class="card-modern p-8 monthly-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg section-icon-float"
      >
        <i class="fas fa-calendar text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Monthly Reports
        </h3>
        <p class="text-[#40657F] text-sm">
          Generate Excel reports for each month
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 w-full"
    >
      {% for _, month in months.items %}
      <a
        href="{% url 'finances:fee_monthly_report' month %}"
        class="month-card group relative overflow-hidden bg-gradient-to-br from-white via-[#F7FAFC] to-[#E2F1F9] border-l-4 border-[#7AB2D3] hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 p-6 rounded-2xl"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 rounded-full -translate-y-8 translate-x-8 group-hover:scale-125 transition-transform duration-700"
        ></div>

        <div class="flex items-center justify-between mb-4 relative z-10">
          <div
            class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center shadow-md group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          >
            <i class="fas fa-file-excel text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-[#74C69D] rounded-full animate-pulse"></div>
            <span class="text-xs text-[#74C69D] font-semibold">Available</span>
          </div>
        </div>

        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#7AB2D3] transition-colors duration-300"
          >
            {{month}}
          </h3>

          <p
            class="text-sm text-[#40657F] bg-[#E2F1F9] rounded-lg py-3 px-4 font-medium mb-4"
          >
            Generate {{month}} fee payment Excel report
          </p>

          <div class="flex items-center justify-between">
            <span class="text-xs text-[#40657F] font-medium"
              >Click to download</span
            >
            <div
              class="w-8 h-8 bg-[#7AB2D3]/20 rounded-full flex items-center justify-center group-hover:bg-[#7AB2D3] transition-colors duration-300"
            >
              <i
                class="fas fa-arrow-right text-[#7AB2D3] group-hover:text-white group-hover:translate-x-1 transition-all duration-300"
              ></i>
            </div>
          </div>
        </div>
      </a>
      {% endfor %}
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .button-slide-in {
    opacity: 0;
    transform: translateX(50px);
    animation: buttonSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Monthly Section Animations */
  .monthly-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: monthlySectionFadeIn 0.8s ease-out 1s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 4s ease-in-out infinite;
  }

  /* Month Card Animations */
  .month-card {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    animation: monthCardSlideUp 0.6s ease-out forwards;
  }

  .month-card:nth-child(1) {
    animation-delay: 1.2s;
  }
  .month-card:nth-child(2) {
    animation-delay: 1.3s;
  }
  .month-card:nth-child(3) {
    animation-delay: 1.4s;
  }
  .month-card:nth-child(4) {
    animation-delay: 1.5s;
  }
  .month-card:nth-child(5) {
    animation-delay: 1.6s;
  }
  .month-card:nth-child(6) {
    animation-delay: 1.7s;
  }
  .month-card:nth-child(7) {
    animation-delay: 1.8s;
  }
  .month-card:nth-child(8) {
    animation-delay: 1.9s;
  }
  .month-card:nth-child(9) {
    animation-delay: 2s;
  }
  .month-card:nth-child(10) {
    animation-delay: 2.1s;
  }
  .month-card:nth-child(11) {
    animation-delay: 2.2s;
  }
  .month-card:nth-child(12) {
    animation-delay: 2.3s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes buttonSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes monthlySectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes sectionIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(3deg);
    }
  }

  @keyframes monthCardSlideUp {
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* Hover Enhancements */
  .month-card:hover {
    transform: translateY(-8px) scale(1.02);
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .month-card {
      animation-delay: 0.8s;
    }

    .month-card:nth-child(n) {
      animation-delay: calc(0.8s + 0.1s * var(--card-index, 1));
    }
  }
</style>

{% endblock %}
