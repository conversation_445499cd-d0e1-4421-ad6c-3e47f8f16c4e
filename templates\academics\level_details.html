{% extends 'academics/base.html' %} {% load static %}
<!--  -->
{% block title %}{{level}} | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl"
        >
          <i class="fas fa-graduation-cap text-white text-lg sm:text-xl"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight"
          >
            {{ level }}
          </h1>
          <p class="text-[#40657F] text-base sm:text-lg font-medium mt-1">
            Class overview and student management
          </p>
          <div class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2"></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3">
        <button
          class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-user-plus mr-2 group-hover:rotate-12 transition-transform duration-300"
          ></i>
          Add Student
        </button>
        <button
          class="bg-[#40657F] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#2C3E50] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-download mr-2 group-hover:translate-y-[-2px] transition-transform duration-300"
          ></i>
          Export List
        </button>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200"
    >
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'academics:levels' %}"
        class="text-[#40657F] hover:text-[#7AB2D3] transition-colors"
        >Classes</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">{{ level }}</span>
    </nav>
  </div>

  <!-- Class Navigation -->
  <div class="card-modern p-6">
    <div class="flex items-center gap-3 mb-6">
      <div
        class="w-8 h-8 sm:w-10 sm:h-10 bg-[#40657F] rounded-lg sm:rounded-xl flex items-center justify-center shadow-md"
      >
        <i class="fas fa-layer-group text-white text-xs sm:text-sm"></i>
      </div>
      <div>
        <h3 class="text-lg sm:text-xl font-bold text-[#2C3E50] font-display">
          All Classes
        </h3>
        <p class="text-[#40657F] text-sm">
          Quick navigation between class levels
        </p>
      </div>
    </div>

    <div
      class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4"
    >
      {% for class_level in levels %}
      <a
        href="{% url 'academics:level_details' class_level.slug %}"
        class="group relative overflow-hidden bg-white border-2 border-[#B9D8EB] rounded-xl p-4 hover:border-[#7AB2D3] hover:bg-[#E2F1F9] hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer text-center {% if class_level.level_name == level.level_name %}border-[#7AB2D3] bg-[#E2F1F9]{% endif %}"
      >
        <!-- Class Icon -->
        <div class="flex flex-col items-center gap-2 relative z-10">
          <div
            class="w-8 h-8 sm:w-10 sm:h-10 bg-[#7AB2D3] rounded-lg flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300 {% if class_level.level_name == level.level_name %}bg-[#40657F] scale-110{% endif %}"
          >
            <i class="fas fa-users text-white text-xs sm:text-sm"></i>
          </div>
          <div
            class="text-xs sm:text-sm font-semibold text-[#2C3E50] group-hover:text-[#7AB2D3] transition-colors duration-300 {% if class_level.level_name == level.level_name %}text-[#7AB2D3]{% endif %}"
          >
            {{ class_level.level_name }}
          </div>
        </div>

        <!-- Active Indicator -->
        {% if class_level.level_name == level.level_name %}
        <div
          class="absolute bottom-0 left-0 right-0 h-1 bg-[#7AB2D3] rounded-b-xl"
        ></div>
        {% endif %}
      </a>
      {% endfor %}
    </div>
  </div>

  <!-- Students Table -->
  <div class="card-modern p-6">
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-3">
        <div
          class="w-8 h-8 sm:w-10 sm:h-10 bg-[#7AB2D3] rounded-lg sm:rounded-xl flex items-center justify-center shadow-md"
        >
          <i class="fas fa-users text-white text-xs sm:text-sm"></i>
        </div>
        <div>
          <h3 class="text-lg sm:text-xl font-bold text-[#2C3E50] font-display">
            Class Students
          </h3>
          <p class="text-[#40657F] text-sm">
            {{ students|length }} student{{ students|length|pluralize }}
            enrolled
          </p>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="flex items-center gap-3">
        <div class="relative">
          <input
            type="text"
            placeholder="Search students..."
            class="pl-10 pr-4 py-2 border border-[#B9D8EB] rounded-lg focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm"
          />
          <i
            class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-[#B9D8EB] text-sm"
          ></i>
        </div>
      </div>
    </div>

    {% if students %}
    <div class="overflow-x-auto">
      <div class="table-modern">
        <table class="min-w-full">
          <thead>
            <tr class="bg-[#E2F1F9] border-b border-[#B9D8EB]">
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Student ID
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Student Name
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Level
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Education Stage
              </th>
              <th
                class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#B9D8EB]">
            {% for student in students %}
            <tr class="hover:bg-[#E2F1F9]/50 transition-all duration-200 group">
              <td class="py-4 px-6">
                <a
                  href="{% url 'academics:student_details' student.slug %}"
                  class="inline-flex items-center gap-2 text-[#7AB2D3] hover:text-[#40657F] font-semibold transition-colors duration-300"
                >
                  <div
                    class="w-8 h-8 bg-[#7AB2D3] rounded-lg flex items-center justify-center shadow-sm"
                  >
                    <span class="text-white text-xs font-bold"
                      >{{ student.student_id|slice:":2" }}</span
                    >
                  </div>
                  {{ student.student_id }}
                </a>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-3">
                  <div
                    class="w-10 h-10 bg-[#40657F] rounded-full flex items-center justify-center shadow-sm"
                  >
                    <span class="text-white text-sm font-bold"
                      >{{ student.name|first|upper }}</span
                    >
                  </div>
                  <div>
                    <div class="font-medium text-[#2C3E50] capitalize">
                      {{ student.name }}
                    </div>
                    <div class="text-xs text-[#40657F]">Student</div>
                  </div>
                </div>
              </td>
              <td class="py-4 px-6">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#E2F1F9] text-[#40657F]"
                >
                  {{ student.level.level_name }}
                </span>
              </td>
              <td class="py-4 px-6">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#B9D8EB] text-[#2C3E50]"
                >
                  {{ student.level.education_stage }}
                </span>
              </td>
              <td class="py-4 px-6">
                <div class="flex items-center gap-2">
                  <a
                    href="{% url 'academics:student_details' student.slug %}"
                    class="inline-flex items-center gap-2 px-3 py-2 bg-[#7AB2D3] text-white text-xs font-medium rounded-lg hover:bg-[#40657F] transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    <i class="fas fa-eye"></i>
                    View
                  </a>
                  <button
                    class="inline-flex items-center gap-2 px-3 py-2 bg-[#40657F] text-white text-xs font-medium rounded-lg hover:bg-[#2C3E50] transition-all duration-300 shadow-sm hover:shadow-md"
                  >
                    <i class="fas fa-edit"></i>
                    Edit
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-16">
      <div class="flex flex-col items-center gap-6">
        <div
          class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center"
        >
          <i class="fas fa-user-graduate text-gray-400 text-3xl"></i>
        </div>
        <div>
          <h3 class="font-display font-bold text-2xl text-gray-800 mb-2">
            No Students Found
          </h3>
          <p class="text-gray-600 text-lg">
            This class doesn't have any enrolled students yet.
          </p>
          <p class="text-gray-500 text-sm mt-2">
            Add students to get started with class management.
          </p>
        </div>
        <button
          class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg"
        >
          <i class="fas fa-user-plus mr-2"></i>
          Add First Student
        </button>
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Class Statistics -->
  {% if students %}
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
    <div class="card-modern p-6 text-center border-l-4 border-[#7AB2D3]">
      <div
        class="w-12 h-12 bg-[#7AB2D3] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-users text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Total Students
      </h3>
      <p class="text-3xl font-bold text-[#7AB2D3]">{{ students|length }}</p>
      <p class="text-[#40657F] text-sm">Enrolled in {{ level }}</p>
    </div>

    <div class="card-modern p-6 text-center border-l-4 border-[#74C69D]">
      <div
        class="w-12 h-12 bg-[#74C69D] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-user-check text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Active Students
      </h3>
      <p class="text-3xl font-bold text-[#74C69D]">{{ students|length }}</p>
      <p class="text-[#40657F] text-sm">Currently enrolled</p>
    </div>

    <div class="card-modern p-6 text-center border-l-4 border-[#40657F]">
      <div
        class="w-12 h-12 bg-[#40657F] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-graduation-cap text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Education Stage
      </h3>
      <p class="text-lg font-bold text-[#40657F]">
        {{ students.0.level.education_stage|default:"N/A" }}
      </p>
      <p class="text-[#40657F] text-sm">Current stage</p>
    </div>

    <div class="card-modern p-6 text-center border-l-4 border-[#F28C8C]">
      <div
        class="w-12 h-12 bg-[#F28C8C] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md"
      >
        <i class="fas fa-chart-line text-white text-lg"></i>
      </div>
      <h3 class="font-display font-bold text-lg text-[#2C3E50] mb-1">
        Class Capacity
      </h3>
      <p class="text-lg font-bold text-[#F28C8C]">{{ students|length }}/30</p>
      <p class="text-[#40657F] text-sm">Students enrolled</p>
    </div>
  </div>
  {% endif %}
</section>
{% endblock %}
