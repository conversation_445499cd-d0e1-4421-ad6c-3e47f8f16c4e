"""
Philosophical Easter Egg View
A delightful surprise for users who search for philosophical terms
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
import json

from core.philosophical_quotes import get_random_quote, is_philosophical_search, get_quote_by_theme, get_quotes_by_author


@login_required(login_url="accounts:login")
def philosophical_easter_egg(request):
    """
    Display a random philosophical quote when triggered by search terms
    """
    # Get the search term that triggered this easter egg
    search_term = request.GET.get('search', '')
    quote_data = get_random_quote()
    
    # Add some context about how they got here
    trigger_context = {
        'search_term': search_term,
        'message': f'Your search for "{search_term}" has awakened the philosophical spirit within the system...'
    }
    
    context = {
        'quote': quote_data,
        'trigger': trigger_context,
        'page_title': 'A Moment of Philosophical Reflection'
    }
    
    return render(request, 'core/philosophical_easter_egg.html', context)


@login_required(login_url="accounts:login")
@csrf_exempt
@require_http_methods(["POST"])
def get_new_quote(request):
    """
    AJAX endpoint to get a new random quote
    """
    try:
        data = json.loads(request.body)
        theme = data.get('theme', None)
        author = data.get('author', None)
        
        if author:
            quotes = get_quotes_by_author(author)
            if quotes:
                import random
                quote_data = random.choice(quotes)
            else:
                quote_data = get_random_quote()
        elif theme:
            quote_data = get_quote_by_theme(theme)
        else:
            quote_data = get_random_quote()
        
        return JsonResponse({
            'success': True,
            'quote': quote_data
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def check_philosophical_search(search_term):
    """
    Helper function to check if a search term should trigger the easter egg
    This can be imported and used in other views
    """
    return is_philosophical_search(search_term)
