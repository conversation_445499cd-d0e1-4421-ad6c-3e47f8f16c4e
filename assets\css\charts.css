/* Chart and Data Visualization Styles */

/* Chart Container Styles */
.chart-container {
    position: relative;
    width: 100%;
    height: 400px;
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.chart-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
}

/* Chart Title Styles */
.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    text-align: center;
}

.chart-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
    margin-bottom: 1.5rem;
}

/* Dashboard Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #40657F;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stat-card-header {
    display: flex;
    items-center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.stat-card-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-card-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    line-height: 1;
}

.stat-card-change {
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
}

.stat-card-change.positive {
    color: #10b981;
}

.stat-card-change.negative {
    color: #ef4444;
}

.stat-card-change.neutral {
    color: #6b7280;
}

/* Icon Background Colors */
.icon-blue {
    background-color: #dbeafe;
    color: #3b82f6;
}

.icon-green {
    background-color: #d1fae5;
    color: #10b981;
}

.icon-yellow {
    background-color: #fef3c7;
    color: #f59e0b;
}

.icon-red {
    background-color: #fee2e2;
    color: #ef4444;
}

.icon-purple {
    background-color: #ede9fe;
    color: #8b5cf6;
}

/* Chart Grid Layout */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Responsive Chart Styles */
@media (max-width: 768px) {
    .chart-container {
        height: 250px;
        padding: 0.75rem;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-card-value {
        font-size: 1.5rem;
    }
    
    .chart-title {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .chart-container {
        height: 200px;
        padding: 0.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card-icon {
        width: 2rem;
        height: 2rem;
        font-size: 1rem;
    }
    
    .stat-card-value {
        font-size: 1.25rem;
    }
}

/* Chart Loading States */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6b7280;
}

.chart-loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #40657F;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chart Error States */
.chart-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #ef4444;
    text-align: center;
}

.chart-error-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.chart-error-message {
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.chart-retry-button {
    background-color: #40657F;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.chart-retry-button:hover {
    background-color: #2c3e50;
}

/* Chart Legend Customization */
.chart-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #4b5563;
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 0.5rem;
}

/* Data Table Styles for Charts */
.chart-data-table {
    width: 100%;
    margin-top: 1rem;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.chart-data-table th,
.chart-data-table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.chart-data-table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.chart-data-table tr:hover {
    background-color: #f9fafb;
}

/* Chart Export Controls */
.chart-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.chart-export-buttons {
    display: flex;
    gap: 0.5rem;
}

.chart-export-btn {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-export-btn:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
}

.chart-export-btn:active {
    background-color: #d1d5db;
}

/* Chart Filters */
.chart-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.chart-filter-select {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    background-color: white;
    color: #374151;
}

.chart-filter-select:focus {
    outline: none;
    border-color: #40657F;
    box-shadow: 0 0 0 1px #40657F;
}

/* Tooltip Styles */
.chart-tooltip {
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    pointer-events: none;
    z-index: 1000;
}

/* Print Styles */
@media print {
    .chart-container {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }
    
    .chart-controls,
    .chart-export-buttons {
        display: none;
    }
    
    .stat-card {
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .chart-container,
    .stat-card {
        background-color: #1f2937;
        color: #f9fafb;
        border-color: #374151;
    }
    
    .chart-title {
        color: #f9fafb;
    }
    
    .chart-subtitle,
    .stat-card-title {
        color: #9ca3af;
    }
    
    .stat-card-value {
        color: #f9fafb;
    }
    
    .chart-data-table th {
        background-color: #374151;
        color: #f9fafb;
    }
    
    .chart-data-table td {
        border-color: #4b5563;
    }
    
    .chart-filter-select {
        background-color: #374151;
        color: #f9fafb;
        border-color: #4b5563;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .chart-container,
    .stat-card {
        border: 2px solid #000;
    }
    
    .chart-title,
    .stat-card-value {
        color: #000;
        font-weight: 700;
    }
}

/* Animation for chart updates */
.chart-update-animation {
    transition: all 0.3s ease-in-out;
}

/* Accessibility improvements */
.chart-container:focus-within {
    outline: 2px solid #40657F;
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
