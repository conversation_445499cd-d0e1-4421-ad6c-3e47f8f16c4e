from datetime import timedelta
from dateutil.relativedelta import relativedelta


def calculate_monthly_fee(fee_category, current_term):
    # Local import to avoid circular dependency
    fees = []

    start_date = current_term.start_date
    end_date = current_term.end_date
    monthly_fee = fee_category.amount

    current = start_date.replace(day=1)

    while current <= end_date:
        next_month = current + relativedelta(months=1)
        last_day = (next_month - timedelta(days=1)).day

        if current.month == start_date.month:
            days_in_month = last_day - start_date.day + 1
        elif current.month == end_date.month:
            days_in_month = end_date.day
        else:
            days_in_month = last_day

        if days_in_month <= 5:
            current = next_month
            continue

        if current.month == start_date.month or current.month == end_date.month:
            if days_in_month > (last_day // 2):
                fee = monthly_fee
            else:
                fee = 15000
        else:
            fee = monthly_fee

        fees.append({
            "month": current,
            "days": days_in_month,
            "fee": fee,
        })

        current += relativedelta(months=1)

    return fees


def calculate_one_month_fee(fee_account):
    fees = 0
    start_date = fee_account.term.start_date
    end_date = fee_account.term.end_date
    current_month = fee_account.month
    last_day = (current_month + relativedelta(months=1) -
                timedelta(days=1)).day
    days_in_month = last_day - current_month.day + 1

    if current_month.month == start_date.month:
        days_in_month = last_day - start_date.day + 1
    elif current_month.month == end_date.month:
        days_in_month = end_date.day
    else:
        days_in_month = last_day

    if current_month.month == start_date.month or end_date.month:
        if days_in_month > (last_day // 2):
            fees = fee_account.category.amount
        else:
            fees = 15000
    else:
        fees = fee_account.category.amount

    return fees


def re_work_on_expenditurers():
    pass
