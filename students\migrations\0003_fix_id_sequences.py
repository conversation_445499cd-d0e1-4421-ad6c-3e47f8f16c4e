# Generated manually to fix ID sequence issues for all students tables

from django.db import migrations


def fix_students_sequences(apps, schema_editor):
    """
    Fix ID sequence issues for all students tables that were renamed in migration 0002.
    """
    if schema_editor.connection.vendor != 'postgresql':
        return  # Only run on PostgreSQL

    # List of tables that were renamed and need sequence fixes
    tables_to_fix = [
        'students_academicyear',
        'students_level',
        'students_student',
        'students_term',
    ]

    with schema_editor.connection.cursor() as cursor:
        for table_name in tables_to_fix:
            try:
                # Check if table exists
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = '{table_name}'
                    );
                """)
                table_exists = cursor.fetchone()[0]

                if not table_exists:
                    continue  # Skip if table doesn't exist

                # Check if ID column is an identity column or already has a default
                cursor.execute(f"""
                    SELECT column_default, is_identity
                    FROM information_schema.columns
                    WHERE table_name = '{table_name}'
                    AND column_name = 'id';
                """)
                result = cursor.fetchone()

                if result:
                    column_default, is_identity = result
                    # Skip if it's already an identity column or has a sequence default
                    if is_identity == 'YES' or (column_default and 'nextval' in str(column_default)):
                        continue

                # Create sequence and set as default
                sequence_name = f"{table_name}_id_seq_new"
                cursor.execute(
                    f"CREATE SEQUENCE IF NOT EXISTS {sequence_name};")

                # Set sequence to start from a safe number (max existing ID + 1, or 1000 if table is empty)
                # Using 1000 as minimum to avoid conflicts in test databases
                cursor.execute(
                    f"SELECT COALESCE(MAX(id), 0) FROM {table_name};")
                max_id = cursor.fetchone()[0]
                start_value = max(max_id + 1, 1000)
                cursor.execute(
                    f"SELECT setval('{sequence_name}', {start_value});")

                cursor.execute(
                    f"ALTER TABLE {table_name} ALTER COLUMN id SET DEFAULT nextval('{sequence_name}');")

            except Exception as e:
                # If it fails due to identity column restrictions, skip silently
                if 'identity column' in str(e).lower():
                    continue
                else:
                    # Log the error but don't fail the migration
                    print(
                        f"Warning: Could not fix sequence for {table_name}: {e}")


def reverse_students_sequences(apps, schema_editor):
    """Reverse the sequence fixes"""
    if schema_editor.connection.vendor != 'postgresql':
        return

    tables_to_fix = [
        'students_academicyear',
        'students_level',
        'students_student',
        'students_term',
    ]

    with schema_editor.connection.cursor() as cursor:
        for table_name in tables_to_fix:
            try:
                sequence_name = f"{table_name}_id_seq_new"
                cursor.execute(
                    f"ALTER TABLE {table_name} ALTER COLUMN id DROP DEFAULT;")
                cursor.execute(
                    f"DROP SEQUENCE IF EXISTS {sequence_name} CASCADE;")
            except Exception:
                pass  # Ignore errors during rollback


class Migration(migrations.Migration):

    dependencies = [
        ('students', '0002_alter_academicyear_table_alter_level_table_and_more'),
    ]

    operations = [
        migrations.RunPython(
            fix_students_sequences,
            reverse_students_sequences,
        ),
    ]
