<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} | Tiny Feet MIS</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        .font-display { font-family: 'Playfair Display', serif; }
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="overflow-x-hidden">
<section class="w-full min-h-screen flex items-center justify-center px-4 py-8 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
  <!-- Animated Background Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <!-- Floating Particles -->
    <div class="particle particle-1"></div>
    <div class="particle particle-2"></div>
    <div class="particle particle-3"></div>
    <div class="particle particle-4"></div>
    <div class="particle particle-5"></div>
    
    <!-- Geometric Shapes -->
    <div class="geometric-shape shape-1"></div>
    <div class="geometric-shape shape-2"></div>
    <div class="geometric-shape shape-3"></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 max-w-4xl mx-auto text-center space-y-8 main-content-fade-in">
    
    <!-- Header Section -->
    <div class="space-y-6 header-slide-in">
      <!-- Philosophical Icon -->
      <div class="flex justify-center">
        <div class="w-24 h-24 bg-gradient-to-br from-purple-400 via-pink-400 to-purple-600 rounded-full flex items-center justify-center shadow-2xl icon-float">
          <i class="fas fa-brain text-white text-4xl icon-pulse"></i>
        </div>
      </div>
      
      <!-- Title -->
      <div class="space-y-4">
        <h1 class="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 font-display title-glow">
          🧠 Philosophical Moment
        </h1>
        <p class="text-xl md:text-2xl text-purple-200 font-light subtitle-fade-in">
          {{ trigger.message }}
        </p>
        <div class="w-32 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mx-auto accent-line-grow"></div>
      </div>
    </div>

    <!-- Quote Card -->
    <div class="quote-card bg-white/10 backdrop-blur-lg border border-white/20 rounded-3xl p-8 md:p-12 shadow-2xl quote-slide-in">
      <!-- Quote Content -->
      <div class="space-y-6" id="quote-content">
        <!-- Quote Text -->
        <blockquote class="text-2xl md:text-3xl lg:text-4xl font-light text-white leading-relaxed quote-text-fade-in">
          <i class="fas fa-quote-left text-purple-400 text-2xl mr-4 opacity-60"></i>
          <span id="quote-text">{{ quote.quote }}</span>
          <i class="fas fa-quote-right text-purple-400 text-2xl ml-4 opacity-60"></i>
        </blockquote>
        
        <!-- Author & Context -->
        <div class="space-y-3 author-info-slide-in">
          <div class="text-xl md:text-2xl font-semibold text-purple-300" id="quote-author">
            — {{ quote.author }}
          </div>
          <div class="text-lg text-purple-200 opacity-80" id="quote-context">
            <i class="fas fa-book mr-2"></i>{{ quote.context }}
          </div>
          <div class="inline-flex items-center gap-2 bg-purple-500/20 text-purple-200 px-4 py-2 rounded-full text-sm font-medium" id="quote-theme">
            <i class="fas fa-tag"></i>
            {{ quote.theme }}
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center buttons-slide-in">
      <!-- New Quote Button -->
      <button
        id="new-quote-btn"
        class="group bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold py-4 px-8 rounded-2xl hover:from-purple-600 hover:to-pink-600 focus:ring-4 focus:ring-purple-500/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl"
      >
        <i class="fas fa-sync-alt mr-3 group-hover:rotate-180 transition-transform duration-500"></i>
        Another Quote
      </button>
      
      <!-- Share Quote Button -->
      <button
        id="share-quote-btn"
        class="group bg-white/10 backdrop-blur-sm text-white font-bold py-4 px-8 rounded-2xl hover:bg-white/20 focus:ring-4 focus:ring-white/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl border border-white/20"
      >
        <i class="fas fa-share mr-3 group-hover:scale-110 transition-transform duration-300"></i>
        Share Wisdom
      </button>
      
      <!-- Return Button -->
      <a
        href="javascript:history.back()"
        class="group bg-slate-700/50 backdrop-blur-sm text-white font-bold py-4 px-8 rounded-2xl hover:bg-slate-600/50 focus:ring-4 focus:ring-slate-500/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl border border-white/10"
      >
        <i class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-300"></i>
        Return to Reality
      </a>
    </div>

    <!-- Easter Egg Discovery Message -->
    <div class="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-400/30 rounded-2xl p-6 discovery-message-fade-in">
      <div class="flex items-center justify-center gap-3 text-purple-200">
        <i class="fas fa-egg text-2xl text-purple-400"></i>
        <div class="text-center">
          <p class="font-semibold">🎉 Congratulations! You've discovered a philosophical easter egg!</p>
          <p class="text-sm opacity-80 mt-1">
            Your search for "{{ trigger.search_term }}" has unlocked this hidden feature. 
            The system appreciates curious minds who seek deeper meaning.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  /* Background Animations */
  .particle {
    position: absolute;
    background: linear-gradient(45deg, #a855f7, #ec4899);
    border-radius: 50%;
    opacity: 0.6;
    animation: float 6s ease-in-out infinite;
  }

  .particle-1 {
    width: 8px;
    height: 8px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
  }

  .particle-2 {
    width: 12px;
    height: 12px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  .particle-3 {
    width: 6px;
    height: 6px;
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
  }

  .particle-4 {
    width: 10px;
    height: 10px;
    top: 40%;
    right: 30%;
    animation-delay: 1s;
  }

  .particle-5 {
    width: 14px;
    height: 14px;
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
  }

  .geometric-shape {
    position: absolute;
    border: 2px solid rgba(168, 85, 247, 0.3);
    animation: rotate 20s linear infinite;
  }

  .shape-1 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 20%;
    border-radius: 20px;
    animation-delay: 0s;
  }

  .shape-2 {
    width: 80px;
    height: 80px;
    bottom: 15%;
    left: 15%;
    border-radius: 50%;
    animation-delay: 5s;
  }

  .shape-3 {
    width: 120px;
    height: 120px;
    top: 50%;
    left: 5%;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation-delay: 10s;
  }

  /* Content Animations */
  .main-content-fade-in {
    opacity: 0;
    animation: mainContentFadeIn 1s ease-out 0.5s forwards;
  }

  .header-slide-in {
    opacity: 0;
    transform: translateY(-50px);
    animation: headerSlideIn 1s ease-out 0.8s forwards;
  }

  .icon-float {
    animation: iconFloat 4s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 3s ease-in-out infinite;
  }

  .title-glow {
    animation: titleGlow 3s ease-in-out infinite;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 1s ease-out 1.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 1s ease-out 1.5s forwards;
  }

  .quote-slide-in {
    opacity: 0;
    transform: translateY(50px);
    animation: quoteSlideIn 1s ease-out 1.8s forwards;
  }

  .quote-text-fade-in {
    opacity: 0;
    animation: quoteTextFadeIn 1s ease-out 2.2s forwards;
  }

  .author-info-slide-in {
    opacity: 0;
    transform: translateX(-30px);
    animation: authorInfoSlideIn 1s ease-out 2.5s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: buttonsSlideIn 1s ease-out 2.8s forwards;
  }

  .discovery-message-fade-in {
    opacity: 0;
    animation: discoveryMessageFadeIn 1s ease-out 3.2s forwards;
  }

  /* Keyframe Definitions */
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }

  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes mainContentFadeIn {
    to { opacity: 1; }
  }

  @keyframes headerSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  @keyframes titleGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 8rem; }
  }

  @keyframes quoteSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes quoteTextFadeIn {
    to { opacity: 1; }
  }

  @keyframes authorInfoSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes buttonsSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes discoveryMessageFadeIn {
    to { opacity: 1; }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .particle, .geometric-shape {
      display: none;
    }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const newQuoteBtn = document.getElementById('new-quote-btn');
    const shareQuoteBtn = document.getElementById('share-quote-btn');
    
    // New Quote functionality
    newQuoteBtn.addEventListener('click', function() {
        // Add loading state
        const originalText = this.innerHTML;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-3"></i>Loading...';
        this.disabled = true;
        
        // Fetch new quote
        fetch('{% url "core:get_new_quote" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update quote content with animation
                const quoteContent = document.getElementById('quote-content');
                quoteContent.style.opacity = '0';
                quoteContent.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    document.getElementById('quote-text').textContent = data.quote.quote;
                    document.getElementById('quote-author').textContent = '— ' + data.quote.author;
                    document.getElementById('quote-context').innerHTML = '<i class="fas fa-book mr-2"></i>' + data.quote.context;
                    document.getElementById('quote-theme').innerHTML = '<i class="fas fa-tag"></i> ' + data.quote.theme;
                    
                    quoteContent.style.opacity = '1';
                    quoteContent.style.transform = 'translateY(0)';
                }, 300);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        })
        .finally(() => {
            // Restore button
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 1000);
        });
    });
    
    // Share Quote functionality
    shareQuoteBtn.addEventListener('click', function() {
        const quote = document.getElementById('quote-text').textContent;
        const author = document.getElementById('quote-author').textContent;
        const shareText = `"${quote}" ${author}`;
        
        if (navigator.share) {
            navigator.share({
                title: 'Philosophical Wisdom',
                text: shareText,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareText).then(() => {
                // Show feedback
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check mr-3"></i>Copied!';
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            });
        }
    });
});
</script>

</body>
</html>
