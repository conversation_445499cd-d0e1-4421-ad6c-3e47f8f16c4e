{% extends "academics/base.html" %}
<!--  -->
{% block title %}Enter Grades | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl"
        >
          <i class="fas fa-edit text-white text-lg sm:text-xl"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight"
          >
            Enter Grades
          </h1>
          <p class="text-[#40657F] text-base sm:text-lg font-medium mt-1">
            Record student assessment scores and performance
          </p>
          <div class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2"></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3">
        <a
          href="{% url 'academics:view_grades' %}"
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#7AB2D3] hover:text-white focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-eye mr-2 group-hover:scale-110 transition-transform duration-300"
          ></i>
          View Grades
        </a>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200"
    >
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#40657F]">Performance</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Enter Grades</span>
    </nav>
  </div>

  <!-- Grades Form -->
  <div class="card-modern p-8">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-clipboard-list text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Grade Entry Form
        </h2>
        <p class="text-[#40657F] text-sm">
          Enter assessment scores for {{subject}} students
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <form method="post" class="space-y-8">
      {% csrf_token %}

      <!-- Maximum Marks Section -->
      <div
        class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]/30 rounded-2xl p-6 border border-[#B9D8EB]/50"
      >
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-10 h-10 bg-[#7AB2D3] rounded-xl flex items-center justify-center shadow-md"
          >
            <i class="fas fa-calculator text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-lg font-bold text-[#2C3E50] font-display">
              Assessment Configuration
            </h3>
            <p class="text-[#40657F] text-sm">
              Set the maximum marks for this assessment
            </p>
          </div>
        </div>

        <div class="form-field">
          <label
            for="max_marks"
            class="flex items-center gap-3 font-bold text-[#2C3E50] text-sm mb-3"
          >
            <div
              class="w-5 h-5 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center shadow-sm"
            >
              <i class="fas fa-hashtag text-white text-xs"></i>
            </div>
            Assessment Maximum Marks
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30"
            >
              Required
            </span>
          </label>
          <div class="flex items-center gap-4">
            <input
              type="number"
              name="max_marks"
              id="max_marks"
              min="1"
              max="1000"
              required
              placeholder="e.g. 100"
              class="w-full max-w-xs pl-4 pr-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm bg-white text-[#2C3E50]"
            />
            <div class="text-[#40657F] text-sm font-medium">marks</div>
          </div>
          <p
            class="text-xs text-[#40657F] mt-3 font-medium flex items-center gap-2"
          >
            <i class="fas fa-info-circle text-[#7AB2D3]"></i>
            Enter the total marks this assessment is out of (e.g., 10, 50, 100).
          </p>
        </div>
      </div>
      <!-- Student Grades Table -->
      <div class="space-y-6">
        <div class="flex items-center gap-4">
          <div
            class="w-10 h-10 bg-[#40657F] rounded-xl flex items-center justify-center shadow-md"
          >
            <i class="fas fa-users text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">
              Student {{subject}} Grades
            </h3>
            <p class="text-[#40657F] text-sm">
              {{ students|length }} student{{ students|length|pluralize }} to
              grade
            </p>
          </div>
        </div>

        {% if students %}
        <div class="overflow-x-auto">
          <div class="table-modern">
            <table class="min-w-full">
              <thead>
                <tr class="bg-[#E2F1F9] border-b border-[#B9D8EB]">
                  <th
                    class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
                  >
                    Student ID
                  </th>
                  <th
                    class="py-4 px-6 text-left font-semibold text-[#2C3E50] text-sm"
                  >
                    Student Name
                  </th>
                  <th
                    class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm"
                  >
                    Grade Entry
                  </th>
                  <th
                    class="py-4 px-6 text-center font-semibold text-[#2C3E50] text-sm"
                  >
                    Status
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-[#B9D8EB]">
                {% for student in students %}
                <tr
                  class="hover:bg-[#E2F1F9]/50 transition-all duration-200 group"
                >
                  <td class="py-4 px-6">
                    <div class="flex items-center gap-3">
                      <div
                        class="w-8 h-8 bg-[#7AB2D3] rounded-lg flex items-center justify-center shadow-sm"
                      >
                        <span class="text-white text-xs font-bold"
                          >{{ student.student_id|slice:":2" }}</span
                        >
                      </div>
                      <span class="font-bold text-[#2C3E50] font-mono"
                        >{{ student.student_id }}</span
                      >
                    </div>
                  </td>
                  <td class="py-4 px-6">
                    <div class="flex items-center gap-3">
                      <div
                        class="w-10 h-10 bg-[#40657F] rounded-full flex items-center justify-center shadow-sm"
                      >
                        <span class="text-white text-sm font-bold"
                          >{{ student.name|first|upper }}</span
                        >
                      </div>
                      <div>
                        <div class="font-medium text-[#2C3E50] capitalize">
                          {{ student.name }}
                        </div>
                        <div class="text-xs text-[#40657F]">Student</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-6 text-center">
                    <div class="flex flex-col items-center gap-1">
                      <div class="flex items-center justify-center gap-2">
                        <input
                          type="number"
                          name="{{ student.id }}"
                          min="0"
                          max="1000"
                          step="any"
                          required
                          placeholder="0"
                          class="w-24 px-3 py-2 border border-[#B9D8EB] rounded-lg focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm text-center font-bold grade-input bg-white text-[#2C3E50]"
                          onchange="updateGradeStatus(this)"
                          oninput="validateGradeInput(this)"
                          data-student-id="{{ student.id }}"
                        />
                        <span class="text-[#40657F] text-sm font-medium"
                          >/ <span class="max-display">100</span></span
                        >
                      </div>
                      <div
                        class="grade-error-message hidden text-xs text-[#F28C8C] font-medium bg-[#FEF2F2] px-2 py-1 rounded border border-[#F28C8C]/30"
                      >
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <span class="error-text"></span>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-6 text-center">
                    <span
                      class="grade-status inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gray-200 text-gray-600"
                    >
                      <i class="fas fa-clock mr-1"></i>
                      Pending
                    </span>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="text-center py-16">
          <div class="flex flex-col items-center gap-6">
            <div
              class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center"
            >
              <i class="fas fa-users text-gray-400 text-3xl"></i>
            </div>
            <div>
              <h3 class="font-display font-bold text-2xl text-gray-800 mb-2">
                No Students Found
              </h3>
              <p class="text-gray-600 text-lg">
                No students are enrolled in this class for {{subject}}.
              </p>
              <p class="text-gray-500 text-sm mt-2">
                Students must be enrolled before grades can be entered.
              </p>
            </div>
          </div>
        </div>
        {% endif %}
      </div>

      <!-- Form Actions -->
      {% if students %}
      <div
        class="flex flex-col sm:flex-row gap-4 justify-between pt-8 border-t border-[#B9D8EB]"
      >
        <div class="flex items-center gap-4">
          <div class="text-sm text-[#40657F]">
            <span class="font-bold" id="completed-count">0</span> of
            {{students|length }} grades entered
          </div>
          <div class="w-48 bg-[#E2F1F9] rounded-full h-2">
            <div
              id="progress-bar"
              class="bg-[#74C69D] h-2 rounded-full transition-all duration-300"
              style="width: 0%"
            ></div>
          </div>
        </div>

        <div class="flex gap-4">
          <button
            type="button"
            onclick="clearAllGrades()"
            class="bg-[#E2F1F9] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#B9D8EB] hover:text-[#2C3E50] focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
          >
            <i
              class="fas fa-eraser mr-2 group-hover:rotate-12 transition-transform duration-300"
            ></i>
            Clear All
          </button>
          <button
            type="submit"
            class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
          >
            <i
              class="fas fa-save mr-2 group-hover:scale-110 transition-transform duration-300"
            ></i>
            Submit Grades
          </button>
        </div>
      </div>
      {% endif %}
    </form>
  </div>
</section>

<script>
  // Update max marks display when max_marks input changes
  document.getElementById("max_marks").addEventListener("input", function () {
    const maxMarks = this.value || "100";

    // Update all max-display elements
    const maxDisplayElements = document.querySelectorAll(".max-display");
    maxDisplayElements.forEach((element) => {
      element.textContent = maxMarks;
    });

    // Update all grade input max values
    const gradeInputs = document.querySelectorAll(".grade-input");
    gradeInputs.forEach((input) => {
      input.max = maxMarks;
      // Re-validate existing values with new max
      validateGradeInput(input);
    });
  });

  // Real-time validation for grade inputs
  function validateGradeInput(input) {
    const maxMarks =
      parseFloat(document.getElementById("max_marks").value) || 100;
    const value = parseFloat(input.value);
    const errorContainer = input
      .closest("td")
      .querySelector(".grade-error-message");
    const errorText = errorContainer.querySelector(".error-text");

    // Update the max attribute dynamically
    input.max = maxMarks;

    // Reset styles and hide error message
    input.style.borderColor = "#B9D8EB";
    input.style.backgroundColor = "white";
    errorContainer.classList.add("hidden");

    // Check for validation errors
    if (input.value !== "") {
      if (value > maxMarks) {
        // Show error for exceeding maximum
        input.style.borderColor = "#F28C8C";
        input.style.backgroundColor = "#FEF2F2";
        errorText.textContent = `Tryna break the system? ${maxMarks} is the max`;
        errorContainer.classList.remove("hidden");
      } else if (value < 0) {
        // Show error for negative values
        input.style.borderColor = "#F28C8C";
        input.style.backgroundColor = "#FEF2F2";
        errorText.textContent = "Grade cannot be negative";
        errorContainer.classList.remove("hidden");
      }
    }
  }

  // Update grade status based on input
  function updateGradeStatus(input) {
    const statusElement = input.closest("tr").querySelector(".grade-status");
    const value = parseFloat(input.value);
    const maxMarks =
      parseFloat(document.getElementById("max_marks").value) || 100;
    const errorContainer = input
      .closest("td")
      .querySelector(".grade-error-message");

    // First run validation to show any errors
    validateGradeInput(input);

    // Don't update status if there are validation errors
    if (!errorContainer.classList.contains("hidden")) {
      // Keep status as pending if there are errors
      statusElement.className =
        "grade-status inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30";
      statusElement.innerHTML =
        '<i class="fas fa-exclamation-triangle mr-1"></i>Invalid';
      updateProgress();
      return;
    }

    // Update status based on valid input
    if (input.value === "") {
      statusElement.className =
        "grade-status inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gray-200 text-gray-600";
      statusElement.innerHTML = '<i class="fas fa-clock mr-1"></i>Pending';
    } else if (value >= maxMarks * 0.5) {
      statusElement.className =
        "grade-status inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30";
      statusElement.innerHTML = '<i class="fas fa-check mr-1"></i>Pass';
    } else {
      statusElement.className =
        "grade-status inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30";
      statusElement.innerHTML = '<i class="fas fa-times mr-1"></i>Fail';
    }

    updateProgress();
  }

  // Update progress bar
  function updateProgress() {
    const gradeInputs = document.querySelectorAll(".grade-input");
    const completedInputs = Array.from(gradeInputs).filter((input) => {
      // Only count as completed if input has value and no error message is shown
      const errorContainer = input
        .closest("td")
        .querySelector(".grade-error-message");
      return input.value !== "" && errorContainer.classList.contains("hidden");
    });
    const completedCount = completedInputs.length;
    const totalCount = gradeInputs.length;
    const percentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

    document.getElementById("completed-count").textContent = completedCount;
    document.getElementById("progress-bar").style.width = percentage + "%";
  }

  // Clear all grades
  function clearAllGrades() {
    if (confirm("Are you sure you want to clear all entered grades?")) {
      const gradeInputs = document.querySelectorAll(".grade-input");
      gradeInputs.forEach((input) => {
        input.value = "";
        updateGradeStatus(input);
      });
    }
  }

  // Initialize progress on page load
  document.addEventListener("DOMContentLoaded", function () {
    updateProgress();
  });
</script>

{% endblock %}
