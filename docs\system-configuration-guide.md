# ⚙️ System Configuration Guide

A comprehensive guide for initial system setup, configuration, and maintenance of the Tiny Feet MIS.

## 📋 Table of Contents

- [Initial Setup](#initial-setup)
- [RBAC Configuration](#rbac-configuration)
- [Database Setup](#database-setup)
- [Environment Configuration](#environment-configuration)
- [Security Settings](#security-settings)
- [Maintenance Tasks](#maintenance-tasks)

## 🚀 Initial Setup

### Prerequisites
- Python 3.8+ installed
- PostgreSQL/MySQL database
- Git for version control
- Virtual environment tool

### Step 1: Clone and Setup
```bash
# Clone repository
git clone <repository-url>
cd receipt_gen

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

### Step 2: Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env  # or your preferred editor
```

### Step 3: Database Setup
```bash
# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Setup RBAC system
python manage.py setup_rbac --assign-superuser-roles
```

### Step 4: Load Initial Data
```bash
# Load sample data (optional)
python manage.py loaddata fixtures/initial_data.json

# Create academic periods
python manage.py setup_academic_periods

# Setup fee categories
python manage.py setup_fee_categories
```

## 🎭 RBAC Configuration

### Initial RBAC Setup
```bash
# Setup default roles and permissions
python manage.py setup_rbac

# Assign roles to existing users
python manage.py setup_rbac --assign-superuser-roles --migrate-staff

# Reset RBAC (WARNING: Deletes all RBAC data)
python manage.py setup_rbac --reset
```

### Role Configuration
```python
# Default roles created:
- Student (Level 1)
- Teacher (Level 2) 
- Academic Coordinator (Level 3)
- Finance Manager (Level 4)
- Administrator (Level 5)
- Super Administrator (Level 6)
```

### Permission Categories
- **Students**: 5 permissions
- **Finances**: 7 permissions  
- **Academics**: 7 permissions
- **Teachers**: 5 permissions
- **System**: 2 permissions
- **Reports**: 3 permissions

### Custom Role Creation
```python
# In Django shell
from accounts.utils import RBACManager

# Create custom permission
permission = RBACManager.create_permission(
    name='manage_library',
    display_name='Manage Library',
    category='library',
    description='Can manage library books and borrowing'
)

# Create custom role
role = RBACManager.create_role(
    name='librarian',
    display_name='Librarian',
    level=3,
    description='Library management role',
    permissions=['manage_library', 'view_students']
)
```

## 🗄️ Database Setup

### Supported Databases
- **PostgreSQL** (Recommended for production)
- **MySQL/MariaDB** (Alternative for production)
- **SQLite** (Development only)

### PostgreSQL Configuration
```python
# settings/prod.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'tinyfeet_mis',
        'USER': 'tinyfeet_user',
        'PASSWORD': 'secure_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

### MySQL Configuration
```python
# settings/prod.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'tinyfeet_mis',
        'USER': 'tinyfeet_user',
        'PASSWORD': 'secure_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'sql_mode': 'traditional',
        }
    }
}
```

### Database Maintenance
```bash
# Backup database
python manage.py dbbackup

# Restore database
python manage.py dbrestore

# Optimize database
python manage.py optimize_db

# Check database integrity
python manage.py check_db_integrity
```

## 🌍 Environment Configuration

### Environment Variables
```bash
# .env file
DEBUG=False
SECRET_KEY=your-secret-key-here
DATABASE_URL=postgresql://user:pass@localhost/dbname

# Email settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=True

# Static files
STATIC_URL=/static/
STATIC_ROOT=/var/www/tinyfeet/static/
MEDIA_URL=/media/
MEDIA_ROOT=/var/www/tinyfeet/media/

# Security
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
CSRF_TRUSTED_ORIGINS=https://yourdomain.com

# Cache (Redis)
CACHE_URL=redis://localhost:6379/1

# Logging
LOG_LEVEL=INFO
LOG_FILE=/var/log/tinyfeet/django.log
```

### Settings Structure
```
settings/
├── __init__.py
├── base.py      # Common settings
├── dev.py       # Development settings
├── prod.py      # Production settings
└── test.py      # Testing settings
```

### Development Settings
```python
# settings/dev.py
from .base import *

DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1']

# Development database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Debug toolbar
INSTALLED_APPS += ['debug_toolbar']
MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
```

### Production Settings
```python
# settings/prod.py
from .base import *

DEBUG = False
ALLOWED_HOSTS = ['yourdomain.com', 'www.yourdomain.com']

# Security settings
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# Session security
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True
```

## 🔒 Security Settings

### Authentication Security
```python
# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {'min_length': 8,}
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Session settings
SESSION_COOKIE_AGE = 1209600  # 2 weeks
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_SAVE_EVERY_REQUEST = True
```

### CORS Configuration
```python
# For API access
CORS_ALLOWED_ORIGINS = [
    "https://yourdomain.com",
    "https://www.yourdomain.com",
]

CORS_ALLOW_CREDENTIALS = True
```

### Rate Limiting
```python
# Django-ratelimit settings
RATELIMIT_ENABLE = True
RATELIMIT_USE_CACHE = 'default'

# Login rate limiting
LOGIN_RATE_LIMIT = '5/m'  # 5 attempts per minute
```

## 🔧 Maintenance Tasks

### Regular Maintenance
```bash
# Daily tasks
python manage.py clearsessions
python manage.py cleanup_uploads
python manage.py update_search_index

# Weekly tasks  
python manage.py dbbackup
python manage.py check_permissions
python manage.py audit_user_access

# Monthly tasks
python manage.py optimize_db
python manage.py cleanup_logs
python manage.py security_audit
```

### System Health Checks
```bash
# Check system status
python manage.py check --deploy

# Database health
python manage.py check_db_health

# Permission integrity
python manage.py check_rbac_integrity

# File system checks
python manage.py check_file_permissions
```

### Performance Monitoring
```python
# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/tinyfeet/django.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
}
```

## 📊 Monitoring and Analytics

### System Metrics
- User activity tracking
- Performance monitoring
- Error rate tracking
- Database query optimization

### Health Endpoints
```python
# Health check URLs
/health/          # Basic health check
/health/db/       # Database connectivity
/health/cache/    # Cache connectivity
/health/storage/  # File storage check
```

### Backup Strategy
```bash
# Automated backups
0 2 * * * /path/to/backup_script.sh  # Daily at 2 AM
0 2 * * 0 /path/to/weekly_backup.sh  # Weekly on Sunday
0 2 1 * * /path/to/monthly_backup.sh # Monthly on 1st
```

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Check database status
sudo systemctl status postgresql
sudo systemctl status mysql

# Test connection
python manage.py dbshell
```

#### Permission Errors
```bash
# Fix file permissions
sudo chown -R www-data:www-data /var/www/tinyfeet/
sudo chmod -R 755 /var/www/tinyfeet/

# Fix media permissions
sudo chmod -R 775 /var/www/tinyfeet/media/
```

#### Static Files Issues
```bash
# Collect static files
python manage.py collectstatic --noinput

# Check static file serving
python manage.py findstatic admin/css/base.css
```

### Emergency Procedures

#### Reset Admin Password
```bash
python manage.py changepassword admin
```

#### Restore from Backup
```bash
python manage.py dbrestore --uncompress
python manage.py migrate
python manage.py collectstatic --noinput
```

#### System Recovery
```bash
# Check system integrity
python manage.py check --deploy

# Rebuild search index
python manage.py rebuild_index

# Clear all caches
python manage.py clear_cache
```

## 📝 Configuration Checklist

### Pre-Production Checklist
- [ ] Environment variables configured
- [ ] Database optimized and backed up
- [ ] RBAC system properly configured
- [ ] Security settings enabled
- [ ] SSL certificates installed
- [ ] Monitoring systems active
- [ ] Backup procedures tested
- [ ] Performance benchmarks established

### Post-Deployment Checklist
- [ ] All services running
- [ ] Database connectivity verified
- [ ] User authentication working
- [ ] File uploads functional
- [ ] Email notifications working
- [ ] Backup systems operational
- [ ] Monitoring alerts configured
- [ ] Documentation updated

---

**Remember**: Always test configuration changes in a development environment before applying to production. Keep configuration documentation updated and maintain regular backups.
