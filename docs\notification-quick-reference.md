# 📢 Notification Quick Reference

## 🚀 Management Command Cheat Sheet

### Basic Syntax
```bash
python manage.py create_notification --title "TITLE" --message "MESSAGE" [OPTIONS]
```

### Quick Examples

#### System-Wide Notifications
```bash
# General announcement
python manage.py create_notification \
    --title "School Holiday Notice" \
    --message "School will be closed tomorrow for public holiday"

# Urgent system alert
python manage.py create_notification \
    --title "🚨 System Maintenance" \
    --message "System will be down for 2 hours starting 10 PM" \
    --priority urgent \
    --type "System Alert"
```

#### Targeted Notifications
```bash
# Fee reminders to specific parents
python manage.py create_notification \
    --title "Fee Payment Due" \
    --message "Monthly fee payment is due in 3 days" \
    --type "Fee Reminder" \
    --priority high \
    --target-users parent1 parent2 parent3

# Academic update to teachers
python manage.py create_notification \
    --title "Staff Meeting Tomorrow" \
    --message "Mandatory staff meeting at 3 PM in conference room" \
    --target-users teacher1 teacher2 teacher3
```

#### Time-Limited Notifications
```bash
# Notification with expiry
python manage.py create_notification \
    --title "Exam Schedule Available" \
    --message "Download your exam timetable from student portal" \
    --expires "2025-12-31 23:59:59" \
    --type "Academic Update"
```

## 📋 Command Options

| Option | Required | Default | Description |
|--------|----------|---------|-------------|
| `--title` | ✅ | - | Notification title |
| `--message` | ✅ | - | Notification content |
| `--type` | ❌ | "General Info" | Notification type |
| `--priority` | ❌ | "medium" | Priority level |
| `--expires` | ❌ | None | Expiry date (YYYY-MM-DD HH:MM:SS) |
| `--target-users` | ❌ | None | Space-separated usernames |
| `--inactive` | ❌ | False | Create as inactive/draft |

## 🎯 Priority Levels

| Priority | Color | Use Case |
|----------|-------|----------|
| `low` | 🟢 Green | General info, non-urgent updates |
| `medium` | 🔵 Blue | Standard announcements, reminders |
| `high` | 🟠 Orange | Important deadlines, urgent info |
| `urgent` | 🔴 Red | Critical alerts, emergency notices |

## 📝 Notification Types

| Type | Icon | Color | Purpose |
|------|------|-------|---------|
| General Info | ℹ️ | Blue | General announcements |
| Fee Reminder | 💰 | Red | Payment notifications |
| Academic Update | 🎓 | Blue | Academic announcements |
| System Alert | ⚠️ | Red | System notifications |

## 🔧 Common Use Cases

### 1. Emergency Alerts
```bash
python manage.py create_notification \
    --title "🚨 EMERGENCY: School Closure" \
    --message "Due to severe weather, school is closed today. Stay safe!" \
    --priority urgent \
    --type "System Alert"
```

### 2. Fee Reminders
```bash
python manage.py create_notification \
    --title "Fee Payment Reminder" \
    --message "Your child's school fees are due by month end. Avoid late charges." \
    --type "Fee Reminder" \
    --priority high \
    --target-users parent1 parent2
```

### 3. Academic Announcements
```bash
python manage.py create_notification \
    --title "Exam Results Published" \
    --message "Mid-term results are now available in your student portal." \
    --type "Academic Update" \
    --priority medium \
    --expires "2025-08-31 23:59:59"
```

### 4. Event Reminders
```bash
python manage.py create_notification \
    --title "Parent-Teacher Meeting Tomorrow" \
    --message "Don't forget about the scheduled meeting at 2 PM in classroom A1." \
    --priority high \
    --expires "2025-07-07 15:00:00" \
    --target-users parent_john parent_jane
```

### 5. System Maintenance
```bash
python manage.py create_notification \
    --title "Scheduled Maintenance Tonight" \
    --message "System will be unavailable from 11 PM to 1 AM for updates." \
    --type "System Alert" \
    --priority medium \
    --expires "2025-07-07 01:00:00"
```

## 🛠️ Troubleshooting

### Check Available Users
```bash
python manage.py shell -c "
from django.contrib.auth import get_user_model;
User = get_user_model();
users = User.objects.filter(is_active=True).values_list('username', flat=True);
print('Available users:', list(users))
"
```

### List Notification Types
```bash
python manage.py shell -c "
from core.models import NotificationType;
types = NotificationType.objects.values_list('name', flat=True);
print('Available types:', list(types))
"
```

### View Recent Notifications
```bash
python manage.py shell -c "
from core.models import Notification;
recent = Notification.objects.order_by('-created_at')[:5];
for n in recent:
    print(f'{n.id}: {n.title} ({n.priority}) - Active: {n.is_active}')
"
```

## ⚡ Pro Tips

1. **Use Descriptive Titles**: Make titles clear and actionable
2. **Set Appropriate Priority**: Don't overuse urgent priority
3. **Add Expiry Dates**: For time-sensitive notifications
4. **Test with Inactive**: Use `--inactive` to create drafts first
5. **Batch User Targeting**: List multiple users in one command
6. **Use Emojis Sparingly**: Only for urgent or special notifications

## 🚨 Production Safety

- ✅ **Always test** with `--inactive` first
- ✅ **Double-check usernames** before targeting
- ✅ **Use appropriate priority** levels
- ✅ **Set expiry dates** for time-sensitive content
- ❌ **Don't spam** users with too many notifications
- ❌ **Don't use urgent** for non-critical messages

## 📞 Need Help?

- Check the full [Notification Management Guide](notification-management-guide.md)
- Review [System Configuration Guide](system-configuration-guide.md)
- Contact system administrator for complex scenarios

---

**Quick Reference Version**: 1.0  
**Last Updated**: July 6, 2025
