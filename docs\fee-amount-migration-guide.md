# Fee Amount Migration Guide

## Overview

This guide explains the migration from the stored `amount_paid` field to the dynamic `amount` property in the FeeAccount model, and how the `is_paid` status is now correctly updated.

## Problem Statement

Previously, several stored fields could become inconsistent with actual receipt data:

- `amount_paid`: Could get out of sync with actual receipts
- `balance`: Calculated from stored `amount_paid`, not actual payments
- `carry_on`: Based on stored `balance`, not dynamic calculations
- `is_paid`: Only updated based on stored fields, not actual payment status

The dynamic `amount` property provides more accurate payment calculations, and now we have dynamic properties for all payment-related calculations.

## Solution

### 1. Added Dynamic Properties

New dynamic properties that calculate values from actual receipt data:

```python
@property
def current_balance(self):
    """Calculate the current balance dynamically using distributed amount"""
    return self.total_due - self.amount

@property
def is_fully_paid(self):
    """Check if account is fully paid based on dynamic calculation"""
    return self.current_balance <= 0
```

### 2. Simplified Status Updates

The system now uses a streamlined approach for updating payment status:

```python
def update_balance_and_paid_status(self):
    """Update is_paid status based on dynamic calculation"""
    self.is_paid = self.is_fully_paid
```

### 3. Added Efficient Group Update Method

To avoid performance issues from repeatedly calculating the dynamic `amount`, we added a class method that efficiently updates all related fee accounts:

```python
@classmethod
def update_paid_status_for_group(cls, student, category, term):
    # Calculates receipt total once and distributes across all accounts
    # Updates is_paid status for all accounts in the group
    # Uses bulk_update for minimal database impact
```

### 4. Updated Receipt Model

The Receipt model now calls the efficient group update method when receipts are created or reversed:

```python
def update_amount_paid(self):
    # Update stored amount_paid for backward compatibility
    self.fee_account.amount_paid = calculate_sum_of_receipts(self)
    self.fee_account.save()
    
    # Efficiently update is_paid status for all related fee accounts
    FeeAccount.update_paid_status_for_group(
        self.fee_account.student,
        self.fee_account.category,
        self.fee_account.term
    )
    
    sync_income(self.fee_account, self.date)
```

## Migration Steps

### 1. Test the Changes

Run the test suite to ensure the new logic works correctly:

```bash
python manage.py test finances.fee_management.tests.test_dynamic_amount_migration
```

### 2. Dry Run Migration

Check for any inconsistencies between stored and dynamic amounts:

```bash
python manage.py migrate_to_dynamic_amount --dry-run
```

### 3. Run Migration

Update all fee accounts to use the new logic:

```bash
python manage.py migrate_to_dynamic_amount
```

### 4. Optional: Migrate Specific Term

If you want to migrate only a specific term:

```bash
python manage.py migrate_to_dynamic_amount --term-id=1
```

## Benefits

1. **Accuracy**: All payment-related calculations are now based on actual receipt data
2. **Consistency**: Single source of truth for payment calculations
3. **Performance**: Efficient bulk updates minimize database impact
4. **Backward Compatibility**: All stored fields are maintained for existing code
5. **Dynamic Calculations**: Real-time accurate balance and payment status
6. **Simplified Logic**: Use properties instead of complex stored field management

## Future Considerations

### Phase 2: Remove Deprecated Fields

Once you're confident the dynamic approach works well, you can:

1. Update all code references to use dynamic properties:
   - `amount_paid` → `amount`
   - `balance` → `current_balance`
   - `is_paid` → `is_fully_paid`
2. Create a migration to remove the deprecated fields
3. Remove the backward compatibility code in Receipt model
4. Remove deprecated methods like `update_balance_and_paid_status()`

### Performance Monitoring

Monitor the performance impact of the dynamic `amount` property. If needed, consider:

1. Adding database indexes on receipt fields
2. Implementing caching for frequently accessed fee accounts
3. Using select_related/prefetch_related in queries

## Troubleshooting

### Common Issues

1. **Inconsistent Data**: Run the migration command to fix inconsistencies
2. **Performance Issues**: Use the group update method instead of individual saves
3. **Missing Receipts**: Ensure all receipts are properly linked to fee accounts

### Debugging

To debug payment calculation issues:

```python
# Check dynamic calculations
fee_account = FeeAccount.objects.get(id=123)
print(f"Dynamic amount: {fee_account.amount}")
print(f"Dynamic balance: {fee_account.current_balance}")
print(f"Dynamic is_paid: {fee_account.is_fully_paid}")
print(f"Stored is_paid: {fee_account.is_paid}")

# Check related receipts
receipts = Receipt.objects.filter(
    fee_account__student=fee_account.student,
    fee_account__category=fee_account.category,
    fee_account__term=fee_account.term,
    is_reversed=False
)
print(f"Total receipts: {sum(r.amount_paid for r in receipts)}")

# Note: Individual accounts don't show overpayments
# Overpayments are handled through receipt distribution to other accounts
```

## Conclusion

This migration provides a more robust and accurate fee payment system while maintaining backward compatibility. The `is_paid` status is now correctly updated based on actual receipt data, and the efficient group update method ensures good performance.
