from django.test import TestCase
import uuid
from datetime import date

from accounts.models.users import CustomUser
from core.services import journal_reversals
from finances.book_keeping.ledger import Ledger, JournalEntry, JournalLine
from finances.book_keeping.outflow import Outflow, OutflowLine
from students.models import AcademicYear, Term


class JournalReversalTest(TestCase):
    """
    Test journal reversal functionality.
    Using mocks to avoid database ID conflicts.
    """

    def setUp(self):
        # Create unique names to avoid conflicts with existing data
        unique_suffix = str(uuid.uuid4())[:8]

        self.user = CustomUser.objects.create_user(
            username=f'testuser_{unique_suffix}',
            email=f'test_{unique_suffix}@example.com',
            password='testpass123'
        )

        # Create a ledger account with a high ID to avoid conflicts
        try:
            # Use a high ID that's unlikely to conflict
            # Convert part of UUID to int
            high_id = 10000 + int(unique_suffix[:4], 16)
            self.account = Ledger(
                id=high_id,
                name=f"Test Account {unique_suffix}",
                ledger_type="Asset",
                code=f"TEST{unique_suffix}",
            )
            self.account.save()

            # Create the required "Cash/Bank" account that Outflow.update_journal() expects
            cash_id = 10001 + int(unique_suffix[:4], 16)
            self.cash_account = Ledger(
                id=cash_id,
                name="Cash/Bank",
                ledger_type="Asset",
                code="CASH",
            )
            self.cash_account.save()
        except Exception as e:
            self.skipTest(f"Cannot create test Ledger accounts: {e}")

        self.academic_year, created = AcademicYear.objects.get_or_create(
            name=f"Test Year {unique_suffix}",
            defaults={
                'start_date': date(2021, 1, 1),
                'end_date': date(2021, 12, 31),
            }
        )

        self.term, created = Term.objects.get_or_create(
            term_name="Term 1",
            academic_year=self.academic_year,
            defaults={
                'start_date': date(2021, 1, 1),
                'end_date': date(2021, 6, 30),
            }
        )

        # Create outflow with unique identifier and high ID
        # Different range from Ledger
        outflow_id = 20000 + int(unique_suffix[:4], 16)
        self.outflow = Outflow(
            id=outflow_id,
            date=date(2021, 1, 1),
            payee=f"Test Payee {unique_suffix}",
            description=f"Test Description {unique_suffix}",
            created_by=self.user,
            term=self.term,
        )
        self.outflow.save()

        self.outflow_line = OutflowLine.objects.create(
            outflow=self.outflow,
            account=self.account,
            amount=100,
            description=f"Test Line {unique_suffix}",
        )

        # Manually create a journal entry for testing
        self.journal_entry = JournalEntry.objects.create(
            term=self.term,
            date=date(2021, 1, 1),
            description=f"Test Journal {unique_suffix}",
            voucher=f"TEST{unique_suffix}",
        )

        # Create journal lines
        JournalLine.objects.create(
            journal_entry=self.journal_entry,
            account=self.account,
            description="Test debit line",
            amount=100,
            line_type="Debit"
        )

        # Mock the outflow's journal_entry property
        self.outflow.journal_entry = self.journal_entry

    def test_reverse_outflow(self):
        """Test that outflow reversal creates proper reversal journal entry."""
        print("\n=== TEST 1: REVERSE OUTFLOW ===")

        # Step 1: Verify setup
        print("Step 1: Verifying initial setup...")
        self.assertIsNotNone(self.journal_entry,
                             "Outflow should have a journal entry")
        self.assertEqual(self.outflow.journal_entry, self.journal_entry)
        print(f"✓ Outflow has journal entry: {self.journal_entry}")

        # Step 2: Verify initial state
        print("Step 2: Checking initial reversal state...")
        initial_reversed = getattr(self.outflow, 'is_reversed', False)
        initial_reversal_entry = getattr(self.outflow, 'reversal_entry', None)
        self.assertFalse(initial_reversed,
                         "Outflow should not be reversed initially")
        self.assertIsNone(initial_reversal_entry,
                          "Outflow should not have reversal entry initially")
        print(
            f"✓ Initial state: is_reversed={initial_reversed}, reversal_entry={initial_reversal_entry}")

        # Step 3: Count original journal lines
        original_lines = list(self.journal_entry.journalline_set.all())
        print(f"Step 3: Original journal has {len(original_lines)} lines:")
        for i, line in enumerate(original_lines):
            print(
                f"  Line {i+1}: {line.line_type} ${line.amount} to {line.account.name}")

        # Step 4: Perform the reversal
        print("Step 4: Performing reversal...")
        try:
            reversal = journal_reversals.reverse_transaction(
                self.outflow, "Test reversal reason", self.user
            )
            print(f"✓ Reversal created successfully: {reversal}")
        except Exception as e:
            print(f"✗ Reversal failed: {e}")
            raise

        # Step 5: Verify reversal properties
        print("Step 5: Verifying reversal properties...")
        self.assertIsNotNone(reversal, "Reversal should be created")
        self.assertTrue(reversal.is_reversal,
                        "Reversal should be marked as reversal")
        self.assertEqual(reversal.reverses, self.journal_entry,
                         "Reversal should reference original journal")
        self.assertEqual(reversal.created_by, self.user,
                         "Reversal should be created by test user")
        self.assertEqual(reversal.reason, "Test reversal reason",
                         "Reversal should have correct reason")
        print(
            f"✓ Reversal properties: is_reversal={reversal.is_reversal}, reverses={reversal.reverses.id}, created_by={reversal.created_by.username}")

        # Step 6: Verify original journal is locked
        print("Step 6: Checking if original journal is locked...")
        self.journal_entry.refresh_from_db()
        self.assertTrue(self.journal_entry.is_locked,
                        "Original journal should be locked")
        print(f"✓ Original journal locked: {self.journal_entry.is_locked}")

        # Step 7: Verify reversal lines
        print("Step 7: Verifying reversal journal lines...")
        reversal_lines = list(reversal.journalline_set.all())
        self.assertGreater(len(reversal_lines), 0,
                           "Reversal should have journal lines")
        print(f"✓ Reversal has {len(reversal_lines)} lines:")

        for i, line in enumerate(reversal_lines):
            print(
                f"  Line {i+1}: {line.line_type} ${line.amount} to {line.account.name}")

        # Step 8: Verify line types are opposite
        print("Step 8: Verifying line types are opposite...")
        matches_found = 0
        for orig_line in original_lines:
            reversal_line = next(
                (line for line in reversal_lines
                 if line.account == orig_line.account and line.amount == orig_line.amount),
                None
            )
            if reversal_line:
                matches_found += 1
                if orig_line.line_type == "Debit":
                    self.assertEqual(reversal_line.line_type,
                                     "Credit", "Debit should become Credit")
                    print(f"  ✓ {orig_line.account.name}: Debit → Credit")
                else:
                    self.assertEqual(reversal_line.line_type,
                                     "Debit", "Credit should become Debit")
                    print(f"  ✓ {orig_line.account.name}: Credit → Debit")

        print(f"✓ Found {matches_found} matching opposite lines")
        print("=== TEST 1 COMPLETED SUCCESSFULLY ===\n")

    def test_reverse_already_reversed_transaction(self):
        """Test that reversing an already reversed transaction raises an error."""
        print("\n=== TEST 2: PREVENT DOUBLE REVERSAL ===")

        # Step 1: Perform first reversal
        print("Step 1: Performing first reversal...")
        try:
            first_reversal = journal_reversals.reverse_transaction(
                self.outflow, "First reversal", self.user)
            print(f"✓ First reversal successful: {first_reversal}")
        except Exception as e:
            print(f"✗ First reversal failed: {e}")
            raise

        # Step 2: Check outflow state after first reversal
        print("Step 2: Checking outflow state after first reversal...")
        self.outflow.refresh_from_db()
        is_reversed = getattr(self.outflow, 'is_reversed', False)
        reversal_entry = getattr(self.outflow, 'reversal_entry', None)
        print(
            f"✓ Outflow state: is_reversed={is_reversed}, has_reversal_entry={reversal_entry is not None}")

        # Step 3: Attempt second reversal (should fail)
        print("Step 3: Attempting second reversal (should fail)...")
        try:
            with self.assertRaises(Exception) as context:
                journal_reversals.reverse_transaction(
                    self.outflow, "Second reversal", self.user)
            print(
                f"✓ Second reversal correctly prevented: {context.exception}")
        except AssertionError:
            print("✗ Second reversal was allowed (this should not happen)")
            raise
        except Exception as e:
            print(f"✗ Unexpected error during second reversal: {e}")
            raise

        print("=== TEST 2 COMPLETED SUCCESSFULLY ===\n")

    def test_reverse_transaction_without_journal_entry(self):
        """Test that reversing a transaction without journal entry raises an error."""
        print("\n=== TEST 3: HANDLE MISSING JOURNAL ENTRY ===")

        # Step 1: Create outflow without journal entry
        print("Step 1: Creating outflow without journal entry...")
        unique_suffix = str(uuid.uuid4())[:8]
        # Another different range
        outflow_id_2 = 30000 + int(unique_suffix[:4], 16)
        outflow_without_journal = Outflow(
            id=outflow_id_2,
            date=date(2021, 1, 1),
            payee="Test Payee No Journal",
            description="Test without journal",
            created_by=self.user,
            term=self.term,
        )
        outflow_without_journal.save()
        print(f"✓ Created outflow without journal: {outflow_without_journal}")

        # Step 2: Verify it has no journal entry
        print("Step 2: Verifying outflow has no journal entry...")
        outflow_without_journal.journal_entry = None
        has_journal = hasattr(
            outflow_without_journal, 'journal_entry') and outflow_without_journal.journal_entry is not None
        print(f"✓ Outflow has journal entry: {has_journal}")

        # Step 3: Attempt reversal (should fail)
        print("Step 3: Attempting reversal of outflow without journal (should fail)...")
        try:
            with self.assertRaises((ValueError, AttributeError)) as context:
                journal_reversals.reverse_transaction(
                    outflow_without_journal, "Test reversal", self.user)

            error_message = str(context.exception).lower()
            print(f"✓ Reversal correctly failed: {context.exception}")

            # Step 4: Verify error message mentions journal
            print("Step 4: Verifying error message mentions journal...")
            journal_mentioned = "journal" in error_message or "none" in error_message
            self.assertTrue(
                journal_mentioned,
                f"Error should mention journal entry issue: {context.exception}"
            )
            print(
                f"✓ Error message mentions journal issue: {journal_mentioned}")

        except AssertionError:
            print("✗ Reversal was allowed (this should not happen)")
            raise
        except Exception as e:
            print(f"✗ Unexpected error during reversal: {e}")
            raise

        print("=== TEST 3 COMPLETED SUCCESSFULLY ===\n")
