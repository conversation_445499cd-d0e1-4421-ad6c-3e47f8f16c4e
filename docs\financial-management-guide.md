# 💰 Financial Management Guide

A comprehensive guide for financial managers and administrators to manage finances, fees, and financial reporting in the Tiny Feet MIS.

## 📋 Table of Contents

- [Overview](#overview)
- [Dashboard Overview](#dashboard-overview)
- [Fee Management](#fee-management)
- [Receipt Management](#receipt-management)
- [Expenditure Tracking](#expenditure-tracking)
- [Financial Reporting](#financial-reporting)
- [Budget Management](#budget-management)

## 🎯 Overview

The Financial Management module provides comprehensive tools for managing school finances, including fee collection, expenditure tracking, budget management, and financial reporting.

### Access Requirements
- **Finance Manager**, **Administrator**, or **Super Administrator** role
- Login redirect: **Finances Dashboard** (`/finances/`)

### Key Features
- Fee collection and receipt generation
- Expenditure tracking and approval
- Budget planning and monitoring
- Financial reporting and analytics
- Integration with accounting systems

## 📊 Dashboard Overview

### Financial Summary Cards
Upon login, financial managers see:

#### 1. Total Income Card
- **Amount**: Total fees collected this period
- **Trend**: Percentage change from last month
- **Details**: Breakdown by fee categories

#### 2. Total Expenditure Card  
- **Amount**: Total expenses this period
- **Budget**: Comparison to budgeted amounts
- **Categories**: Major expense categories

#### 3. Balance Card
- **Available**: Current available balance
- **Outstanding**: Fees yet to be collected
- **Projections**: End-of-term projections

### Quick Actions
- **Add Receipt**: Record new fee payment
- **Add Expenditure**: Record new expense
- **Generate Report**: Create financial reports
- **View Outstanding**: See unpaid fees

## 💳 Fee Management

### Fee Categories
Standard fee categories include:
- **Tuition Fees**: Academic instruction fees
- **Food Fees**: Meal and nutrition programs
- **Transport Fees**: School transportation
- **Activity Fees**: Extracurricular activities
- **Examination Fees**: Assessment and testing
- **Uniform Fees**: School uniforms and supplies

### Setting Up Fee Structures

#### Step 1: Define Fee Categories
1. Navigate to **Finances** → **Fee Management**
2. Click **"Manage Fee Categories"**
3. Add or edit fee categories:
   ```
   Category Details:
   - Name (e.g., "Tuition Fees")
   - Description
   - Default amount
   - Billing cycle (Monthly/Termly)
   - Active status
   ```

#### Step 2: Set Fee Amounts by Level
1. Go to **Fee Structure** settings
2. Set amounts for each education level:
   ```
   Primary Level:
   - Tuition: MWK 50,000/term
   - Food: MWK 30,000/term
   
   Secondary Level:
   - Tuition: MWK 75,000/term
   - Food: MWK 35,000/term
   ```

#### Step 3: Apply Fee Structures
1. Select academic term
2. Choose student levels
3. Apply fee structure
4. Generate fee accounts for all students

### Fee Account Management
Each student has fee accounts for each category:
- **Total Due**: Amount owed for the term
- **Amount Paid**: Payments received
- **Balance**: Outstanding amount
- **Status**: Paid/Partially Paid/Unpaid

## 🧾 Receipt Management

### Recording Payments

#### Method 1: Individual Receipt
1. Navigate to **Finances** → **Fee Collection**
2. Search for student by name or ID
3. Click **"Add Receipt"**
4. Fill receipt details:
   ```
   Receipt Information:
   - Student name (auto-filled)
   - Amount paid
   - Payment method (Cash/Bank/Mobile Money)
   - Fee categories (select which fees)
   - Receipt number (auto-generated)
   - Date of payment
   - Notes (optional)
   ```
5. Click **"Save Receipt"**
6. Print receipt for student

#### Method 2: Bulk Payment Upload
1. Go to **Fee Collection** → **Bulk Upload**
2. Download Excel template
3. Fill in payment data:
   ```
   Excel Columns:
   - Student ID
   - Student Name
   - Amount
   - Payment Method
   - Date
   - Fee Category
   ```
4. Upload completed file
5. Review and confirm payments
6. Generate receipts in batch

### Receipt Features
- **Auto-numbering**: Sequential receipt numbers
- **PDF Generation**: Printable receipt format
- **Email Delivery**: Send receipts via email
- **Duplicate Prevention**: Prevents duplicate entries
- **Audit Trail**: Complete payment history

### Payment Methods
- **Cash**: Direct cash payments
- **Bank Transfer**: Electronic bank transfers
- **Mobile Money**: Airtel Money, TNM Mpamba
- **Cheque**: Bank cheques (with clearing tracking)

## 📈 Expenditure Tracking

### Recording Expenditures

#### Step 1: Add New Expenditure
1. Navigate to **Finances** → **Expenditures**
2. Click **"Add Expenditure"**
3. Fill expenditure details:
   ```
   Expenditure Information:
   - Category (Salaries, Utilities, Supplies, etc.)
   - Amount
   - Description
   - Date
   - Receipt/Invoice number
   - Payment method
   - Responsibility (who authorized)
   - Supporting documents
   ```

#### Step 2: Approval Process
1. Submit expenditure for approval
2. Supervisor reviews and approves
3. Finance manager processes payment
4. Update expenditure status

### Expenditure Categories
- **Salaries & Benefits**: Staff compensation
- **Utilities**: Electricity, water, internet
- **Supplies**: Office and teaching materials
- **Maintenance**: Building and equipment upkeep
- **Transport**: Vehicle fuel and maintenance
- **Professional Services**: Legal, accounting, consulting

### Budget Monitoring
- **Budget vs Actual**: Compare spending to budget
- **Variance Analysis**: Identify over/under spending
- **Alerts**: Notifications for budget limits
- **Forecasting**: Predict end-of-period spending

## 📊 Financial Reporting

### Standard Reports

#### 1. Income Statement
- **Revenue**: All fee collections by category
- **Expenses**: All expenditures by category
- **Net Income**: Revenue minus expenses
- **Period**: Monthly, termly, or annual

#### 2. Fee Collection Report
- **Collections by Period**: Daily, weekly, monthly
- **Collections by Category**: Tuition, food, etc.
- **Collections by Level**: Primary, secondary
- **Outstanding Balances**: Unpaid fees by student

#### 3. Expenditure Report
- **Spending by Category**: Breakdown of expenses
- **Budget Variance**: Actual vs budgeted amounts
- **Trend Analysis**: Spending patterns over time
- **Approval Status**: Pending, approved, paid

#### 4. Student Financial Status
- **Individual Statements**: Per-student fee status
- **Class Summaries**: Fee status by class
- **Outstanding Reports**: Students with unpaid fees
- **Payment History**: Complete payment records

### Generating Reports

#### Step 1: Select Report Type
1. Navigate to **Finances** → **Reports**
2. Choose report category
3. Select specific report

#### Step 2: Set Parameters
```
Report Parameters:
- Date range (From/To dates)
- Academic term
- Student level/class
- Fee categories
- Payment status
- Format (PDF/Excel/CSV)
```

#### Step 3: Generate and Export
1. Click **"Generate Report"**
2. Review report preview
3. Export in desired format
4. Save or email report

### Custom Reports
- **Query Builder**: Create custom financial queries
- **Dashboard Widgets**: Add metrics to dashboard
- **Scheduled Reports**: Automatic report generation
- **Data Export**: Export data for external analysis

## 💼 Budget Management

### Budget Planning

#### Step 1: Create Annual Budget
1. Navigate to **Finances** → **Budget Management**
2. Click **"Create New Budget"**
3. Set budget parameters:
   ```
   Budget Details:
   - Academic year
   - Budget period (Annual/Termly)
   - Currency
   - Approval status
   ```

#### Step 2: Define Budget Lines
1. Add income projections:
   ```
   Income Categories:
   - Tuition fees (by level)
   - Food fees
   - Other fees
   - Grants/donations
   ```

2. Add expense budgets:
   ```
   Expense Categories:
   - Salaries (teaching/non-teaching)
   - Utilities
   - Supplies
   - Maintenance
   - Capital expenditure
   ```

#### Step 3: Budget Approval
1. Submit budget for review
2. Management approval process
3. Board approval (if required)
4. Activate approved budget

### Budget Monitoring
- **Real-time Tracking**: Current vs budgeted amounts
- **Variance Alerts**: Notifications for significant variances
- **Forecast Updates**: Adjust projections based on actuals
- **Approval Workflows**: Control spending authorization

### Budget Reports
- **Budget vs Actual**: Performance against budget
- **Variance Analysis**: Detailed variance explanations
- **Cash Flow Projections**: Future cash requirements
- **Budget Utilization**: Percentage of budget used

## 🔧 System Integration

### Accounting Software Integration
- **Export to QuickBooks**: Financial data export
- **Sage Integration**: Direct data synchronization
- **Excel Export**: Flexible data export options
- **API Access**: Custom integration capabilities

### Banking Integration
- **Bank Reconciliation**: Match payments to bank records
- **Electronic Statements**: Import bank statements
- **Payment Processing**: Online payment gateways
- **Mobile Money**: Integration with mobile payment systems

## 🚨 Financial Controls

### Security Measures
- **Role-based Access**: Limit access by user role
- **Approval Workflows**: Multi-level approval processes
- **Audit Trails**: Complete transaction history
- **Data Backup**: Regular financial data backups

### Compliance Features
- **Tax Reporting**: Generate tax-compliant reports
- **Audit Support**: Detailed transaction records
- **Regulatory Compliance**: Meet education sector requirements
- **Financial Transparency**: Stakeholder reporting

## 📝 Quick Reference

### Common Tasks
```bash
# Record fee payment
Finances → Fee Collection → Search Student → Add Receipt

# Add expenditure
Finances → Expenditures → Add Expenditure → Fill Details

# Generate income report
Finances → Reports → Income Statement → Set Dates → Generate

# Check outstanding fees
Finances → Fee Collection → Outstanding Balances

# Create budget
Finances → Budget → Create New Budget → Add Lines
```

### Keyboard Shortcuts
- `Ctrl + N`: New receipt (when in fee collection)
- `Ctrl + R`: Generate report (when in reports section)
- `Ctrl + F`: Search students/transactions
- `Ctrl + P`: Print current document

---

**Remember**: Always verify payment amounts and maintain proper documentation for all financial transactions. Regular reconciliation and reporting ensure accurate financial management.
