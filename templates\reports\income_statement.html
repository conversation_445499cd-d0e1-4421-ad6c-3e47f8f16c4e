{% extends 'base.html' %} {% load humanize %} {% load static %}
<!--title  -->
{% block title %}Income Statement{% endblock %}
<!-- body -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-chart-line text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Income Statement
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Financial revenue & expense overview
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#74C69D] to-[#5fb085] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <a
        href="{% url 'finances:generate_income_statement' %}"
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group button-slide-in"
      >
        <i
          class="fas fa-download text-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        ></i>
        <span>Download PDF</span>
      </a>
    </div>
  </div>

  <!-- Financial Summary Cards -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 summary-cards-fade-in">
    <!-- Revenue Summary Card -->
    <div
      class="summary-card group relative overflow-hidden bg-gradient-to-br from-[#74C69D]/20 via-white to-[#E2F1F9] border-l-4 border-[#74C69D] hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 p-8 rounded-2xl"
    >
      <div
        class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#74C69D]/20 to-[#5fb085]/10 rounded-full -translate-y-10 translate-x-10 group-hover:scale-125 transition-transform duration-700"
      ></div>

      <div class="flex items-center gap-4 mb-6 relative z-10">
        <div
          class="w-14 h-14 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-300"
        >
          <i class="fas fa-arrow-up text-white text-xl"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-xl">Total Revenue</h3>
          <p class="text-[#40657F] text-sm">Income generated</p>
        </div>
      </div>

      <div class="relative z-10">
        <p
          class="text-3xl md:text-4xl font-bold text-[#74C69D] font-display counter-animate leading-none"
        >
          MWK {{total_revenue|intcomma}}
        </p>
      </div>
    </div>

    <!-- Expenses Summary Card -->
    <div
      class="summary-card group relative overflow-hidden bg-gradient-to-br from-[#F28C8C]/20 via-white to-[#E2F1F9] border-l-4 border-[#F28C8C] hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 p-8 rounded-2xl"
    >
      <div
        class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#F28C8C]/20 to-[#e07575]/10 rounded-full -translate-y-10 translate-x-10 group-hover:scale-125 transition-transform duration-700"
      ></div>

      <div class="flex items-center gap-4 mb-6 relative z-10">
        <div
          class="w-14 h-14 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-300"
        >
          <i class="fas fa-arrow-down text-white text-xl"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-xl">Total Expenses</h3>
          <p class="text-[#40657F] text-sm">Money spent</p>
        </div>
      </div>

      <div class="relative z-10">
        <p
          class="text-3xl md:text-4xl font-bold text-[#F28C8C] font-display counter-animate leading-none"
        >
          MWK {{total_expense|intcomma}}
        </p>
      </div>
    </div>

    <!-- Net Profit Summary Card -->
    <div
      class="summary-card group relative overflow-hidden bg-gradient-to-br from-[#7AB2D3]/20 via-white to-[#E2F1F9] border-l-4 border-[#7AB2D3] hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 p-8 rounded-2xl"
    >
      <div
        class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 rounded-full -translate-y-10 translate-x-10 group-hover:scale-125 transition-transform duration-700"
      ></div>

      <div class="flex items-center gap-4 mb-6 relative z-10">
        <div
          class="w-14 h-14 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-300"
        >
          <i class="fas fa-chart-pie text-white text-xl"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-xl">Net Profit</h3>
          <p class="text-[#40657F] text-sm">Revenue - Expenses</p>
        </div>
      </div>

      <div class="relative z-10">
        <p
          class="text-3xl md:text-4xl font-bold text-[#7AB2D3] font-display counter-animate leading-none"
        >
          MWK {{net_profit|intcomma}}
        </p>
      </div>
    </div>
  </div>

  <!-- Detailed Financial Table -->
  <div class="card-modern p-8 table-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg section-icon-float"
      >
        <i class="fas fa-table text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Detailed Financial Statement
        </h3>
        <p class="text-[#40657F] text-sm">
          Complete breakdown of revenue and expenses
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div
      class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg"
    >
      <table class="min-w-full bg-white">
        <!-- Table Header -->
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-8 py-6 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-3">
                <i class="fas fa-list text-[#7AB2D3]"></i>
                <span>Category</span>
              </div>
            </th>
            <th
              class="px-8 py-6 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-3">
                <i class="fas fa-info-circle text-[#74C69D]"></i>
                <span>Description</span>
              </div>
            </th>
            <th
              class="px-8 py-6 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-end gap-3">
                <i class="fas fa-money-bill text-[#F28C8C]"></i>
                <span>Amount</span>
              </div>
            </th>
          </tr>
        </thead>

        <!-- Revenue Section -->
        <tbody class="divide-y divide-[#B9D8EB]/30">
          <tr
            class="bg-gradient-to-r from-[#74C69D]/10 to-[#74C69D]/5 revenue-section-header"
          >
            <td class="px-8 py-5 font-bold text-[#74C69D] text-lg">
              <div class="flex items-center gap-3">
                <i class="fas fa-arrow-up"></i>
                <span>REVENUE</span>
              </div>
            </td>
            <td class="px-8 py-5"></td>
            <td class="px-8 py-5"></td>
          </tr>

          {% for revenue in revenue %}
          <tr
            class="revenue-row hover:bg-[#74C69D]/5 transition-colors duration-200"
          >
            <td class="px-8 py-4 text-[#2C3E50] font-medium">
              {{revenue.name}}
            </td>
            <td class="px-8 py-4 text-[#40657F]">Revenue stream</td>
            <td class="px-8 py-4 text-right font-bold text-[#74C69D] text-lg">
              MWK {{revenue.total_amount|intcomma}}
            </td>
          </tr>
          {% endfor %}

          <tr class="bg-[#74C69D]/20 font-bold revenue-total">
            <td class="px-8 py-5 text-[#2C3E50] text-lg">TOTAL REVENUE</td>
            <td class="px-8 py-5"></td>
            <td
              class="px-8 py-5 text-right text-[#74C69D] text-xl font-display"
            >
              MWK {{total_revenue|intcomma}}
            </td>
          </tr>

          <!-- Spacer Row -->
          <tr class="spacer-row">
            <td class="px-8 py-4"></td>
            <td class="px-8 py-4"></td>
            <td class="px-8 py-4"></td>
          </tr>

          <!-- Expenses Section -->
          <tr
            class="bg-gradient-to-r from-[#F28C8C]/10 to-[#F28C8C]/5 expense-section-header"
          >
            <td class="px-8 py-5 font-bold text-[#F28C8C] text-lg">
              <div class="flex items-center gap-3">
                <i class="fas fa-arrow-down"></i>
                <span>EXPENSES</span>
              </div>
            </td>
            <td class="px-8 py-5"></td>
            <td class="px-8 py-5"></td>
          </tr>

          {% for expense in expenses %}
          <tr
            class="expense-row hover:bg-[#F28C8C]/5 transition-colors duration-200"
          >
            <td class="px-8 py-4 text-[#2C3E50] font-medium">
              {{expense.name}}
            </td>
            <td class="px-8 py-4 text-[#40657F]">Operating expense</td>
            <td class="px-8 py-4 text-right font-bold text-[#F28C8C] text-lg">
              MWK {{expense.total_amount|intcomma}}
            </td>
          </tr>
          {% endfor %}

          <tr class="bg-[#F28C8C]/20 font-bold expense-total">
            <td class="px-8 py-5 text-[#2C3E50] text-lg">TOTAL EXPENSES</td>
            <td class="px-8 py-5"></td>
            <td
              class="px-8 py-5 text-right text-[#F28C8C] text-xl font-display"
            >
              MWK {{total_expense|intcomma}}
            </td>
          </tr>

          <!-- Spacer Row -->
          <tr class="spacer-row">
            <td class="px-8 py-4"></td>
            <td class="px-8 py-4"></td>
            <td class="px-8 py-4"></td>
          </tr>

          <!-- Net Profit Section -->
          <tr
            class="bg-gradient-to-r from-[#7AB2D3]/20 to-[#40657F]/10 net-profit-row"
          >
            <td class="px-8 py-6 font-bold text-[#2C3E50] text-xl">
              <div class="flex items-center gap-3">
                <i class="fas fa-chart-pie text-[#7AB2D3]"></i>
                <span>NET PROFIT</span>
              </div>
            </td>
            <td class="px-8 py-6 text-[#40657F] font-medium">
              Revenue - Expenses
            </td>
            <td
              class="px-8 py-6 text-right text-[#7AB2D3] text-2xl font-display font-bold"
            >
              MWK {{net_profit|intcomma}}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .button-slide-in {
    opacity: 0;
    transform: translateX(50px);
    animation: buttonSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Summary Cards Animations */
  .summary-cards-fade-in {
    opacity: 0;
    animation: summaryCardsFadeIn 0.8s ease-out 1s forwards;
  }

  .summary-card {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    animation: summaryCardSlideUp 0.6s ease-out forwards;
  }

  .summary-card:nth-child(1) {
    animation-delay: 1.1s;
  }
  .summary-card:nth-child(2) {
    animation-delay: 1.2s;
  }
  .summary-card:nth-child(3) {
    animation-delay: 1.3s;
  }

  .counter-animate {
    opacity: 0;
    animation: counterFadeIn 1s ease-out 1.6s forwards;
  }

  /* Table Animations */
  .table-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: tableFadeIn 0.8s ease-out 1.4s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 4s ease-in-out infinite;
  }

  .revenue-section-header {
    opacity: 0;
    animation: sectionHeaderSlide 0.6s ease-out 1.8s forwards;
  }

  .revenue-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: rowSlideIn 0.4s ease-out forwards;
  }

  .revenue-row:nth-child(3) {
    animation-delay: 1.9s;
  }
  .revenue-row:nth-child(4) {
    animation-delay: 2s;
  }
  .revenue-row:nth-child(5) {
    animation-delay: 2.1s;
  }
  .revenue-row:nth-child(6) {
    animation-delay: 2.2s;
  }

  .revenue-total {
    opacity: 0;
    animation: totalRowHighlight 0.8s ease-out 2.4s forwards;
  }

  .expense-section-header {
    opacity: 0;
    animation: sectionHeaderSlide 0.6s ease-out 2.6s forwards;
  }

  .expense-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: rowSlideIn 0.4s ease-out forwards;
  }

  .expense-row:nth-child(9) {
    animation-delay: 2.7s;
  }
  .expense-row:nth-child(10) {
    animation-delay: 2.8s;
  }
  .expense-row:nth-child(11) {
    animation-delay: 2.9s;
  }
  .expense-row:nth-child(12) {
    animation-delay: 3s;
  }

  .expense-total {
    opacity: 0;
    animation: totalRowHighlight 0.8s ease-out 3.2s forwards;
  }

  .net-profit-row {
    opacity: 0;
    transform: scale(0.95);
    animation: netProfitReveal 1s ease-out 3.4s forwards;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes buttonSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes summaryCardsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes summaryCardSlideUp {
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes counterFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes tableFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes sectionIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(3deg);
    }
  }

  @keyframes sectionHeaderSlide {
    to {
      opacity: 1;
    }
  }

  @keyframes rowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes totalRowHighlight {
    to {
      opacity: 1;
    }
  }

  @keyframes netProfitReveal {
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Hover Enhancements */
  .summary-card:hover .counter-animate {
    animation: counterPulse 0.6s ease-in-out;
    opacity: 1 !important;
  }

  @keyframes counterPulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 1;
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .summary-card {
      animation-delay: 0.5s;
    }

    .summary-card:nth-child(n) {
      animation-delay: calc(0.5s + 0.1s * var(--card-index, 1));
    }
  }
</style>

{% endblock%}
