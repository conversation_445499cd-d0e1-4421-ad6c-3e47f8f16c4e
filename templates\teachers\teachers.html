{% extends 'academics/base.html' %} {% load static %}
<!--  -->
{% block title %}Teachers | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i
            class="fas fa-chalkboard-teacher text-white text-lg sm:text-xl icon-pulse"
          ></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Teaching Staff
          </h1>
          <p
            class="text-[#40657F] text-base sm:text-lg font-medium mt-1 subtitle-fade-in"
          >
            Manage and view teacher information
          </p>
          <div
            class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3 action-buttons-slide-in">
        <a
          href="{% url 'accounts:add_teacher' %}"
          class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-user-plus mr-2 group-hover:rotate-12 transition-transform duration-300"
          ></i>
          Add Teacher
        </a>
        <button
          class="bg-[#40657F] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#2C3E50] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-download mr-2 group-hover:translate-y-[-2px] transition-transform duration-300"
          ></i>
          Export List
        </button>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200 breadcrumb-fade-in"
    >
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">All Teachers</span>
    </nav>
  </div>

  <!-- Search Section -->
  <div class="card-modern p-6 search-section-fade-in">
    <div class="flex items-center gap-3 mb-6">
      <div
        class="w-8 h-8 sm:w-10 sm:h-10 bg-[#7AB2D3] rounded-lg sm:rounded-xl flex items-center justify-center shadow-md"
      >
        <i class="fas fa-search text-white text-xs sm:text-sm"></i>
      </div>
      <div>
        <h3 class="text-lg sm:text-xl font-bold text-[#2C3E50] font-display">
          Teacher Search
        </h3>
        <p class="text-[#40657F] text-sm">
          Find teachers by name, subject, or ID
        </p>
      </div>
    </div>

    <form action="" class="flex flex-col sm:flex-row gap-4">
      <div class="flex-1 relative">
        <input
          class="w-full pl-12 pr-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3]/30 focus:border-[#7AB2D3] outline-none transition-all duration-300 text-sm bg-white"
          type="text"
          name="search"
          id="search"
          placeholder="Search by name, subject, or ID..."
        />
        <i
          class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#B9D8EB] text-sm"
        ></i>
      </div>
      <button
        class="bg-[#7AB2D3] text-white font-semibold py-3 px-8 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        type="submit"
      >
        <i
          class="fas fa-search mr-2 group-hover:scale-110 transition-transform duration-300"
        ></i>
        Search Teachers
      </button>
    </form>
  </div>

  <!-- Messages -->
  {% if message %}
  <div
    class="card-modern p-4 border-l-4 border-[#74C69D] bg-[#74C69D]/10 message-fade-in"
  >
    <div class="flex items-center gap-3">
      <i class="fas fa-check-circle text-[#74C69D]"></i>
      <p class="text-[#74C69D] font-medium">{{ message }}</p>
    </div>
  </div>
  {% endif %}

  <!-- Teachers Grid -->
  <div class="teachers-grid-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-users text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Teaching Staff Directory
        </h2>
        <p class="text-[#40657F] text-sm">
          {{ teachers|length }} teacher{{ teachers|length|pluralize }} in our
          team
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
    >
      {% for teacher in teachers %}
      <div
        class="teacher-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-6 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45 group-hover:from-[#7AB2D3]/20 group-hover:to-[#40657F]/20"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10 rounded-full translate-y-8 -translate-x-8 transition-all duration-700 group-hover:scale-150 group-hover:-rotate-45 group-hover:from-[#74C69D]/25 group-hover:to-[#5fb085]/25"
        ></div>

        <!-- Teacher Photo -->
        <div class="flex flex-col items-center gap-4 relative z-10">
          <div class="relative">
            <img
              class="rounded-2xl border-4 border-white shadow-lg w-20 h-20 object-cover group-hover:scale-105 transition-transform duration-300"
              src="{% static 'img/student1.jpg' %}"
              alt="{{teacher.first_name}} {{teacher.last_name}}"
              width="80"
              height="80"
            />
            <div
              class="absolute -bottom-1 -right-1 w-6 h-6 bg-[#74C69D] rounded-full flex items-center justify-center border-2 border-white"
            >
              {% if teacher.is_staff %}
              <i class="fas fa-crown text-white text-xs"></i>
              {% else %}
              <i class="fas fa-chalkboard-teacher text-white text-xs"></i>
              {% endif %}
            </div>
          </div>

          <!-- Teacher Info -->
          <div class="text-center">
            <h3
              class="font-bold text-lg text-[#2C3E50] font-display group-hover:text-[#7AB2D3] transition-colors duration-300"
            >
              {{ teacher.first_name}} {{teacher.last_name}}
            </h3>
            {% if teacher.is_staff %}
            <span
              class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30 mt-1"
            >
              <i class="fas fa-user-shield mr-1"></i>
              Administrator
            </span>
            {% else %}
            <span
              class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30 mt-1"
            >
              <i class="fas fa-chalkboard-teacher mr-1"></i>
              Teacher
            </span>
            {% endif %}
          </div>
          <!-- Subjects -->
          <div class="mt-4">
            <div class="flex items-center justify-center gap-2 mb-2">
              <i class="fas fa-book text-[#40657F] text-sm"></i>
              <span
                class="text-xs font-bold text-[#40657F] uppercase tracking-wider"
                >Subjects</span
              >
            </div>
            <div class="flex flex-wrap justify-center gap-2">
              {% for subject in teacher.subject_assignments.all %}
              <span
                class="inline-flex items-center px-2 py-1 rounded-lg text-xs font-semibold {% cycle 'bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30' 'bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30' 'bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30' 'bg-[#40657F]/20 text-[#40657F] border border-[#40657F]/30' %}"
              >
                <i class="fas fa-graduation-cap mr-1"></i>
                {{ subject.subject }}
              </span>
              {% empty %}
              <span class="text-xs text-[#B9D8EB] italic"
                >No subjects assigned</span
              >
              {% endfor %}
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-2 mt-6 w-full">
            <a
              href="{% url 'accounts:teacher_details' teacher.id %}"
              class="flex-1 bg-[#7AB2D3] text-white font-semibold py-2 px-2 rounded-lg hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 shadow-md hover:shadow-lg text-center text-sm group w-full"
            >
              <i
                class="fas fa-eye mr-1 group-hover:scale-110 transition-transform duration-300"
              ></i>
              View
            </a>
            <a
              href="{% url 'accounts:edit_teacher' teacher.id %}"
              class="flex-1 bg-[#40657F] text-white font-semibold py-2 px-4 rounded-lg hover:bg-[#2C3E50] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 shadow-md hover:shadow-lg text-center text-sm group w-full"
            >
              <i
                class="fas fa-edit mr-1 group-hover:scale-110 transition-transform duration-300"
              ></i>
              Edit
            </a>
          </div>
        </div>
      </div>
      {% empty %}
      <div class="col-span-full text-center py-16">
        <div class="flex flex-col items-center gap-6">
          <div
            class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-chalkboard-teacher text-gray-400 text-3xl"></i>
          </div>
          <div>
            <h3 class="font-display font-bold text-2xl text-gray-800 mb-2">
              No Teachers Found
            </h3>
            <p class="text-gray-600 text-lg">
              No teaching staff members have been added yet.
            </p>
            <p class="text-gray-500 text-sm mt-2">
              Add teachers to get started with staff management.
            </p>
          </div>
          <a
            href="{% url 'accounts:add_teacher' %}"
            class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg"
          >
            <i class="fas fa-user-plus mr-2"></i>
            Add First Teacher
          </a>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Search Section Animations */
  .search-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: searchSectionFadeIn 0.8s ease-out 1s forwards;
  }

  /* Message Animations */
  .message-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: messageFadeIn 0.8s ease-out 1.2s forwards;
  }

  /* Teachers Grid Animations */
  .teachers-grid-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: teachersGridFadeIn 0.8s ease-out 1.4s forwards;
  }

  .teacher-card {
    opacity: 0;
    transform: translateY(30px);
    animation: teacherCardSlideIn 0.6s ease-out forwards;
  }

  .teacher-card:nth-child(1) {
    animation-delay: 1.6s;
  }
  .teacher-card:nth-child(2) {
    animation-delay: 1.7s;
  }
  .teacher-card:nth-child(3) {
    animation-delay: 1.8s;
  }
  .teacher-card:nth-child(4) {
    animation-delay: 1.9s;
  }
  .teacher-card:nth-child(5) {
    animation-delay: 2s;
  }
  .teacher-card:nth-child(6) {
    animation-delay: 2.1s;
  }
  .teacher-card:nth-child(7) {
    animation-delay: 2.2s;
  }
  .teacher-card:nth-child(8) {
    animation-delay: 2.3s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes searchSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes messageFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes teachersGridFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes teacherCardSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .teacher-card {
      animation-delay: 1.4s;
    }

    .teacher-card:nth-child(n) {
      animation-delay: calc(1.4s + 0.1s * var(--card-index, 1));
    }
  }
</style>

{% endblock %}
