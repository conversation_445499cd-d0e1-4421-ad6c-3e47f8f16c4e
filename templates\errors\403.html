{% load static %}
<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Access Denied - {{request.tenant.name|default:"Tiny Feet Academy"}}
    </title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
    />

    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-color: #7ab2d3;
        --primary-dark: #5a9bd4;
        --warning-color: #f59e0b;
        --warning-dark: #d97706;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .font-display {
        font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .floating {
        animation: float 6s ease-in-out infinite;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-20px);
        }
      }

      .pulse-warning {
        animation: pulse-warning 3s ease-in-out infinite;
      }

      @keyframes pulse-warning {
        0%,
        100% {
          box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
        }
        50% {
          box-shadow: 0 0 40px rgba(245, 158, 11, 0.6);
        }
      }

      .slide-in {
        animation: slideIn 0.8s ease-out forwards;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body
    class="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-50 flex flex-col items-center justify-center p-4"
  >
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div
        class="absolute -top-40 -right-40 w-80 h-80 bg-yellow-100 opacity-20 rounded-full floating"
      ></div>
      <div
        class="absolute -bottom-40 -left-40 w-96 h-96 bg-orange-100 opacity-15 rounded-full floating"
        style="animation-delay: -3s"
      ></div>
      <div
        class="absolute top-1/2 left-1/4 w-32 h-32 bg-yellow-100 opacity-25 rounded-full floating"
        style="animation-delay: -1s"
      ></div>
    </div>

    <!-- Error Content -->
    <div class="relative z-10 max-w-2xl mx-auto text-center slide-in">
      <!-- Error Icon -->
      <div class="mb-8">
        <div
          class="w-32 h-32 mx-auto bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full flex items-center justify-center shadow-2xl pulse-warning"
        >
          <i class="fas fa-shield-alt text-white text-4xl"></i>
        </div>
      </div>

      <!-- Error Code -->
      <div class="mb-6">
        <h1
          class="font-display font-bold text-8xl md:text-9xl text-gray-800 mb-2"
        >
          403
        </h1>
        <div
          class="w-24 h-1 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full mx-auto"
        ></div>
      </div>

      <!-- Error Message -->
      <div class="mb-8">
        <h2
          class="font-display font-bold text-2xl md:text-3xl text-gray-800 mb-4"
        >
          Hold Up There, Curious Cat! 🚫
        </h2>
        <p class="text-gray-600 text-lg leading-relaxed max-w-lg mx-auto mb-4">
          Whoa there, explorer! You're trying to access something that's more
          restricted than the teacher's lounge coffee. This area requires
          special permissions that you currently don't have.
        </p>
        <div
          class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-xl max-w-md mx-auto"
        >
          <p class="text-red-800 text-sm font-medium">
            <i class="fas fa-user-shield mr-2"></i>
            <strong>Security Lesson:</strong> HTTP 403 errors protect sensitive
            resources from unauthorized access. Think of it as the digital
            equivalent of "You must be THIS tall to ride" - except it's "You
            must have THIS permission to view"! 🎢
          </p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <a
          href="/"
          class="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] text-white font-semibold py-3 px-8 rounded-xl hover:from-[var(--primary-dark)] hover:to-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-home mr-2"></i>
          Go Home
        </a>
        <a
          href="/accounts/login/"
          class="bg-white text-gray-700 font-semibold py-3 px-8 rounded-xl border-2 border-gray-200 hover:border-yellow-500 hover:text-yellow-600 focus:ring-4 focus:ring-yellow-500/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-sign-in-alt mr-2"></i>
          Login
        </a>
      </div>

      <!-- Help Information -->
      <div
        class="mt-12 p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-yellow-200/50 shadow-lg"
      >
        <h3
          class="font-semibold text-gray-800 mb-3 flex items-center justify-center gap-2"
        >
          <i class="fas fa-question-circle text-yellow-500"></i>
          Why am I seeing this?
        </h3>
        <div class="text-sm text-gray-600 leading-relaxed space-y-2">
          <p>• You may not be logged in to the system</p>
          <p>• Your account may not have the required permissions</p>
          <p>• The resource may be restricted to certain user roles</p>
          <p>• Your session may have expired</p>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="mt-8 text-center">
      <p class="text-sm text-gray-500 flex items-center justify-center gap-2">
        <i class="fas fa-graduation-cap text-[var(--primary-color)]"></i>
        Tiny Feet Academy MIS
      </p>
    </footer>
  </body>
</html>
