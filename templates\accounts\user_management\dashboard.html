{% extends 'base.html' %}
{% load static %}
{% load rbac_tags %}

{% block title %}User Management | {% endblock %}

{% block content %}
<!-- Add RBAC context to template -->
{% rbac_context %}

<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex flex-col sm:flex-row items-center justify-between mb-4">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg icon-float">
          <i class="fas fa-users-cog text-white text-xl icon-pulse"></i>
        </div>
        <div>
          <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
            User Management
          </h1>
          <div class="w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full accent-line-grow"></div>
        </div>
      </div>
      
      {% if user|has_any_permission:"add_teacher,manage_students,manage_system" %}
      div
      <a href="{% url 'accounts:create_user' %}" 
         class="btn-primary flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 bg-gradient-to-br from-[#74C69D] to-[#5fb085] w-full sm:w-fit text-center justify-center">
        <i class="fas fa-plus"></i>
        Add New User
      </a>

      {% endif %}
    </div>
    
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">User Management</span>
    </nav>
  </div>

  <!-- Statistics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
    <div class="card-modern p-6 text-center stats-card-slide-in">
      <div class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl mx-auto mb-4 flex items-center justify-center">
        <i class="fas fa-users text-white text-lg"></i>
      </div>
      <div class="text-2xl font-bold text-[#2C3E50] mb-1">{{ stats.total_users }}</div>
      <div class="text-sm text-[#40657F]">Total Users</div>
    </div>
    
    <div class="card-modern p-6 text-center stats-card-slide-in" style="animation-delay: 0.1s">
      <div class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl mx-auto mb-4 flex items-center justify-center">
        <i class="fas fa-user-check text-white text-lg"></i>
      </div>
      <div class="text-2xl font-bold text-[#2C3E50] mb-1">{{ stats.active_users }}</div>
      <div class="text-sm text-[#40657F]">Active Users</div>
    </div>
    
    <div class="card-modern p-6 text-center stats-card-slide-in" style="animation-delay: 0.2s">
      <div class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl mx-auto mb-4 flex items-center justify-center">
        <i class="fas fa-user-times text-white text-lg"></i>
      </div>
      <div class="text-2xl font-bold text-[#2C3E50] mb-1">{{ stats.inactive_users }}</div>
      <div class="text-sm text-[#40657F]">Inactive Users</div>
    </div>
    
    <div class="card-modern p-6 text-center stats-card-slide-in" style="animation-delay: 0.3s">
      <div class="w-12 h-12 bg-gradient-to-br from-[#FFB84D] to-[#e6a43d] rounded-xl mx-auto mb-4 flex items-center justify-center">
        <i class="fas fa-user-tie text-white text-lg"></i>
      </div>
      <div class="text-2xl font-bold text-[#2C3E50] mb-1">{{ stats.staff_users }}</div>
      <div class="text-sm text-[#40657F]">Staff Users</div>
    </div>
    
    <div class="card-modern p-6 text-center stats-card-slide-in" style="animation-delay: 0.4s">
      <div class="w-12 h-12 bg-gradient-to-br from-[#9B59B6] to-[#8e44ad] rounded-xl mx-auto mb-4 flex items-center justify-center">
        <i class="fas fa-crown text-white text-lg"></i>
      </div>
      <div class="text-2xl font-bold text-[#2C3E50] mb-1">{{ stats.superusers }}</div>
      <div class="text-sm text-[#40657F]">Superusers</div>
    </div>
  </div>

  <!-- Role Statistics -->
  <div class="card-modern p-8">
    <div class="flex items-center gap-4 mb-6">
      <div class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg">
        <i class="fas fa-user-shield text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">Role Distribution</h3>
        <p class="text-[#40657F] text-sm">Users by role assignment</p>
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {% for role_stat in role_stats %}
      <div class="p-4 bg-[#F7FAFC] rounded-xl border border-[#E2F1F9]">
        <div class="flex items-center justify-between">
          <div>
            <div class="font-semibold text-[#2C3E50]">{{ role_stat.role.display_name }}</div>
            <div class="text-sm text-[#40657F]">Level {{ role_stat.role.level }}</div>
          </div>
          <div class="text-2xl font-bold text-[#7AB2D3]">{{ role_stat.user_count }}</div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>

  <!-- Search Section -->
  <div class="card-modern p-8">
    <div class="flex items-center gap-4 mb-6">
      <div class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg">
        <i class="fas fa-search text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">Search Users</h3>
        <p class="text-[#40657F] text-sm">Find users by username, name, or email</p>
      </div>
    </div>

    <form method="GET" class="mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
          <input type="text" 
                 name="q" 
                 value="{{ search_query }}" 
                 placeholder="Search by username, name, or email..." 
                 class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] placeholder-[#40657F]/60">
        </div>
        <button type="submit" 
                class="px-8 py-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105 group">
          <i class="fas fa-search mr-2 group-hover:rotate-12 transition-all duration-300"></i>Search
        </button>
      </div>
    </form>

    {% if search_query %}
    <div class="mb-6 p-4 bg-[#E2F1F9] rounded-xl border border-[#B9D8EB]">
      <p class="text-[#40657F] font-medium">
        <i class="fas fa-info-circle mr-2"></i>
        Showing results for: <span class="font-bold">"{{ search_query }}"</span>
        ({{ total_count }} user{{ total_count|pluralize }} found)
      </p>
    </div>
    {% endif %}
  </div>

  <!-- Users List -->
  <div class="card-modern p-8">
    <div class="flex items-center justify-between mb-6 w-full">
      <div class="flex items-center gap-4 w-full">
        <div class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg">
          <i class="fas fa-list text-white text-lg"></i>
        </div>
        <div>
          <h3 class="text-2xl font-bold text-[#2C3E50] font-display">Users Directory</h3>
          <p class="text-[#40657F] text-sm">{{ total_count }} user{{ total_count|pluralize }} total</p>
        </div>
      </div>
      
    </div>

    {% if page_obj %}
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-[#B9D8EB]">
            <th class="text-left py-4 px-4 font-semibold text-[#2C3E50]">User</th>
            <th class="text-left py-4 px-4 font-semibold text-[#2C3E50]">Email</th>
            <th class="text-left py-4 px-4 font-semibold text-[#2C3E50]">Roles</th>
            <th class="text-left py-4 px-4 font-semibold text-[#2C3E50]">Status</th>
            <th class="text-center py-4 px-4 font-semibold text-[#2C3E50]">Actions</th>
          </tr>
        </thead>
        <tbody class="text-nowrap">
          {% for user_obj in page_obj %}
          <tr class="border-b border-[#E2F1F9] hover:bg-[#F7FAFC] transition-colors duration-200">
            <td class="py-4 px-4">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-full flex items-center justify-center text-white font-semibold">
                  {{ user_obj.first_name|first|upper }}{{ user_obj.last_name|first|upper }}
                </div>
                <div>
                  <div class="font-semibold text-[#2C3E50]">{{ user_obj.get_full_name|default:user_obj.username }}</div>
                  <div class="text-sm text-[#40657F]">@{{ user_obj.username }}</div>
                </div>
              </div>
            </td>
            <td class="py-4 px-4">
              <span class="text-[#40657F]">{{ user_obj.email|default:"No email" }}</span>
            </td>
            <td class="py-4 px-4">
              {% for user_role in user_obj.get_active_roles|slice:":2" %}
                <span class="inline-block px-2 py-1 bg-[#E2F1F9] text-[#40657F] rounded-full text-xs font-medium mr-1 mb-1">
                  {{ user_role.role.display_name }}
                </span>
              {% empty %}
                <span class="text-[#B9D8EB] text-sm">No roles</span>
              {% endfor %}
              {% if user_obj.get_active_roles.count > 2 %}
                <span class="text-[#7AB2D3] text-xs">+{{ user_obj.get_active_roles.count|add:"-2" }} more</span>
              {% endif %}
            </td>
            <td class="py-4 px-4">
              {% if user_obj.is_superuser %}
                <span class="px-3 py-1 bg-[#9B59B6] text-white rounded-full text-sm font-semibold">
                  <i class="fas fa-crown mr-1"></i>Superuser
                </span>
              {% elif user_obj.is_active %}
                <span class="px-3 py-1 bg-[#74C69D] text-white rounded-full text-sm font-semibold">
                  <i class="fas fa-check-circle mr-1"></i>Active
                </span>
              {% else %}
                <span class="px-3 py-1 bg-[#F28C8C] text-white rounded-full text-sm font-semibold">
                  <i class="fas fa-times-circle mr-1"></i>Inactive
                </span>
              {% endif %}
            </td>
            <td class="py-4 px-4 text-center">
              <div class="flex items-center justify-center gap-2">
                <a href="{% url 'accounts:user_detail' user_obj.id %}" 
                   class="px-3 py-2 bg-[#7AB2D3] text-white rounded-lg hover:bg-[#40657F] transition-colors duration-200"
                   title="View Details">
                  <i class="fas fa-eye"></i>
                </a>
                
                {% if user.get_highest_role_level >= user_obj.get_highest_role_level %}
                <a href="{% url 'accounts:edit_user' user_obj.id %}" 
                   class="px-3 py-2 bg-[#74C69D] text-white rounded-lg hover:bg-[#5fb085] transition-colors duration-200"
                   title="Edit User">
                  <i class="fas fa-edit"></i>
                </a>
                {% endif %}
                
                {% if user|has_permission:"manage_system" %}
                <a href="{% url 'accounts:assign_role' user_obj.id %}" 
                   class="px-3 py-2 bg-[#FFB84D] text-white rounded-lg hover:bg-[#e6a43d] transition-colors duration-200"
                   title="Assign Role">
                  <i class="fas fa-user-plus"></i>
                </a>
                {% endif %}
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex justify-center mt-8">
      <nav class="flex items-center gap-2">
        {% if page_obj.has_previous %}
          <a href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}" 
             class="px-3 py-2 bg-[#E2F1F9] text-[#40657F] rounded-lg hover:bg-[#B9D8EB] transition-colors">
            <i class="fas fa-angle-double-left"></i>
          </a>
          <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}" 
             class="px-3 py-2 bg-[#E2F1F9] text-[#40657F] rounded-lg hover:bg-[#B9D8EB] transition-colors">
            <i class="fas fa-angle-left"></i>
          </a>
        {% endif %}
        
        <span class="px-4 py-2 bg-[#7AB2D3] text-white rounded-lg font-semibold">
          {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
        </span>
        
        {% if page_obj.has_next %}
          <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}" 
             class="px-3 py-2 bg-[#E2F1F9] text-[#40657F] rounded-lg hover:bg-[#B9D8EB] transition-colors">
            <i class="fas fa-angle-right"></i>
          </a>
          <a href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}" 
             class="px-3 py-2 bg-[#E2F1F9] text-[#40657F] rounded-lg hover:bg-[#B9D8EB] transition-colors">
            <i class="fas fa-angle-double-right"></i>
          </a>
        {% endif %}
      </nav>
    </div>
    {% endif %}
    
    {% else %}
    <div class="text-center py-12">
      <div class="w-24 h-24 bg-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-users text-[#7AB2D3] text-3xl"></i>
      </div>
      <h3 class="text-xl font-bold text-[#2C3E50] mb-2">No Users Found</h3>
      <p class="text-[#40657F] mb-6">
        {% if search_query %}
          No users match your search criteria. Try a different search term.
        {% else %}
          No users have been created yet.
        {% endif %}
      </p>
      {% if user|has_any_permission:"add_teacher,manage_students,manage_system" %}
      <a href="{% url 'accounts:create_user' %}" 
         class="btn-primary inline-flex items-center gap-2 px-6 py-3 rounded-xl font-semibold">
        <i class="fas fa-plus"></i>
        Add First User
      </a>
      {% endif %}
    </div>
    {% endif %}
  </div>
</section>

{% endblock %}

<script>
// Add some interactivity for better UX
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh stats every 30 seconds
    setInterval(function() {
        // You could implement AJAX refresh here
    }, 30000);

    // Add loading states to action buttons
    const actionButtons = document.querySelectorAll('a[href*="edit"], a[href*="assign"]');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        });
    });
});
</script>
