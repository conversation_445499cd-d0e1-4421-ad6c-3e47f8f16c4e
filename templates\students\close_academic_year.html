{% extends 'base.html' %}
<!--  -->
{% block title %}{{ title }} | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex items-center gap-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#F28C8C] to-[#e74c3c] rounded-2xl flex items-center justify-center shadow-xl icon-float"
      >
        <i class="fas fa-times-circle text-white text-2xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
        >
          {{ title }}
        </h1>
        <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
          Permanently close an academic year and all its terms
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
  </div>

  <!-- Warning Section -->
  <div class="card-modern p-6 bg-gradient-to-r from-[#F28C8C]/10 to-[#e74c3c]/10 border-l-4 border-[#F28C8C] warning-fade-in">
    <div class="flex items-center gap-3 mb-4">
      <i class="fas fa-exclamation-triangle text-[#F28C8C] text-2xl"></i>
      <h3 class="font-bold text-[#F28C8C] text-xl">Important Warning</h3>
    </div>
    <div class="text-[#2C3E50] space-y-3">
      <p class="font-medium">
        Closing an academic year will:
      </p>
      <ul class="space-y-2 ml-4">
        <li class="flex items-start gap-2">
          <i class="fas fa-minus-circle text-[#F28C8C] mt-1 text-sm"></i>
          <span>Deactivate the academic year and make it read-only</span>
        </li>
        <li class="flex items-start gap-2">
          <i class="fas fa-minus-circle text-[#F28C8C] mt-1 text-sm"></i>
          <span>Deactivate all terms within this academic year</span>
        </li>
        <li class="flex items-start gap-2">
          <i class="fas fa-minus-circle text-[#F28C8C] mt-1 text-sm"></i>
          <span>Prevent any new data entry for this academic year</span>
        </li>
        <li class="flex items-start gap-2">
          <i class="fas fa-minus-circle text-[#F28C8C] mt-1 text-sm"></i>
          <span>Archive all academic records for this period</span>
        </li>
      </ul>
      <p class="font-medium text-[#F28C8C] mt-4">
        This action cannot be easily undone. Please ensure you have completed all necessary tasks for the academic year before proceeding.
      </p>
    </div>
  </div>

  <!-- Form Section -->
  <div class="card-modern p-8 form-fade-in">
    <form method="POST" class="space-y-6">
      {% csrf_token %}
      
      <!-- Academic Year Selection -->
      <div class="form-group">
        <label for="{{ form.academic_year.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-3">
          <div class="flex items-center gap-2">
            <i class="fas fa-calendar-alt text-[#F28C8C]"></i>
            {{ form.academic_year.label }}
          </div>
        </label>
        {{ form.academic_year }}
        {% if form.academic_year.help_text %}
        <p class="text-sm text-[#40657F]/70 mt-2">{{ form.academic_year.help_text }}</p>
        {% endif %}
        {% if form.academic_year.errors %}
        <div class="mt-2">
          {% for error in form.academic_year.errors %}
          <p class="text-sm text-[#F28C8C] flex items-center gap-2">
            <i class="fas fa-exclamation-circle"></i>
            {{ error }}
          </p>
          {% endfor %}
        </div>
        {% endif %}
      </div>

      <!-- Confirmation Checkbox -->
      <div class="form-group">
        <div class="flex items-start gap-3 p-4 bg-[#F28C8C]/5 border border-[#F28C8C]/20 rounded-xl">
          {{ form.confirm_closure }}
          <label for="{{ form.confirm_closure.id_for_label }}" class="text-sm font-medium text-[#2C3E50] cursor-pointer">
            {{ form.confirm_closure.label }}
          </label>
        </div>
        {% if form.confirm_closure.help_text %}
        <p class="text-sm text-[#40657F]/70 mt-2">{{ form.confirm_closure.help_text }}</p>
        {% endif %}
        {% if form.confirm_closure.errors %}
        <div class="mt-2">
          {% for error in form.confirm_closure.errors %}
          <p class="text-sm text-[#F28C8C] flex items-center gap-2">
            <i class="fas fa-exclamation-circle"></i>
            {{ error }}
          </p>
          {% endfor %}
        </div>
        {% endif %}
      </div>

      <!-- Form-wide Errors -->
      {% if form.non_field_errors %}
      <div class="bg-[#F28C8C]/10 border border-[#F28C8C]/30 rounded-xl p-4">
        <div class="flex items-center gap-2 mb-2">
          <i class="fas fa-exclamation-triangle text-[#F28C8C]"></i>
          <h4 class="font-bold text-[#F28C8C]">Please correct the following errors:</h4>
        </div>
        {% for error in form.non_field_errors %}
        <p class="text-sm text-[#F28C8C]">{{ error }}</p>
        {% endfor %}
      </div>
      {% endif %}

      <!-- Checklist Section -->
      <div class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] rounded-xl p-6 border border-[#B9D8EB]/50">
        <div class="flex items-center gap-3 mb-4">
          <i class="fas fa-clipboard-check text-[#7AB2D3] text-lg"></i>
          <h3 class="font-bold text-[#2C3E50]">Pre-Closure Checklist</h3>
        </div>
        <p class="text-[#40657F] text-sm mb-4">
          Please ensure you have completed the following before closing the academic year:
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <div class="flex items-center gap-2">
              <i class="fas fa-square text-[#B9D8EB]"></i>
              <span class="text-sm text-[#2C3E50]">All grades have been entered</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-square text-[#B9D8EB]"></i>
              <span class="text-sm text-[#2C3E50]">All fees have been collected</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-square text-[#B9D8EB]"></i>
              <span class="text-sm text-[#2C3E50]">Report cards have been generated</span>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex items-center gap-2">
              <i class="fas fa-square text-[#B9D8EB]"></i>
              <span class="text-sm text-[#2C3E50]">Student records are complete</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-square text-[#B9D8EB]"></i>
              <span class="text-sm text-[#2C3E50]">Financial records are finalized</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-square text-[#B9D8EB]"></i>
              <span class="text-sm text-[#2C3E50]">Data backup has been created</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-[#B9D8EB]/30">
        <button
          type="submit"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#F28C8C] to-[#e74c3c] text-white font-bold py-4 px-8 rounded-xl hover:from-[#e74c3c] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
          onclick="return confirm('Are you absolutely sure you want to close this academic year? This action cannot be easily undone.')"
        >
          <i
            class="fas fa-times-circle group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>{{ submit_text }}</span>
        </button>
        <a
          href="{% url 'students:academic_year_management' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-arrow-left group-hover:scale-110 group-hover:-translate-x-1 transition-all duration-300"
          ></i>
          <span>Cancel</span>
        </a>
      </div>
    </form>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  /* Warning Animation */
  .warning-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: warningFadeIn 0.8s ease-out 0.8s forwards;
  }

  /* Form Animation */
  .form-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: formFadeIn 0.8s ease-out 1s forwards;
  }

  /* Form Group Animation */
  .form-group {
    opacity: 0;
    transform: translateX(-20px);
    animation: formGroupSlideIn 0.4s ease-out forwards;
  }

  .form-group:nth-child(1) { animation-delay: 1.2s; }
  .form-group:nth-child(2) { animation-delay: 1.3s; }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes warningFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes formFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes formGroupSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  /* Form Styling */
  .form-group input,
  .form-group select {
    transition: all 0.3s ease;
  }

  .form-group input:focus,
  .form-group select:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(242, 140, 140, 0.15);
  }
</style>

{% endblock %}
