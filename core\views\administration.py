from django.shortcuts import render
from django.http import JsonResponse
from django.core.management import call_command
from accounts.decorators import admin_required, require_permission
import io
from contextlib import redirect_stdout, redirect_stderr
from finances.fee_management.models import FeeCategory


@admin_required
def administration(request):
    """Administration dashboard - requires administrator access"""
    context = {
        'user_role_level': request.user.get_highest_role_level(),
        'user_permissions': request.user.get_all_permissions(),
        'can_manage_system': request.user.has_permission('manage_system'),
        'can_manage_academic_periods': request.user.has_permission('manage_academic_periods'),
    }
    return render(request, 'students/administration.html', context)


@require_permission('manage_system')
def discrepancy_check(request):
    """Run system discrepancy checks - requires manage_system permission"""
    if request.method == 'POST':
        # Handle AJAX request for running specific checks
        check_type = request.POST.get('check_type', 'comprehensive')
        category = request.POST.get('category', '')
        term_id = request.POST.get('term_id', '')

        try:
            # Capture command output
            output_buffer = io.StringIO()
            error_buffer = io.StringIO()

            with redirect_stdout(output_buffer), redirect_stderr(error_buffer):
                if check_type == 'comprehensive':
                    call_command('diagnose_ledger_discrepancies')
                elif check_type == 'category' and category:
                    call_command('diagnose_ledger_discrepancies', category=category)
                elif check_type == 'term' and term_id:
                    call_command('diagnose_ledger_discrepancies', term_id=int(term_id))
                elif check_type == 'dynamic_amount' and category:
                    call_command('debug_dynamic_amount', category=category)

            output = output_buffer.getvalue()
            errors = error_buffer.getvalue()

            if errors:
                return JsonResponse({
                    'success': False,
                    'error': errors,
                    'output': output
                })

            return JsonResponse({
                'success': True,
                'output': output,
                'message': 'Discrepancy check completed successfully.'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e),
                'message': 'Error running discrepancy check.'
            })

    # GET request - show the discrepancy check interface
    context = {
        'title': 'System Discrepancy Check',
        'description': 'Analyze financial data integrity and identify inconsistencies',
    }
    return render(request, 'core/discrepancy_check.html', context)


@require_permission('manage_system')
def get_fee_categories(request):
    """Get active fee categories for dropdown - requires manage_system permission"""
    try:
        categories = FeeCategory.objects.filter(
            is_active=True
        ).values_list('name', flat=True).distinct().order_by('name')

        return JsonResponse({
            'success': True,
            'categories': list(categories)
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })
