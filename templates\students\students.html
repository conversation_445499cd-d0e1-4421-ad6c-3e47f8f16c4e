{% extends 'base.html' %} {% load static %}
<!--  -->
{% block title %}All Students | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg icon-float"
      >
        <i class="fas fa-users text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in"
        >
          All Students
        </h1>
        <div
          class="w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full accent-line-grow"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">All Students</span>
    </nav>
  </div>

  <!-- Search Section -->
  <div class="card-modern p-8 search-section-fade-in">
    <div class="flex items-center gap-4 mb-6">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg search-icon-float"
      >
        <i class="fas fa-search text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Search Students
        </h3>
        <p class="text-[#40657F] text-sm">
          Find students by ID, name, or class
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <form
      method="GET"
      name="q"
      action=""
      class="flex flex-col sm:flex-row gap-4 w-full search-form-slide-in"
    >
      <div class="relative flex-1">
        <input
          class="w-full bg-[#F7FAFC] px-6 py-4 pl-12 rounded-xl border-2 border-[#B9D8EB] focus:ring-4 focus:ring-[#7AB2D3]/20 focus:border-[#7AB2D3] focus:bg-white outline-none transition-all duration-300 font-medium placeholder-[#40657F]/60 text-[#2C3E50]"
          type="text"
          name="q"
          value="{{ search_query }}"
          placeholder="Search by Student ID, name, or class..."
        />
        <i
          class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#7AB2D3] text-lg"
        ></i>
      </div>
      <div class="flex gap-3">
        <button
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group w-full md:w-fit"
          type="submit"
        >
          <i
            class="fas fa-search group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Search</span>
        </button>
        {% if search_query %}
        <a
          href="{% url 'students:students' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-6 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-times group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Clear</span>
        </a>
        {% endif %}
      </div>
    </form>
  </div>

  <!-- Students Table -->
  <div class="card-modern p-8 table-section-fade-in">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6 mb-8"
    >
      <div class="flex flex-col md:flex-row items-center gap-4">
        <div class="flex items-center gap-4">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
          >
            <i class="fas fa-table text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
              Student Records
            </h3>
            <p class="text-[#40657F] text-sm">
              {% if search_query %} Search results for "{{ search_query }}"
              {%else%} Complete list of all enrolled students {% endif %}
            </p>
          </div>
        </div>
        <div
          class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
        ></div>
        <div
          class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30 w-full md:w-fit"
        >
          <i class="fas fa-users text-sm"></i>
          <span>{{ total_count }} student{{ total_count|pluralize }}</span>
        </div>
      </div>
      <a
        href="{% url 'students:admission' %}"
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group add-button-slide-in"
      >
        <i
          class="fas fa-user-plus group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        ></i>
        <span>Add Student</span>
      </a>
    </div>

    <div
      class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg table-fade-in"
    >
      <table class="min-w-full bg-white">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-hashtag text-[#7AB2D3]"></i>
                Student ID
              </div>
            </th>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-user text-[#40657F]"></i>
                Full Name
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-venus-mars text-[#F28C8C]"></i>
                Gender
              </div>
            </th>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-graduation-cap text-[#74C69D]"></i>
                Class Level
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#B9D8EB]/30 text-nowrap">
          {% for student in students %}
          <tr
            class="student-row hover:bg-[#E2F1F9]/50 transition-colors duration-200"
          >
            <td class="px-6 py-4">
              <a
                href="{% url 'students:student_details' student.student_id %}"
                class="inline-flex items-center gap-2 text-[#7AB2D3] hover:text-[#40657F] font-bold hover:underline transition-colors duration-200"
              >
                <i class="fas fa-external-link-alt text-xs"></i>
                {{ student.student_id }}
              </a>
            </td>
            <td class="px-6 py-4">
              <div class="flex items-center gap-3">
                <div
                  class="w-8 h-8 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-full flex items-center justify-center text-white text-xs font-bold"
                >
                  {{ student.name|first }}
                </div>
                <span class="font-medium text-[#2C3E50]"
                  >{{ student.name }}</span
                >
              </div>
            </td>
            <td class="px-6 py-4 text-center">
              <span
                class="inline-flex items-center gap-2 bg-{% if student.gender == 'Male' %}[#7AB2D3]{% else %}[#F28C8C]{% endif %}/20 text-{% if student.gender == 'Male' %}[#7AB2D3]{% else %}[#F28C8C]{% endif %} px-3 py-1 rounded-full text-sm font-semibold border border-{% if student.gender == 'Male' %}[#7AB2D3]{% else %}[#F28C8C]{% endif %}/30"
              >
                <i
                  class="fas fa-{% if student.gender == 'Male' %}mars{% else %}venus{% endif %}"
                ></i>
                {{ student.gender }}
              </span>
            </td>
            <td class="px-6 py-4">
              <a
                href="{% url 'students:class_details' student.level.slug %}"
                class="inline-flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-3 py-1 rounded-full text-sm font-semibold border border-[#74C69D]/30 hover:bg-[#74C69D]/30 transition-colors duration-200"
              >
                <i class="fas fa-chalkboard-teacher text-xs"></i>
                {{ student.level }}
              </a>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="4" class="px-6 py-12 text-center">
              <div class="flex flex-col items-center gap-4">
                <div
                  class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
                >
                  <i class="fas fa-users text-[#B9D8EB] text-2xl"></i>
                </div>
                <div>
                  <h4 class="text-lg font-bold text-[#2C3E50] mb-2">
                    No Students Found
                  </h4>
                  <p class="text-[#40657F]">
                    No students match your search criteria or no students have
                    been enrolled yet.
                  </p>
                </div>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>

  <!-- Pagination Controls -->
  <div class="flex justify-center pagination-fade-in">
    <div
      class="flex items-center gap-4 bg-white rounded-2xl px-6 py-4 shadow-lg border border-[#B9D8EB]/50"
    >
      {% if students.has_previous %}
      <a
        href="?page={{ students.previous_page_number}}"
        class="flex items-center justify-center w-10 h-10 bg-[#E2F1F9] hover:bg-[#7AB2D3] text-[#7AB2D3] hover:text-white rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-lg group"
      >
        <i
          class="fas fa-chevron-left group-hover:scale-110 transition-transform duration-300"
        ></i>
      </a>
      <span class="px-3 py-1 text-[#40657F] font-medium"
        >{{students.previous_page_number}}</span
      >
      {% endif %}

      <div
        class="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] text-white rounded-xl font-bold text-lg shadow-lg"
      >
        {{ students.number }}
      </div>

      {% if students.has_next %}
      <span class="px-3 py-1 text-[#40657F] font-medium"
        >{{ students.next_page_number}}</span
      >
      <a
        href="?page={{students.next_page_number}}"
        class="flex items-center justify-center w-10 h-10 bg-[#E2F1F9] hover:bg-[#7AB2D3] text-[#7AB2D3] hover:text-white rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-lg group"
      >
        <i
          class="fas fa-chevron-right group-hover:scale-110 transition-transform duration-300"
        ></i>
      </a>
      {% endif %}
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: breadcrumbSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Search Section Animations */
  .search-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: searchSectionFadeIn 0.8s ease-out 0.8s forwards;
  }

  .search-icon-float {
    animation: searchIconFloat 4s ease-in-out infinite;
  }

  .search-form-slide-in {
    opacity: 0;
    transform: translateY(20px);
    animation: searchFormSlideIn 0.8s ease-out 1.2s forwards;
  }

  /* Table Section Animations */
  .table-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: tableSectionFadeIn 0.8s ease-out 1.4s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  .add-button-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: addButtonSlideIn 0.8s ease-out 1.6s forwards;
  }

  .table-fade-in {
    opacity: 0;
    animation: tableFadeIn 0.8s ease-out 1.8s forwards;
  }

  .student-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: studentRowSlideIn 0.4s ease-out forwards;
  }

  .student-row:nth-child(1) {
    animation-delay: 2s;
  }
  .student-row:nth-child(2) {
    animation-delay: 2.1s;
  }
  .student-row:nth-child(3) {
    animation-delay: 2.2s;
  }
  .student-row:nth-child(4) {
    animation-delay: 2.3s;
  }
  .student-row:nth-child(5) {
    animation-delay: 2.4s;
  }
  .student-row:nth-child(6) {
    animation-delay: 2.5s;
  }
  .student-row:nth-child(7) {
    animation-delay: 2.6s;
  }
  .student-row:nth-child(8) {
    animation-delay: 2.7s;
  }
  .student-row:nth-child(9) {
    animation-delay: 2.8s;
  }
  .student-row:nth-child(10) {
    animation-delay: 2.9s;
  }

  /* Pagination Animations */
  .pagination-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: paginationFadeIn 0.8s ease-out 3s forwards;
  }

  /* Keyframe Definitions */
  @keyframes breadcrumbSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes searchSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes searchIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(5deg);
    }
  }

  @keyframes searchFormSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes tableSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes tableIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(-3deg);
    }
  }

  @keyframes addButtonSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes tableFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes studentRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes paginationFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .student-row {
      animation-delay: 1.5s;
    }

    .student-row:nth-child(n) {
      animation-delay: calc(1.5s + 0.1s * var(--row-index, 1));
    }
  }
</style>

{% endblock %}
