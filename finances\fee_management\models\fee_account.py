from django.db import models, transaction


from students.utils.fee_calculations import calculate_one_month_fee
from students.models import Student


class FeeCategory(models.Model):
    '''
     This model represents the different fee categories available for the students.
     It also includes the amount of each fee.
     The category can be either Tuition Fee, PTA Fee, Food Fee, or Uniform Charges.
    '''
    name = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    amount = models.IntegerField()

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = 'Fee Categories'
        unique_together = ('name', 'amount')


class FeeAccount(models.Model):
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    term = models.ForeignKey(
        'students.Term', on_delete=models.CASCADE)
    total_due = models.IntegerField(default=0)
    category = models.ForeignKey(FeeCategory, on_delete=models.CASCADE)
    is_paid = models.BooleanField(default=False)

    billing_cycle = models.CharField(
        max_length=100, null=True, blank=True,
        choices=[
            ('Monthly', 'Monthly'), ('Termly', 'Termly')
        ])

    month = models.DateField(blank=True, null=True)

    @property
    def amount(self):
        from finances.fee_management.models import Receipt
        from django.db.models import Sum

        # Total receipts for this student/category/term
        receipt_total = Receipt.objects.filter(
            fee_account__student=self.student,
            fee_account__category=self.category,
            fee_account__term=self.term,
            is_reversed=False
        ).aggregate(total=Sum('amount_paid'))['total'] or 0

        # Get all fee accounts in the same group (student, category, term)
        fee_accounts = FeeAccount.objects.filter(
            student=self.student,
            category=self.category,
            term=self.term
        ).order_by('id')  # or use .order_by('created_at') if available

        remaining = receipt_total

        for account in fee_accounts:
            if remaining <= 0:
                amount_for_this_account = 0

            elif remaining >= account.total_due:
                amount_for_this_account = account.total_due
                remaining -= account.total_due

            else:
                amount_for_this_account = remaining
                remaining = 0

            if account.id == self.id:
                return amount_for_this_account

        return 0  # fallback, just in case

    @property
    def current_balance(self):
        """
        Calculate the current balance dynamically using distributed amount.
        Returns the amount still owed (always >= 0 since amount distributes properly).
        """
        return self.total_due - self.amount

    @property
    def is_fully_paid(self):
        """
        Check if this account is fully paid based on dynamic calculation.
        Returns True if fully paid or overpaid.
        """
        return self.current_balance <= 0

    def set_initial_total_due(self):
        if self.total_due == 0:
            if self.billing_cycle == 'Monthly':
                self.total_due = calculate_one_month_fee(self)
            else:
                self.total_due = self.category.amount

    def update_balance_and_paid_status(self):
        self.is_paid = self.is_fully_paid

    @classmethod
    def update_paid_status_for_group(cls, student, category, term):
        """
        Efficiently update is_paid status for all fee accounts in a group
        using the dynamic amount property.
        Call this when receipts are created/updated to avoid recalculating
        the amount property multiple times.
        """
        fee_accounts = cls.objects.filter(
            student=student,
            category=category,
            term=term
        ).order_by('id')

        if not fee_accounts.exists():
            return

        # Calculate total receipts once
        from finances.fee_management.models import Receipt
        from django.db.models import Sum

        receipt_total = Receipt.objects.filter(
            fee_account__student=student,
            fee_account__category=category,
            fee_account__term=term,
            is_reversed=False
        ).aggregate(total=Sum('amount_paid'))['total'] or 0

        remaining = receipt_total
        accounts_to_update = []

        for account in fee_accounts:
            if remaining <= 0:
                amount_for_account = 0
            elif remaining >= account.total_due:
                amount_for_account = account.total_due
                remaining -= account.total_due
            else:
                amount_for_account = remaining
                remaining = 0

            # Update is_paid status based on dynamic calculation
            account.is_paid = (account.total_due - amount_for_account) <= 0
            accounts_to_update.append(account)

        # Bulk update to minimize database hits
        if accounts_to_update:
            cls.objects.bulk_update(accounts_to_update, ['is_paid'])

    def sync_budget_line(self):
        from finances.book_keeping import BudgetLine, Ledger
        from students.models import Term

        term = Term.objects.get_active()

        account = Ledger.objects.get(name=self.category.name)

        budget_line, _ = BudgetLine.objects.get_or_create(
            budget__term=term,
            account=account,
        )

        budget_line.amount += self.total_due
        budget_line.save()

    def save(self, *args, **kwargs):
        is_new = self._state.adding

        with transaction.atomic():
            if is_new:
                self.set_initial_total_due()

            super().save(*args, **kwargs)

        self.sync_budget_line()
        # IncomeTotal removed - using modern budget/ledger system

    def __str__(self):
        return f"{self.student.name} - {self.category} - {self.term} - {self.month}"

    class Meta:
        unique_together = ('student', 'category', 'term', 'month')
