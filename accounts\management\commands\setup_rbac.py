from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone

from accounts.utils import setup_default_permissions, setup_default_roles, RBACManager
from accounts.models import Role, Permission, UserRole

User = get_user_model()


class Command(BaseCommand):
    help = 'Set up default RBAC system with roles, permissions, and sample assignments'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all RBAC data (WARNING: This will delete all existing roles and permissions)',
        )
        parser.add_argument(
            '--assign-admin',
            type=str,
            help='Username of user to assign administrator role to',
        )
        parser.add_argument(
            '--assign-superuser-roles',
            action='store_true',
            help='Assign appropriate roles to existing superusers',
        )
        parser.add_argument(
            '--migrate-staff',
            action='store_true',
            help='Migrate existing staff users to appropriate roles',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up RBAC system...'))

        try:
            with transaction.atomic():
                if options['reset']:
                    self._reset_rbac_data()

                # Create default permissions and roles
                permissions_created = setup_default_permissions()
                roles_created = setup_default_roles()

                self.stdout.write(
                    self.style.SUCCESS(
                        f'Created {permissions_created} permissions and {roles_created} roles'
                    )
                )

                # Assign roles to specific users
                if options['assign_admin']:
                    self._assign_admin_role(options['assign_admin'])

                if options['assign_superuser_roles']:
                    self._assign_superuser_roles()

                if options['migrate_staff']:
                    self._migrate_staff_users()

                # Display summary
                self._display_summary()

        except Exception as e:
            raise CommandError(f'Error setting up RBAC: {str(e)}')

    def _reset_rbac_data(self):
        """Reset all RBAC data"""
        self.stdout.write(self.style.WARNING('Resetting RBAC data...'))
        
        UserRole.objects.all().delete()
        Role.objects.all().delete()
        Permission.objects.all().delete()
        
        self.stdout.write(self.style.SUCCESS('RBAC data reset complete'))

    def _assign_admin_role(self, username):
        """Assign administrator role to a specific user"""
        try:
            user = User.objects.get(username=username)
            admin_role = Role.objects.get(name='administrator')
            
            user_role = RBACManager.assign_role_to_user(
                user=user,
                role=admin_role,
                notes="Assigned via setup_rbac command"
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Assigned administrator role to {username}'
                )
            )
            
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User {username} not found')
            )
        except Role.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Administrator role not found')
            )

    def _assign_superuser_roles(self):
        """Assign super administrator role to existing superusers"""
        try:
            super_admin_role = Role.objects.get(name='super_administrator')
            superusers = User.objects.filter(is_superuser=True)
            
            assigned_count = 0
            for user in superusers:
                # Check if user already has this role
                if not user.has_role('super_administrator'):
                    RBACManager.assign_role_to_user(
                        user=user,
                        role=super_admin_role,
                        notes="Auto-assigned to existing superuser"
                    )
                    assigned_count += 1
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Assigned super administrator role to {assigned_count} superusers'
                )
            )
            
        except Role.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Super administrator role not found')
            )

    def _migrate_staff_users(self):
        """Migrate existing staff users to appropriate roles"""
        try:
            admin_role = Role.objects.get(name='administrator')
            teacher_role = Role.objects.get(name='teacher')
            
            # Get staff users who are not superusers
            staff_users = User.objects.filter(is_staff=True, is_superuser=False)
            
            assigned_count = 0
            for user in staff_users:
                # Check if user already has roles
                if user.get_active_roles().count() == 0:
                    # Assign administrator role to staff users
                    RBACManager.assign_role_to_user(
                        user=user,
                        role=admin_role,
                        notes="Migrated from is_staff=True"
                    )
                    assigned_count += 1
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Migrated {assigned_count} staff users to administrator role'
                )
            )
            
        except Role.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Required roles not found')
            )

    def _display_summary(self):
        """Display RBAC system summary"""
        self.stdout.write(self.style.SUCCESS('\n=== RBAC System Summary ==='))
        
        # Roles summary
        roles = Role.objects.filter(is_active=True).order_by('level')
        self.stdout.write(f'\nRoles ({roles.count()}):')
        for role in roles:
            user_count = UserRole.objects.filter(role=role, is_active=True).count()
            perm_count = role.permissions.filter(is_active=True).count()
            self.stdout.write(
                f'  • {role.display_name} (Level {role.level}): '
                f'{user_count} users, {perm_count} permissions'
            )
        
        # Permissions summary
        permissions = Permission.objects.filter(is_active=True)
        categories = permissions.values_list('category', flat=True).distinct()
        self.stdout.write(f'\nPermissions by Category:')
        for category in categories:
            count = permissions.filter(category=category).count()
            self.stdout.write(f'  • {category.title()}: {count} permissions')
        
        # Users summary
        total_users = User.objects.count()
        users_with_roles = User.objects.filter(
            user_roles__is_active=True
        ).distinct().count()
        
        self.stdout.write(f'\nUsers:')
        self.stdout.write(f'  • Total users: {total_users}')
        self.stdout.write(f'  • Users with roles: {users_with_roles}')
        self.stdout.write(f'  • Users without roles: {total_users - users_with_roles}')
        
        # Role distribution
        self.stdout.write(f'\nRole Distribution:')
        for role in roles:
            user_count = UserRole.objects.filter(role=role, is_active=True).count()
            if user_count > 0:
                self.stdout.write(f'  • {role.display_name}: {user_count} users')
        
        self.stdout.write(self.style.SUCCESS('\n=== Setup Complete ==='))
        self.stdout.write(
            'You can now manage roles and permissions through the Django admin interface.'
        )
        self.stdout.write(
            'Use the accounts.decorators and accounts.mixins for view protection.'
        )
