from django.db import models

from accounts.models import CustomUser
from finances.book_keeping import JournalEntry, Ledger
from helpers.strings import generate_unique_ids


class Outflow(models.Model):
    outflow_id = models.CharField(
        max_length=50, unique=True, blank=True, null=True)
    date = models.DateField()
    payee = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(
        CustomUser, on_delete=models.PROTECT, related_name="outflows"
    )
    term = models.ForeignKey('students.Term', on_delete=models.PROTECT)
    created_at = models.DateTimeField(auto_now_add=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)
    reversal_entry = models.OneToOneField(
        'finances.JournalEntry', on_delete=models.SET_NULL, null=True, blank=True, related_name="reversal_of_outflow")
    is_reversed = models.BooleanField(default=False)

    def calculate_total(self):
        return sum(line.amount for line in self.outflow_lines.all())

    def update_journal(self):
        journal, _ = JournalEntry.objects.get_or_create(
            term=self.term,
            date=self.date,
            description=f"Outflow for {self.payee}",
            voucher=self.outflow_id,
        )

        # Add debit lines per outflow line
        for line in self.outflow_lines.all():
            line.create_debit_line(journal)

    # Add the credit line once
        cash_account = Ledger.objects.get(name="Cash/Bank")
        credit_line, created = journal.journalline_set.get_or_create(
            account=cash_account,
            line_type='Credit',
            defaults={
                'description': f"Outflow for {self.payee} - {self.outflow_id}",
                'amount': self.calculate_total(),
            }
        )

        if not created:
            credit_line.amount = self.calculate_total()
            credit_line.save(update_fields=["amount"])

        return journal

    def generate_outflow_id(self):
        prefix = "DIS"
        self.outflow_id = generate_unique_ids(self, prefix)

    @property
    def total_amount(self):
        return sum(line.amount for line in self.outflow_lines.all())

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        if not self.outflow_id:
            self.generate_outflow_id()
            super().save(*args, **kwargs)

        self.update_journal()

    def __str__(self):
        return f"{self.outflow_id} - {self.payee} - {self.date}"

    class Meta:
        ordering = ['-date']


class OutflowLine(models.Model):
    outflow = models.ForeignKey(
        Outflow, on_delete=models.CASCADE, related_name="outflow_lines")
    account = models.ForeignKey(Ledger, on_delete=models.CASCADE)
    amount = models.DecimalField(default=0, decimal_places=2, max_digits=20)
    description = models.TextField(blank=True, null=True)
    jounal_line_created = models.BooleanField(default=False)

    def create_debit_line(self, journal):
        if self.jounal_line_created:
            return

        journal.journalline_set.create(
            account=self.account,
            description=f"{self.description or 'Outflow'} - {self.amount} - No: {self.outflow.outflow_id}",
            amount=self.amount,
            line_type='Debit'
        )

        self.jounal_line_created = True
        self.save(update_fields=["jounal_line_created"])

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.outflow} - {self.account} - MWk {self.amount}"
