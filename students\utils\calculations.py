from django.db.models import Sum, Count, Q

from datetime import date


current_month = date.today()


def calculate_fees_totals(term):
    from finances.book_keeping import BudgetLine

    tuition_fees_total = BudgetLine.objects.filter(
        budget__term=term,
        account__name__icontains="Tuition").aggregate(total=Sum('amount'))['total']

    food_fees_total = BudgetLine.objects.filter(
        budget__term=term,
        account__name__icontains="Food Fees").aggregate(total=Sum('amount'))['total']

    return tuition_fees_total, food_fees_total


def calculate_fees_collections():
    from finances.book_keeping import Ledger

    tuition_ledgers = Ledger.objects.filter(name__icontains="Tuition")

    tuition_collection = sum(
        tuition_ledger.total_amount for tuition_ledger in tuition_ledgers)

    food_ledgers = Ledger.objects.filter(name__icontains="Food Fees")
    food_collection = sum(
        food_ledger.total_amount for food_ledger in food_ledgers)

    return tuition_collection, food_collection


def get_income_totals(term):
    from finances.book_keeping import BudgetLine
    from finances.book_keeping import Ledger

    fee_ledgers = Ledger.objects.filter(
        name__icontains="Fees")
    total_collected = sum(
        fee_ledger.total_amount for fee_ledger in fee_ledgers)
    overall_total = BudgetLine.objects.filter(
        budget__term=term, account__name__icontains="Fees").aggregate(total=Sum('amount'))['total']
    fee_receivables = overall_total - total_collected

    return total_collected, overall_total, fee_receivables


def get_expenditure_total():
    from finances.book_keeping import Ledger
    expenses = Ledger.objects.filter(ledger_type="Expense")
    expense_total = sum(expense.total_amount for expense in expenses)
    return expense_total if expense_total else 0


def get_student_counts():
    from students.models import Student
    student_counts = Student.objects.filter(is_active=True).aggregate(
        total=Count("id"),
        male=Count("id", filter=Q(gender="Male")),
        female=Count("id", filter=Q(gender="Female")),
    )

    students_count = student_counts["total"]
    male_students_count = student_counts["male"]
    female_students_count = student_counts["female"]

    return students_count, male_students_count, female_students_count


def get_student_fee_accounts(student, term):
    from finances.fee_management.models import FeeAccount
    fee_accounts = []
    pta_account = FeeAccount.objects.get(
        student=student, term=term, category__name__contains="PTA")
    fee_accounts.append(pta_account)
    tuition_account = FeeAccount.objects.get(
        student=student, term=term, billing_cycle="Termly", category__name__contains="Tuition"
    )
    fee_accounts.append(tuition_account)
    food_fee = FeeAccount.objects.get(
        student=student, term=term, month__month=current_month.month
    )
    fee_accounts.append(food_fee)
    return fee_accounts


def get_levels_student_count(levels):
    from students.models import Student
    levels_student_count = []
    for level in levels:
        student_at_level = Student.objects.filter(level=level, is_active=True)
        total_counts = student_at_level.count()
        female_counts = student_at_level.filter(gender="Female").count()
        male_counts = student_at_level.filter(gender="Male").count()
        levels_student_count.append(
            (level, total_counts, female_counts, male_counts))
    return levels_student_count


def get_outstanding_balances(cycle, term):
    from finances.fee_management.models import FeeAccount

    if cycle == "Monthly":
        return FeeAccount.objects.filter(
            is_paid=False, term=term, month__month=current_month.month, billing_cycle=cycle
        ).order_by('student__name')
    else:
        return FeeAccount.objects.filter(
            is_paid=False, term=term, billing_cycle=cycle
        ).order_by('student__name')
