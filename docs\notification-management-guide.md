# 📢 Notification Management Guide

This guide covers how to create, manage, and troubleshoot notifications in the Tiny Feet Academy Management System.

## 📋 Table of Contents

- [Overview](#overview)
- [Notification Types](#notification-types)
- [Creating Notifications](#creating-notifications)
- [Management Command Usage](#management-command-usage)
- [Admin Interface](#admin-interface)
- [API Endpoints](#api-endpoints)
- [Notification Badge](#notification-badge)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)

## 🎯 Overview

The notification system allows administrators to send targeted or system-wide notifications to users. Notifications appear on the dashboard and can be configured with different priorities, expiration dates, and targeting options.

### Key Features

- **System-wide or targeted notifications**
- **Priority levels** (Low, Medium, High, Urgent)
- **Expiration dates** for time-sensitive notifications
- **Rich notification types** with custom icons and colors
- **Read/unread tracking** per user
- **Related object linking** (students, fee accounts)
- **Visual notification indicators** with badge counts on dashboard
- **Real-time badge updates** when notifications are read/created

## 📝 Notification Types

### Default Notification Types

| Type | Icon | Color | Use Case |
|------|------|-------|----------|
| **Fee Reminder** | `fas fa-dollar-sign` | `#F28C8C` | Payment reminders, fee notifications |
| **Academic Update** | `fas fa-graduation-cap` | `#7AB2D3` | Grade releases, academic announcements |
| **System Alert** | `fas fa-exclamation-triangle` | `#e07575` | System maintenance, urgent updates |
| **General Info** | `fas fa-info-circle` | `#74C69D` | General announcements, information |

### Creating Custom Notification Types

```python
# In Django shell or management command
from core.models import NotificationType

NotificationType.objects.create(
    name="Event Reminder",
    icon="fas fa-calendar",
    color="#9B59B6",
    is_active=True
)
```

## 🚀 Creating Notifications

### Method 1: Management Command (Recommended)

The management command is the safest and most reliable way to create notifications, especially in production environments.

#### Basic Usage

```bash
# System-wide notification
python manage.py create_notification \
    --title "System Maintenance Tonight" \
    --message "The system will be under maintenance from 10 PM to 2 AM. Please save your work." \
    --priority high

# Targeted notification
python manage.py create_notification \
    --title "Fee Payment Reminder" \
    --message "Your school fees are due by the end of this month. Please make payment to avoid late fees." \
    --type "Fee Reminder" \
    --priority medium \
    --target-users john_doe jane_smith admin_user
```

#### Advanced Usage

```bash
# Notification with expiry date
python manage.py create_notification \
    --title "Exam Schedule Released" \
    --message "The final exam schedule has been published. Check your dashboard for details." \
    --type "Academic Update" \
    --priority high \
    --expires "2025-12-31 23:59:59"

# Create inactive notification (draft)
python manage.py create_notification \
    --title "Draft Announcement" \
    --message "This is a draft notification" \
    --inactive
```

### Method 2: Django Shell

```python
from core.models import Notification, NotificationType
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

# Get notification type
notification_type = NotificationType.objects.get(name="General Info")

# Create notification
notification = Notification.objects.create(
    title="Welcome to New Academic Year",
    message="We're excited to welcome everyone to the new academic year!",
    notification_type=notification_type,
    priority="medium",
    expires_at=timezone.now() + timedelta(days=30),
    is_active=True
)

# Add target users (optional)
target_users = User.objects.filter(is_active=True, is_staff=True)
notification.target_users.set(target_users)
```

### Method 3: Admin Interface (Use with Caution)

⚠️ **Note**: Due to performance optimizations, the admin interface may have limited functionality for user targeting. Use the management command for complex notifications.

1. Navigate to **Admin Panel** → **Core** → **Notifications**
2. Click **Add Notification**
3. Fill in the required fields:
   - **Title**: Clear, concise notification title
   - **Message**: Detailed notification content
   - **Notification Type**: Select appropriate type
   - **Priority**: Choose priority level
   - **Expires At**: Optional expiration date
   - **Is Active**: Enable/disable notification

## 🛠️ Management Command Usage

### Command Syntax

```bash
python manage.py create_notification [OPTIONS]
```

### Required Arguments

| Argument | Description | Example |
|----------|-------------|---------|
| `--title` | Notification title | `--title "System Update"` |
| `--message` | Notification message | `--message "System will be updated tonight"` |

### Optional Arguments

| Argument | Description | Default | Example |
|----------|-------------|---------|---------|
| `--type` | Notification type name | `"General Info"` | `--type "Fee Reminder"` |
| `--priority` | Priority level | `medium` | `--priority high` |
| `--expires` | Expiry date | None | `--expires "2025-12-31 23:59:59"` |
| `--target-users` | Target usernames | None (system-wide) | `--target-users user1 user2` |
| `--inactive` | Create as inactive | False | `--inactive` |

### Examples

```bash
# Emergency system alert
python manage.py create_notification \
    --title "🚨 URGENT: System Emergency" \
    --message "Critical system issue detected. All users please log out immediately." \
    --type "System Alert" \
    --priority urgent

# Fee reminder with expiry
python manage.py create_notification \
    --title "Monthly Fee Due" \
    --message "Your monthly school fee is due in 3 days. Please make payment to avoid late charges." \
    --type "Fee Reminder" \
    --priority high \
    --expires "2025-07-31 23:59:59" \
    --target-users parent1 parent2 parent3

# Academic announcement
python manage.py create_notification \
    --title "Exam Results Published" \
    --message "Mid-term examination results are now available in your student portal." \
    --type "Academic Update" \
    --priority medium
```

## 🔧 Admin Interface

### Optimized Admin Features

- **Performance Optimizations**: Raw ID fields for related objects
- **Search Functionality**: Search by title and message
- **Filtering Options**: Filter by type, priority, status, and date
- **Bulk Actions**: Activate/deactivate multiple notifications
- **Read Statistics**: View notification read counts

### Admin Interface Limitations

Due to performance optimizations in production:

- **User Targeting**: Limited user selection interface
- **Large User Lists**: May timeout with many users
- **Recommendation**: Use management command for complex targeting

## 📡 API Endpoints

### Get Notifications

```http
GET /core/api/notifications/
```

**Query Parameters:**
- `limit`: Number of notifications to return (default: 10)
- `unread_only`: Return only unread notifications (true/false)

**Response:**
```json
{
    "success": true,
    "notifications": [
        {
            "id": 1,
            "title": "System Maintenance",
            "message": "System will be under maintenance tonight",
            "priority": "high",
            "priority_color": "#F28C8C",
            "type": {
                "name": "System Alert",
                "icon": "fas fa-exclamation-triangle",
                "color": "#e07575"
            },
            "is_read": false,
            "created_at": "2025-07-06T10:30:00Z",
            "time_ago": "2 hours ago",
            "related_student": null
        }
    ],
    "count": 1
}
```

### Mark Notification as Read

```http
POST /core/api/notifications/mark-read/
Content-Type: application/json

{
    "notification_id": 1
}
```

**Response:**
```json
{
    "success": true,
    "message": "Notification marked as read"
}
```

## � Notification Badge

The dashboard includes a visual notification indicator that appears on the Notifications tab to alert users when unread notifications are available.

### Badge Behavior

#### **Badge Display Logic**
- **No Badge**: No unread notifications
- **Red Dot**: 1-9 unread notifications (shows exact count)
- **"9+" Badge**: 10 or more unread notifications

#### **Badge Features**
- **Real-time Updates**: Badge updates automatically when notifications are read
- **Animated Pulse**: Eye-catching pulse animation to draw attention
- **Auto-hide**: Badge disappears when notifications tab is opened
- **Periodic Refresh**: Checks for new notifications every 2 minutes

#### **Badge Styling**
- **Color**: Red (`#F28C8C`) to indicate urgency
- **Position**: Top-right corner of notifications tab button
- **Animation**: Subtle pulse effect with scale and opacity changes
- **Border**: White border for better visibility against backgrounds

### Technical Implementation

#### **JavaScript Functions**
```javascript
// Check for unread notifications
checkUnreadNotifications()

// Update badge display
updateNotificationBadge(count)

// Automatic refresh every 2 minutes
setInterval(checkUnreadNotifications, 120000)
```

#### **API Integration**
The badge uses the existing notification API with unread filtering:
```http
GET /core/api/notifications/?unread_only=true&limit=50
```

#### **Badge Elements**
```html
<!-- Notification Badge (for counts 1-9+) -->
<span id="notification-badge" class="...">
  <span id="notification-count">0</span>
</span>

<!-- Notification Dot (alternative simple indicator) -->
<span id="notification-dot" class="..."></span>
```

### User Experience

#### **Visual Feedback**
1. **Badge Appearance**: Users immediately see when new notifications arrive
2. **Count Display**: Users know exactly how many unread notifications they have
3. **Badge Removal**: Badge disappears when notifications are viewed
4. **Smooth Animations**: Professional, non-intrusive visual effects

#### **Accessibility**
- **Tooltip**: Hover text explains the badge purpose
- **High Contrast**: Red badge stands out against blue tab background
- **Screen Reader**: Proper ARIA labels for accessibility compliance

### Customization Options

#### **Badge Colors**
To change badge colors, update the CSS classes:
```css
/* Primary badge color */
.bg-[#F28C8C] { background-color: #F28C8C; }

/* Alternative colors */
.bg-[#7AB2D3] { background-color: #7AB2D3; } /* Blue */
.bg-[#74C69D] { background-color: #74C69D; } /* Green */
.bg-[#e07575] { background-color: #e07575; } /* Dark Red */
```

#### **Animation Speed**
Adjust the pulse animation timing:
```css
@keyframes notificationPulse {
  /* Change duration for faster/slower pulse */
  animation: notificationPulse 1s ease-in-out infinite; /* Faster */
  animation: notificationPulse 3s ease-in-out infinite; /* Slower */
}
```

#### **Refresh Interval**
Change how often the badge checks for new notifications:
```javascript
// Check every 1 minute (60000ms)
setInterval(checkUnreadNotifications, 60000);

// Check every 5 minutes (300000ms)
setInterval(checkUnreadNotifications, 300000);
```

### Troubleshooting Badge Issues

#### **Badge Not Appearing**
1. Check browser console for JavaScript errors
2. Verify API endpoint is accessible: `/core/api/notifications/`
3. Ensure user has unread notifications in database
4. Check CSS classes are not conflicting

#### **Badge Not Updating**
1. Verify network connectivity for API calls
2. Check if JavaScript intervals are running
3. Clear browser cache and reload page
4. Inspect network tab for failed API requests

#### **Badge Count Incorrect**
1. Check database for notification read status
2. Verify API returns correct unread count
3. Test with different user accounts
4. Check for timezone issues with notification timestamps

## �🚨 Troubleshooting

### Common Issues

#### 1. Worker Timeout in Admin

**Problem**: Admin interface times out when creating notifications

**Solution**: 
- Use the management command instead
- Check production logs for specific errors
- Ensure database indexes are applied

```bash
# Apply performance indexes
python manage.py migrate core 0002
```

#### 2. Users Not Found

**Problem**: Target users not found when using management command

**Solution**:
```bash
# Check available usernames
python manage.py shell -c "
from django.contrib.auth import get_user_model;
User = get_user_model();
print('Available users:', list(User.objects.values_list('username', flat=True)))
"
```

#### 3. Notification Type Not Found

**Problem**: Specified notification type doesn't exist

**Solution**:
```bash
# List available notification types
python manage.py shell -c "
from core.models import NotificationType;
print('Available types:', list(NotificationType.objects.values_list('name', flat=True)))
"

# Create new notification type
python manage.py shell -c "
from core.models import NotificationType;
NotificationType.objects.create(name='Custom Type', icon='fas fa-star', color='#FF6B6B')
"
```

#### 4. Database Performance Issues

**Problem**: Slow notification queries

**Solution**:
```bash
# Check if indexes are applied
python manage.py dbshell -c "
\d core_notification
\d core_notificationread
"

# Apply missing indexes
python manage.py migrate core
```

### Debug Commands

```bash
# Check notification system status
python manage.py shell -c "
from core.models import Notification, NotificationType;
print(f'Total notifications: {Notification.objects.count()}');
print(f'Active notifications: {Notification.objects.filter(is_active=True).count()}');
print(f'Notification types: {NotificationType.objects.count()}')
"

# List recent notifications
python manage.py shell -c "
from core.models import Notification;
for n in Notification.objects.order_by('-created_at')[:5]:
    print(f'{n.id}: {n.title} ({n.priority}) - {n.created_at}')
"
```

## ✅ Best Practices

### 1. Notification Content

- **Clear Titles**: Use descriptive, action-oriented titles
- **Concise Messages**: Keep messages brief but informative
- **Appropriate Priority**: Use priority levels consistently
- **Expiration Dates**: Set expiry for time-sensitive notifications

### 2. Targeting

- **System-wide**: Use for general announcements
- **Targeted**: Use for specific user groups or individuals
- **Role-based**: Consider user roles when targeting

### 3. Performance

- **Use Management Command**: For production environments
- **Batch Operations**: Create multiple notifications efficiently
- **Monitor Performance**: Check logs for slow queries

### 4. Maintenance

- **Regular Cleanup**: Remove expired notifications
- **Monitor Read Rates**: Track notification effectiveness
- **Update Types**: Keep notification types relevant

### Example Cleanup Script

```bash
# Remove expired notifications older than 30 days
python manage.py shell -c "
from core.models import Notification;
from django.utils import timezone;
from datetime import timedelta;
cutoff = timezone.now() - timedelta(days=30);
expired = Notification.objects.filter(expires_at__lt=cutoff);
count = expired.count();
expired.delete();
print(f'Removed {count} expired notifications')
"
```

## 📊 Monitoring and Analytics

### Notification Statistics

```python
# Get notification statistics
from core.models import Notification, NotificationRead
from django.db.models import Count

# Notifications by priority
priority_stats = Notification.objects.values('priority').annotate(
    count=Count('id')
).order_by('priority')

# Read rates
total_notifications = Notification.objects.filter(is_active=True).count()
total_reads = NotificationRead.objects.count()
read_rate = (total_reads / total_notifications * 100) if total_notifications > 0 else 0

print(f"Read rate: {read_rate:.1f}%")
```

---

## 🔗 Related Documentation

- [System Configuration Guide](system-configuration-guide.md)
- [Admin Interface Guide](admin-interface-guide.md)
- [API Documentation](api-documentation.md)
- [Troubleshooting Guide](debugging-guide.md)

---

**Last Updated**: July 6, 2025  
**Version**: 1.0  
**Maintainer**: System Administrator
