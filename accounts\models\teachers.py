from django.db import models

from academics.models import Subject
from accounts.models import CustomUser
from students.models import Level


# Create your models here.


class TeacherAssignment(models.Model):
    """
    Model to assign teachers to subjects.
    """
    teacher = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name='subject_assignments')
    subject = models.ForeignKey(
        Subject, on_delete=models.CASCADE, related_name='teacher_assignments')
    class_assigned = models.ForeignKey(
        Level, on_delete=models.CASCADE, related_name='teacher_assignments',
        verbose_name="Class Assigned", help_text="Class assigned to the teacher"
    )
    term = models.ForeignKey('students.Term', on_delete=models.CASCADE)

    def save(self, *args, **kwargs):
        if self.teacher.is_superuser:
            raise ValueError("Superusers cannot be assigned to subjects.")
        from students.models import Term
        self.term = Term.objects.get_active()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.teacher.get_full_name()} - {self.subject.name} ({self.term})"

    class Meta:
        unique_together = ('class_assigned', 'subject', 'term')
        verbose_name = "Teacher Assignment"
        verbose_name_plural = "Teacher Assignments"
