from django.db import models
from django.utils.text import slugify

from core.managers import SingletonActiveManager
from students.utils.term_utils import activate_period


class AcademicYear(models.Model):
    name = models.CharField(max_length=100)
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=False)
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)

    objects = SingletonActiveManager()

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)

        activate_period(self)

        super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class Term(models.Model):
    term_choices = [
        ('Term 1', 'Term 1'),
        ('Term 2', 'Term 2'),
        ('Term 3', 'Term 3'),
    ]
    term_name = models.CharField(max_length=50, choices=term_choices)
    academic_year = models.ForeignKey(
        AcademicYear, on_delete=models.CASCADE, related_name="academic_year")
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)
    is_active = models.BooleanField(default=False)
    start_date = models.DateField()
    end_date = models.DateField()

    objects = SingletonActiveManager()

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(f'{self.term_name}-{self.academic_year}')

        activate_period(self)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.term_name} of {self.academic_year.name}"

    class Meta:
        unique_together = ('term_name', 'academic_year')
