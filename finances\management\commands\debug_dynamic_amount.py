from django.core.management.base import BaseCommand
from django.db.models import Sum
from finances.fee_management.models import Receipt, FeeAccount, FeeCategory
from students.models import Term


class Command(BaseCommand):
    help = 'Debug dynamic amount calculation discrepancies'

    def add_arguments(self, parser):
        parser.add_argument(
            '--category',
            type=str,
            required=True,
            help='Fee category name to debug',
        )

    def handle(self, *args, **options):
        category_name = options['category']
        
        try:
            category = FeeCategory.objects.get(name=category_name)
            term = Term.objects.get_active()
        except (FeeCategory.DoesNotExist, Term.DoesNotExist) as e:
            self.stdout.write(self.style.ERROR(f'Error: {e}'))
            return

        self.stdout.write(f'Debugging dynamic amount for: {category_name}')
        
        # Get all receipts for this category
        receipts = Receipt.objects.filter(
            fee_account__category=category,
            fee_account__term=term,
            is_reversed=False
        )
        
        receipts_total = receipts.aggregate(total=Sum('amount_paid'))['total'] or 0
        self.stdout.write(f'Total receipts: {receipts_total:,}')
        
        # Get all fee accounts
        fee_accounts = FeeAccount.objects.filter(
            category=category,
            term=term
        ).order_by('id')
        
        dynamic_total = sum(account.amount for account in fee_accounts)
        self.stdout.write(f'Dynamic total: {dynamic_total:,}')
        self.stdout.write(f'Difference: {receipts_total - dynamic_total:,}')
        
        # Group receipts by student to debug distribution
        student_groups = {}
        for receipt in receipts:
            student = receipt.fee_account.student
            if student not in student_groups:
                student_groups[student] = []
            student_groups[student].append(receipt)
        
        self.stdout.write(f'\nAnalyzing {len(student_groups)} student groups...')
        
        total_discrepancy = 0
        
        for student, student_receipts in student_groups.items():
            # Get student's fee accounts for this category/term
            student_accounts = FeeAccount.objects.filter(
                student=student,
                category=category,
                term=term
            ).order_by('id')
            
            if not student_accounts.exists():
                self.stdout.write(f'❌ No fee accounts for {student}')
                continue
            
            # Calculate total receipts for this student
            student_receipt_total = sum(r.amount_paid for r in student_receipts)
            
            # Calculate dynamic amounts for this student's accounts
            student_dynamic_total = sum(account.amount for account in student_accounts)
            
            # Calculate expected distribution manually
            remaining = student_receipt_total
            expected_total = 0
            
            for account in student_accounts:
                if remaining <= 0:
                    amount_for_account = 0
                elif remaining >= account.total_due:
                    amount_for_account = account.total_due
                    remaining -= account.total_due
                else:
                    amount_for_account = remaining
                    remaining = 0
                
                expected_total += amount_for_account
            
            discrepancy = student_receipt_total - student_dynamic_total
            expected_discrepancy = student_receipt_total - expected_total
            
            if abs(discrepancy) > 0.01:  # Allow for small rounding differences
                total_discrepancy += discrepancy
                self.stdout.write(
                    f'🔍 {student.name}: '
                    f'Receipts={student_receipt_total:,}, '
                    f'Dynamic={student_dynamic_total:,}, '
                    f'Expected={expected_total:,}, '
                    f'Discrepancy={discrepancy:,}'
                )
                
                # Show individual account details
                for account in student_accounts:
                    self.stdout.write(
                        f'   Account {account.id}: '
                        f'due={account.total_due:,}, '
                        f'amount={account.amount:,}'
                    )
        
        self.stdout.write(f'\nTotal discrepancy found: {total_discrepancy:,}')
        
        if abs(total_discrepancy - (receipts_total - dynamic_total)) > 0.01:
            self.stdout.write(
                self.style.ERROR('❌ Discrepancy calculation mismatch!')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ Discrepancy calculation matches')
            )
