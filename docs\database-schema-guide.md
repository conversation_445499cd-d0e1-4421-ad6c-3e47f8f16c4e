# Database Schema Guide

## 🗄️ Overview

This guide provides comprehensive documentation of the Receipt Generator database schema, including table relationships, constraints, and data flow patterns.

## 📊 Schema Overview

### Core Entities
- **Students**: Student enrollment and profile management
- **Finances**: Fee management, receipts, and financial tracking
- **Academics**: Grades, assessments, and academic records
- **Accounts**: User authentication and authorization
- **Core**: Shared utilities and base models

## 🏗️ Database Architecture

### Entity Relationship Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Students     │    │    Finances     │    │   Academics     │
│                 │    │                 │    │                 │
│ • Student       │────│ • Receipt       │    │ • Assessment    │
│ • Level         │    │ • FeeAccount    │    │ • Activity      │
│ • AcademicYear  │    │ • Outflow       │    │ • Subject       │
│ • Term          │    │ • JournalEntry  │    │ • Enrollment    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │    Accounts     │
                    │                 │
                    │ • CustomUser    │
                    │ • UserProfile   │
                    └─────────────────┘
```

## 📋 Table Specifications

### Students Module

#### students_student
```sql
CREATE TABLE students_student (
    id BIGINT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(200) NOT NULL,
    student_id VARCHAR(50) UNIQUE,
    gender VARCHAR(10) DEFAULT 'Male',
    is_active BOOLEAN DEFAULT TRUE,
    slug VARCHAR(200) UNIQUE,
    level_id BIGINT REFERENCES students_level(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_student_level ON students_student(level_id);
CREATE INDEX idx_student_active ON students_student(is_active);
CREATE INDEX idx_student_name ON students_student(name);
```

#### students_level
```sql
CREATE TABLE students_level (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    abbrv VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    slug VARCHAR(200) UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_level_active ON students_level(is_active);
CREATE UNIQUE INDEX idx_level_abbrv ON students_level(abbrv);
```

#### students_academicyear
```sql
CREATE TABLE students_academicyear (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    slug VARCHAR(200) UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Constraints
ALTER TABLE students_academicyear 
ADD CONSTRAINT check_date_range 
CHECK (end_date > start_date);

-- Indexes
CREATE INDEX idx_academic_year_active ON students_academicyear(is_active);
CREATE INDEX idx_academic_year_dates ON students_academicyear(start_date, end_date);
```

#### students_term
```sql
CREATE TABLE students_term (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    academic_year_id BIGINT REFERENCES students_academicyear(id),
    slug VARCHAR(200) UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Constraints
ALTER TABLE students_term 
ADD CONSTRAINT check_term_date_range 
CHECK (end_date > start_date);

-- Indexes
CREATE INDEX idx_term_active ON students_term(is_active);
CREATE INDEX idx_term_academic_year ON students_term(academic_year_id);
```

### Finances Module

#### finances_receipt
```sql
CREATE TABLE finances_receipt (
    id BIGINT PRIMARY KEY,
    receipt_number VARCHAR(50) UNIQUE,
    date DATE DEFAULT CURRENT_DATE,
    amount_paid INTEGER NOT NULL,
    student_id BIGINT REFERENCES students_student(id),
    fee_account_id BIGINT REFERENCES finances_feeaccount(id),
    slug VARCHAR(200) UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Constraints
ALTER TABLE finances_receipt 
ADD CONSTRAINT check_amount_positive 
CHECK (amount_paid > 0);

-- Indexes
CREATE INDEX idx_receipt_student ON finances_receipt(student_id);
CREATE INDEX idx_receipt_date ON finances_receipt(date);
CREATE INDEX idx_receipt_fee_account ON finances_receipt(fee_account_id);
CREATE UNIQUE INDEX idx_receipt_number ON finances_receipt(receipt_number);
```

#### finances_feeaccount
```sql
CREATE TABLE finances_feeaccount (
    id BIGINT PRIMARY KEY,
    student_id BIGINT REFERENCES students_student(id),
    term_id BIGINT REFERENCES students_term(id),
    amount_due INTEGER NOT NULL,
    amount_paid INTEGER DEFAULT 0,
    is_waived BOOLEAN DEFAULT FALSE,
    waiver_reason TEXT,
    slug VARCHAR(200) UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Constraints
ALTER TABLE finances_feeaccount 
ADD CONSTRAINT check_amounts_non_negative 
CHECK (amount_due >= 0 AND amount_paid >= 0);

ALTER TABLE finances_feeaccount 
ADD CONSTRAINT unique_student_term 
UNIQUE (student_id, term_id);

-- Indexes
CREATE INDEX idx_feeaccount_student ON finances_feeaccount(student_id);
CREATE INDEX idx_feeaccount_term ON finances_feeaccount(term_id);
CREATE INDEX idx_feeaccount_waived ON finances_feeaccount(is_waived);
```

#### finances_outflow
```sql
CREATE TABLE finances_outflow (
    id BIGINT PRIMARY KEY,
    description TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    date DATE DEFAULT CURRENT_DATE,
    category VARCHAR(100),
    term_id BIGINT REFERENCES students_term(id),
    slug VARCHAR(200) UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Constraints
ALTER TABLE finances_outflow 
ADD CONSTRAINT check_outflow_amount_positive 
CHECK (amount > 0);

-- Indexes
CREATE INDEX idx_outflow_date ON finances_outflow(date);
CREATE INDEX idx_outflow_category ON finances_outflow(category);
CREATE INDEX idx_outflow_term ON finances_outflow(term_id);
```

### Academics Module

#### academics_subject
```sql
CREATE TABLE academics_subject (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    slug VARCHAR(200) UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_subject_active ON academics_subject(is_active);
CREATE UNIQUE INDEX idx_subject_code ON academics_subject(code);
```

#### academics_enrollment
```sql
CREATE TABLE academics_enrollment (
    id BIGINT PRIMARY KEY,
    student_id BIGINT REFERENCES students_student(id),
    subject_id BIGINT REFERENCES academics_subject(id),
    term_id BIGINT REFERENCES students_term(id),
    enrollment_date DATE DEFAULT CURRENT_DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Constraints
ALTER TABLE academics_enrollment 
ADD CONSTRAINT unique_student_subject_term 
UNIQUE (student_id, subject_id, term_id);

-- Indexes
CREATE INDEX idx_enrollment_student ON academics_enrollment(student_id);
CREATE INDEX idx_enrollment_subject ON academics_enrollment(subject_id);
CREATE INDEX idx_enrollment_term ON academics_enrollment(term_id);
CREATE INDEX idx_enrollment_active ON academics_enrollment(is_active);
```

#### academics_assessmenttype
```sql
CREATE TABLE academics_assessmenttype (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    weight DECIMAL(5,2) DEFAULT 0.00,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    slug VARCHAR(200) UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Constraints
ALTER TABLE academics_assessmenttype 
ADD CONSTRAINT check_weight_range 
CHECK (weight >= 0 AND weight <= 100);

-- Indexes
CREATE INDEX idx_assessmenttype_active ON academics_assessmenttype(is_active);
```

#### academics_assessment
```sql
CREATE TABLE academics_assessment (
    id BIGINT PRIMARY KEY,
    student_id BIGINT REFERENCES students_student(id),
    subject_id BIGINT REFERENCES academics_subject(id),
    assessment_type_id BIGINT REFERENCES academics_assessmenttype(id),
    term_id BIGINT REFERENCES students_term(id),
    score DECIMAL(5,2),
    max_score DECIMAL(5,2) DEFAULT 100.00,
    date_assessed DATE DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Constraints
ALTER TABLE academics_assessment 
ADD CONSTRAINT check_score_range 
CHECK (score >= 0 AND score <= max_score);

ALTER TABLE academics_assessment 
ADD CONSTRAINT unique_student_subject_assessment_term 
UNIQUE (student_id, subject_id, assessment_type_id, term_id);

-- Indexes
CREATE INDEX idx_assessment_student ON academics_assessment(student_id);
CREATE INDEX idx_assessment_subject ON academics_assessment(subject_id);
CREATE INDEX idx_assessment_type ON academics_assessment(assessment_type_id);
CREATE INDEX idx_assessment_term ON academics_assessment(term_id);
CREATE INDEX idx_assessment_date ON academics_assessment(date_assessed);
```

### Accounts Module

#### accounts_customuser
```sql
CREATE TABLE accounts_customuser (
    id BIGINT PRIMARY KEY,
    username VARCHAR(150) UNIQUE NOT NULL,
    email VARCHAR(254) UNIQUE NOT NULL,
    first_name VARCHAR(150),
    last_name VARCHAR(150),
    is_active BOOLEAN DEFAULT TRUE,
    is_staff BOOLEAN DEFAULT FALSE,
    is_superuser BOOLEAN DEFAULT FALSE,
    date_joined TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP,
    password VARCHAR(128) NOT NULL
);

-- Indexes
CREATE UNIQUE INDEX idx_user_username ON accounts_customuser(username);
CREATE UNIQUE INDEX idx_user_email ON accounts_customuser(email);
CREATE INDEX idx_user_active ON accounts_customuser(is_active);
```

## 🔗 Relationships and Constraints

### Primary Relationships

1. **Student → FeeAccount**: One-to-Many
   - Each student can have multiple fee accounts (one per term)
   - Cascade delete: When student is deleted, fee accounts are deleted

2. **FeeAccount → Receipt**: One-to-Many
   - Each fee account can have multiple receipts
   - Cascade delete: When fee account is deleted, receipts are deleted

3. **Student → Assessment**: One-to-Many
   - Each student can have multiple assessments
   - Cascade delete: When student is deleted, assessments are deleted

4. **Term → FeeAccount**: One-to-Many
   - Each term can have multiple fee accounts
   - Cascade delete: When term is deleted, fee accounts are deleted

### Business Rules

#### Fee Management
- Fee accounts are automatically created when a student is activated
- Receipt numbers are auto-generated with level prefix (e.g., "RECPRI001")
- Amount paid cannot exceed amount due unless specifically waived
- Fee accounts are unique per student per term

#### Academic Records
- Students can only be enrolled in subjects once per term
- Assessment scores must be within 0 to max_score range
- Assessment types have weights that should total 100% per subject

#### Student Management
- Student IDs are auto-generated with level abbreviation prefix
- Only one academic year and one term can be active at a time
- Student status affects fee account creation/deletion

## 📈 Data Flow Patterns

### Fee Collection Workflow
```
1. Student Registration
   ↓
2. Fee Account Creation (automatic)
   ↓
3. Payment Recording
   ↓
4. Receipt Generation
   ↓
5. Financial Reporting
```

### Academic Assessment Workflow
```
1. Subject Enrollment
   ↓
2. Assessment Type Setup
   ↓
3. Grade Entry
   ↓
4. Performance Analysis
   ↓
5. Academic Reporting
```

## 🔧 Maintenance Queries

### Data Integrity Checks
```sql
-- Check for orphaned records
SELECT * FROM finances_receipt 
WHERE fee_account_id NOT IN (SELECT id FROM finances_feeaccount);

-- Check for students without fee accounts in active term
SELECT s.* FROM students_student s
WHERE s.is_active = TRUE 
AND s.id NOT IN (
    SELECT fa.student_id FROM finances_feeaccount fa
    JOIN students_term t ON fa.term_id = t.id
    WHERE t.is_active = TRUE
);

-- Check for assessment score anomalies
SELECT * FROM academics_assessment 
WHERE score > max_score OR score < 0;
```

### Performance Optimization
```sql
-- Analyze table statistics
ANALYZE students_student;
ANALYZE finances_receipt;
ANALYZE academics_assessment;

-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

## 🚀 Migration Strategies

### Adding New Fields
```python
# Example migration for adding new field
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('students', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='student',
            name='phone_number',
            field=models.CharField(max_length=20, blank=True, null=True),
        ),
        migrations.AddIndex(
            model_name='student',
            index=models.Index(fields=['phone_number'], name='idx_student_phone'),
        ),
    ]
```

### Data Migrations
```python
# Example data migration
from django.db import migrations

def populate_student_ids(apps, schema_editor):
    Student = apps.get_model('students', 'Student')
    for student in Student.objects.filter(student_id__isnull=True):
        student.generate_student_id()
        student.save(update_fields=['student_id'])

class Migration(migrations.Migration):
    dependencies = [
        ('students', '0002_add_phone_number'),
    ]

    operations = [
        migrations.RunPython(populate_student_ids),
    ]
```

## 📊 Backup and Recovery

### Backup Strategy
```bash
# Full database backup
pg_dump receipt_gen > backup_$(date +%Y%m%d_%H%M%S).sql

# Table-specific backup
pg_dump -t students_student receipt_gen > students_backup.sql

# Schema-only backup
pg_dump --schema-only receipt_gen > schema_backup.sql
```

### Recovery Procedures
```bash
# Full restore
psql receipt_gen < backup_20240101_120000.sql

# Selective restore
pg_restore -t students_student backup_file.sql
```

---

This schema guide provides the foundation for understanding and maintaining the Receipt Generator database structure. Always test schema changes in a development environment before applying to production.
