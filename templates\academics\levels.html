{% extends 'academics/base.html' %} {% load static %}
<!--  -->
{% block title %}Classes | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-chalkboard text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Academic Classes
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Manage and view class information
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex items-center gap-3 action-buttons-slide-in">
        <div
          class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30"
        >
          <i class="fas fa-graduation-cap text-sm"></i>
          <span
            >{{level_student_count|length}}
            class{{level_student_count|length|pluralize:"es"}}</span
          >
        </div>
      </div>
    </div>
    <nav
      class="flex items-center gap-3 text-sm font-medium mt-6 breadcrumb-fade-in"
    >
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Classes</span>
    </nav>
  </div>

  <!-- Classes Grid -->
  <div class="classes-grid-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-school text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Class Overview
        </h2>
        <p class="text-[#40657F] text-sm">Student enrollment by class level</p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
    >
      {% for level, total, female, male in level_student_count %}
      <a
        href="{% url 'academics:level_details' level.slug %}"
        class="class-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-6 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 rounded-full -translate-y-10 translate-x-10"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10 rounded-full translate-y-8 -translate-x-8"
        ></div>

        <!-- Class Header -->
        <div class="flex items-center gap-3 mb-4 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-graduation-cap text-white text-lg"></i>
          </div>
          <div>
            <h3
              class="font-bold text-lg text-[#2C3E50] font-display group-hover:text-[#7AB2D3] transition-colors duration-300"
            >
              {{ level.level_name }}
            </h3>
            <p class="text-sm text-[#40657F] font-medium">Class Level</p>
          </div>
        </div>

        <!-- Total Students -->
        <div class="mb-4 relative z-10">
          <div class="flex items-center gap-2 mb-2">
            <i class="fas fa-users text-[#74C69D] text-sm"></i>
            <span
              class="text-sm font-bold text-[#40657F] uppercase tracking-wider"
              >Total Students</span
            >
          </div>
          <div class="text-3xl font-bold text-[#74C69D] font-display">
            {{ total }}
          </div>
        </div>

        <!-- Gender Breakdown -->
        <div class="space-y-2 relative z-10">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-[#7AB2D3] rounded-full"></div>
              <span class="text-sm font-semibold text-[#2C3E50]">Male</span>
            </div>
            <span class="text-sm font-bold text-[#7AB2D3]">{{ male }}</span>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-[#F28C8C] rounded-full"></div>
              <span class="text-sm font-semibold text-[#2C3E50]">Female</span>
            </div>
            <span class="text-sm font-bold text-[#F28C8C]">{{ female }}</span>
          </div>
        </div>

        <!-- Gender Distribution Bar -->
        <div class="mt-4 relative z-10">
          <div class="mb-2">
            <span
              class="text-xs font-bold text-[#40657F] uppercase tracking-wider"
              >Gender Distribution</span
            >
          </div>
          <div
            class="w-full bg-gray-100 rounded-full h-4 overflow-hidden shadow-inner border border-gray-200"
          >
            <!-- Male Section (Blue) -->
            <div class="flex h-full">
              <div
                class="male-bar bg-gradient-to-r from-[#7AB2D3] to-[#40657F] transition-all duration-700 group-hover:from-[#5a9bd4] group-hover:to-[#4a7c95] relative overflow-hidden"
                style="width: {% if total > 0 %}{% widthratio male total 100 %}%{% else %}0%{% endif %}"
              >
                <!-- Animated shine effect for male -->
                <div
                  class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 animate-shine"
                ></div>
              </div>
              <!-- Female Section (Pink) -->
              <div
                class="female-bar bg-gradient-to-r from-[#F28C8C] to-[#e07575] transition-all duration-700 group-hover:from-[#f4a6a6] group-hover:to-[#e89999] relative overflow-hidden flex-1"
              >
                <!-- Animated shine effect for female -->
                <div
                  class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 animate-shine"
                  style="animation-delay: 3.5s"
                ></div>
              </div>
            </div>
          </div>

          <!-- Gender Legend -->
          <div class="flex justify-between items-center mt-3 progress-labels">
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-2">
                <div
                  class="w-3 h-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full shadow-sm"
                ></div>
                <span class="text-xs font-semibold text-[#40657F]"
                  >{{ male }} Boys</span
                >
              </div>
              <div class="flex items-center gap-2">
                <div
                  class="w-3 h-3 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full shadow-sm"
                ></div>
                <span class="text-xs font-semibold text-[#40657F]"
                  >{{ female }} Girls</span
                >
              </div>
            </div>
            <span class="text-xs font-bold text-[#2C3E50]"
              >{{ total }} Total</span
            >
          </div>
        </div>

        <!-- Hover Arrow -->
        <div
          class="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        >
          <i class="fas fa-arrow-right text-[#7AB2D3] text-lg"></i>
        </div>
      </a>
      {% empty %}
      <div class="col-span-full text-center py-12">
        <div class="flex flex-col items-center gap-4">
          <div
            class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-chalkboard text-[#B9D8EB] text-2xl"></i>
          </div>
          <div>
            <h4 class="text-lg font-bold text-[#2C3E50] mb-2">
              No Classes Found
            </h4>
            <p class="text-[#40657F]">
              No academic classes have been created yet.
            </p>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Classes Grid Animations */
  .classes-grid-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: classesGridFadeIn 0.8s ease-out 1s forwards;
  }

  .class-card {
    opacity: 0;
    transform: translateY(30px);
    animation: classCardSlideIn 0.6s ease-out forwards;
  }

  .class-card:nth-child(1) {
    animation-delay: 1.2s;
  }
  .class-card:nth-child(2) {
    animation-delay: 1.3s;
  }
  .class-card:nth-child(3) {
    animation-delay: 1.4s;
  }
  .class-card:nth-child(4) {
    animation-delay: 1.5s;
  }
  .class-card:nth-child(5) {
    animation-delay: 1.6s;
  }
  .class-card:nth-child(6) {
    animation-delay: 1.7s;
  }
  .class-card:nth-child(7) {
    animation-delay: 1.8s;
  }
  .class-card:nth-child(8) {
    animation-delay: 1.9s;
  }

  /* Progress Bar Animations */
  .progress-bar {
    animation-delay: 2.2s;
    transform: scaleX(0);
    transform-origin: left;
    animation: progressBarGrow 1.5s ease-out forwards;
  }

  .progress-labels {
    opacity: 0;
    animation: progressLabelsFadeIn 0.8s ease-out forwards;
    animation-delay: 2.8s;
  }

  /* Enhanced Card Hover Effects */
  .class-card:hover .progress-bar {
    animation: progressBarPulse 2s ease-in-out infinite;
  }

  /* Gender Breakdown Animation */
  .class-card .space-y-2 > div {
    opacity: 0;
    transform: translateX(-10px);
    animation: genderItemSlideIn 0.4s ease-out forwards;
  }

  .class-card .space-y-2 > div:nth-child(1) {
    animation-delay: 2s;
  }

  .class-card .space-y-2 > div:nth-child(2) {
    animation-delay: 2.1s;
  }

  /* Total Students Counter Animation */
  .class-card .text-3xl {
    opacity: 0;
    transform: scale(0.5);
    animation: counterPop 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
    animation-delay: 1.8s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes classesGridFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes classCardSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Progress Bar Keyframes */
  @keyframes progressBarGrow {
    from {
      transform: scaleX(0);
      opacity: 0;
    }
    to {
      transform: scaleX(1);
      opacity: 1;
    }
  }

  @keyframes progressLabelsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes progressBarPulse {
    0%,
    100% {
      transform: scaleY(1);
      filter: brightness(1);
    }
    50% {
      transform: scaleY(1.1);
      filter: brightness(1.2);
    }
  }

  /* Enhanced Hover Effects */

  @keyframes genderItemSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes counterPop {
    0% {
      opacity: 0;
      transform: scale(0.5) rotate(-5deg);
    }
    70% {
      transform: scale(1.1) rotate(2deg);
    }
    100% {
      opacity: 1;
      transform: scale(1) rotate(0deg);
    }
  }

  /* Shine Effect */
  @keyframes shine {
    0% {
      transform: translateX(-100%) skewX(-12deg);
    }
    100% {
      transform: translateX(200%) skewX(-12deg);
    }
  }

  .animate-shine {
    animation: shine 2s ease-in-out infinite;
    animation-delay: 3s;
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .class-card {
      animation-delay: 1s;
    }

    .class-card:nth-child(n) {
      animation-delay: calc(1s + 0.1s * var(--card-index, 1));
    }
  }
</style>

{% endblock %}
