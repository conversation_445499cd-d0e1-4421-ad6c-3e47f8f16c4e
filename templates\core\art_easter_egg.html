<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} | Tiny Feet MIS</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Pacifico:wght@400&display=swap" rel="stylesheet">
    
    <style>
        .font-display { font-family: 'Pacifico', cursive; }
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="overflow-x-hidden">

<section class="w-full min-h-screen flex items-center justify-center px-4 py-8 bg-gradient-to-br from-rose-100 via-orange-50 to-amber-100 relative overflow-hidden">
  <!-- Animated Background Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <!-- Paint Splashes -->
    <div class="paint-splash splash-1"></div>
    <div class="paint-splash splash-2"></div>
    <div class="paint-splash splash-3"></div>
    <div class="paint-splash splash-4"></div>
    <div class="paint-splash splash-5"></div>
    
    <!-- Floating Art Elements -->
    <div class="art-element element-1">🎨</div>
    <div class="art-element element-2">🖌️</div>
    <div class="art-element element-3">🎭</div>
    <div class="art-element element-4">🖼️</div>
    <div class="art-element element-5">✨</div>
    <div class="art-element element-6">🌈</div>
    <div class="art-element element-7">🎨</div>
    <div class="art-element element-8">🖌️</div>
    
    <!-- Color Waves -->
    <div class="color-wave wave-1"></div>
    <div class="color-wave wave-2"></div>
    <div class="color-wave wave-3"></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 max-w-4xl mx-auto text-center space-y-8 main-content-fade-in">
    
    <!-- Header Section -->
    <div class="space-y-6 header-slide-in">
      <!-- Art Icon -->
      <div class="flex justify-center">
        <div class="w-24 h-24 bg-gradient-to-br from-rose-400 via-orange-400 to-amber-500 rounded-full flex items-center justify-center shadow-2xl icon-float relative overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent rounded-full"></div>
          <i class="fas fa-palette text-white text-4xl icon-pulse relative z-10"></i>
        </div>
      </div>
      
      <!-- Title -->
      <div class="space-y-4">
        <h1 class="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-rose-500 via-orange-500 to-amber-600 font-display title-glow">
          🎨 Artistic Moment
        </h1>
        <p class="text-xl md:text-2xl text-rose-700 font-light subtitle-fade-in">
          {{ trigger.message }}
        </p>
        <div class="w-32 h-1 bg-gradient-to-r from-rose-400 to-amber-400 rounded-full mx-auto accent-line-grow"></div>
      </div>
    </div>

    <!-- Quote Card -->
    <div class="quote-card bg-white/80 backdrop-blur-lg border border-white/40 rounded-3xl p-8 md:p-12 shadow-2xl quote-slide-in relative overflow-hidden">
      <!-- Artistic Background Effect -->
      <div class="absolute inset-0 bg-gradient-to-br from-rose-200/30 via-orange-200/30 to-amber-200/30 rounded-3xl"></div>
      
      <!-- Quote Content -->
      <div class="space-y-6 relative z-10" id="quote-content">
        <!-- Quote Text -->
        <blockquote class="text-2xl md:text-3xl lg:text-4xl font-light text-gray-800 leading-relaxed quote-text-fade-in">
          <i class="fas fa-quote-left text-rose-500 text-2xl mr-4 opacity-60"></i>
          <span id="quote-text">{{ quote.quote }}</span>
          <i class="fas fa-quote-right text-rose-500 text-2xl ml-4 opacity-60"></i>
        </blockquote>
        
        <!-- Author & Context -->
        <div class="space-y-3 author-info-slide-in">
          <div class="text-xl md:text-2xl font-semibold text-rose-600" id="quote-author">
            — {{ quote.author }}
          </div>
          <div class="text-lg text-orange-600 opacity-80" id="quote-context">
            <i class="fas fa-brush mr-2"></i>{{ quote.context }}
          </div>
          <div class="flex flex-wrap gap-2 justify-center">
            <div class="inline-flex items-center gap-2 bg-rose-500/20 text-rose-700 px-4 py-2 rounded-full text-sm font-medium" id="quote-theme">
              <i class="fas fa-heart"></i>
              {{ quote.theme }}
            </div>
            <div class="inline-flex items-center gap-2 bg-orange-500/20 text-orange-700 px-4 py-2 rounded-full text-sm font-medium" id="quote-category">
              <i class="fas fa-paint-brush"></i>
              {{ quote.category }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center buttons-slide-in">
      <!-- New Quote Button -->
      <button
        id="new-quote-btn"
        class="group bg-gradient-to-r from-rose-500 to-orange-500 text-white font-bold py-4 px-8 rounded-2xl hover:from-rose-600 hover:to-orange-600 focus:ring-4 focus:ring-rose-500/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl"
      >
        <i class="fas fa-magic mr-3 group-hover:rotate-180 transition-transform duration-500"></i>
        Create Another
      </button>
      
      <!-- Share Quote Button -->
      <button
        id="share-quote-btn"
        class="group bg-white/60 backdrop-blur-sm text-gray-700 font-bold py-4 px-8 rounded-2xl hover:bg-white/80 focus:ring-4 focus:ring-white/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl border border-white/40"
      >
        <i class="fas fa-share mr-3 group-hover:scale-110 transition-transform duration-300"></i>
        Share Inspiration
      </button>
      
      <!-- Return Button -->
      <a
        href="javascript:history.back()"
        class="group bg-gray-600/50 backdrop-blur-sm text-white font-bold py-4 px-8 rounded-2xl hover:bg-gray-700/50 focus:ring-4 focus:ring-gray-500/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl border border-white/20"
      >
        <i class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-300"></i>
        Back to Reality
      </a>
    </div>

    <!-- Easter Egg Discovery Message -->
    <div class="bg-gradient-to-r from-rose-500/10 to-orange-500/10 border border-rose-400/30 rounded-2xl p-6 discovery-message-fade-in">
      <div class="flex items-center justify-center gap-3 text-rose-700">
        <i class="fas fa-egg text-2xl text-rose-500"></i>
        <div class="text-center">
          <p class="font-semibold">🎉 Congratulations! You've discovered an artistic easter egg!</p>
          <p class="text-sm opacity-80 mt-1">
            Your search for "{{ trigger.search_term }}" has unlocked this hidden feature. 
            The system appreciates creative souls who see beauty in learning.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  /* Artistic Background Animations */
  .paint-splash {
    position: absolute;
    border-radius: 50%;
    animation: paintSplash 6s ease-in-out infinite;
  }

  .splash-1 {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(244, 63, 94, 0.3), transparent);
    top: 15%;
    left: 10%;
    animation-delay: 0s;
  }

  .splash-2 {
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(251, 146, 60, 0.3), transparent);
    top: 30%;
    right: 15%;
    animation-delay: 1s;
  }

  .splash-3 {
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(245, 158, 11, 0.3), transparent);
    bottom: 20%;
    left: 20%;
    animation-delay: 2s;
  }

  .splash-4 {
    width: 70px;
    height: 70px;
    background: radial-gradient(circle, rgba(236, 72, 153, 0.3), transparent);
    top: 60%;
    right: 25%;
    animation-delay: 3s;
  }

  .splash-5 {
    width: 90px;
    height: 90px;
    background: radial-gradient(circle, rgba(168, 85, 247, 0.3), transparent);
    bottom: 40%;
    right: 10%;
    animation-delay: 4s;
  }

  .art-element {
    position: absolute;
    font-size: 2rem;
    animation: artFloat 8s ease-in-out infinite;
    opacity: 0.7;
  }

  .element-1 { top: 10%; left: 15%; animation-delay: 0s; }
  .element-2 { top: 25%; right: 20%; animation-delay: 1s; }
  .element-3 { top: 45%; left: 10%; animation-delay: 2s; }
  .element-4 { top: 65%; right: 15%; animation-delay: 3s; }
  .element-5 { top: 80%; left: 25%; animation-delay: 4s; }
  .element-6 { top: 20%; left: 60%; animation-delay: 5s; }
  .element-7 { top: 50%; right: 30%; animation-delay: 6s; }
  .element-8 { top: 75%; right: 40%; animation-delay: 7s; }

  .color-wave {
    position: absolute;
    height: 200px;
    width: 200px;
    border-radius: 50%;
    filter: blur(60px);
    animation: colorWave 10s ease-in-out infinite;
  }

  .wave-1 {
    background: linear-gradient(45deg, rgba(244, 63, 94, 0.2), rgba(251, 146, 60, 0.2));
    top: 20%;
    left: 30%;
    animation-delay: 0s;
  }

  .wave-2 {
    background: linear-gradient(45deg, rgba(251, 146, 60, 0.2), rgba(245, 158, 11, 0.2));
    bottom: 30%;
    right: 20%;
    animation-delay: -3s;
  }

  .wave-3 {
    background: linear-gradient(45deg, rgba(236, 72, 153, 0.2), rgba(168, 85, 247, 0.2));
    top: 50%;
    left: 10%;
    animation-delay: -6s;
  }

  /* Content Animations */
  .main-content-fade-in {
    opacity: 0;
    animation: mainContentFadeIn 1s ease-out 0.5s forwards;
  }

  .header-slide-in {
    opacity: 0;
    transform: translateY(-50px);
    animation: headerSlideIn 1s ease-out 0.8s forwards;
  }

  .icon-float {
    animation: iconFloat 4s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 3s ease-in-out infinite;
  }

  .title-glow {
    animation: titleGlow 3s ease-in-out infinite;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 1s ease-out 1.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 1s ease-out 1.5s forwards;
  }

  .quote-slide-in {
    opacity: 0;
    transform: translateY(50px);
    animation: quoteSlideIn 1s ease-out 1.8s forwards;
  }

  .quote-text-fade-in {
    opacity: 0;
    animation: quoteTextFadeIn 1s ease-out 2.2s forwards;
  }

  .author-info-slide-in {
    opacity: 0;
    transform: translateX(-30px);
    animation: authorInfoSlideIn 1s ease-out 2.5s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: buttonsSlideIn 1s ease-out 2.8s forwards;
  }

  .discovery-message-fade-in {
    opacity: 0;
    animation: discoveryMessageFadeIn 1s ease-out 3.2s forwards;
  }

  /* Keyframe Definitions */
  @keyframes paintSplash {
    0%, 100% { 
      transform: scale(1) rotate(0deg); 
      opacity: 0.3; 
    }
    50% { 
      transform: scale(1.2) rotate(180deg); 
      opacity: 0.6; 
    }
  }

  @keyframes artFloat {
    0%, 100% { 
      transform: translateY(0px) rotate(0deg); 
      opacity: 0.7; 
    }
    25% { 
      transform: translateY(-20px) rotate(90deg); 
      opacity: 0.9; 
    }
    50% { 
      transform: translateY(-10px) rotate(180deg); 
      opacity: 1; 
    }
    75% { 
      transform: translateY(-15px) rotate(270deg); 
      opacity: 0.8; 
    }
  }

  @keyframes colorWave {
    0%, 100% { 
      transform: scale(1) rotate(0deg); 
      opacity: 0.2; 
    }
    50% { 
      transform: scale(1.3) rotate(180deg); 
      opacity: 0.4; 
    }
  }

  @keyframes mainContentFadeIn {
    to { opacity: 1; }
  }

  @keyframes headerSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  @keyframes titleGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 8rem; }
  }

  @keyframes quoteSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes quoteTextFadeIn {
    to { opacity: 1; }
  }

  @keyframes authorInfoSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes buttonsSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes discoveryMessageFadeIn {
    to { opacity: 1; }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .paint-splash, .art-element, .color-wave {
      display: none;
    }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const newQuoteBtn = document.getElementById('new-quote-btn');
    const shareQuoteBtn = document.getElementById('share-quote-btn');

    // New Quote functionality
    newQuoteBtn.addEventListener('click', function() {
        // Add loading state
        const originalText = this.innerHTML;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-3"></i>Loading...';
        this.disabled = true;

        // Fetch new quote
        fetch('{% url "core:get_new_art_quote" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update quote content with animation
                const quoteContent = document.getElementById('quote-content');
                quoteContent.style.opacity = '0';
                quoteContent.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    document.getElementById('quote-text').textContent = data.quote.quote;
                    document.getElementById('quote-author').textContent = '— ' + data.quote.author;
                    document.getElementById('quote-context').innerHTML = '<i class="fas fa-brush mr-2"></i>' + data.quote.context;
                    document.getElementById('quote-theme').innerHTML = '<i class="fas fa-heart"></i> ' + data.quote.theme;
                    document.getElementById('quote-category').innerHTML = '<i class="fas fa-paint-brush"></i> ' + data.quote.category;

                    quoteContent.style.opacity = '1';
                    quoteContent.style.transform = 'translateY(0)';
                }, 300);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        })
        .finally(() => {
            // Restore button
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 1000);
        });
    });

    // Share Quote functionality
    shareQuoteBtn.addEventListener('click', function() {
        const quote = document.getElementById('quote-text').textContent;
        const author = document.getElementById('quote-author').textContent;
        const shareText = `"${quote}" ${author}`;

        if (navigator.share) {
            navigator.share({
                title: 'Artistic Inspiration',
                text: shareText,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareText).then(() => {
                // Show feedback
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check mr-3"></i>Copied!';
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            });
        }
    });
});
</script>

</body>
</html>
