# Deployment Guide

## 🚀 Overview

This guide provides comprehensive instructions for deploying the Receipt Generator system to production environments, including server setup, configuration, and maintenance procedures.

## 🏗️ Deployment Architecture

### Production Stack
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Server    │    │    Database     │
│    (Nginx)      │────│   (Gun<PERSON>)    │────│  (PostgreSQL)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │  Static Files   │              │
         └──────────────│     (CDN)       │──────────────┘
                        │                 │
                        └─────────────────┘
```

### Environment Types
- **Development**: Local development with SQLite
- **Staging**: Production-like environment for testing
- **Production**: Live environment with full security and performance optimizations

## 🛠️ Server Requirements

### Minimum System Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **OS**: Ubuntu 20.04 LTS or CentOS 8
- **Python**: 3.8+
- **PostgreSQL**: 12+
- **Nginx**: 1.18+

### Recommended Production Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **Backup Storage**: Additional space for database backups
- **SSL Certificate**: Let's Encrypt or commercial certificate

## 🔧 Server Setup

### 1. System Updates and Dependencies
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y python3 python3-pip python3-venv postgresql postgresql-contrib nginx git curl

# Install Node.js for Tailwind CSS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Install additional tools
sudo apt install -y htop ufw fail2ban certbot python3-certbot-nginx
```

### 2. PostgreSQL Setup
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE receipt_gen;
CREATE USER receipt_gen_user WITH PASSWORD 'secure_password_here';
ALTER ROLE receipt_gen_user SET client_encoding TO 'utf8';
ALTER ROLE receipt_gen_user SET default_transaction_isolation TO 'read committed';
ALTER ROLE receipt_gen_user SET timezone TO 'UTC';
GRANT ALL PRIVILEGES ON DATABASE receipt_gen TO receipt_gen_user;
\q

# Configure PostgreSQL
sudo nano /etc/postgresql/12/main/postgresql.conf
# Uncomment and modify:
# listen_addresses = 'localhost'
# max_connections = 100

sudo nano /etc/postgresql/12/main/pg_hba.conf
# Add line:
# local   receipt_gen     receipt_gen_user                md5

# Restart PostgreSQL
sudo systemctl restart postgresql
sudo systemctl enable postgresql
```

### 3. Application User Setup
```bash
# Create application user
sudo adduser receipt_gen --disabled-password --gecos ""

# Add to sudo group (optional, for maintenance)
sudo usermod -aG sudo receipt_gen

# Switch to application user
sudo su - receipt_gen
```

## 📦 Application Deployment

### 1. Code Deployment
```bash
# Clone repository
git clone https://github.com/your-repo/receipt_gen.git
cd receipt_gen

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt

# Install Node.js dependencies
npm install
```

### 2. Environment Configuration
```bash
# Create production environment file
nano .env
```

```env
# Production Environment Configuration
DJANGO_SECRET_KEY=your-super-secret-production-key-here
DEBUG=False
DEV_MODE=False
DJANGO_ALLOWED_HOST=yourdomain.com,www.yourdomain.com

# Database Configuration
DATABASE_URL=postgresql://receipt_gen_user:secure_password_here@localhost:5432/receipt_gen

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Static Files
STATIC_ROOT=/var/www/receipt_gen/static/
MEDIA_ROOT=/var/www/receipt_gen/media/
```

### 3. Database Migration
```bash
# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput

# Compile Tailwind CSS for production
npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css --minify
```

### 4. Static Files Directory Setup
```bash
# Create static files directories
sudo mkdir -p /var/www/receipt_gen/static
sudo mkdir -p /var/www/receipt_gen/media
sudo chown -R receipt_gen:receipt_gen /var/www/receipt_gen
sudo chmod -R 755 /var/www/receipt_gen
```

## 🔧 Web Server Configuration

### 1. Gunicorn Setup
```bash
# Create Gunicorn configuration
nano /home/<USER>/receipt_gen/gunicorn.conf.py
```

```python
# gunicorn.conf.py
bind = "127.0.0.1:8000"
workers = 3
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
daemon = False
user = "receipt_gen"
group = "receipt_gen"
tmp_upload_dir = None
errorlog = "/var/log/receipt_gen/gunicorn_error.log"
accesslog = "/var/log/receipt_gen/gunicorn_access.log"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
loglevel = "info"
```

### 2. Systemd Service
```bash
# Create systemd service file
sudo nano /etc/systemd/system/receipt_gen.service
```

```ini
[Unit]
Description=Receipt Generator Django Application
After=network.target postgresql.service
Requires=postgresql.service

[Service]
Type=exec
User=receipt_gen
Group=receipt_gen
WorkingDirectory=/home/<USER>/receipt_gen
Environment=PATH=/home/<USER>/receipt_gen/venv/bin
ExecStart=/home/<USER>/receipt_gen/venv/bin/gunicorn --config gunicorn.conf.py receipt_gen.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

```bash
# Create log directory
sudo mkdir -p /var/log/receipt_gen
sudo chown receipt_gen:receipt_gen /var/log/receipt_gen

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable receipt_gen
sudo systemctl start receipt_gen
sudo systemctl status receipt_gen
```

### 3. Nginx Configuration
```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/receipt_gen
```

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
    
    # Static Files
    location /static/ {
        alias /var/www/receipt_gen/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /media/ {
        alias /var/www/receipt_gen/media/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # Django Application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Security
    location ~ /\.ht {
        deny all;
    }
    
    # Favicon
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
    
    # Robots
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }
}
```

```bash
# Enable site and restart Nginx
sudo ln -s /etc/nginx/sites-available/receipt_gen /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 🔒 SSL Certificate Setup

### Let's Encrypt SSL
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test automatic renewal
sudo certbot renew --dry-run

# Set up automatic renewal
sudo crontab -e
# Add line:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔥 Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
sudo ufw status
```

## 📊 Monitoring and Logging

### 1. Log Rotation
```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/receipt_gen
```

```
/var/log/receipt_gen/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 receipt_gen receipt_gen
    postrotate
        systemctl reload receipt_gen
    endscript
}
```

### 2. System Monitoring
```bash
# Install monitoring tools
sudo apt install htop iotop nethogs

# Monitor application
sudo systemctl status receipt_gen
sudo journalctl -u receipt_gen -f

# Monitor Nginx
sudo systemctl status nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Monitor PostgreSQL
sudo systemctl status postgresql
sudo tail -f /var/log/postgresql/postgresql-12-main.log
```

## 🔄 Backup Strategy

### 1. Database Backup
```bash
# Create backup script
nano /home/<USER>/backup_db.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="receipt_gen"
DB_USER="receipt_gen_user"

mkdir -p $BACKUP_DIR

# Create database backup
pg_dump -U $DB_USER -h localhost $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: db_backup_$DATE.sql.gz"
```

```bash
# Make script executable
chmod +x /home/<USER>/backup_db.sh

# Add to crontab for daily backups
crontab -e
# Add line:
# 0 2 * * * /home/<USER>/backup_db.sh
```

### 2. Application Backup
```bash
# Create application backup script
nano /home/<USER>/backup_app.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/home/<USER>/receipt_gen"

mkdir -p $BACKUP_DIR

# Backup application files (excluding venv and node_modules)
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz \
    --exclude='venv' \
    --exclude='node_modules' \
    --exclude='*.pyc' \
    --exclude='__pycache__' \
    -C /home/<USER>

# Remove backups older than 7 days
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +7 -delete

echo "Application backup completed: app_backup_$DATE.tar.gz"
```

## 🚀 Deployment Automation

### 1. Deployment Script
```bash
# Create deployment script
nano /home/<USER>/deploy.sh
```

```bash
#!/bin/bash
set -e

APP_DIR="/home/<USER>/receipt_gen"
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

echo "Starting deployment..."

# Backup current version
echo "Creating backup..."
cp -r $APP_DIR $BACKUP_DIR/app_backup_$DATE

# Pull latest code
echo "Pulling latest code..."
cd $APP_DIR
git pull origin main

# Activate virtual environment
source venv/bin/activate

# Install/update dependencies
echo "Installing dependencies..."
pip install -r requirements.txt
npm install

# Run migrations
echo "Running migrations..."
python manage.py migrate

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Compile CSS
echo "Compiling CSS..."
npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css --minify

# Restart services
echo "Restarting services..."
sudo systemctl restart receipt_gen
sudo systemctl reload nginx

echo "Deployment completed successfully!"
```

```bash
# Make script executable
chmod +x /home/<USER>/deploy.sh
```

## 🔍 Health Checks

### 1. Application Health Check
```bash
# Create health check script
nano /home/<USER>/health_check.sh
```

```bash
#!/bin/bash

# Check if application is responding
if curl -f -s http://localhost:8000/health/ > /dev/null; then
    echo "Application is healthy"
    exit 0
else
    echo "Application is not responding"
    # Restart application
    sudo systemctl restart receipt_gen
    exit 1
fi
```

### 2. System Health Monitoring
```bash
# Add to crontab for regular health checks
crontab -e
# Add lines:
# */5 * * * * /home/<USER>/health_check.sh
# 0 6 * * * /home/<USER>/backup_db.sh
# 0 3 * * 0 /home/<USER>/backup_app.sh
```

## 📋 Maintenance Procedures

### Regular Maintenance Tasks
1. **Weekly**: Review logs for errors
2. **Monthly**: Update system packages
3. **Quarterly**: Review and update dependencies
4. **Annually**: Renew SSL certificates (if not automated)

### Emergency Procedures
1. **Application Down**: Check service status, restart if needed
2. **Database Issues**: Check PostgreSQL logs, restart service
3. **High Load**: Monitor resources, scale if necessary
4. **Security Incident**: Review logs, update passwords, patch vulnerabilities

---

This deployment guide provides a comprehensive foundation for running the Receipt Generator system in production. Always test deployment procedures in a staging environment before applying to production.
