from django import forms
from django.core.exceptions import ValidationError
from datetime import date, timedelta

from students.models import AcademicYear, Term


class AcademicYearForm(forms.ModelForm):
    """Form for creating and editing academic years"""
    
    class Meta:
        model = AcademicYear
        fields = ['name', 'start_date', 'end_date']
        
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'placeholder': 'e.g., 2024-2025'
            }),
            'start_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]'
            }),
            'end_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]'
            }),
        }
        
        labels = {
            'name': 'Academic Year Name',
            'start_date': 'Start Date',
            'end_date': 'End Date',
        }
        
        help_texts = {
            'name': 'Enter the academic year name (e.g., 2024-2025)',
            'start_date': 'Select the start date of the academic year',
            'end_date': 'Select the end date of the academic year',
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if start_date and end_date:
            # Validate that end date is after start date
            if end_date <= start_date:
                raise ValidationError('End date must be after start date.')
            
            # Validate that the academic year is approximately one year
            if (end_date - start_date).days < 300:
                raise ValidationError('Academic year should be at least 300 days long.')
            
            if (end_date - start_date).days > 400:
                raise ValidationError('Academic year should not exceed 400 days.')
        
        return cleaned_data

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            # Check for duplicate names (excluding current instance if editing)
            existing = AcademicYear.objects.filter(name=name)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError('An academic year with this name already exists.')
        
        return name


class TermForm(forms.ModelForm):
    """Form for creating and editing terms"""
    
    class Meta:
        model = Term
        fields = ['term_name', 'academic_year', 'start_date', 'end_date']
        
        widgets = {
            'term_name': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
            }),
            'academic_year': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
            }),
            'start_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]'
            }),
            'end_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]'
            }),
        }
        
        labels = {
            'term_name': 'Term Name',
            'academic_year': 'Academic Year',
            'start_date': 'Start Date',
            'end_date': 'End Date',
        }
        
        help_texts = {
            'term_name': 'Select the term (Term 1, Term 2, or Term 3)',
            'academic_year': 'Select the academic year this term belongs to',
            'start_date': 'Select the start date of the term',
            'end_date': 'Select the end date of the term',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only show active academic years or the current one if editing
        if self.instance.pk:
            self.fields['academic_year'].queryset = AcademicYear.objects.all()
        else:
            self.fields['academic_year'].queryset = AcademicYear.objects.filter(is_active=True)

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        academic_year = cleaned_data.get('academic_year')
        term_name = cleaned_data.get('term_name')
        
        if start_date and end_date:
            # Validate that end date is after start date
            if end_date <= start_date:
                raise ValidationError('End date must be after start date.')
            
            # Validate that term is between 60-120 days
            term_length = (end_date - start_date).days
            if term_length < 60:
                raise ValidationError('Term should be at least 60 days long.')
            
            if term_length > 120:
                raise ValidationError('Term should not exceed 120 days.')
        
        if academic_year and start_date and end_date:
            # Validate that term dates are within academic year
            if start_date < academic_year.start_date:
                raise ValidationError('Term start date must be within the academic year.')
            
            if end_date > academic_year.end_date:
                raise ValidationError('Term end date must be within the academic year.')
        
        if academic_year and term_name:
            # Check for duplicate terms in the same academic year
            existing = Term.objects.filter(
                academic_year=academic_year,
                term_name=term_name
            )
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError(f'{term_name} already exists for {academic_year.name}.')
        
        return cleaned_data


class CloseAcademicYearForm(forms.Form):
    """Form for closing an academic year"""
    
    academic_year = forms.ModelChoiceField(
        queryset=AcademicYear.objects.filter(is_active=True),
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
        }),
        label='Academic Year to Close',
        help_text='Select the academic year you want to close'
    )
    
    confirm_closure = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-4 h-4 text-[#7AB2D3] bg-gray-100 border-gray-300 rounded focus:ring-[#7AB2D3] focus:ring-2'
        }),
        label='I confirm that I want to close this academic year',
        help_text='This action will deactivate the academic year and all its terms'
    )


class ActivateTermForm(forms.Form):
    """Form for activating a specific term"""
    
    term = forms.ModelChoiceField(
        queryset=Term.objects.all(),
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
        }),
        label='Term to Activate',
        help_text='Select the term you want to activate'
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only show terms from active academic years
        self.fields['term'].queryset = Term.objects.filter(
            academic_year__is_active=True
        ).select_related('academic_year')
