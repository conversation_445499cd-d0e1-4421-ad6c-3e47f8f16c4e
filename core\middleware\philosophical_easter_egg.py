"""
Philosophical Easter Egg Middleware
Detects philosophical search terms and redirects to the easter egg page
"""

from django.shortcuts import redirect
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin
from urllib.parse import urlencode

from core.philosophical_quotes import is_philosophical_search


class PhilosophicalEasterEggMiddleware(MiddlewareMixin):
    """
    Middleware to detect philosophical search terms and redirect to easter egg
    """
    
    def process_request(self, request):
        """
        Check if the request contains philosophical search terms
        """
        # Skip if already on the easter egg page
        if request.path.startswith('/core/philosophical-moment/'):
            return None
        
        # Skip for admin, API, and static files
        if any(request.path.startswith(path) for path in ['/admin/', '/api/', '/static/', '/media/']):
            return None
        
        # Check GET parameters for search terms
        search_params = [
            'search', 'q', 'query', 'search_query', 'term', 
            'subject_search', 'assessment_search', 'activity_search',
            'student_search', 'teacher_search', 'finance_search'
        ]
        
        for param in search_params:
            search_term = request.GET.get(param, '').strip()
            if search_term and is_philosophical_search(search_term):
                # Redirect to philosophical easter egg with the search term
                easter_egg_url = reverse('core:philosophical_easter_egg')
                query_params = urlencode({'search': search_term})
                return redirect(f'{easter_egg_url}?{query_params}')
        
        # Check POST data for search terms (for forms)
        if request.method == 'POST':
            for param in search_params:
                search_term = request.POST.get(param, '').strip()
                if search_term and is_philosophical_search(search_term):
                    # For POST requests, redirect with GET parameters
                    easter_egg_url = reverse('core:philosophical_easter_egg')
                    query_params = urlencode({'search': search_term})
                    return redirect(f'{easter_egg_url}?{query_params}')
        
        return None
