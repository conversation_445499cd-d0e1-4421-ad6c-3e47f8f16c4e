# 🧪 Testing Notification Badge Functionality

## Quick Test Commands

### 1. Create Test Notifications
```bash
# Create a test notification to see the badge
python manage.py create_notification \
    --title "Test Badge Notification" \
    --message "This is a test notification to verify the badge appears correctly" \
    --priority medium

# Create multiple notifications to test count display
python manage.py create_notification \
    --title "Test Notification 1" \
    --message "First test notification" \
    --priority low

python manage.py create_notification \
    --title "Test Notification 2" \
    --message "Second test notification" \
    --priority high

python manage.py create_notification \
    --title "Test Notification 3" \
    --message "Third test notification" \
    --priority urgent
```

### 2. Test Badge Display
1. **Navigate to Dashboard**: Go to `/students/` (home page)
2. **Check Badge**: Look for red badge on "Notifications" tab
3. **Verify Count**: Badge should show number of unread notifications
4. **Test Animation**: Badge should have a subtle pulse animation

### 3. Test Badge Behavior
1. **Click Notifications Tab**: Badge should disappear when tab is opened
2. **Mark Notifications Read**: Badge count should decrease
3. **Mark All Read**: Badge should completely disappear
4. **Create New Notification**: Badge should reappear

### 4. Test Different Scenarios

#### Test High Count (9+ Badge)
```bash
# Create 12 notifications to test "9+" display
for i in {1..12}; do
    python manage.py create_notification \
        --title "Bulk Test $i" \
        --message "Testing high count badge display" \
        --priority medium
done
```

#### Test Targeted Notifications
```bash
# Create notification for specific user
python manage.py create_notification \
    --title "Personal Notification" \
    --message "This notification is targeted to you specifically" \
    --target-users your_username_here
```

### 5. Verify API Responses

#### Check Unread Count
```bash
# Test the API endpoint directly
curl -H "Cookie: sessionid=your_session_id" \
     "http://localhost:8000/core/api/notifications/?unread_only=true&limit=50"
```

#### Expected Response
```json
{
    "success": true,
    "notifications": [...],
    "count": 3
}
```

## Visual Test Checklist

### ✅ Badge Appearance
- [ ] Badge appears when unread notifications exist
- [ ] Badge shows correct count (1-9)
- [ ] Badge shows "9+" for 10+ notifications
- [ ] Badge has red color (#F28C8C)
- [ ] Badge has white border
- [ ] Badge has pulse animation

### ✅ Badge Behavior
- [ ] Badge disappears when notifications tab is clicked
- [ ] Badge updates when notifications are marked read
- [ ] Badge updates when "Mark All Read" is clicked
- [ ] Badge refreshes automatically (every 2 minutes)
- [ ] Badge appears for new notifications

### ✅ User Experience
- [ ] Badge is clearly visible against tab background
- [ ] Badge doesn't interfere with tab text
- [ ] Badge animation is smooth and non-distracting
- [ ] Badge tooltip shows helpful text
- [ ] Badge positioning is consistent across browsers

## Troubleshooting Test Issues

### Badge Not Appearing
1. **Check Console**: Open browser dev tools, look for JavaScript errors
2. **Verify API**: Test `/core/api/notifications/?unread_only=true` endpoint
3. **Check Database**: Ensure notifications exist and are unread
4. **Clear Cache**: Hard refresh the page (Ctrl+F5)

### Badge Count Wrong
1. **Database Check**: Verify notification read status in admin
2. **API Response**: Check if API returns correct count
3. **User Targeting**: Ensure notifications target current user
4. **Session Issues**: Verify user is properly logged in

### Badge Not Updating
1. **Network Tab**: Check for failed API requests
2. **JavaScript Errors**: Look for console errors
3. **Interval Check**: Verify automatic refresh is working
4. **Manual Refresh**: Test by reloading the page

## Browser Compatibility

### Tested Browsers
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### Known Issues
- **IE 11**: CSS animations may not work properly
- **Mobile Safari**: Badge positioning may need adjustment
- **Old Chrome**: Pulse animation may be choppy

## Performance Notes

### API Call Frequency
- **Initial Load**: 1 API call on page load
- **Tab Switch**: 1 API call when notifications tab opened
- **Mark Read**: 1 API call per notification marked
- **Auto Refresh**: 1 API call every 2 minutes

### Optimization Tips
- Badge uses lightweight API calls (unread_only=true)
- Automatic refresh can be disabled if needed
- Badge elements are hidden/shown with CSS (no DOM manipulation)

## Clean Up Test Data

### Remove Test Notifications
```bash
# Remove all test notifications
python manage.py shell -c "
from core.models import Notification;
test_notifications = Notification.objects.filter(title__icontains='test');
count = test_notifications.count();
test_notifications.delete();
print(f'Removed {count} test notifications')
"
```

### Reset Notification Read Status
```bash
# Mark all notifications as unread for testing
python manage.py shell -c "
from core.models import NotificationRead;
NotificationRead.objects.all().delete();
print('Reset all notification read statuses')
"
```

---

**Test Date**: July 6, 2025  
**Version**: 1.0  
**Status**: Ready for Testing
