from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class NotificationType(models.Model):
    """
    Types of notifications (e.g., <PERSON><PERSON>, Academic Update, System Alert)
    """
    name = models.CharField(max_length=100)
    icon = models.CharField(max_length=50, default='fas fa-bell', 
                           help_text="FontAwesome icon class")
    color = models.Char<PERSON>ield(max_length=20, default='#7AB2D3',
                           help_text="Color for the notification type")
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = "Notification Type"
        verbose_name_plural = "Notification Types"


class Notification(models.Model):
    """
    System notifications for dashboard
    """
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    
    title = models.Char<PERSON><PERSON>(max_length=200)
    message = models.TextField()
    notification_type = models.ForeignKey(
        NotificationType, 
        on_delete=models.CASCADE,
        related_name='notifications'
    )
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    is_read = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True,
                                    help_text="Optional expiration date for the notification")
    
    # Optional: Link to specific users (if None, it's a system-wide notification)
    target_users = models.ManyToManyField(User, blank=True, related_name='notifications')
    
    # Optional: Link to related objects
    related_student = models.ForeignKey(
        'students.Student', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='notifications'
    )
    related_fee_account = models.ForeignKey(
        'finances.FeeAccount', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='notifications'
    )
    
    def __str__(self):
        return f"{self.title} - {self.priority}"
    
    @property
    def is_expired(self):
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False
    
    @property
    def priority_color(self):
        colors = {
            'low': '#74C69D',
            'medium': '#7AB2D3', 
            'high': '#F28C8C',
            'urgent': '#e07575'
        }
        return colors.get(self.priority, '#7AB2D3')
    
    class Meta:
        verbose_name = "Notification"
        verbose_name_plural = "Notifications"
        ordering = ['-created_at']


class NotificationRead(models.Model):
    """
    Track which users have read which notifications
    """
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    read_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('notification', 'user')
        verbose_name = "Notification Read Status"
        verbose_name_plural = "Notification Read Statuses"
