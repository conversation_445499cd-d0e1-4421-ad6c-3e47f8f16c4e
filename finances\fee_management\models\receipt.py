from django.db import models, transaction
from django.utils.timezone import now


from helpers.strings import generate_unique_ids
from .fee_account import FeeAccount
from finances.fee_management.utils import sync_income
from students.models import Student


class Receipt(models.Model):
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    receipt_number = models.CharField(
        max_length=50, unique=True, blank=True, null=True)
    date = models.DateField(default=now)
    amount_paid = models.IntegerField()
    fee_account = models.ForeignKey(FeeAccount, on_delete=models.CASCADE)
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)
    is_reversed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    created_by = models.ForeignKey(
        'accounts.CustomUser', on_delete=models.SET_NULL, null=True, blank=True)
    reversal_entry = models.OneToOneField(
        'finances.JournalEntry', on_delete=models.SET_NULL, null=True, blank=True, related_name="reversal_of_receipt")

    def generate_receipt_number(self):
        prefix = f"REC{self.student.level.abbrv}"
        self.receipt_number = generate_unique_ids(self, prefix)

    def update_amount_paid(self):
        # Efficiently update is_paid status for all related fee accounts
        FeeAccount.update_paid_status_for_group(
            self.fee_account.student,
            self.fee_account.category,
            self.fee_account.term
        )

        sync_income(self.fee_account, self.date)

    def update_fee_account_on_reversal(self):
        """Update fee account when this receipt is reversed"""
        # Efficiently update is_paid status for all related fee accounts
        FeeAccount.update_paid_status_for_group(
            self.fee_account.student,
            self.fee_account.category,
            self.fee_account.term
        )

        sync_income(self.fee_account, self.date)

    def update_journals(self):
        from finances.book_keeping import JournalEntry, Ledger

        try:
            credit_account = Ledger.objects.get(
                name=self.fee_account.category.name)
            debit_account = Ledger.objects.get(name="Cash/Bank")
        except Ledger.DoesNotExist as e:
            raise ValueError(f"Missing ledger account: {e}")

        with transaction.atomic():
            journal, _ = JournalEntry.objects.get_or_create(
                term=self.fee_account.term,
                date=self.date,
                description=f"Receipt for {self.student.name}",
                voucher=self.receipt_number,
            )

            desc = (
                f"Receipt for {self.student.name} - {self.fee_account.category.name}. "
                f"Receipt No: {self.receipt_number}"
            )

            debit_line, _ = journal.journalline_set.get_or_create(
                account=debit_account,
                description=desc,
                line_type="Debit"
            )

            credit_line, _ = journal.journalline_set.get_or_create(
                account=credit_account,
                description=desc,
                line_type="Credit"
            )

            debit_line.amount = self.amount_paid
            credit_line.amount = self.amount_paid

            debit_line.save()
            credit_line.save()

        return journal

    def save(self, *args, **kwargs):
        is_new = self._state.adding

        if is_new:
            super().save(*args, **kwargs)

        if not self.receipt_number:
            self.generate_receipt_number()

        self.update_amount_paid()

        super().save(*args, **kwargs)
        self.update_journals()

    def __str__(self):
        return f"Receipt {self.receipt_number} for {self.student.name}"
