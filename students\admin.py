from django.contrib import admin

from .models import Level, Student, AcademicYear, Term
from finances.fee_management.models import FeeAccount


admin.site.site_header = "Tiny Feet Academy School MIS"
admin.site.site_title = "Tiny Feet Academy Admin Portal"
admin.site.index_title = "Welcome to Tiny Feet Academy Management System"


class TermInline(admin.TabularInline):
    model = Term
    extra = 1


class AcademicYearAdmin(admin.ModelAdmin):
    filedsets = [(None, {"fields": ["name", "start_date", "end_date"]})]
    inlines = [TermInline]


class FeeAccountInline(admin.StackedInline):
    model = FeeAccount
    extra = 0


admin.site.register(Level)
admin.site.register(AcademicYear, AcademicYearAdmin)


@admin.action(description='Seed FeeAccount Records for selected Students')
def seed_fee_category(modeladmin, request, queryset):
    for student in queryset:
        created_count = 0
        student.create_fee_accounts()

        if student.create_fee_accounts():
            created_count += 1

        modeladmin.message_user(
            request, f"{student.name}: {created_count} FeeAccount records created.", level='success')


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ('name', 'student_id', 'level', 'is_active')
    list_filter = ['name', 'level', 'is_active']
    search_fields = ['name', 'student_id']
    actions = [seed_fee_category]
