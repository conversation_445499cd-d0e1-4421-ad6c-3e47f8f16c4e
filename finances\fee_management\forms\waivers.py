from django import forms

from finances.fee_management.models import Fee<PERSON><PERSON>unt, FeeCategory, FeesWaiver


class WaiverForm(forms.ModelForm):
    category = forms.ModelChoiceField(
        queryset=FeeCategory.objects.all(),
        widget=forms.Select(attrs={'class': 'django-form'}),
        required=True,
    )

    class Meta:
        model = FeesWaiver
        fields = ["amount_waived",  "waived_by", "reason", "date_waived"]
        widgets = {
            'amount_waived': forms.TextInput(attrs={
                'class': 'django-form',
                'placeholder': 'Amount'
            }),
            'date_waived': forms.DateInput(attrs={
                'class': 'django-form',
                'placeholder': 'Date'
            }),
            'waived_by': forms.DateInput(attrs={
                'class': 'django-form',
                'placeholder': 'Waived By'
            }),
            'reason': forms.DateInput(attrs={
                'class': 'django-form',
                'placeholder': 'Reason'
            }),
        }

    def save(self, commit=True, student=None):
        fee_waived = super().save(commit=False)
        fee_category = self.cleaned_data['category']

        if "Food" in fee_category.name:
            fee_account = FeeAccount.objects.get(
                student=student, category=fee_category, month__month=fee_waived.date.month, term__is_active=True)
        else:
            fee_account = FeeAccount.objects.get(
                student=student, category=fee_category, term__is_active=True
            )
        if commit:
            fee_waived.student = student
            fee_waived.account = fee_account

        return fee_waived
