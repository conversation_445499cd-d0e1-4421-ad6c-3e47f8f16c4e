from django.db import models


class Budget(models.Model):
    term = models.ForeignKey('students.Term', on_delete=models.CASCADE)
    description = models.TextField(blank=True, null=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.term} - MWk {self.description}"


class BudgetLine(models.Model):
    budget = models.ForeignKey(
        Budget, on_delete=models.CASCADE, related_name="budget_lines")
    account = models.ForeignKey('finances.Ledger', on_delete=models.CASCADE)
    amount = models.DecimalField(default=0, decimal_places=2, max_digits=20)

    def get_revenue_total(self):
        from finances.fee_management.models import FeeAccount
        if "Fees" in self.account.name:
            revenue_total = FeeAccount.objects.filter(
                category__name=self.account.name,
                term=self.budget.term).aggregate(total=models.Sum('total_due'))['total'] or 0

            self.amount = revenue_total

    def save(self, *args, **kwargs):
        self.get_revenue_total()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.budget} - {self.account} - MWk {self.amount}"

    class Meta:
        unique_together = ('budget', 'account')
        ordering = ['budget']
