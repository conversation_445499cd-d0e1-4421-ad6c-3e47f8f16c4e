# Performance Optimization Guide

## 🚀 Overview

This guide provides comprehensive strategies for optimizing the performance of the Receipt Generator system, covering database optimization, caching, frontend performance, and scalability considerations.

## 🗄️ Database Optimization

### Query Optimization Patterns

#### Use Select Related and Prefetch Related
```python
# Bad: N+1 queries
students = Student.objects.all()
for student in students:
    print(student.level.name)  # Triggers additional query

# Good: Single query with join
students = Student.objects.select_related('level').all()
for student in students:
    print(student.level.name)  # No additional query

# For reverse foreign keys and many-to-many
students = Student.objects.prefetch_related('enrollment_set__subject').all()
```

#### Optimize Complex Queries
```python
# Use annotations for calculated fields
from django.db.models import Count, Sum, Avg

students_with_stats = Student.objects.annotate(
    enrollment_count=Count('enrollment'),
    total_fees=Sum('feeaccount__amount'),
    average_grade=Avg('enrollment__final_grade')
)

# Use values() for specific fields only
student_names = Student.objects.values_list('name', 'student_id')
```

#### Database Indexing Strategy
```python
# Add indexes to frequently queried fields
class Student(models.Model):
    student_id = models.SlugField(unique=True, db_index=True)
    level = models.ForeignKey('Level', on_delete=models.CASCADE, db_index=True)
    is_active = models.BooleanField(default=True, db_index=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['level', 'is_active']),
            models.Index(fields=['created_at', 'is_active']),
        ]
```

### Bulk Operations

#### Bulk Create for Multiple Records
```python
# Instead of multiple save() calls
fee_accounts = []
for student in students:
    for category in fee_categories:
        fee_accounts.append(FeeAccount(
            student=student,
            category=category,
            term=active_term
        ))

# Use bulk_create for better performance
FeeAccount.objects.bulk_create(fee_accounts, batch_size=1000)
```

#### Bulk Update Operations
```python
# Update multiple records efficiently
Student.objects.filter(level=old_level).update(level=new_level)

# For complex updates, use bulk_update
students = Student.objects.filter(is_active=True)
for student in students:
    student.calculated_field = some_calculation()

Student.objects.bulk_update(students, ['calculated_field'], batch_size=1000)
```

## 🔄 Caching Strategies

### Django Cache Framework

#### View-Level Caching
```python
from django.views.decorators.cache import cache_page
from django.core.cache import cache

@cache_page(60 * 15)  # Cache for 15 minutes
def student_list(request):
    students = Student.objects.select_related('level').all()
    return render(request, 'students/list.html', {'students': students})
```

#### Template Fragment Caching
```html
{% load cache %}

{% cache 500 student_sidebar student.id %}
    <!-- Expensive template rendering -->
    <div class="student-sidebar">
        {% for enrollment in student.enrollments %}
            <!-- Complex calculations -->
        {% endfor %}
    </div>
{% endcache %}
```

#### Low-Level Cache API
```python
from django.core.cache import cache

def get_student_statistics(student_id):
    cache_key = f'student_stats_{student_id}'
    stats = cache.get(cache_key)
    
    if stats is None:
        # Expensive calculation
        stats = calculate_student_statistics(student_id)
        cache.set(cache_key, stats, 60 * 30)  # Cache for 30 minutes
    
    return stats
```

### Redis Configuration

#### Settings Configuration
```python
# settings/production.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Session storage in Redis
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
```

## 🎨 Frontend Performance

### Static File Optimization

#### CSS Optimization
```bash
# Minify CSS in production
npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css --minify

# Use PurgeCSS to remove unused styles
npx purgecss --css assets/css/tailwind.css --content templates/**/*.html --output assets/css/
```

#### JavaScript Optimization
```html
<!-- Defer non-critical JavaScript -->
<script src="{% static 'js/charts.js' %}" defer></script>

<!-- Preload critical resources -->
<link rel="preload" href="{% static 'css/tailwind.css' %}" as="style">

<!-- Use async for non-blocking scripts -->
<script src="{% static 'js/analytics.js' %}" async></script>
```

### Image Optimization

#### Responsive Images
```html
<!-- Use appropriate image formats and sizes -->
<img src="{% static 'img/logo.webp' %}" 
     alt="School Logo"
     loading="lazy"
     width="200" 
     height="100">

<!-- Responsive images for different screen sizes -->
<picture>
    <source media="(min-width: 768px)" srcset="{% static 'img/banner-large.webp' %}">
    <source media="(min-width: 480px)" srcset="{% static 'img/banner-medium.webp' %}">
    <img src="{% static 'img/banner-small.webp' %}" alt="Banner">
</picture>
```

### JavaScript Performance

#### Efficient DOM Manipulation
```javascript
// Batch DOM updates
const fragment = document.createDocumentFragment();
students.forEach(student => {
    const row = createStudentRow(student);
    fragment.appendChild(row);
});
tableBody.appendChild(fragment);

// Use event delegation
document.addEventListener('click', function(e) {
    if (e.target.matches('.delete-btn')) {
        handleDelete(e.target.dataset.studentId);
    }
});
```

## 📊 Memory Management

### Large Dataset Handling

#### Pagination for Large Queries
```python
from django.core.paginator import Paginator

def student_list(request):
    students = Student.objects.select_related('level').order_by('name')
    paginator = Paginator(students, 50)  # 50 students per page
    
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'students/list.html', {'page_obj': page_obj})
```

#### Iterator for Memory-Efficient Processing
```python
# For processing large datasets
def process_all_students():
    for student in Student.objects.iterator(chunk_size=1000):
        # Process each student without loading all into memory
        process_student(student)
```

### File Upload Optimization

#### Chunked File Processing
```python
import pandas as pd

def process_excel_upload(file_path):
    # Process large Excel files in chunks
    chunk_size = 1000
    
    for chunk in pd.read_excel(file_path, chunksize=chunk_size):
        students_to_create = []
        
        for _, row in chunk.iterrows():
            student = Student(
                name=row['name'],
                student_id=row['student_id'],
                level_id=row['level_id']
            )
            students_to_create.append(student)
        
        Student.objects.bulk_create(students_to_create, ignore_conflicts=True)
```

## 🔍 Monitoring and Profiling

### Django Debug Toolbar

#### Installation and Configuration
```python
# settings/development.py
INSTALLED_APPS += ['debug_toolbar']
MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']

INTERNAL_IPS = ['127.0.0.1']

DEBUG_TOOLBAR_CONFIG = {
    'SHOW_TOOLBAR_CALLBACK': lambda request: DEBUG,
}
```

### Database Query Analysis

#### Using Django Extensions
```bash
# Install django-extensions
pip install django-extensions

# Analyze queries
python manage.py shell_plus --print-sql

# Profile specific views
python manage.py runprofileserver
```

### Custom Performance Monitoring

#### Middleware for Response Time Tracking
```python
import time
import logging

logger = logging.getLogger('performance')

class PerformanceMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        start_time = time.time()
        
        response = self.get_response(request)
        
        duration = time.time() - start_time
        
        if duration > 1.0:  # Log slow requests
            logger.warning(f'Slow request: {request.path} took {duration:.2f}s')
        
        response['X-Response-Time'] = f'{duration:.3f}s'
        return response
```

## 🚀 Production Optimization

### Server Configuration

#### Gunicorn Configuration
```python
# gunicorn.conf.py
bind = "127.0.0.1:8000"
workers = 4  # 2 * CPU cores
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
```

#### Nginx Configuration
```nginx
# /etc/nginx/sites-available/receipt_gen
server {
    listen 80;
    server_name your-domain.com;
    
    # Gzip compression
    gzip on;
    gzip_types text/css application/javascript application/json;
    
    # Static files caching
    location /static/ {
        alias /path/to/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Application proxy
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Database Optimization

#### PostgreSQL Configuration
```sql
-- postgresql.conf optimizations
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

-- Enable query logging for analysis
log_statement = 'all'
log_min_duration_statement = 1000  -- Log queries > 1 second
```

## 📈 Performance Testing

### Load Testing with Locust

#### Basic Load Test
```python
# locustfile.py
from locust import HttpUser, task, between

class ReceiptGenUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        # Login
        self.client.post("/accounts/login/", {
            "username": "testuser",
            "password": "testpass"
        })
    
    @task(3)
    def view_students(self):
        self.client.get("/students/")
    
    @task(2)
    def view_dashboard(self):
        self.client.get("/")
    
    @task(1)
    def view_finances(self):
        self.client.get("/finances/")
```

### Performance Benchmarking

#### Database Query Benchmarking
```python
import time
from django.test import TestCase
from django.test.utils import override_settings

class PerformanceTestCase(TestCase):
    def test_student_list_performance(self):
        # Create test data
        for i in range(1000):
            Student.objects.create(name=f'Student {i}')
        
        start_time = time.time()
        list(Student.objects.select_related('level').all())
        duration = time.time() - start_time
        
        self.assertLess(duration, 0.1, "Query should complete in under 100ms")
```

## 🔧 Best Practices Summary

### Development Guidelines
1. **Always use select_related() and prefetch_related()** for foreign key relationships
2. **Implement pagination** for large datasets
3. **Use bulk operations** for multiple database writes
4. **Cache expensive calculations** and frequently accessed data
5. **Optimize static files** with compression and proper caching headers
6. **Monitor query performance** in development and production
7. **Use database indexes** strategically on frequently queried fields
8. **Implement proper error handling** to prevent performance degradation

### Production Checklist
- [ ] Database indexes on all foreign keys and frequently queried fields
- [ ] Redis caching configured and active
- [ ] Static files compressed and cached
- [ ] Gunicorn/uWSGI properly configured
- [ ] Nginx serving static files directly
- [ ] Database connection pooling enabled
- [ ] Monitoring and alerting in place
- [ ] Regular performance testing scheduled

This guide provides the foundation for maintaining optimal performance as your Receipt Generator system scales.
