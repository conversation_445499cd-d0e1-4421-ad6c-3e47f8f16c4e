{% load static %} {% load humanize %}

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'img/favicon.ico' %}"
    />

    <!-- font awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css"
      integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- google fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />

    <!-- style css -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />

    <title>Expenditure Statement</title>
  </head>
  <body class="bg-white font-sans">
    <div class="max-w-4xl mx-auto p-8">
      <!-- Header Section -->
      <div class="text-center mb-12 border-b-2 border-[#2C3E50] pb-8">
        <div class="flex justify-center items-center mb-6">
          <img
            src="{% static 'img/tinyfeet.jpg' %}"
            alt="Tiny Feet Academy Logo"
            class="w-24 h-24 rounded-full border-2 border-[#2C3E50]"
          />
        </div>
        <h1 class="text-4xl font-bold text-[#2C3E50] mb-2 font-display">
          Tiny Feet Academy
        </h1>
        <h2 class="text-2xl font-semibold text-[#40657F] mb-3">
          Expenditure Statement
        </h2>

        {% if start_date %}
        <div
          class="inline-flex items-center gap-2 border border-[#40657F] px-6 py-2 rounded"
        >
          <i class="fas fa-calendar text-[#40657F]"></i>
          <p class="font-semibold text-[#40657F]">
            {{ start_date|date:"j F Y" }} to {{ end_date|date:"j F Y" }}
          </p>
        </div>
        {% else %}
        <div
          class="inline-flex items-center gap-2 border border-[#40657F] px-6 py-2 rounded"
        >
          <i class="fas fa-calendar text-[#40657F]"></i>
          <p class="font-semibold text-[#40657F]">{{term}}</p>
        </div>
        {% endif %}

        <div class="w-32 h-1 bg-[#2C3E50] mx-auto mt-4"></div>
      </div>

      <!-- Total Expenditure Summary -->
      <div class="bg-[#F7FAFC] border-l-4 border-[#F28C8C] p-8 rounded mb-10">
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-[#F28C8C] rounded flex items-center justify-center"
          >
            <i class="fas fa-money-bill-wave text-white text-lg"></i>
          </div>
          <div>
            <h3 class="font-bold text-[#2C3E50] text-xl">Total Expenditure</h3>
            <p class="text-[#40657F] text-sm">Sum of all expenses</p>
          </div>
        </div>
        <p class="text-3xl font-bold text-[#F28C8C]">
          MWK {{ total_expense|intcomma }}
        </p>
      </div>

      <!-- Category Breakdown -->
      <div
        class="bg-white rounded border border-[#2C3E50] overflow-hidden mb-10"
      >
        <div class="bg-[#F7FAFC] px-6 py-4 border-b border-[#2C3E50]">
          <h3 class="text-xl font-bold text-[#2C3E50] flex items-center gap-3">
            <i class="fas fa-chart-bar text-[#F28C8C]"></i>
            Expense Categories
          </h3>
        </div>

        <table class="w-full">
          <thead class="bg-[#F7FAFC]">
            <tr class="border-b border-[#2C3E50]">
              <th
                class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center gap-2">
                  <i class="fas fa-list text-[#F28C8C]"></i>
                  Category
                </div>
              </th>
              <th
                class="px-6 py-4 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center justify-end gap-2">
                  <i class="fas fa-money-bill text-[#F28C8C]"></i>
                  Amount
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            {% for expense in expenses %}
            <tr class="border-b border-gray-200">
              <td class="px-6 py-3 text-[#2C3E50] font-medium">
                {{ expense.name }}
              </td>
              <td class="px-6 py-3 text-right font-semibold text-[#F28C8C]">
                MWK {{ expense.total_amount|intcomma }}
              </td>
            </tr>
            {% endfor %}
            <tr class="bg-[#F7FAFC] border-b-2 border-[#2C3E50]">
              <td class="px-6 py-4 font-bold text-[#2C3E50] text-lg">
                TOTAL EXPENDITURE
              </td>
              <td class="px-6 py-4 text-right font-bold text-[#F28C8C] text-xl">
                MWK {{ total_expense|intcomma }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- Detailed Expenditure -->
      <div class="bg-white rounded border border-[#2C3E50] overflow-hidden">
        <div class="bg-[#F7FAFC] px-6 py-4 border-b border-[#2C3E50]">
          <h3 class="text-xl font-bold text-[#2C3E50] flex items-center gap-3">
            <i class="fas fa-list-alt text-[#40657F]"></i>
            Detailed Expenditure
          </h3>
        </div>

        <table class="w-full">
          <thead class="bg-[#F7FAFC]">
            <tr class="border-b border-[#2C3E50]">
              <th
                class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center gap-2">
                  <i class="fas fa-hashtag text-[#7AB2D3]"></i>
                  Expense ID
                </div>
              </th>
              <th
                class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center gap-2">
                  <i class="fas fa-file-alt text-[#74C69D]"></i>
                  Description
                </div>
              </th>
              <th
                class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center justify-center gap-2">
                  <i class="fas fa-calendar text-[#F28C8C]"></i>
                  Date
                </div>
              </th>
              <th
                class="px-6 py-4 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center justify-end gap-2">
                  <i class="fas fa-money-bill text-[#F28C8C]"></i>
                  Amount
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            {% for line in outflow %}
            <tr class="border-b border-gray-200">
              <td class="px-6 py-3 text-[#7AB2D3] font-bold">
                {{ line.outflow_id }}
              </td>
              <td class="px-6 py-3 text-[#2C3E50] font-medium">
                {{ line.description }}
              </td>
              <td class="px-6 py-3 text-center text-[#40657F]">
                {{ line.date }}
              </td>
              <td class="px-6 py-3 text-right font-semibold text-[#F28C8C]">
                MWK {{ line.calculate_total|intcomma }}
              </td>
            </tr>
            {% endfor %}
            <tr class="bg-[#F7FAFC] border-b-2 border-[#2C3E50]">
              <td class="px-6 py-4 font-bold text-[#2C3E50] text-lg">
                GRAND TOTAL
              </td>
              <td class="px-6 py-4"></td>
              <td class="px-6 py-4"></td>
              <td class="px-6 py-4 text-right font-bold text-[#F28C8C] text-xl">
                MWK {{ total_expense|intcomma }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Footer -->
      <div class="mt-10 pt-6 border-t border-[#2C3E50] text-center">
        <div class="flex justify-between items-center text-sm text-[#40657F]">
          <div class="flex items-center gap-2">
            <i class="fas fa-calendar"></i>
            <span>Generated on: {{ "now"|date:"F d, Y" }}</span>
          </div>
          <div class="flex items-center gap-2">
            <i class="fas fa-building"></i>
            <span>Tiny Feet Academy</span>
          </div>
          <div class="flex items-center gap-2">
            <i class="fas fa-file-alt"></i>
            <span>Expenditure Statement</span>
          </div>
        </div>
      </div>
    </div>

    <style>
      /* Print-specific styles */
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }

        .border-2 {
          border-width: 2px;
        }

        .border-l-4 {
          border-left-width: 4px;
        }

        table {
          break-inside: avoid;
        }

        tr {
          break-inside: avoid;
        }
      }

      /* Font styling */
      .font-display {
        font-family: "Inter", "Roboto", sans-serif;
      }

      /* Clean, printer-friendly styling */
      body {
        font-family: "Inter", "Roboto", sans-serif;
      }

      /* Ensure consistent borders for printing */
      .border-l-4 {
        border-left-width: 4px;
      }

      .border-2 {
        border-width: 2px;
      }
    </style>

    <script>
      window.onload = function () {
        window.print();
      };
    </script>
  </body>
</html>
