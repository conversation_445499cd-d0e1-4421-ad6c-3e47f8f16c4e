from django.shortcuts import render
from django.db.models import Count
from django.contrib.auth.decorators import login_required


from academics.models import Subject
from students.models import Student, Level
# Create your views here.


@login_required(login_url="accounts:login")
def dashboard(request):
    subject_count = Subject.objects.all().aggregate(count=Count('id'))['count']
    level_count = Level.objects.all().aggregate(count=Count('id'))['count']
    student_count = Student.objects.filter(
        is_active=True).aggregate(count=Count('id'))['count']
    context = {
        "subject_count": subject_count,
        "student_count": student_count,
        "level_count": level_count,
    }
    return render(request, 'academics/dashboard.html', context)
