{% extends 'academics/base.html' %}
{% load static %}
{% load rbac_tags %}

{% block title %}Edit Profile | {% endblock %}

{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg icon-float">
        <i class="fas fa-edit text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
          Edit Profile
        </h1>
        <div class="w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full accent-line-grow"></div>
        <p class="text-[#40657F] mt-2">Update your personal information</p>
      </div>
    </div>
    
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <a href="{% url 'academics:user_profile' %}" class="text-[#40657F] hover:text-[#7AB2D3]">My Account</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Edit Profile</span>
    </nav>
  </div>

  <!-- Form -->
  <div class="card-modern p-8">
    <form method="post" class="space-y-8">
      {% csrf_token %}
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- First Name -->
        <div class="form-group">
          <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-2">
            <i class="fas fa-user text-[#7AB2D3] mr-2"></i>
            {{ form.first_name.label }}
          </label>
          {{ form.first_name }}
          {% if form.first_name.help_text %}
          <p class="text-sm text-[#40657F] mt-2">{{ form.first_name.help_text }}</p>
          {% endif %}
          {% if form.first_name.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.first_name.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>

        <!-- Last Name -->
        <div class="form-group">
          <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-2">
            <i class="fas fa-user text-[#7AB2D3] mr-2"></i>
            {{ form.last_name.label }}
          </label>
          {{ form.last_name }}
          {% if form.last_name.help_text %}
          <p class="text-sm text-[#40657F] mt-2">{{ form.last_name.help_text }}</p>
          {% endif %}
          {% if form.last_name.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.last_name.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>

        <!-- Email -->
        <div class="form-group">
          <label for="{{ form.email.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-2">
            <i class="fas fa-envelope text-[#40657F] mr-2"></i>
            {{ form.email.label }}
          </label>
          {{ form.email }}
          {% if form.email.help_text %}
          <p class="text-sm text-[#40657F] mt-2">{{ form.email.help_text }}</p>
          {% endif %}
          {% if form.email.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.email.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>

        <!-- Phone Number -->
        <div class="form-group">
          <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-2">
            <i class="fas fa-phone text-[#74C69D] mr-2"></i>
            {{ form.phone_number.label }}
          </label>
          {{ form.phone_number }}
          {% if form.phone_number.help_text %}
          <p class="text-sm text-[#40657F] mt-2">{{ form.phone_number.help_text }}</p>
          {% endif %}
          {% if form.phone_number.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.phone_number.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>

        <!-- Gender -->
        <div class="form-group md:col-span-2">
          <label for="{{ form.gender.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-2">
            <i class="fas fa-venus-mars text-[#F28C8C] mr-2"></i>
            {{ form.gender.label }}
          </label>
          {{ form.gender }}
          {% if form.gender.help_text %}
          <p class="text-sm text-[#40657F] mt-2">{{ form.gender.help_text }}</p>
          {% endif %}
          {% if form.gender.errors %}
          <div class="text-[#F28C8C] text-sm mt-2">
            {% for error in form.gender.errors %}
            <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Non-field errors -->
      {% if form.non_field_errors %}
      <div class="bg-[#FEF2F2] border border-[#F28C8C] rounded-lg p-4">
        <div class="flex items-center gap-2 text-[#F28C8C]">
          <i class="fas fa-exclamation-triangle"></i>
          <span class="font-semibold">Please correct the following errors:</span>
        </div>
        <ul class="mt-2 text-sm text-[#F28C8C] space-y-1">
          {% for error in form.non_field_errors %}
          <li>{{ error }}</li>
          {% endfor %}
        </ul>
      </div>
      {% endif %}

      <!-- Form Actions -->
      <div class="flex items-center justify-between pt-6 border-t border-[#E2F1F9]">
        <a href="{% url 'academics:user_profile' %}" 
           class="px-6 py-3 border border-[#B9D8EB] text-[#40657F] rounded-xl font-semibold hover:bg-[#F7FAFC] transition-colors">
          <i class="fas fa-arrow-left mr-2"></i>Cancel
        </a>
        
        <button type="submit" 
                class="px-8 py-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
          <i class="fas fa-save mr-2"></i>Save Changes
        </button>
      </div>
    </form>
  </div>
</section>

{% endblock %}
