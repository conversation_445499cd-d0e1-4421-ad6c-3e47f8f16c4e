{% extends 'base.html' %} {% load humanize %}
<!--  -->
{% block title %}{{ academic_year.name }} Details | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 {% if academic_year.is_active %}bg-gradient-to-br from-[#7AB2D3] to-[#40657F]{% else %}bg-gradient-to-br from-[#B9D8EB] to-[#E2F1F9]{% endif %} rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-calendar-alt text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in flex items-center gap-3"
          >
            {{ academic_year.name }}
            {% if academic_year.is_active %}
            <span class="bg-[#74C69D] text-white text-lg px-3 py-1 rounded-full">Active</span>
            {% else %}
            <span class="bg-[#B9D8EB] text-[#40657F] text-lg px-3 py-1 rounded-full">Inactive</span>
            {% endif %}
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            {{ academic_year.start_date }} - {{ academic_year.end_date }}
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 action-buttons-slide-in">
        <a
          href="{% url 'students:edit_academic_year' academic_year.pk %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-edit group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Edit Academic Year</span>
        </a>
        <a
          href="{% url 'students:add_term' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-plus group-hover:rotate-90 transition-all duration-300"
          ></i>
          <span>Add Term</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Academic Year Overview -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 overview-cards-fade-in">
    <!-- Duration -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5 border-l-4 border-[#7AB2D3]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#7AB2D3] rounded-xl flex items-center justify-center">
          <i class="fas fa-clock text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Duration</h3>
          <p class="text-[#40657F] font-medium">
            {% with days_count=academic_year.end_date|timeuntil:academic_year.start_date %}
            {{ academic_year.start_date|timesince:academic_year.end_date|floatformat:0 }} days
            {% endwith %}
          </p>
        </div>
      </div>
    </div>

    <!-- Terms Count -->
    <div class="card-modern p-6 bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5 border-l-4 border-[#74C69D]">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-[#74C69D] rounded-xl flex items-center justify-center">
          <i class="fas fa-list text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Terms</h3>
          <p class="text-[#40657F] font-medium">
            {{ terms.count }} term{{ terms.count|pluralize }}
          </p>
        </div>
      </div>
    </div>

    <!-- Status -->
    <div class="card-modern p-6 bg-gradient-to-br from-{% if academic_year.is_active %}[#74C69D]/5 to-[#5fb085]/5 border-l-4 border-[#74C69D]{% else %}[#F28C8C]/5 to-[#e74c3c]/5 border-l-4 border-[#F28C8C]{% endif %}">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 {% if academic_year.is_active %}bg-[#74C69D]{% else %}bg-[#F28C8C]{% endif %} rounded-xl flex items-center justify-center">
          <i class="fas {% if academic_year.is_active %}fa-check-circle{% else %}fa-pause-circle{% endif %} text-white"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-lg">Status</h3>
          <p class="{% if academic_year.is_active %}text-[#74C69D]{% else %}text-[#F28C8C]{% endif %} font-medium">
            {% if academic_year.is_active %}Active{% else %}Inactive{% endif %}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Terms Section -->
  <div class="card-modern p-8 terms-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
      >
        <i class="fas fa-calendar-week text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Academic Terms
        </h3>
        <p class="text-[#40657F] text-sm">
          Terms for {{ academic_year.name }}
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    {% if terms %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {% for term in terms %}
      <div
        class="term-card bg-gradient-to-br from-white via-[#F7FAFC] to-[#E2F1F9] border-2 {% if term.is_active %}border-[#74C69D] ring-2 ring-[#74C69D]/30{% else %}border-[#B9D8EB]{% endif %} hover:border-[#7AB2D3] hover:shadow-xl hover:-translate-y-2 transition-all duration-300 p-6 rounded-2xl relative overflow-hidden"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-12 h-12 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 rounded-full -translate-y-6 translate-x-6 group-hover:scale-125 transition-transform duration-500"
        ></div>

        <!-- Term Header -->
        <div class="relative z-10 mb-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-xl font-bold text-[#2C3E50] flex items-center gap-2">
              {{ term.term_name }}
              {% if term.is_active %}
              <span class="bg-[#74C69D] text-white text-xs px-2 py-1 rounded-full">Active</span>
              {% endif %}
            </h4>
            <a
              href="{% url 'students:edit_term' term.pk %}"
              class="text-[#7AB2D3] hover:text-[#40657F] transition-colors duration-200"
              title="Edit Term"
            >
              <i class="fas fa-edit"></i>
            </a>
          </div>
          <p class="text-[#40657F] text-sm font-medium">
            {{ term.start_date }} - {{ term.end_date }}
          </p>
        </div>

        <!-- Term Details -->
        <div class="relative z-10 space-y-3">
          <div class="flex items-center gap-2">
            <i class="fas fa-calendar-day text-[#7AB2D3] text-sm"></i>
            <span class="text-sm text-[#40657F]">
              {% with days_count=term.end_date|timeuntil:term.start_date %}
              Duration: {{ term.start_date|timesince:term.end_date|floatformat:0 }} days
              {% endwith %}
            </span>
          </div>
          
          <div class="flex items-center gap-2">
            <i class="fas fa-info-circle text-[#74C69D] text-sm"></i>
            <span class="text-sm {% if term.is_active %}text-[#74C69D]{% else %}text-[#40657F]{% endif %} font-medium">
              {% if term.is_active %}Currently Active{% else %}Inactive{% endif %}
            </span>
          </div>
        </div>

        <!-- Term Actions -->
        <div class="relative z-10 mt-4 pt-4 border-t border-[#B9D8EB]/30">
          <div class="flex gap-2">
            {% if not term.is_active %}
            <a
              href="{% url 'students:activate_term' %}?term={{ term.pk }}"
              class="flex-1 bg-[#74C69D] text-white text-center py-2 px-3 rounded-lg hover:bg-[#5fb085] transition-colors duration-200 text-sm font-medium"
            >
              <i class="fas fa-play mr-1"></i>
              Activate
            </a>
            {% endif %}
            <a
              href="{% url 'students:edit_term' term.pk %}"
              class="flex-1 bg-[#7AB2D3] text-white text-center py-2 px-3 rounded-lg hover:bg-[#40657F] transition-colors duration-200 text-sm font-medium"
            >
              <i class="fas fa-edit mr-1"></i>
              Edit
            </a>
          </div>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#7AB2D3]/5 to-[#40657F]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>
      {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-12">
      <div class="flex flex-col items-center gap-4">
        <div
          class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
        >
          <i class="fas fa-calendar-week text-[#B9D8EB] text-2xl"></i>
        </div>
        <div>
          <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
            No Terms Created
          </h3>
          <p class="text-[#40657F]">
            This academic year doesn't have any terms yet.
          </p>
        </div>
        <a
          href="{% url 'students:add_term' %}"
          class="inline-flex items-center gap-2 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-plus"></i>
          <span>Add First Term</span>
        </a>
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Back Button -->
  <div class="flex justify-center back-button-fade-in">
    <a
      href="{% url 'students:academic_year_management' %}"
      class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
    >
      <i
        class="fas fa-arrow-left group-hover:scale-110 group-hover:-translate-x-1 transition-all duration-300"
      ></i>
      <span>Back to Academic Year Management</span>
    </a>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Overview Cards Animation */
  .overview-cards-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: overviewCardsFadeIn 0.8s ease-out 1s forwards;
  }

  /* Terms Section Animation */
  .terms-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: termsSectionFadeIn 0.8s ease-out 1.2s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  /* Term Cards Animation */
  .term-card {
    opacity: 0;
    transform: translateY(20px);
    animation: termCardSlideIn 0.4s ease-out forwards;
  }

  .term-card:nth-child(1) { animation-delay: 1.4s; }
  .term-card:nth-child(2) { animation-delay: 1.5s; }
  .term-card:nth-child(3) { animation-delay: 1.6s; }

  .back-button-fade-in {
    opacity: 0;
    animation: backButtonFadeIn 0.8s ease-out 1.8s forwards;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes actionButtonsSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes overviewCardsFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes termsSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes tableIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes termCardSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes backButtonFadeIn {
    to { opacity: 1; }
  }
</style>

{% endblock %}
