from django.core.management.base import BaseCommand
from django.db.models import Sum
from finances.book_keeping import Ledger, JournalLine
from finances.fee_management.models import Receipt, FeeAccount, FeeCategory
from students.models import Term


class Command(BaseCommand):
    help = 'Diagnose discrepancies between ledger total_amount, fee_accounts, and receipts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--term-id',
            type=int,
            help='Specific term ID to analyze (default: active term)',
        )
        parser.add_argument(
            '--category',
            type=str,
            help='Specific fee category to analyze',
        )

    def handle(self, *args, **options):
        term_id = options.get('term_id')
        category_name = options.get('category')

        if term_id:
            try:
                term = Term.objects.get(id=term_id)
            except Term.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Term with ID {term_id} not found'))
                return
        else:
            term = Term.objects.get_active()

        self.stdout.write(
            self.style.SUCCESS(f'Analyzing discrepancies for term: {term}')
        )

        # Get categories to analyze
        if category_name:
            categories = FeeCategory.objects.filter(name__icontains=category_name)
            if not categories.exists():
                self.stdout.write(self.style.ERROR(f'No categories found matching: {category_name}'))
                return
        else:
            categories = FeeCategory.objects.all()

        for category in categories:
            self.analyze_category(term, category)

    def analyze_category(self, term, category):
        self.stdout.write(f'\n{"="*60}')
        self.stdout.write(f'ANALYZING CATEGORY: {category.name}')
        self.stdout.write(f'{"="*60}')

        # 1. Get Ledger total_amount
        try:
            ledger = Ledger.objects.get(name=category.name)
            ledger_total = ledger.total_amount
            self.stdout.write(f'📊 Ledger total_amount: {ledger_total:,}')
        except Ledger.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'❌ No ledger found for category: {category.name}'))
            return

        # 2. Get actual receipts total
        receipts_total = Receipt.objects.filter(
            fee_account__category=category,
            fee_account__term=term,
            is_reversed=False
        ).aggregate(total=Sum('amount_paid'))['total'] or 0
        self.stdout.write(f'🧾 Receipts total: {receipts_total:,}')

        # 3. Get fee accounts total_due and dynamic amount
        fee_accounts = FeeAccount.objects.filter(category=category, term=term)
        total_due = fee_accounts.aggregate(total=Sum('total_due'))['total'] or 0
        
        # Calculate dynamic amount total
        dynamic_amount_total = sum(account.amount for account in fee_accounts)
        self.stdout.write(f'💰 Fee accounts total_due: {total_due:,}')
        self.stdout.write(f'🔄 Dynamic amount total: {dynamic_amount_total:,}')

        # 4. Get journal lines for this category (active entries only)
        # Note: Excludes reversal and locked entries for comparison with active receipts
        journal_lines = JournalLine.objects.filter(
            account=ledger,
            journal_entry__term=term,
            line_type='Credit',
            journal_entry__is_reversal=False,
            journal_entry__is_locked=False
        )
        journal_total = journal_lines.aggregate(total=Sum('amount'))['total'] or 0
        self.stdout.write(f'📝 Journal lines total (active): {journal_total:,}')


        # 5. Identify discrepancies
        self.stdout.write(f'\n🔍 DISCREPANCY ANALYSIS:')

        discrepancies = []

        if ledger_total != receipts_total:
            discrepancies.append(f'Ledger vs Receipts: {ledger_total - receipts_total:,}')

        if ledger_total != journal_total:
            discrepancies.append(f'Ledger vs Journal Lines: {ledger_total - journal_total:,}')

        if receipts_total != dynamic_amount_total:
            discrepancies.append(f'Receipts vs Dynamic Amount: {receipts_total - dynamic_amount_total:,}')

        # Note: Income model comparison removed - deprecated in favor of ledger system

        if discrepancies:
            for discrepancy in discrepancies:
                self.stdout.write(self.style.ERROR(f'❌ {discrepancy}'))
        else:
            self.stdout.write(self.style.SUCCESS('✅ All amounts match!'))

        # 6. Detailed breakdown
        self.stdout.write(f'\n🔬 DETAILED BREAKDOWN:')
        
        # Check for orphaned journal entries (including reversal journals)
        # Get all active receipt numbers for this category/term
        active_receipt_numbers = Receipt.objects.filter(
            fee_account__category=category,
            fee_account__term=term,
            is_reversed=False,
        ).values_list('receipt_number', flat=True)

        # Find journal entries that don't have corresponding active receipts
        # This includes reversal journals and other orphaned entries
        orphaned_journals = JournalLine.objects.filter(
            account=ledger,
            journal_entry__term=term,
            line_type='Credit',
        ).exclude(
            journal_entry__voucher__in=active_receipt_numbers
        )
        
        if orphaned_journals.exists():
            orphaned_total = orphaned_journals.aggregate(total=Sum('amount'))['total'] or 0
            self.stdout.write(self.style.WARNING(f'⚠️  Orphaned journal entries: {orphaned_total:,}'))

            # Break down orphaned journals by type
            reversal_orphaned = orphaned_journals.filter(journal_entry__is_reversal=True)
            locked_orphaned = orphaned_journals.filter(journal_entry__is_locked=True)
            regular_orphaned = orphaned_journals.filter(
                journal_entry__is_reversal=False,
                journal_entry__is_locked=False
            )

            if reversal_orphaned.exists():
                reversal_total = reversal_orphaned.aggregate(total=Sum('amount'))['total'] or 0
                self.stdout.write(f'   🔄 Reversal journals: {reversal_total:,} ({reversal_orphaned.count()} entries)')

            if locked_orphaned.exists():
                locked_total = locked_orphaned.aggregate(total=Sum('amount'))['total'] or 0
                self.stdout.write(f'   🔒 Locked journals: {locked_total:,} ({locked_orphaned.count()} entries)')

            if regular_orphaned.exists():
                regular_total = regular_orphaned.aggregate(total=Sum('amount'))['total'] or 0
                self.stdout.write(f'   📝 Regular orphaned: {regular_total:,} ({regular_orphaned.count()} entries)')

                # Show some examples of regular orphaned entries for debugging
                self.stdout.write('   Examples of regular orphaned entries:')
                for journal in regular_orphaned[:3]:  # Show first 3 examples
                    self.stdout.write(f'     - Voucher: {journal.journal_entry.voucher}, Amount: {journal.amount:,}')
            
        # Check for receipts without journal entries
        receipt_numbers = set(Receipt.objects.filter(
            fee_account__category=category,
            fee_account__term=term,
            is_reversed=False
        ).values_list('receipt_number', flat=True))
        
        journal_vouchers = set(JournalLine.objects.filter(
            account=ledger,
            journal_entry__term=term,
            line_type='Credit'
        ).values_list('journal_entry__voucher', flat=True))
        
        missing_journals = receipt_numbers - journal_vouchers
        if missing_journals:
            self.stdout.write(self.style.WARNING(f'⚠️  Receipts without journals: {len(missing_journals)}'))

        # Check the problematic ledger total_amount calculation
        self.stdout.write(f'\n🔧 LEDGER CALCULATION DEBUG:')
        
        # Manual calculation of what ledger.total_amount should be
        manual_total = JournalLine.objects.filter(
            account=ledger,
            journal_entry__term=term,
            journal_entry__is_reversal=False,  # Only non-reversal entries
            journal_entry__is_locked=False     # Exclude locked (reversed) entries
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        self.stdout.write(f'🧮 Manual ledger calculation: {manual_total:,}')
        
        if manual_total != ledger_total:
            self.stdout.write(self.style.ERROR(f'❌ Ledger property calculation is wrong!'))
            self.stdout.write(f'   Expected: {manual_total:,}, Got: {ledger_total:,}')

            # Check for locked entries that might be causing the issue
            locked_total = JournalLine.objects.filter(
                account=ledger,
                journal_entry__term=term,
                journal_entry__is_locked=True
            ).aggregate(total=Sum('amount'))['total'] or 0

            reversal_total = JournalLine.objects.filter(
                account=ledger,
                journal_entry__term=term,
                journal_entry__is_reversal=True
            ).aggregate(total=Sum('amount'))['total'] or 0

            self.stdout.write(f'   Locked entries total: {locked_total:,}')
            self.stdout.write(f'   Reversal entries total: {reversal_total:,}')
