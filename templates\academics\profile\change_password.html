{% extends 'academics/base.html' %}
{% load static %}
{% load rbac_tags %}

{% block title %}Change Password | {% endblock %}

{% block content %}
<section class="w-full max-w-3xl mx-auto px-4 py-8 space-y-8">
  <!-- Header -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div class="w-12 h-12 bg-gradient-to-br from-[#FFB84D] to-[#e6a43d] rounded-2xl flex items-center justify-center shadow-lg icon-float">
        <i class="fas fa-key text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
          Change Password
        </h1>
        <div class="w-20 h-1 bg-gradient-to-r from-[#FFB84D] to-[#e6a43d] rounded-full accent-line-grow"></div>
        <p class="text-[#40657F] mt-2">Update your account password</p>
      </div>
    </div>
    
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <a href="{% url 'academics:user_profile' %}" class="text-[#40657F] hover:text-[#7AB2D3]">My Account</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Change Password</span>
    </nav>
  </div>

  <!-- Form -->
  <div class="card-modern p-8">
    <form method="post" class="space-y-8">
      {% csrf_token %}
      
      <!-- Current Password -->
      <div class="form-group">
        <label for="{{ form.old_password.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-3">
          <i class="fas fa-lock text-[#40657F] mr-2"></i>
          {{ form.old_password.label }}
        </label>
        <div class="relative">
          {{ form.old_password }}
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button type="button" onclick="togglePassword('{{ form.old_password.id_for_label }}')" 
                    class="text-[#40657F] hover:text-[#7AB2D3] transition-colors">
              <i class="fas fa-eye" id="{{ form.old_password.id_for_label }}-toggle"></i>
            </button>
          </div>
        </div>
        {% if form.old_password.help_text %}
        <p class="text-sm text-[#40657F] mt-2">{{ form.old_password.help_text }}</p>
        {% endif %}
        {% if form.old_password.errors %}
        <div class="text-[#F28C8C] text-sm mt-2">
          {% for error in form.old_password.errors %}
          <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
          {% endfor %}
        </div>
        {% endif %}
      </div>

      <!-- New Password -->
      <div class="form-group">
        <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-3">
          <i class="fas fa-key text-[#74C69D] mr-2"></i>
          {{ form.new_password1.label }}
        </label>
        <div class="relative">
          {{ form.new_password1 }}
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button type="button" onclick="togglePassword('{{ form.new_password1.id_for_label }}')" 
                    class="text-[#40657F] hover:text-[#7AB2D3] transition-colors">
              <i class="fas fa-eye" id="{{ form.new_password1.id_for_label }}-toggle"></i>
            </button>
          </div>
        </div>
        {% if form.new_password1.help_text %}
        <div class="bg-[#E2F1F9] border border-[#B9D8EB] rounded-lg p-4 mt-3">
          <h4 class="font-semibold text-[#40657F] mb-2">Password Requirements:</h4>
          <div class="text-sm text-[#40657F] space-y-1">
            {{ form.new_password1.help_text|safe }}
          </div>
        </div>
        {% endif %}
        {% if form.new_password1.errors %}
        <div class="text-[#F28C8C] text-sm mt-2">
          {% for error in form.new_password1.errors %}
          <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
          {% endfor %}
        </div>
        {% endif %}
      </div>

      <!-- Confirm New Password -->
      <div class="form-group">
        <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-3">
          <i class="fas fa-check-circle text-[#74C69D] mr-2"></i>
          {{ form.new_password2.label }}
        </label>
        <div class="relative">
          {{ form.new_password2 }}
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button type="button" onclick="togglePassword('{{ form.new_password2.id_for_label }}')" 
                    class="text-[#40657F] hover:text-[#7AB2D3] transition-colors">
              <i class="fas fa-eye" id="{{ form.new_password2.id_for_label }}-toggle"></i>
            </button>
          </div>
        </div>
        {% if form.new_password2.help_text %}
        <p class="text-sm text-[#40657F] mt-2">{{ form.new_password2.help_text }}</p>
        {% endif %}
        {% if form.new_password2.errors %}
        <div class="text-[#F28C8C] text-sm mt-2">
          {% for error in form.new_password2.errors %}
          <p><i class="fas fa-exclamation-circle mr-1"></i>{{ error }}</p>
          {% endfor %}
        </div>
        {% endif %}
      </div>

      <!-- Non-field errors -->
      {% if form.non_field_errors %}
      <div class="bg-[#FEF2F2] border border-[#F28C8C] rounded-lg p-4">
        <div class="flex items-center gap-2 text-[#F28C8C]">
          <i class="fas fa-exclamation-triangle"></i>
          <span class="font-semibold">Please correct the following errors:</span>
        </div>
        <ul class="mt-2 text-sm text-[#F28C8C] space-y-1">
          {% for error in form.non_field_errors %}
          <li>{{ error }}</li>
          {% endfor %}
        </ul>
      </div>
      {% endif %}

      <!-- Form Actions -->
      <div class="flex items-center justify-between pt-6 border-t border-[#E2F1F9]">
        <a href="{% url 'academics:user_profile' %}" 
           class="px-6 py-3 border border-[#B9D8EB] text-[#40657F] rounded-xl font-semibold hover:bg-[#F7FAFC] transition-colors">
          <i class="fas fa-arrow-left mr-2"></i>Cancel
        </a>
        
        <button type="submit" 
                class="px-8 py-3 bg-gradient-to-r from-[#FFB84D] to-[#e6a43d] text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
          <i class="fas fa-key mr-2"></i>Change Password
        </button>
      </div>
    </form>
  </div>
</section>

<script>
function togglePassword(fieldId) {
  const field = document.getElementById(fieldId);
  const toggle = document.getElementById(fieldId + '-toggle');
  
  if (field.type === 'password') {
    field.type = 'text';
    toggle.classList.remove('fa-eye');
    toggle.classList.add('fa-eye-slash');
  } else {
    field.type = 'password';
    toggle.classList.remove('fa-eye-slash');
    toggle.classList.add('fa-eye');
  }
}
</script>

{% endblock %}
