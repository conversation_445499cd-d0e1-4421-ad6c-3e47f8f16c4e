from django import forms
from django.core.exceptions import ValidationError
from django.db import transaction

from academics.models import Subject, AssessmentType, Activity, ActivityType
from students.models import Term, Level


class SubjectForm(forms.ModelForm):
    """Form for creating and editing subjects"""

    levels = forms.ModelMultipleChoiceField(
        queryset=Level.objects.all(),
        widget=forms.CheckboxSelectMultiple,
        help_text="Select the classes where this subject is taught"
    )

    class Meta:
        model = Subject
        fields = ['name', 'abbrv', 'category', 'levels']
        
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'placeholder': 'e.g., Mathematics, English, Science'
            }),
            'abbrv': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'placeholder': 'e.g., MATH, ENG, SCI'
            }),
            'category': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'placeholder': 'e.g., Core, Elective, Language'
            }),
            'level': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
            }),
        }
        
        labels = {
            'name': 'Subject Name',
            'abbrv': 'Abbreviation',
            'category': 'Subject Category',
            'level': 'Education Level',
        }
        
        help_texts = {
            'name': 'Enter the full name of the subject',
            'abbrv': 'Short abbreviation for the subject (3-5 characters)',
            'category': 'Category or department this subject belongs to',
            'level': 'Which education level this subject is for',
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            # Check for duplicate names (excluding current instance if editing)
            existing = Subject.objects.filter(name=name)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError('A subject with this name already exists.')
        
        return name

    def clean_abbrv(self):
        abbrv = self.cleaned_data.get('abbrv')
        if abbrv:
            # Check for duplicate abbreviations (excluding current instance if editing)
            existing = Subject.objects.filter(abbrv=abbrv)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError('A subject with this abbreviation already exists.')
        
        return abbrv.upper()


class AssessmentTypeForm(forms.ModelForm):
    """Form for creating and editing assessment types"""
    
    class Meta:
        model = AssessmentType
        fields = ['name', 'weight', 'is_final', 'sequence', 'description']
        
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'placeholder': 'e.g., Continuous Assessment, Final Exam'
            }),
            'weight': forms.NumberInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'min': '0',
                'max': '100',
                'placeholder': '0-100'
            }),
            'sequence': forms.NumberInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'min': '1',
                'placeholder': '1, 2, 3...'
            }),
            'is_final': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-[#7AB2D3] bg-gray-100 border-gray-300 rounded focus:ring-[#7AB2D3] focus:ring-2'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'rows': 3,
                'placeholder': 'Description of this assessment type...'
            }),
        }
        
        labels = {
            'name': 'Assessment Type Name',
            'weight': 'Weight (%)',
            'is_final': 'Is Final Assessment',
            'sequence': 'Sequence Order',
            'description': 'Description',
        }
        
        help_texts = {
            'name': 'Name of the assessment type (e.g., Quiz, Test, Final Exam)',
            'weight': 'Percentage weight of this assessment (0-100)',
            'is_final': 'Check if this is a final/summative assessment',
            'sequence': 'Order in which this assessment appears',
            'description': 'Optional description of the assessment type',
        }

    def clean_weight(self):
        weight = self.cleaned_data.get('weight')
        if weight is not None:
            if weight < 0 or weight > 100:
                raise ValidationError('Weight must be between 0 and 100.')
        
        return weight

    def clean_sequence(self):
        sequence = self.cleaned_data.get('sequence')
        if sequence is not None:
            if sequence < 1:
                raise ValidationError('Sequence must be a positive number.')
            
            # Check for duplicate sequences (excluding current instance if editing)
            existing = AssessmentType.objects.filter(sequence=sequence)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError('An assessment type with this sequence already exists.')
        
        return sequence


class ActivityTypeForm(forms.ModelForm):
    """Form for creating and editing activity types"""
    
    class Meta:
        model = ActivityType
        fields = ['name', 'description']
        
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'placeholder': 'e.g., Quiz, Project, Presentation'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'rows': 3,
                'placeholder': 'Description of this activity type...'
            }),
        }
        
        labels = {
            'name': 'Activity Type Name',
            'description': 'Description',
        }
        
        help_texts = {
            'name': 'Name of the activity type (e.g., Quiz, Project, Field Trip)',
            'description': 'Description of what this activity type involves',
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            # Check for duplicate names (excluding current instance if editing)
            existing = ActivityType.objects.filter(name=name)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError('An activity type with this name already exists.')
        
        return name


class ActivityForm(forms.ModelForm):
    """Form for creating and editing activities"""
    
    class Meta:
        model = Activity
        fields = ['activity_type', 'subject', 'term', 'class_assigned', 'date', 'notes']
        
        widgets = {
            'activity_type': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
            }),
            'subject': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
            }),
            'term': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
            }),
            'class_assigned': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
            }),
            'date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'rows': 3,
                'placeholder': 'Notes about this activity...'
            }),
        }
        
        labels = {
            'activity_type': 'Activity Type',
            'subject': 'Subject',
            'term': 'Academic Term',
            'class_assigned': 'Class Level',
            'date': 'Activity Date',
            'notes': 'Notes',
        }
        
        help_texts = {
            'activity_type': 'Select the type of activity',
            'subject': 'Subject this activity is for (optional)',
            'term': 'Academic term when this activity takes place',
            'class_assigned': 'Class level participating in this activity (optional)',
            'date': 'Date when the activity takes place',
            'notes': 'Additional notes or instructions for the activity',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default term to active term
        active_term = Term.objects.filter(is_active=True).first()
        if active_term and not self.instance.pk:
            self.fields['term'].initial = active_term
        
        # Make subject and class_assigned optional
        self.fields['subject'].required = False
        self.fields['class_assigned'].required = False

    def clean_date(self):
        date = self.cleaned_data.get('date')
        term = self.cleaned_data.get('term')
        
        if date and term:
            # Validate that activity date is within the term dates
            if date < term.start_date or date > term.end_date:
                raise ValidationError(f'Activity date must be between {term.start_date} and {term.end_date}.')
        
        return date


class BulkSubjectDeleteForm(forms.Form):
    """Form for bulk deleting subjects"""
    
    subjects = forms.ModelMultipleChoiceField(
        queryset=Subject.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'mr-2'
        }),
        label='Select Subjects to Delete',
        help_text='Select the subjects you want to delete. Warning: This will also delete all related enrollments and assessments.'
    )
    
    confirm_deletion = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-4 h-4 text-[#F28C8C] bg-gray-100 border-gray-300 rounded focus:ring-[#F28C8C] focus:ring-2'
        }),
        label='I confirm that I want to delete the selected subjects',
        help_text='This action cannot be undone and will delete all related data'
    )


class BulkAssessmentTypeDeleteForm(forms.Form):
    """Form for bulk deleting assessment types"""
    
    assessment_types = forms.ModelMultipleChoiceField(
        queryset=AssessmentType.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'mr-2'
        }),
        label='Select Assessment Types to Delete',
        help_text='Select the assessment types you want to delete. This will also delete all related assessments.'
    )
    
    confirm_deletion = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-4 h-4 text-[#F28C8C] bg-gray-100 border-gray-300 rounded focus:ring-[#F28C8C] focus:ring-2'
        }),
        label='I confirm that I want to delete the selected assessment types',
        help_text='This action cannot be undone and will delete all related assessments'
    )


class BulkActivityDeleteForm(forms.Form):
    """Form for bulk deleting activities"""

    activities = forms.ModelMultipleChoiceField(
        queryset=Activity.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'mr-2'
        }),
        label='Select Activities to Delete',
        help_text='Select the activities you want to delete. This will also delete all participation records.'
    )

    confirm_deletion = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-4 h-4 text-[#F28C8C] bg-gray-100 border-gray-300 rounded focus:ring-[#F28C8C] focus:ring-2'
        }),
        label='I confirm that I want to delete the selected activities',
        help_text='This action cannot be undone and will delete all participation records'
    )


class BulkActivityTypeDeleteForm(forms.Form):
    """Form for bulk deleting activity types"""

    activity_types = forms.ModelMultipleChoiceField(
        queryset=ActivityType.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'mr-2'
        }),
        label='Select Activity Types to Delete',
        help_text='Select the activity types you want to delete. This will also delete all related activities.'
    )

    confirm_deletion = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-4 h-4 text-[#F28C8C] bg-gray-100 border-gray-300 rounded focus:ring-[#F28C8C] focus:ring-2'
        }),
        label='I confirm that I want to delete the selected activity types',
        help_text='This action cannot be undone and will delete all related activities'
    )
