from django import forms

from students.models import Level, Student

import pandas as pd


class AdmissionForm(forms.ModelForm):
    term = forms.ChoiceField(
        choices=[
            ('Term 1', 'Term 1'),
            ('Term 2', 'Term 2'),
            ('Term 3', 'Term 3'),

        ],
        widget=forms.Select(attrs={'class': 'django-form'}),
        required=False
    )

    class Meta:
        model = Student
        fields = ['name', 'gender', 'level']

        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'django-form',
                'placeholder': 'Name of the Student'
            }),
            'gender': forms.Select(attrs={
                'class': 'django-form',
            }),
            'level': forms.Select(attrs={
                'class': 'django-form',
            }),
        }

    def save(self, commit=True):
        student = super().save(commit=False)

        if commit:
            student.save()

        return student


class ExcelAdmissionForm(forms.Form):
    excel_student_file = forms.FileField(
        label='Student Excel File',
        widget=forms.FileInput(attrs={'class': 'django-file'}),
        required=False
    )

    def save(self, request, commit=True):
        upload_student_file = self.cleaned_data.get("excel_student_file")

        if upload_student_file:
            df_students = pd.read_excel(upload_student_file)

            for index, row in df_students.iterrows():
                try:

                    current_class_instance = Level.objects.get(
                        level_name=row['current_class']
                    )

                except Level.DoesNotExist:
                    raise ValueError(
                        f"Class Level {row['current_class']} cannot be found"
                    )

                student_data = {
                    'name': row['student'],
                    'gender': row['gender'],
                    'level': current_class_instance,
                }

                student = Student(
                    **student_data
                )
                student.save()


class EditStudentForm(forms.ModelForm):
    """Form for editing student details including activation/deactivation"""

    class Meta:
        model = Student
        fields = ['name', 'gender', 'level', 'is_active']

        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border-2 border-[#B9D8EB] rounded-xl bg-[#F7FAFC] text-[#2C3E50] font-medium focus:border-[#7AB2D3] focus:bg-white focus:ring-4 focus:ring-[#7AB2D3]/20 transition-all duration-300',
                'placeholder': 'Enter student name'
            }),
            'gender': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border-2 border-[#B9D8EB] rounded-xl bg-[#F7FAFC] text-[#2C3E50] font-medium focus:border-[#7AB2D3] focus:bg-white focus:ring-4 focus:ring-[#7AB2D3]/20 transition-all duration-300'
            }),
            'level': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border-2 border-[#B9D8EB] rounded-xl bg-[#F7FAFC] text-[#2C3E50] font-medium focus:border-[#7AB2D3] focus:bg-white focus:ring-4 focus:ring-[#7AB2D3]/20 transition-all duration-300'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'toggle-input'
            }),
        }

        labels = {
            'name': 'Student Name',
            'gender': 'Gender',
            'level': 'Class Level',
            'is_active': 'Student Status'
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make all fields required except is_active
        for field_name, field in self.fields.items():
            if field_name != 'is_active':
                field.required = True

    def save(self, commit=True):
        student = super().save(commit=False)

        if commit:
            student.save()

        return student
