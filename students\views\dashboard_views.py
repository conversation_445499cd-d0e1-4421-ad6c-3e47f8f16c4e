from datetime import date
from calendar import month_abbr
import json

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Q
from django.db.models.functions import ExtractMonth
from django.conf import settings


from finances.book_keeping import JournalLine
from finances.fee_management.models import FeeAccount, Receipt
from students.models import Student

#

from students.utils.calculations import calculate_fees_collections, calculate_fees_totals, get_expenditure_total, get_outstanding_balances, get_student_counts

current_month = date.today()


# TODO: add to dashboard_views
@login_required(login_url="accounts:login")
def home(request):
    from students.models import Term
    term = Term.objects.get_active()
    tuition_fees_total, food_fees_total = calculate_fees_totals(term)
    tuition_collection, food_collection = calculate_fees_collections()

    tuition_balance = tuition_fees_total - tuition_collection
    food_balance = food_fees_total - food_collection

    total_collected = food_collection + tuition_collection
    overall_total = tuition_fees_total + food_fees_total
    fee_receivables = overall_total - total_collected

    expenditure_total = get_expenditure_total()
    students_count, male_students_count, female_students_count = get_student_counts()

    incomes = (
        JournalLine.objects
        .annotate(month=ExtractMonth("journal_entry__date"))
        .values("month")
        .annotate(amount=Sum("amount", filter=Q(account__ledger_type="Revenue")))
        .order_by("month")
    )

    expenditures = (
        JournalLine.objects
        .annotate(month=ExtractMonth("journal_entry__date"))
        .values("month")
        .annotate(amount=Sum("amount", filter=Q(account__ledger_type="Expense")))
        .order_by("month")
    )

# Format into list with month labels
    income_data = [
        {
            "month": month_abbr[income["month"]],
            "income": int(income["amount"]) if income["amount"] else 0,
            "expense": int(expense["amount"]) if expense["amount"] else 0
        }
        for income, expense in zip(incomes, expenditures)
    ]

    context = {
        "students_count": students_count,
        "male_students_count": male_students_count,
        "female_students_count": female_students_count,
        "overall_total": overall_total,
        "total_collected": total_collected,
        "fee_receivables": fee_receivables,
        "expenditure_total": expenditure_total,
        "tuition_fees_total": tuition_fees_total,
        "food_fees_total": food_fees_total,
        "tuition_collection": tuition_collection,
        "food_collection": food_collection,
        "food_balance": food_balance,
        "tuition_balance": tuition_balance,
        "income_data": json.dumps(income_data),
    }

    return render(request, "students/home.html", context)


@login_required(login_url="accounts:login")
def outstanding_balances(request, slug):
    cycle = slug.capitalize()
    fee_accounts = get_outstanding_balances(cycle)
    context = {
        "fee_accounts": fee_accounts,
        "current_month": current_month,
        "cycle": cycle
    }
    return render(request, "students/outstanding_balances.html", context)


@login_required(login_url="accounts:login")
def payment_summary(request, slug):
    from students.models import Term
    term = Term.objects.get_active()
    student = Student.objects.get(slug=slug)
    fee_account = FeeAccount.objects.filter(
        student=student, term=term).order_by('month')
    start_date = term.start_date
    end_date = term.end_date
    receipts = Receipt.objects.filter(
        student=student, date__gte=start_date, date__lte=end_date).order_by("-date", "-receipt_number")
    context = {
        'student': student,
        'receipts': receipts,
        'fee_account': fee_account,
    }
    return render(request, 'students/payment_summary.html', context)
