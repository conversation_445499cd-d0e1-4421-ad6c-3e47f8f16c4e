from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import CustomUser, TeacherAssignment, Role, Permission, UserRole


class UserRoleInline(admin.TabularInline):
    """Inline for managing user roles in the user admin"""
    model = UserRole
    fk_name = 'user'
    extra = 1
    fields = ('role', 'is_active', 'assigned_by', 'expires_at', 'notes')
    readonly_fields = ('assigned_at',)


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """Enhanced user admin with RBAC support"""

    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff',
                   'is_active', 'get_roles', 'get_role_level')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'date_joined',
                  'user_roles__role__name')
    search_fields = ('username', 'first_name', 'last_name', 'email')
    ordering = ('username',)

    # Enable autocomplete for notification targeting
    def get_search_results(self, request, queryset, search_term):
        """Optimize search for autocomplete fields"""
        queryset, may_have_duplicates = super().get_search_results(
            request, queryset, search_term
        )
        # Limit results for performance in autocomplete
        if 'autocomplete' in request.path:
            queryset = queryset[:20]
        return queryset, may_have_duplicates

    fieldsets = UserAdmin.fieldsets + (
        ('Additional Info', {
            'fields': ('gender', 'phone_number')
        }),
        ('RBAC Info', {
            'fields': ('get_current_roles', 'get_permissions_summary'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = UserAdmin.readonly_fields + ('get_current_roles', 'get_permissions_summary')
    inlines = [UserRoleInline]

    def get_roles(self, obj):
        """Display user's active roles"""
        roles = obj.get_active_roles()
        if not roles:
            return "No roles"

        role_links = []
        for user_role in roles[:3]:  # Show first 3 roles
            url = reverse('admin:accounts_role_change', args=[user_role.role.pk])
            role_links.append(f'<a href="{url}">{user_role.role.display_name}</a>')

        result = ', '.join(role_links)
        if len(roles) > 3:
            result += f' (+{len(roles) - 3} more)'

        return mark_safe(result)
    get_roles.short_description = 'Active Roles'

    def get_role_level(self, obj):
        """Display user's highest role level"""
        level = obj.get_highest_role_level()
        if level == 0:
            return "No role"
        return f"Level {level}"
    get_role_level.short_description = 'Role Level'

    def get_current_roles(self, obj):
        """Display detailed role information"""
        roles = obj.get_active_roles()
        if not roles:
            return "No active roles"

        role_info = []
        for user_role in roles:
            info = f"• {user_role.role.display_name} (Level {user_role.role.level})"
            if user_role.expires_at:
                info += f" - Expires: {user_role.expires_at.strftime('%Y-%m-%d')}"
            if user_role.is_expired():
                info += " [EXPIRED]"
            role_info.append(info)

        return mark_safe('<br>'.join(role_info))
    get_current_roles.short_description = 'Current Roles'

    def get_permissions_summary(self, obj):
        """Display permissions summary"""
        permissions = obj.get_all_permissions()
        if not permissions:
            return "No permissions"

        # Group by category
        by_category = {}
        for perm in permissions:
            category = perm.category
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(perm.display_name)

        summary = []
        for category, perms in by_category.items():
            summary.append(f"<strong>{category.title()}:</strong> {len(perms)} permissions")

        return mark_safe('<br>'.join(summary))
    get_permissions_summary.short_description = 'Permissions Summary'


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    """Admin interface for roles"""

    list_display = ('display_name', 'name', 'level', 'get_permissions_count',
                   'get_users_count', 'is_active', 'is_default')
    list_filter = ('level', 'is_active', 'is_default', 'created_at')
    search_fields = ('name', 'display_name', 'description')
    ordering = ('level', 'name')

    fieldsets = (
        (None, {
            'fields': ('name', 'display_name', 'level', 'description')
        }),
        ('Settings', {
            'fields': ('is_active', 'is_default')
        }),
        ('Permissions', {
            'fields': ('permissions',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')
    filter_horizontal = ('permissions',)

    def get_permissions_count(self, obj):
        """Display number of permissions"""
        count = obj.permissions.filter(is_active=True).count()
        return f"{count} permissions"
    get_permissions_count.short_description = 'Permissions'

    def get_users_count(self, obj):
        """Display number of users with this role"""
        count = UserRole.objects.filter(role=obj, is_active=True).count()
        url = reverse('admin:accounts_userrole_changelist') + f'?role__id__exact={obj.pk}'
        return format_html('<a href="{}">{} users</a>', url, count)
    get_users_count.short_description = 'Users'


@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    """Admin interface for permissions"""

    list_display = ('display_name', 'name', 'category', 'get_roles_count', 'is_active')
    list_filter = ('category', 'is_active', 'created_at')
    search_fields = ('name', 'display_name', 'description', 'category')
    ordering = ('category', 'name')

    fieldsets = (
        (None, {
            'fields': ('name', 'display_name', 'category', 'description')
        }),
        ('Settings', {
            'fields': ('is_active',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')

    def get_roles_count(self, obj):
        """Display number of roles with this permission"""
        count = obj.roles.filter(is_active=True).count()
        return f"{count} roles"
    get_roles_count.short_description = 'Roles'


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    """Admin interface for user role assignments"""

    list_display = ('user', 'role', 'is_active', 'assigned_at', 'assigned_by',
                   'expires_at', 'get_status')
    list_filter = ('role', 'is_active', 'assigned_at', 'expires_at')
    search_fields = ('user__username', 'user__first_name', 'user__last_name',
                    'role__name', 'role__display_name')
    ordering = ('-assigned_at',)

    fieldsets = (
        (None, {
            'fields': ('user', 'role', 'is_active')
        }),
        ('Assignment Details', {
            'fields': ('assigned_by', 'assigned_at', 'expires_at', 'notes')
        }),
    )

    readonly_fields = ('assigned_at',)

    def get_status(self, obj):
        """Display assignment status"""
        if not obj.is_active:
            return format_html('<span style="color: red;">Inactive</span>')
        elif obj.is_expired():
            return format_html('<span style="color: orange;">Expired</span>')
        else:
            return format_html('<span style="color: green;">Active</span>')
    get_status.short_description = 'Status'


@admin.register(TeacherAssignment)
class TeacherAssignmentAdmin(admin.ModelAdmin):
    """Admin interface for teacher assignments"""

    list_display = ('teacher', 'subject', 'class_assigned', 'term')
    list_filter = ('subject', 'class_assigned', 'term')
    search_fields = ('teacher__username', 'teacher__first_name', 'teacher__last_name',
                    'subject__name', 'class_assigned__name')
    ordering = ('teacher', 'subject')


# Customize admin site
admin.site.site_header = "School Management System - RBAC Administration"
admin.site.site_title = "RBAC Admin"
admin.site.index_title = "Role-Based Access Control Management"
