/* Easter Egg Styles */

/* Common Easter Egg Container */
.easter-egg-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Philosophical Easter Egg */
.philosophical-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Music Easter Egg */
.music-container {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

/* Space Easter Egg */
.space-container {
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
}

/* Art Easter Egg */
.art-container {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* Travel Easter Egg */
.travel-container {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* Quote Card Styles */
.quote-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 3rem;
    max-width: 600px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 1s ease-out;
}

.quote-text {
    font-size: 1.5rem;
    font-style: italic;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.quote-author {
    font-size: 1.1rem;
    color: #40657F;
    font-weight: 600;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
    font-size: 2rem;
}

/* Action Buttons */
.action-buttons {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-easter {
    background: linear-gradient(45deg, #40657F, #7AB2D3);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
}

.btn-easter:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(64, 101, 127, 0.3);
    color: white;
    text-decoration: none;
}

.btn-easter i {
    margin-right: 0.5rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
    }
    50% { 
        transform: translateY(-20px) rotate(180deg); 
    }
}

@keyframes mysticalGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(64, 101, 127, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(64, 101, 127, 0.6);
    }
}

@keyframes floatingText {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from { 
        transform: translateX(100%); 
    }
    to { 
        transform: translateX(0); 
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Animation Classes */
.mystical-glow {
    animation: mysticalGlow 3s ease-in-out infinite;
}

.floating-text {
    animation: floatingText 4s ease-in-out infinite;
}

.sparkle-effect {
    animation: sparkle 2s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .quote-card {
        padding: 2rem;
        margin: 1rem;
        max-width: 90vw;
    }
    
    .quote-text {
        font-size: 1.25rem;
    }
    
    .quote-author {
        font-size: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .btn-easter {
        width: 100%;
        justify-content: center;
    }
    
    .floating-element {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .quote-card {
        padding: 1.5rem;
    }
    
    .quote-text {
        font-size: 1.125rem;
    }
    
    .floating-element {
        font-size: 1.25rem;
    }
}

/* Theme-specific customizations */
.space-container .quote-card {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.space-container .quote-text {
    color: #ffffff;
}

.space-container .quote-author {
    color: #a8edea;
}

.music-container .btn-easter {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
}

.art-container .btn-easter {
    background: linear-gradient(45deg, #ff9a56, #ff6b9d);
}

.travel-container .btn-easter {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
}

.space-container .btn-easter {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

/* Notification styles */
.easter-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #74C69D;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Loading states */
.btn-easter.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Accessibility improvements */
.quote-card:focus-within {
    outline: 2px solid #40657F;
    outline-offset: 4px;
}

.btn-easter:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .quote-card {
        background: rgba(255, 255, 255, 1);
        border: 2px solid #000;
    }
    
    .quote-text {
        color: #000;
    }
    
    .quote-author {
        color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .quote-card {
        animation: none;
    }
    
    .floating-element {
        animation: none;
    }
    
    .btn-easter {
        transition: none;
    }
    
    .btn-easter:hover {
        transform: none;
    }
}
