"""
Art/Creativity Easter Egg - Random Art & Creativity Quote Generator
A delightful surprise for creative minds and art lovers
"""

import random

ART_QUOTES = [
    # Famous Artists
    {
        "quote": "Every artist was first an amateur.",
        "author": "<PERSON>",
        "context": "American Philosopher",
        "theme": "Beginning & Growth",
        "category": "Artistic Journey"
    },
    {
        "quote": "Art is not what you see, but what you make others see.",
        "author": "Edgar <PERSON>",
        "context": "French Impressionist",
        "theme": "Perspective & Vision",
        "category": "Artistic Purpose"
    },
    {
        "quote": "I dream of painting and then I paint my dream.",
        "author": "<PERSON>",
        "context": "Post-Impressionist Master",
        "theme": "Dreams & Reality",
        "category": "Creative Process"
    },
    {
        "quote": "Art washes away from the soul the dust of everyday life.",
        "author": "Pablo Picasso",
        "context": "Cubist Pioneer",
        "theme": "Soul Cleansing",
        "category": "Art's Power"
    },
    {
        "quote": "Creativity takes courage.",
        "author": "<PERSON>",
        "context": "Fauvism Leader",
        "theme": "Bravery in Art",
        "category": "Creative Courage"
    },
    {
        "quote": "The purpose of art is washing the dust of daily life off our souls.",
        "author": "<PERSON>",
        "context": "Spanish Artist",
        "theme": "Spiritual Cleansing",
        "category": "Art's Purpose"
    },
    {
        "quote": "Art enables us to find ourselves and lose ourselves at the same time.",
        "author": "Thomas Merton",
        "context": "Writer & Mystic",
        "theme": "Self-Discovery",
        "category": "Personal Growth"
    },
    {
        "quote": "Every child is an artist. The problem is how to remain an artist once we grow up.",
        "author": "Pablo Picasso",
        "context": "Master of Modern Art",
        "theme": "Childlike Wonder",
        "category": "Artistic Development"
    },
    {
        "quote": "Art is the most intense mode of individualism that the world has known.",
        "author": "Oscar Wilde",
        "context": "Irish Playwright",
        "theme": "Individual Expression",
        "category": "Artistic Identity"
    },
    {
        "quote": "The artist is nothing without the gift, but the gift is nothing without work.",
        "author": "Émile Zola",
        "context": "French Novelist",
        "theme": "Talent & Effort",
        "category": "Artistic Development"
    },
    {
        "quote": "Art is the lie that enables us to realize the truth.",
        "author": "Pablo Picasso",
        "context": "Revolutionary Artist",
        "theme": "Truth Through Fiction",
        "category": "Artistic Philosophy"
    },
    {
        "quote": "Imagination is more important than knowledge.",
        "author": "Albert Einstein",
        "context": "Theoretical Physicist",
        "theme": "Creative Thinking",
        "category": "Innovation"
    },
    {
        "quote": "Art is freedom. Being able to bend things most people see as a straight line.",
        "author": "Banksy",
        "context": "Anonymous Street Artist",
        "theme": "Creative Freedom",
        "category": "Modern Art"
    },
    {
        "quote": "The world always seems brighter when you've just made something that wasn't there before.",
        "author": "Neil Gaiman",
        "context": "Fantasy Author",
        "theme": "Creation Joy",
        "category": "Creative Satisfaction"
    },
    {
        "quote": "Art is not a thing; it is a way.",
        "author": "Elbert Hubbard",
        "context": "American Writer",
        "theme": "Artistic Approach",
        "category": "Philosophy of Art"
    },
    {
        "quote": "Color is my day-long obsession, joy and torment.",
        "author": "Claude Monet",
        "context": "Impressionist Master",
        "theme": "Passion for Color",
        "category": "Artistic Obsession"
    },
    {
        "quote": "Art is the only way to run away without leaving home.",
        "author": "Twyla Tharp",
        "context": "Choreographer",
        "theme": "Escapism",
        "category": "Art as Journey"
    },
    {
        "quote": "Creativity is intelligence having fun.",
        "author": "Albert Einstein",
        "context": "Genius Scientist",
        "theme": "Playful Intelligence",
        "category": "Creative Process"
    },
    {
        "quote": "Art is the stored honey of the human soul.",
        "author": "Theodore Dreiser",
        "context": "American Novelist",
        "theme": "Soul's Essence",
        "category": "Spiritual Art"
    },
    {
        "quote": "The aim of art is to represent not the outward appearance of things, but their inward significance.",
        "author": "Aristotle",
        "context": "Ancient Greek Philosopher",
        "theme": "Inner Meaning",
        "category": "Classical Philosophy"
    },
    {
        "quote": "Art is the signature of civilizations.",
        "author": "Beverly Sills",
        "context": "Opera Singer",
        "theme": "Cultural Legacy",
        "category": "Civilization"
    },
    {
        "quote": "Without art, the crudeness of reality would make the world unbearable.",
        "author": "George Bernard Shaw",
        "context": "Irish Playwright",
        "theme": "Beauty in Life",
        "category": "Art's Necessity"
    },
    {
        "quote": "Art is the journey of a free soul.",
        "author": "Alev Oguz",
        "context": "Contemporary Artist",
        "theme": "Spiritual Freedom",
        "category": "Soul Expression"
    },
    {
        "quote": "Every artist dips his brush in his own soul, and paints his own nature into his pictures.",
        "author": "Henry Ward Beecher",
        "context": "American Clergyman",
        "theme": "Personal Expression",
        "category": "Artistic Soul"
    },
    {
        "quote": "Art is the antidote that can call us back from the edge of numbness, restoring the ability to feel for another.",
        "author": "Barbara Kingsolver",
        "context": "American Novelist",
        "theme": "Emotional Healing",
        "category": "Art's Power"
    }
]

# Art and creativity related trigger words
ART_TRIGGER_WORDS = [
    'art',
    'artist',
    'paint',
    'painting',
    'draw',
    'drawing',
    'sketch',
    'creative',
    'creativity',
    'design',
    'designer',
    'color',
    'colour',
    'brush',
    'canvas',
    'palette',
    'sculpture',
    'sculptor',
    'gallery',
    'museum',
    'exhibition',
    'masterpiece',
    'artwork',
    'fine art',
    'visual art',
    'abstract',
    'realistic',
    'impressionist',
    'expressionist',
    'cubist',
    'surreal',
    'surrealism',
    'renaissance',
    'baroque',
    'modern art',
    'contemporary art',
    'street art',
    'graffiti',
    'mural',
    'fresco',
    'oil painting',
    'watercolor',
    'acrylic',
    'pastel',
    'charcoal',
    'pencil',
    'ink',
    'digital art',
    'graphic design',
    'illustration',
    'portrait',
    'landscape',
    'still life',
    'picasso',
    'van gogh',
    'monet',
    'da vinci',
    'michelangelo',
    'rembrandt',
    'degas',
    'renoir',
    'cezanne',
    'matisse',
    'dali',
    'warhol',
    'pollock',
    'kahlo',
    'banksy',
    'basquiat',
    'hockney',
    'rothko',
    'kandinsky',
    'klimt',
    'munch',
    'manet',
    'goya',
    'turner',
    'constable',
    'hopper',
    'o\'keeffe',
    'imagination',
    'inspire',
    'inspiration',
    'vision',
    'aesthetic',
    'beauty',
    'beautiful',
    'artistic',
    'craftsmanship',
    'technique',
    'style',
    'composition',
    'perspective',
    'light',
    'shadow',
    'texture',
    'form',
    'shape',
    'line',
    'pattern',
    'rhythm',
    'balance',
    'harmony',
    'contrast',
    'unity',
    'variety',
    'emphasis',
    'movement',
    'proportion',
    'scale',
    'space',
    'negative space',
    'positive space',
    'foreground',
    'background',
    'middle ground',
    'focal point',
    'visual',
    'imagery',
    'symbolism',
    'metaphor',
    'expression',
    'emotion',
    'feeling',
    'mood',
    'atmosphere',
    'ambiance',
    'culture',
    'cultural',
    'tradition',
    'heritage',
    'history',
    'historical',
    'contemporary',
    'avant-garde',
    'experimental',
    'innovative',
    'original',
    'unique',
    'personal',
    'individual',
    'self-expression',
    'communication',
    'message',
    'story',
    'narrative',
    'concept',
    'idea',
    'theme',
    'subject',
    'motif',
    'genre',
    'medium',
    'media',
    'mixed media',
    'collage',
    'montage',
    'installation',
    'performance art',
    'conceptual art',
    'minimalism',
    'pop art',
    'folk art',
    'naive art',
    'primitive art',
    'tribal art',
    'ethnic art',
    'religious art',
    'sacred art',
    'secular art',
    'decorative art',
    'applied art',
    'craft',
    'handicraft',
    'pottery',
    'ceramics',
    'jewelry',
    'textile',
    'fashion',
    'architecture',
    'photography',
    'film',
    'cinema',
    'animation',
    'cartoon',
    'comic',
    'manga',
    'anime'
]

def get_random_art_quote():
    """Get a random art/creativity quote"""
    return random.choice(ART_QUOTES)

def is_art_search(search_term):
    """Check if search term contains art/creativity trigger words"""
    if not search_term:
        return False
    
    search_lower = search_term.lower().strip()
    
    # Check for exact matches and partial matches
    for trigger in ART_TRIGGER_WORDS:
        if trigger in search_lower:
            return True
    
    return False

def get_art_quote_by_category(category=None):
    """Get quotes filtered by category"""
    if not category:
        return get_random_art_quote()
    
    category_quotes = [q for q in ART_QUOTES if category.lower() in q['category'].lower()]
    return random.choice(category_quotes) if category_quotes else get_random_art_quote()

def get_art_quotes_by_author(author):
    """Get all art quotes by a specific author"""
    return [q for q in ART_QUOTES if author.lower() in q['author'].lower()]
