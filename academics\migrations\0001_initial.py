# Generated by Django 5.1.2 on 2025-06-26 19:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('students', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ActivityType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=50)),
                ('slug', models.SlugField(blank=True, null=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AssessmentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=50)),
                ('slug', models.SlugField(blank=True, null=True)),
                ('weight', models.IntegerField()),
                ('is_final', models.BooleanField(default=False)),
                ('sequence', models.IntegerField()),
                ('description', models.TextField(blank=True, help_text='Description of the assessment type, e.g., Continuous Assessment, Final Exam', null=True)),
            ],
            options={
                'verbose_name': 'Assessment Type',
                'verbose_name_plural': 'Assessment Types',
                'db_table': 'academics_assessment_type',
                'ordering': ['sequence'],
            },
        ),
        migrations.CreateModel(
            name='Enrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.student')),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='students.term')),
            ],
            options={
                'db_table': 'academics_enrollment',
            },
        ),
        migrations.CreateModel(
            name='Assessment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.IntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now=True)),
                ('assessment_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.assessmenttype')),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessments', to='academics.enrollment')),
            ],
            options={
                'verbose_name': 'Assessment',
                'verbose_name_plural': 'Assessments',
                'db_table': 'academics_assessment',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('slug', models.SlugField(blank=True, null=True)),
                ('abbrv', models.CharField(max_length=100)),
                ('category', models.CharField(max_length=50)),
                ('level', models.CharField(choices=[('Primary', 'Primary'), ('Nursery', 'Nursery'), ('Both', 'Both')], default='Both', max_length=50)),
                ('student', models.ManyToManyField(through='academics.Enrollment', to='students.student')),
            ],
            options={
                'db_table': 'academics_subject',
            },
        ),
        migrations.AddField(
            model_name='enrollment',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='academics.subject'),
        ),
        migrations.CreateModel(
            name='Activity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('notes', models.TextField(blank=True, help_text='Notes about the activity', null=True)),
                ('class_assigned', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='students.level')),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='students.term')),
                ('activity_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='academics.activitytype')),
                ('subject', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='academics.subject')),
            ],
            options={
                'verbose_name': 'Activity',
                'verbose_name_plural': 'Activities',
                'db_table': 'academics_activity',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='StudentActivityParticipation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('participation_status', models.CharField(choices=[('attended', 'Attended'), ('absent', 'Absent'), ('excused', 'Excused')], default='attended', max_length=20)),
                ('score', models.IntegerField(blank=True, help_text='Score for the activity, if applicable', null=True)),
                ('activity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participations', to='academics.activity')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_participations', to='students.student')),
            ],
            options={
                'verbose_name': 'Student Activity Participation',
                'verbose_name_plural': 'Student Activity Participations',
                'db_table': 'academics_student_activity_participation',
                'unique_together': {('student', 'activity')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='enrollment',
            unique_together={('student', 'subject', 'term')},
        ),
    ]
