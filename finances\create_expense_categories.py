'''
    DEPRECATED: This file was used to create monthly reports for Expense Categories.

    The ExpenseCategory model has been removed and replaced with the modern
    Outflow and Ledger system for expense tracking.

    For expense management, use:
    - finances.book_keeping.Outflow for expense transactions
    - finances.book_keeping.Ledger for expense account tracking
    - finances.book_keeping.JournalEntry/JournalLine for double-entry bookkeeping
'''

# from datetime import date
# from finances.expenses.models import ExpenseCategory  # REMOVED - deprecated model


def create_monthly_category():
    """
    DEPRECATED: ExpenseCategory model has been removed.
    Use the modern Outflow and Ledger system instead.
    """
    print("DEPRECATED: ExpenseCategory model has been removed.")
    print("Use the modern Outflow and Ledger system for expense tracking.")
    return
