from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages

from accounts.models import CustomUser, TeacherAssignment
from accounts.forms import AddAssignmentForm, RegisterTeacherForm, UpdateTeacherForm
from accounts.decorators import require_permission, staff_or_permission_required


'''
    this is the start of a collection of views for the school level admin,
    to manage teachers on a school level
'''
# Teacher view


@staff_or_permission_required('view_teachers')
def teachers(request):
    """View all teachers - requires view_teachers permission or staff status"""
    teachers = CustomUser.objects.filter(is_superuser=False, is_staff=False)

    context = {
        'teachers': teachers,
    }

    return render(request, 'teachers/teachers.html', context)


@staff_or_permission_required('add_teacher')
def add_teacher(request):
    """Add new teacher - requires add_teacher permission or staff status"""
    form = RegisterTeacherForm()
    if request.method == 'POST':
        form = RegisterTeacherForm(
            request.POST, request.FILES)
        if form.is_valid():
            user = form.save(commit=True)
            user.save()

            messages.success(request, ("Registration Successful"))

            return redirect('accounts:teachers')

    context = {
        'form': form
    }

    return render(request, 'teachers/teacher_form.html', context)


@staff_or_permission_required('edit_teacher')
def edit_teacher(request, pk):
    """Edit teacher information - requires edit_teacher permission or staff status"""
    instance = get_object_or_404(CustomUser, pk=pk)

    form = UpdateTeacherForm(instance=instance)
    if request.method == 'POST':
        form = UpdateTeacherForm(
            request.POST, request.FILES, instance=instance)

        if form.is_valid():
            form.save()
            return redirect('accounts:teachers')

    context = {"form": form}

    return render(request, 'teachers/teacher_form.html', context)


@staff_or_permission_required('view_teacher_details')
def teacher_details(request, id):
    """View teacher details - requires view_teacher_details permission or staff status"""
    teacher = CustomUser.objects.get(id=id)

    context = {
        "teacher": teacher,
    }

    return render(request, 'teachers/teacher_details.html', context)


@staff_or_permission_required('manage_teacher_assignments')
def add_assignment(request, pk):
    """Add teacher assignment - requires manage_teacher_assignments permission or staff status"""
    teacher = get_object_or_404(CustomUser, pk=pk)

    form = AddAssignmentForm()
    if request.method == "POST":
        form = AddAssignmentForm(request.POST)
        if form.is_valid():
            assignment = form.save(commit=False)
            assignment.teacher = teacher
            assignment.save()

            return redirect('accounts:teachers')

    context = {'form': form}

    return render(request, 'teachers/assignment_form.html', context)


@staff_or_permission_required('manage_teacher_assignments')
def remove_assignment(request, pk):
    """Remove teacher assignment - requires manage_teacher_assignments permission or staff status"""
    instance = get_object_or_404(TeacherAssignment, pk=pk)
    instance.delete()

    return redirect('accounts:teachers')


'''
    this is the end of the teacher management views on a school level by the admin
'''
