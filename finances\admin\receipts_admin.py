from django.contrib import admin
from django.utils.html import format_html


from finances.fee_management.models import FeesWaiver, Receipt, FeeAccount, FeeCategory
# Income model removed - deprecated in favor of Budget/Ledger system


admin.site.register(FeesWaiver)


@admin.action(description='Seed Journal Entries for selected Receipts')
def seed_journal_entries(modeladmin, request, queryset):
    created_count = 0

    for receipt in queryset:
        receipt.save()
        created_count += 1

    modeladmin.message_user(
        request, f"{created_count} journal entries created.")


@admin.register(Receipt)
class ReceiptAdmin(admin.ModelAdmin):
    search_fields = ["student__name", "receipt_number"]
    list_display = ["receipt_number", "student",
                    "amount_paid", "date", "amount_paid"]
    list_filter = ["fee_account__category", "fee_account__term"]
    actions = [seed_journal_entries]


@admin.register(FeeAccount)
class FeeAccountAdmin(admin.ModelAdmin):
    search_fields = ["student__name", "term__term_name"]
    list_display = [
        "student", "term", "category", "total_due",
        "amount", "colored_status"
    ]
    list_filter = ["term", "category", ]

    def colored_status(self, obj):
        if obj.is_fully_paid:
            return format_html('<span style="color: green;">{}</span>', "Paid")
        elif obj.amount > 0:
            return format_html('<span style="color: orange;">{}</span>', "Pending")
        else:
            return format_html('<span style="color: red;">{}</span>', "Not Paid")
    colored_status.short_description = "Status"


# Income seeding removed - deprecated model
# Income tracking is now handled through the modern Budget/Ledger system:
# - Budget/BudgetLine for income planning
# - Ledger for income account tracking
# - JournalEntry/JournalLine for income transactions


@admin.register(FeeCategory)
class FeeCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'amount')
    # Income seeding action removed - use Budget/Ledger system instead
