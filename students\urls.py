from django.urls import path
from .views import (
    admission, admission_by_excel, class_details, home, outstanding_balances,
    payment_summary, student_details, students, classes, edit_student,
    student_progression, progress_students_view, academic_year_management,
    add_academic_year, edit_academic_year, add_term, edit_term,
    close_academic_year, activate_term, academic_year_details
)

app_name = 'students'

urlpatterns = [
    path('', home, name="home"),
    path('students/', students, name="students"),
    path('student/<slug:slug>/', student_details, name="student_details"),
    path('student/<slug:slug>/edit/', edit_student, name="edit_student"),
    path('admission/', admission, name="admission"),
    path("admission-by-excel/", admission_by_excel, name="admission_by_excel"),
    path('classes/', classes, name="classes"),
    path('class/<slug:slug>/', class_details, name="class_details"),
    path('outstanding/<slug:slug>/', outstanding_balances,
         name="outstanding_balances"),
    path("student/payment-summary/<slug:slug>/",
         payment_summary, name="payment_summary"),
    path('progression/', student_progression, name="student_progression"),
    path('progression/progress/', progress_students_view, name="progress_students"),

    # Academic Year Management URLs
    path('academic-years/', academic_year_management,
         name="academic_year_management"),
    path('academic-years/add/', add_academic_year, name="add_academic_year"),
    path('academic-years/<int:pk>/edit/',
         edit_academic_year, name="edit_academic_year"),
    path('academic-years/<int:pk>/details/',
         academic_year_details, name="academic_year_details"),
    path('academic-years/close/', close_academic_year, name="close_academic_year"),

    # Term Management URLs
    path('terms/add/', add_term, name="add_term"),
    path('terms/<int:pk>/edit/', edit_term, name="edit_term"),
    path('terms/activate/', activate_term, name="activate_term"),
]
