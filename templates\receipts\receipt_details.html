{% load static %} {% load humanize %}
<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-color: #7ab2d3;
        --primary-dark: #5a9bd4;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .font-display {
        font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      @media print {
        body {
          background: white !important;
        }
        .no-print {
          display: none !important;
        }
      }
    </style>

    <title>
      Receipt - {{ student.student.name }}_{{student.receipt_number}}
    </title>
  </head>
  <body
    class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4"
  >
    <!-- Modern Receipt Container -->
    <div class="w-full max-w-2xl mx-auto bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-200/50">
      <!-- Header Section -->
      <div class="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] p-8 text-white">
        <div class="flex justify-between items-center mb-6">
          <div class="flex items-center gap-4">
            <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
              <img src="{% static 'img/tinyfeet.jpg' %}" alt="Tiny Feet Academy" class="w-12 h-12 rounded-xl object-cover" />
            </div>
            <div>
              <h1 class="font-display font-bold text-2xl">Tiny Feet Academy</h1>
              <p class="text-white/80 text-sm font-medium">Fee Payment Receipt</p>
            </div>
          </div>
          <div class="text-right">
            <div class="bg-white/20 rounded-xl p-3 backdrop-blur-sm">
              <p class="text-xs font-medium text-white/80 uppercase tracking-wider">Contact</p>
              <p class="text-white font-bold">0999865060</p>
            </div>
          </div>
        </div>

        <div class="bg-white/10 rounded-2xl p-4 backdrop-blur-sm">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-white/80 text-xs font-medium uppercase tracking-wider">Receipt Number</p>
              <p class="text-white font-bold text-lg">{{student.receipt_number}}</p>
            </div>
            <div class="text-right">
              <p class="text-white/80 text-xs font-medium uppercase tracking-wider">Date Issued</p>
              <p class="text-white font-bold">{{ student.date_created|date:"M d, Y" }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Student Information -->
      <div class="p-8 space-y-6">
        <div class="flex items-center gap-3 mb-6">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
            <i class="fas fa-user text-white text-sm"></i>
          </div>
          <h2 class="text-xl font-bold text-gray-800 font-display">Student Information</h2>
          <div class="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-4 border border-blue-200/50">
            <p class="text-xs font-bold text-gray-600 uppercase tracking-wider mb-1">Student ID</p>
            <p class="text-blue-600 font-bold text-lg">{{ student.student.student_id }}</p>
          </div>

          <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-4 border border-green-200/50">
            <p class="text-xs font-bold text-gray-600 uppercase tracking-wider mb-1">Student Name</p>
            <p class="text-green-600 font-bold text-lg">{{ student.student.name }}</p>
          </div>

          <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-2xl p-4 border border-purple-200/50">
            <p class="text-xs font-bold text-gray-600 uppercase tracking-wider mb-1">Term</p>
            <p class="text-purple-600 font-bold text-lg">{{ student.fee_account.term }}</p>
          </div>

          <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-4 border border-orange-200/50">
            <p class="text-xs font-bold text-gray-600 uppercase tracking-wider mb-1">Category</p>
            <p class="text-orange-600 font-bold text-lg">{{ student.fee_account.category }}</p>
        </div>
        <div class="flex justify-between w-full border-b-2">
          <p class="uppercase font-medium">amount</p>
          <p class="font-semibold">K {{ student.amount_paid|intcomma }}</p>
        </div>
        <div class="flex justify-between w-full border-b-2">
          <p class="uppercase font-medium">total amount</p>
          <p class="font-semibold">
            K {{ student.fee_account.total_due|intcomma }}
          </p>
        </div>
        <div class="flex justify-between w-full border-b-2">
          <p class="uppercase font-medium">Balance</p>
          <p class="font-semibold">
            K {{ student.fee_account.current_balance|intcomma }}
          </p>
        </div>
        {% comment %}
        Overpayments are handled at the group level through receipt distribution,
        individual accounts should not show overpayments
        {% endcomment %}
        <div class="flex justify-between w-full border-b-2">
          <p class="uppercase font-medium">Class</p>
          <p class="font-semibold">{{ student.student.level}}</p>
        </div>
        <div class="flex justify-between w-full border-b-2">
          <p class="uppercase font-medium">date</p>
          <p class="font-semibold">{{ student.date }}</p>
        </div>
        <div class="flex justify-between w-full border-b-2 border-dashed">
          <p class="uppercase font-medium">description</p>
          <p class="font-semibold">{{ student.fee_account.category }}</p>
        </div>

        <div
          class="flex flex-col gap-1 items-center justify-center items-centerw-full"
        >
          <p class="text-xl font-semibold text-[#7AB2D3] border-b-2 px-10">
            Payment Successful <i class="fa-regular fa-circle-check"></i>
          </p>
          <p class="italic">Nurturing the young minds</p>
        </div>
      </section>
    </section>

    <script>
      onload(window.print());
    </script>
  </body>
</html>
