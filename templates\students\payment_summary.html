{% load static %} {% load humanize %}
<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-color: #7AB2D3;
        --primary-dark: #5a9bd4;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .font-display {
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      @media print {
        body {
          background: white !important;
        }
        .no-print {
          display: none !important;
        }
      }
    </style>

    <title>{{student.name}} - Fee Summary Report</title>
  </head>
  <body class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
    <div class="w-full max-w-6xl mx-auto space-y-8">
      <!-- Header Section -->
      <div class="bg-white rounded-3xl shadow-2xl p-8 border border-gray-200/50">
        <div class="flex flex-col lg:flex-row items-center justify-between gap-6">
          <div class="flex items-center gap-6">
            <div class="w-20 h-20 bg-gradient-to-br from-[var(--primary-color)] to-[var(--primary-dark)] rounded-3xl flex items-center justify-center shadow-xl">
              <img src="{% static 'img/tinyfeet.jpg' %}" alt="Tiny Feet Academy" class="w-16 h-16 rounded-2xl object-cover" />
            </div>
            <div>
              <h1 class="font-display font-bold text-3xl text-gray-800">Fee Summary Report</h1>
              <p class="text-gray-600 text-lg font-medium">Comprehensive payment overview</p>
              <div class="w-32 h-1 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] rounded-full mt-2"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Student Information -->
      <div class="bg-white rounded-3xl shadow-2xl p-8 border border-gray-200/50">
        <div class="flex items-center gap-3 mb-6">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
            <i class="fas fa-user text-white text-sm"></i>
          </div>
          <h2 class="text-xl font-bold text-gray-800 font-display">Student Information</h2>
          <div class="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200/50">
            <div class="flex items-center gap-3 mb-3">
              <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-user text-white text-xs"></i>
              </div>
              <p class="text-xs font-bold text-gray-600 uppercase tracking-wider">Student Name</p>
            </div>
            <p class="text-blue-600 font-bold text-xl">{{ student.name }}</p>
          </div>

          <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200/50">
            <div class="flex items-center gap-3 mb-3">
              <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-id-card text-white text-xs"></i>
              </div>
              <p class="text-xs font-bold text-gray-600 uppercase tracking-wider">Student ID</p>
            </div>
            <p class="text-green-600 font-bold text-xl">{{ student.student_id }}</p>
          </div>

          <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-2xl p-6 border border-purple-200/50">
            <div class="flex items-center gap-3 mb-3">
              <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-graduation-cap text-white text-xs"></i>
              </div>
              <p class="text-xs font-bold text-gray-600 uppercase tracking-wider">Class</p>
            </div>
            <p class="text-purple-600 font-bold text-xl">{{ student.level.level_name }}</p>
          </div>
        </div>
      </div>

      <section class="flex flex-col w-full gap-2">
        <h1 class="text-xl font-semibold text-[#333]">Fee Account Overview</h1>
        <table class="bg-white rounded-lg border border-gray-200 w-full">
          <thead>
            <tr>
              <th
                class="px-6 text-left py-2 border-b border-gray-200 bg-gray-50"
              >
                Fee Account Name
              </th>
              <th
                class="px-6 text-left py-2 border-b border-gray-200 bg-gray-50"
              >
                Total Due
              </th>
              <th
                class="px-6 text-left py-2 border-b border-gray-200 bg-gray-50"
              >
                Amount Paid
              </th>
              <th
                class="px-6 text-left py-2 border-b border-gray-200 bg-gray-50"
              >
                Balance
              </th>
              <th
                class="px-6 text-left py-2 border-b border-gray-200 bg-gray-50"
              >
                Billing_cycle
              </th>
              <th
                class="px-6 text-left py-2 border-b border-gray-200 bg-gray-50"
              >
                Month
              </th>
            </tr>
          </thead>
          <tbody>
            {% for account in fee_account %}
            <tr class="{% cycle 'bg-white' 'bg-gray-100' %}">
              <td class="px-6 py-2 border-b border-gray-200">
                {{ account.category }}
              </td>
              <td class="px-6 py-2 border-b border-gray-200">
                MWK {{ account.total_due|intcomma }}
              </td>
              <td class="px-6 py-2 border-b border-gray-200">
                MWK {{ account.amount_paid|intcomma }}
              </td>
              <td class="px-6 py-2 border-b border-gray-200">
                MWK {{ account.balance|intcomma}}
              </td>
              <td class="px-6 py-2 border-b border-gray-200">
                {{ account.billing_cycle }}
              </td>
              <td class="px-6 py-2 border-b border-gray-200">
                {{ account.month|date:'F' }}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </section>

      <div class="w-full h-0.5 bg-gray-300"></div>

      <section class="flex flex-col w-full gap-2">
        <h1 class="text-xl font-semibold text-[#333]">Fee Payment History</h1>
        <table class="bg-white rounded-lg border border-gray-200 w-full">
          <thead>
            <tr>
              <th
                class="px-6 text-left py-2 border-b border-gray-200 bg-gray-50"
              >
                Payment Date
              </th>
              <th
                class="px-6 text-left py-2 border-b border-gray-200 bg-gray-50"
              >
                Receipt Number
              </th>
              <th
                class="px-6 text-left py-2 border-b border-gray-200 bg-gray-50"
              >
                Amount Paid
              </th>
              <th
                class="px-6 text-left py-2 border-b border-gray-200 bg-gray-50"
              >
                Payment Category
              </th>
            </tr>
          </thead>
          <tbody>
            {% for payment in receipts %}
            <tr class="{% cycle 'bg-white' 'bg-gray-100' %}">
              <td class="px-6 py-2 border-b border-gray-200">
                {{ payment.date }}
              </td>
              <td class="px-6 py-2 border-b border-gray-200">
                {{ payment.receipt_number }}
              </td>
              <td class="px-6 py-2 border-b border-gray-200">
                {{ payment.amount_paid }}
              </td>
              <td class="px-6 py-2 border-b border-gray-200">
                {{ payment.fee_account.category }}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </section>
    </main>

    <script>
      onload(window.print());
    </script>
  </body>
</html>
