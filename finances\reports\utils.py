from finances.book_keeping import Ledger, Outflow
from django.db.models.functions import Coalesce
from django.db.models import Sum, Case, When, Value, DecimalField, F
from django.db.models import Sum


def get_month_number(month_name, months_dict):
    return {value: key for key, value in months_dict.items() if value}.get(month_name.capitalize())


def get_sum_of_field(queryset, field_name):
    return queryset.aggregate(total=Sum(field_name))['total'] or 0


def get_aggregate(queryset, *fields):
    aggreation = queryset.aggregate(**{field: Sum(field) for field in fields})

    return {key: value or 0 for key, value in aggreation.items()}


def get_expenditure_data(start_date=None, end_date=None, term=None):
    if start_date and end_date:
        expenses = Ledger.objects.filter(
            ledger_type="Expense"
        ).annotate(
            total_amount=Coalesce(Sum(
                Case(
                    When(
                        journal_line__journal_entry__date__range=(
                            start_date, end_date),
                        then=F('journal_line__amount')
                    ),
                ),
            ), Value(0, output_field=DecimalField()))
        ).values('name', 'total_amount')

        outflow = Outflow.objects.filter(date__range=(start_date, end_date))
    else:
        expenses = Ledger.objects.filter(
            ledger_type="Expense").order_by('name')
        outflow = Outflow.objects.filter(term=term)

    return expenses, outflow
