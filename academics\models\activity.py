from django.db import models

from core.base_models import NameSlugModels
from students.models import Level, Student


class ActivityType(NameSlugModels):
    description = models.TextField(null=True, blank=True)


class Activity(models.Model):
    activity_type = models.ForeignKey(
        ActivityType, on_delete=models.CASCADE, related_name='activities')
    subject = models.ForeignKey(
        'academics.Subject', on_delete=models.CASCADE, related_name='activities', blank=True, null=True)
    term = models.ForeignKey(
        'students.Term', on_delete=models.CASCADE, related_name='activities')
    class_assigned = models.ForeignKey(
        Level, on_delete=models.CASCADE, related_name='activities', blank=True, null=True)
    date = models.DateField()
    notes = models.TextField(null=True, blank=True,
                             help_text="Notes about the activity")

    def __str__(self):
        return f"{self.activity_type.name} | {self.subject.name if self.subject else 'N/A'} | {self.term} | {self.date}"

    class Meta:
        verbose_name = "Activity"
        verbose_name_plural = "Activities"
        ordering = ['-date']


class StudentActivityParticipation(models.Model):
    student = models.ForeignKey(
        Student, on_delete=models.CASCADE, related_name='activity_participations')
    activity = models.ForeignKey(
        Activity, on_delete=models.CASCADE, related_name='participations')
    participation_status = models.CharField(max_length=20, choices=[
        ('attended', 'Attended'),
        ('absent', 'Absent'),
        ('excused', 'Excused'),
    ], default='attended')
    score = models.IntegerField(
        null=True, blank=True, help_text="Score for the activity, if applicable")

    @property
    def get_position(self):
        students = self.activity.participations.filter(
            participation_status='attended', activity=self.activity).order_by('-score')

        for index, participation in enumerate(students, start=1):
            if participation.student.id == self.student.id:
                return index

        return None

    def __str__(self):
        return f"{self.student.name} | {self.activity.activity_type.name} | {self.participation_status}"

    class Meta:
        unique_together = ('student', 'activity')
        verbose_name = "Student Activity Participation"
        verbose_name_plural = "Student Activity Participations"
