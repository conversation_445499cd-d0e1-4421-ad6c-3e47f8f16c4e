{% extends 'base.html' %}
{% load static %}
{% load rbac_tags %}

{% block title %}{{ user_obj.get_full_name|default:user_obj.username }} | User Details{% endblock %}

{% block content %}
<section class="w-full max-w-6xl mx-auto px-4 py-8 space-y-8">
  <!-- Header -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-4">
        <div class="w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg icon-float">
          <span class="text-white text-2xl font-bold">
            {{ user_obj.first_name|first|upper }}{{ user_obj.last_name|first|upper }}
          </span>
        </div>
        <div>
          <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
            {{ user_obj.get_full_name|default:user_obj.username }}
          </h1>
          <div class="w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full accent-line-grow"></div>
          <p class="text-[#40657F] mt-2">@{{ user_obj.username }}</p>
        </div>
      </div>
      
      <div class="flex gap-3">
        {% if can_edit %}
        <a href="{% url 'accounts:edit_user' user_obj.id %}" 
           class="btn-secondary flex items-center gap-2 px-4 py-2 rounded-lg font-medium">
          <i class="fas fa-edit"></i>
          Edit User
        </a>
        {% endif %}
        
        {% if can_assign_roles %}
        <a href="{% url 'accounts:assign_role' user_obj.id %}" 
           class="btn-primary flex items-center gap-2 px-4 py-2 rounded-lg font-medium">
          <i class="fas fa-user-plus"></i>
          Assign Role
        </a>
        {% endif %}
      </div>
    </div>
    
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <a href="{% url 'accounts:user_management' %}" class="text-[#40657F] hover:text-[#7AB2D3]">User Management</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">{{ user_obj.get_full_name|default:user_obj.username }}</span>
    </nav>
  </div>

  <!-- User Information Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    
    <!-- Basic Information -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Personal Details -->
      <div class="card-modern p-8">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-user text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">Personal Information</h3>
            <p class="text-[#40657F] text-sm">Basic user details and contact information</p>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-semibold text-[#2C3E50] mb-2">First Name</label>
            <div class="p-3 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#2C3E50]">
              {{ user_obj.first_name|default:"Not provided" }}
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Last Name</label>
            <div class="p-3 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#2C3E50]">
              {{ user_obj.last_name|default:"Not provided" }}
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Email Address</label>
            <div class="p-3 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#2C3E50]">
              {{ user_obj.email|default:"Not provided" }}
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Phone Number</label>
            <div class="p-3 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#2C3E50]">
              {{ user_obj.phone_number|default:"Not provided" }}
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Gender</label>
            <div class="p-3 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#2C3E50]">
              {{ user_obj.gender|default:"Not specified" }}
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Date Joined</label>
            <div class="p-3 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg text-[#2C3E50]">
              {{ user_obj.date_joined|date:"M d, Y" }}
            </div>
          </div>
        </div>
      </div>

      <!-- Active Roles -->
      <div class="card-modern p-8">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-12 h-12 bg-gradient-to-br from-[#FFB84D] to-[#e6a43d] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-user-shield text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">Active Roles</h3>
            <p class="text-[#40657F] text-sm">Current role assignments and permissions</p>
          </div>
        </div>
        
        {% if active_roles %}
        <div class="space-y-4">
          {% for user_role in active_roles %}
          <div class="p-4 border border-[#E2F1F9] rounded-xl {% if user_role.is_expired %}bg-[#FFF5F5] border-[#F28C8C]{% else %}bg-[#F7FAFC]{% endif %}">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">L{{ user_role.role.level }}</span>
                </div>
                <div>
                  <div class="font-semibold text-[#2C3E50]">{{ user_role.role.display_name }}</div>
                  <div class="text-sm text-[#40657F]">
                    Assigned {{ user_role.assigned_at|date:"M d, Y" }}
                    {% if user_role.assigned_by %}by {{ user_role.assigned_by.get_full_name|default:user_role.assigned_by.username }}{% endif %}
                  </div>
                </div>
              </div>
              
              <div class="text-right">
                {% if user_role.is_expired %}
                  <span class="px-3 py-1 bg-[#F28C8C] text-white rounded-full text-sm font-semibold">
                    <i class="fas fa-clock mr-1"></i>Expired
                  </span>
                {% elif user_role.expires_at %}
                  <span class="px-3 py-1 bg-[#FFB84D] text-white rounded-full text-sm font-semibold">
                    <i class="fas fa-calendar mr-1"></i>Expires {{ user_role.expires_at|date:"M d, Y" }}
                  </span>
                {% else %}
                  <span class="px-3 py-1 bg-[#74C69D] text-white rounded-full text-sm font-semibold">
                    <i class="fas fa-infinity mr-1"></i>Permanent
                  </span>
                {% endif %}
              </div>
            </div>
            
            {% if user_role.notes %}
            <div class="mt-3 p-3 bg-[#E2F1F9] rounded-lg">
              <div class="text-sm text-[#40657F]">
                <strong>Notes:</strong> {{ user_role.notes }}
              </div>
            </div>
            {% endif %}
          </div>
          {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8">
          <div class="w-16 h-16 bg-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-user-shield text-[#7AB2D3] text-xl"></i>
          </div>
          <h4 class="text-lg font-bold text-[#2C3E50] mb-2">No Active Roles</h4>
          <p class="text-[#40657F] mb-4">This user has no active role assignments.</p>
          {% if can_assign_roles %}
          <a href="{% url 'accounts:assign_role' user_obj.id %}" 
             class="btn-primary inline-flex items-center gap-2 px-4 py-2 rounded-lg font-medium">
            <i class="fas fa-plus"></i>
            Assign First Role
          </a>
          {% endif %}
        </div>
        {% endif %}
      </div>

      <!-- Permissions -->
      <div class="card-modern p-8">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-12 h-12 bg-gradient-to-br from-[#9B59B6] to-[#8e44ad] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-key text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">Permissions</h3>
            <p class="text-[#40657F] text-sm">Inherited permissions from role assignments</p>
          </div>
        </div>
        
        {% if permissions_by_category %}
        <div class="space-y-6">
          {% for category, perms in permissions_by_category.items %}
          <div>
            <h4 class="text-lg font-semibold text-[#2C3E50] mb-3 capitalize">{{ category }} Permissions</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              {% for permission in perms %}
              <div class="flex items-center gap-3 p-3 bg-[#F7FAFC] border border-[#E2F1F9] rounded-lg">
                <div class="w-2 h-2 bg-[#74C69D] rounded-full"></div>
                <div>
                  <div class="font-medium text-[#2C3E50]">{{ permission.display_name }}</div>
                  <div class="text-xs text-[#40657F]">{{ permission.name }}</div>
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
          {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8">
          <div class="w-16 h-16 bg-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-key text-[#7AB2D3] text-xl"></i>
          </div>
          <h4 class="text-lg font-bold text-[#2C3E50] mb-2">No Permissions</h4>
          <p class="text-[#40657F]">This user has no permissions assigned through roles.</p>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-8">
      
      <!-- Account Status -->
      <div class="card-modern p-6">
        <div class="flex items-center gap-3 mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-lg flex items-center justify-center">
            <i class="fas fa-info-circle text-white text-sm"></i>
          </div>
          <h4 class="text-lg font-bold text-[#2C3E50]">Account Status</h4>
        </div>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-[#40657F]">Status</span>
            {% if user_obj.is_active %}
              <span class="px-3 py-1 bg-[#74C69D] text-white rounded-full text-sm font-semibold">
                <i class="fas fa-check-circle mr-1"></i>Active
              </span>
            {% else %}
              <span class="px-3 py-1 bg-[#F28C8C] text-white rounded-full text-sm font-semibold">
                <i class="fas fa-times-circle mr-1"></i>Inactive
              </span>
            {% endif %}
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-[#40657F]">Staff Status</span>
            {% if user_obj.is_staff %}
              <span class="px-3 py-1 bg-[#FFB84D] text-white rounded-full text-sm font-semibold">
                <i class="fas fa-user-tie mr-1"></i>Staff
              </span>
            {% else %}
              <span class="px-3 py-1 bg-[#B9D8EB] text-[#40657F] rounded-full text-sm font-semibold">
                Regular User
              </span>
            {% endif %}
          </div>
          
          {% if user_obj.is_superuser %}
          <div class="flex items-center justify-between">
            <span class="text-[#40657F]">Superuser</span>
            <span class="px-3 py-1 bg-[#9B59B6] text-white rounded-full text-sm font-semibold">
              <i class="fas fa-crown mr-1"></i>Yes
            </span>
          </div>
          {% endif %}
          
          <div class="flex items-center justify-between">
            <span class="text-[#40657F]">Role Level</span>
            <span class="px-3 py-1 bg-[#7AB2D3] text-white rounded-full text-sm font-semibold">
              Level {{ audit_data.role_level }}
            </span>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-[#40657F]">Last Login</span>
            <span class="text-[#2C3E50] text-sm">
              {% if user_obj.last_login %}
                {{ user_obj.last_login|date:"M d, Y H:i" }}
              {% else %}
                Never
              {% endif %}
            </span>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="card-modern p-6">
        <div class="flex items-center gap-3 mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-lg flex items-center justify-center">
            <i class="fas fa-bolt text-white text-sm"></i>
          </div>
          <h4 class="text-lg font-bold text-[#2C3E50]">Quick Actions</h4>
        </div>
        
        <div class="space-y-3">
          {% if can_edit %}
          <a href="{% url 'accounts:edit_user' user_obj.id %}" 
             class="w-full flex items-center gap-3 p-3 bg-[#F7FAFC] hover:bg-[#E2F1F9] border border-[#E2F1F9] rounded-lg transition-colors">
            <i class="fas fa-edit text-[#7AB2D3]"></i>
            <span class="text-[#2C3E50] font-medium">Edit User Information</span>
          </a>
          {% endif %}
          
          {% if can_assign_roles %}
          <a href="{% url 'accounts:assign_role' user_obj.id %}" 
             class="w-full flex items-center gap-3 p-3 bg-[#F7FAFC] hover:bg-[#E2F1F9] border border-[#E2F1F9] rounded-lg transition-colors">
            <i class="fas fa-user-plus text-[#74C69D]"></i>
            <span class="text-[#2C3E50] font-medium">Assign New Role</span>
          </a>
          {% endif %}
          
          <a href="{% url 'accounts:user_management' %}" 
             class="w-full flex items-center gap-3 p-3 bg-[#F7FAFC] hover:bg-[#E2F1F9] border border-[#E2F1F9] rounded-lg transition-colors">
            <i class="fas fa-arrow-left text-[#40657F]"></i>
            <span class="text-[#2C3E50] font-medium">Back to User List</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

{% endblock %}
