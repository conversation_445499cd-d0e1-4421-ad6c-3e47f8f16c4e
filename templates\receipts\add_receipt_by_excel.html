{% extends 'base.html' %} {% load static %} {% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-6 space-y-6">
  <!-- Breadcrumb -->
  <div class="card-modern p-5">
    <div class="flex items-center gap-3 mb-3">
      <div
        class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-file-excel text-white text-lg"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-xl text-gray-800">
          Excel Upload
        </h1>
        <div
          class="w-16 h-1 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] rounded-full"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-2 text-sm font-medium">
      <span class="text-gray-500">Home</span>
      <i class="fa-solid fa-chevron-right text-gray-400 text-xs"></i>
      <span class="text-[var(--primary-color)] font-semibold"
        >Excel Upload</span
      >
    </nav>
  </div>

  <!-- Main Form Section -->
  <div class="card-modern p-6 space-y-6">
    <div
      class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4"
    >
      <div class="flex items-center gap-3">
        <div
          class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-md"
        >
          <i class="fas fa-upload text-white text-sm"></i>
        </div>
        <div>
          <h1 class="text-lg font-bold text-gray-800 font-display">
            Upload Excel File
          </h1>
          <p class="text-gray-600 text-sm font-medium">
            Bulk import student data
          </p>
        </div>
      </div>
      <a
        href="{% url 'finances:generate_excel' %}"
        class="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] text-white font-semibold py-2.5 px-5 rounded-xl hover:from-[var(--primary-dark)] hover:to-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group text-sm w-full md:w-fit text-center"
      >
        <i
          class="fas fa-download mr-2 group-hover:scale-110 transition-transform duration-300"
        ></i>
        Get Excel Template
      </a>
    </div>

    <!-- Upload Instructions -->
    <div
      class="bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200/50 rounded-2xl p-6 space-y-4"
    >
      <div class="flex items-center gap-3">
        <div
          class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center"
        >
          <i class="fas fa-info text-white text-sm"></i>
        </div>
        <h3 class="font-display font-bold text-lg text-gray-800">
          Upload Instructions
        </h3>
      </div>
      <div class="space-y-2 text-sm text-gray-600">
        <p class="flex items-center gap-2">
          <i class="fas fa-check text-green-500"></i>
          Download the Excel template using the button above
        </p>
        <p class="flex items-center gap-2">
          <i class="fas fa-check text-green-500"></i>
          Fill in receipt information in the provided format
        </p>
        <p class="flex items-center gap-2">
          <i class="fas fa-check text-green-500"></i>
          Upload the completed file using the form below
        </p>
        <p class="flex items-center gap-2">
          <i class="fas fa-exclamation-triangle text-yellow-500"></i>
          Ensure all required fields are filled correctly
        </p>
      </div>
    </div>

    <!-- Upload Form -->
    <form method="post" enctype="multipart/form-data" class="space-y-6">
      {% csrf_token %}

      <div class="space-y-6">
        {% for field in form %}
        <div class="form-field space-y-3">
          <label
            class="flex items-center gap-2 font-bold text-gray-700 text-sm"
            for="{{ field.auto_id }}"
          >
            <div
              class="w-4 h-4 bg-gradient-to-br from-[var(--primary-color)] to-[var(--primary-dark)] rounded-lg flex items-center justify-center"
            >
              <i class="fas fa-circle text-white text-xs"></i>
            </div>
            {{ field.label }} {% if field.field.required %}
            <span class="text-red-500 text-xs">*</span>
            {% endif %}
          </label>
          <div class="relative">
            {{ field }} {% if field.errors %}
            <div class="text-red-500 text-xs mt-1 flex items-center gap-1">
              <i class="fas fa-exclamation-circle"></i>
              {{ field.errors.0 }}
            </div>
            {% endif %}
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Form Actions -->
      <div
        class="flex flex-col sm:flex-row gap-4 justify-end pt-6 border-t border-gray-200"
      >
        <button
          type="reset"
          class="bg-gray-100 text-gray-700 font-semibold py-3 px-6 rounded-xl hover:bg-gray-200 focus:ring-4 focus:ring-gray-300/30 transition-all duration-300 border border-gray-200 hover:border-gray-300 group"
        >
          <i class="fas fa-undo mr-2 group-hover:rotate-180 transition-all duration-200"></i>
          Reset Form
        </button>
        <button
          type="submit"
          class="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] text-white font-semibold py-3 px-6 rounded-xl hover:from-[var(--primary-dark)] hover:to-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group"
        >
          <i
            class="fas fa-upload mr-2 group-hover:translate-y-[-2px] transition-transform duration-300"
          ></i>
          Upload Receipts
        </button>
      </div>
    </form>
  </div>
</section>
{% endblock %}
