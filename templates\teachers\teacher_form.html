{% extends 'academics/base.html' %} {% load static %} {% block title %}Add
Teacher | {% endblock %} {% block content %}
<section class="w-full max-w-5xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl"
        >
          <i class="fas fa-user-plus text-white text-lg sm:text-xl"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight"
          >
            Teacher Registration
          </h1>
          <p class="text-[#40657F] text-base sm:text-lg font-medium mt-1">
            Add new teaching staff member
          </p>
          <div class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2"></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3">
        <a
          href="{% url 'accounts:teachers' %}"
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#7AB2D3] hover:text-white focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-arrow-left mr-2 group-hover:translate-x-[-2px] transition-transform duration-300"
          ></i>
          Back to Teachers
        </a>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200"
    >
      <span class="text-[#40657F]">Academics</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a
        href="{% url 'accounts:teachers' %}"
        class="text-[#40657F] hover:text-[#7AB2D3] transition-colors"
        >Teachers</a
      >
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Add Teacher</span>
    </nav>
  </div>

  <!-- Form Section -->
  <div class="card-modern p-8">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-chalkboard-teacher text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Teacher Information
        </h2>
        <p class="text-[#40657F] text-sm">
          Complete the form below to add a new teacher
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <!-- Error Messages -->
    {% if form.errors %}
    <div
      class="card-modern p-6 border-l-4 border-[#F28C8C] bg-[#F28C8C]/10 mb-8"
    >
      <div class="flex items-center gap-4">
        <div
          class="w-10 h-10 bg-[#F28C8C] rounded-xl flex items-center justify-center shadow-md"
        >
          <i class="fas fa-exclamation-triangle text-white text-sm"></i>
        </div>
        <div class="flex-1">
          <h4 class="text-lg font-bold text-[#F28C8C] mb-2">
            Form Validation Errors
          </h4>
          <p class="text-[#F28C8C] text-sm font-medium">
            Please review and correct the highlighted fields below.
          </p>
        </div>
      </div>
    </div>
    {% endif %}
    <!-- Form -->
    <form method="post" enctype="multipart/form-data" class="space-y-8">
      {% csrf_token %}

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {% for field in form %}
        <div
          class="form-field space-y-3 {% if field.field.widget.input_type == 'textarea' %}md:col-span-2{% endif %}"
        >
          <label
            class="flex items-center gap-3 font-bold text-[#2C3E50] text-sm"
            for="{{ field.auto_id }}"
          >
            <div
              class="w-5 h-5 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center shadow-sm"
            >
              <i class="fas fa-circle text-white text-xs"></i>
            </div>
            {{ field.label }} {% if field.field.required %}
            <span
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-[#F28C8C]/20 text-[#F28C8C] border border-[#F28C8C]/30"
            >
              Required
            </span>
            {% endif %}
          </label>
          <div class="relative">
            {{ field }} {% if field.errors %}
            <div
              class="mt-2 p-3 bg-[#F28C8C]/10 border border-[#F28C8C]/30 rounded-lg"
            >
              <div
                class="flex items-center gap-2 text-[#F28C8C] text-sm font-medium"
              >
                <i class="fas fa-exclamation-circle"></i>
                {{ field.errors.0 }}
              </div>
            </div>
            {% endif %}
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Form Actions -->
      <div
        class="flex flex-col sm:flex-row gap-4 justify-end pt-8 border-t border-[#B9D8EB]"
      >
        <button
          type="reset"
          class="bg-[#E2F1F9] text-[#40657F] font-semibold py-3 px-8 rounded-xl hover:bg-[#B9D8EB] hover:text-[#2C3E50] focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-undo mr-2 group-hover:rotate-180 transition-transform duration-300"
          ></i>
          Reset Form
        </button>
        <button
          type="submit"
          class="bg-[#74C69D] text-white font-semibold py-3 px-8 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-save mr-2 group-hover:scale-110 transition-transform duration-300"
          ></i>
          Save Teacher
        </button>
      </div>
    </form>
  </div>
</section>
{% endblock %}
