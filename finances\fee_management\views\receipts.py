from django.shortcuts import redirect, render
from django.http import HttpResponse
from django.views import View
from django.db.models import Q
from django.core.paginator import Paginator

from finances.fee_management.utils import create_pdf
from finances.fee_management.models import Receipt
from students.utils.date_utils import get_months
from students.models import Student
from finances.fee_management.forms import ExcelPaymentForm, ReceiptForm

from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages


from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font


@login_required(login_url="accounts:login")
def generate_receipt_pdf(request, student_id, receipt_number):
    student = Receipt.objects.get(
        receipt_number=receipt_number, student__student_id=student_id)

    pdf_buffer = create_pdf(student)
    response = HttpResponse(pdf_buffer, content_type="application/pdf")
    response['Content-Disposition'] = f'attachment; filename="{student.student.name}_{student.receipt_number}.pdf"'
    context = {"student": student}

    return response


@login_required(login_url="accounts:login")
def receipt_payment_months(request):
    months = get_months()

    months_and_receipts = [
        (month, Receipt.objects.filter(date__month=month_index, is_reversed=False).count()) for month_index, month in months
    ]

    context = {
        'months_and_receipts': months_and_receipts,
        'months': months,
    }
    return render(request, 'receipts/receipt_payment_months.html', context)


@login_required(login_url="accounts:login")
def receipt_list(request):
    """
    Comprehensive receipt search and listing view
    """
    # Get search query and filters from request
    search_query = request.GET.get('search', '').strip()
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    category_filter = request.GET.get('category', '')
    level_filter = request.GET.get('level', '')

    # Base queryset with optimized select_related
    receipts = Receipt.objects.filter(
        is_reversed=False
    ).select_related(
        'student',
        'fee_account',
        'fee_account__category',
        'student__level',
        'created_by'
    )

    # Apply search filters
    if search_query:
        receipts = receipts.filter(
            Q(student__name__icontains=search_query) |
            Q(student__student_id__icontains=search_query) |
            Q(receipt_number__icontains=search_query) |
            Q(fee_account__category__name__icontains=search_query) |
            Q(student__level__level_name__icontains=search_query)
        )

    # Apply date filters
    if date_from:
        receipts = receipts.filter(date__gte=date_from)
    if date_to:
        receipts = receipts.filter(date__lte=date_to)

    # Apply category filter
    if category_filter:
        receipts = receipts.filter(fee_account__category__name=category_filter)

    # Apply level filter
    if level_filter:
        receipts = receipts.filter(student__level__level_name=level_filter)

    # Order results
    receipts = receipts.order_by('-date', '-receipt_number')

    # Add pagination
    paginator = Paginator(receipts, 25)  # Show 25 receipts per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options for dropdowns
    from finances.fee_management.models import FeeCategory
    from students.models import Level

    categories = FeeCategory.objects.filter(
        is_active=True).values_list('name', flat=True).distinct()
    levels = Level.objects.all().values_list('level_name', flat=True).distinct()

    context = {
        'receipts': page_obj,
        'search_query': search_query,
        'date_from': date_from,
        'date_to': date_to,
        'category_filter': category_filter,
        'level_filter': level_filter,
        'total_count': receipts.count(),
        'page_obj': page_obj,
        'categories': categories,
        'levels': levels,
    }
    return render(request, 'receipts/receipt_list.html', context)


@login_required(login_url="accounts:login")
def receipts(request, slug):
    months = get_months()
    month_index = next(
        (index for index, month in months if month == slug), None)
    if month_index is None:
        return HttpResponse("Invalid month", status=400)

    # Get search query from request
    search_query = request.GET.get('search', '').strip()

    # Base queryset
    receipts = Receipt.objects.filter(
        date__month=month_index, is_reversed=False
    ).select_related('student', 'fee_account', 'fee_account__category', 'student__level')

    # Apply search filters if search query exists
    if search_query:
        receipts = receipts.filter(
            Q(student__name__icontains=search_query) |
            Q(student__student_id__icontains=search_query) |
            Q(receipt_number__icontains=search_query) |
            Q(fee_account__category__name__icontains=search_query) |
            Q(student__level__level_name__icontains=search_query)
        )

    # Order results
    receipts = receipts.order_by('-receipt_number')

    # Add pagination
    paginator = Paginator(receipts, 25)  # Show 25 receipts per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'receipts': page_obj,
        'month': slug,
        'search_query': search_query,
        'total_count': receipts.count(),
        'page_obj': page_obj,
    }
    return render(request, 'receipts/receipts.html', context)


@login_required(login_url="accounts:login")
def add_receipt(request, slug):
    student = Student.objects.get(student_id=slug)

    form = ReceiptForm()
    if request.method == 'POST':
        form = ReceiptForm(request.POST)
        if form.is_valid():
            receipt = form.save(student=student)

            receipt.save()
            messages.success(
                request, f"{receipt.student} created successfully")

            return redirect('students:student_details', slug)

    context = {
        'form': form,
        'student': student,
    }
    return render(request, 'receipts/add_receipt.html', context)


class ExcelPaymentView(LoginRequiredMixin, View):
    template_name = "receipts/add_receipt_by_excel.html"

    def get(self, request, *args, **kwargs):
        form = ExcelPaymentForm()
        context = {'form': form}
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        form = ExcelPaymentForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            return redirect('finances:receipts')


class GenerateExcelView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        data = Student.objects.filter(is_active=True).values_list(
            'student_id', 'name'
        )

        # create a new workbook and add a worksheet
        wb = Workbook()
        ws = wb.active

        # Add headers to the worksheet
        ws['A1'] = 'Student ID'
        ws['B1'] = 'Student Name'
        ws['C1'] = 'Tuition Payment'
        ws['D1'] = 'Tuition Date'
        ws['E1'] = 'Food Payment'
        ws['F1'] = 'Food Date'
        ws['G1'] = 'PTA'
        ws['H1'] = 'PTA Date'

        for cell in ws[1]:
            cell.font = Font(bold=True)

        # populate the worksheet with data from the database
        for row_num, row_data in enumerate(data, start=2):
            for col_num, value in enumerate(row_data, start=1):
                cell = ws.cell(row=row_num, column=col_num, value=value)

                # get the column letter (eg 'A' or 'B')
                col_letter = get_column_letter(col_num)

                # Calculate the width based on the length of the value
                column_width = len(str(value)) + 2  # add some padding
                if ws.column_dimensions[col_letter].width is None or ws.column_dimensions[col_letter].width < column_width:
                    ws.column_dimensions[col_letter].width = column_width
                if col_num == 3:
                    ws.column_dimensions[col_letter].width = column_width

        # Create a response with the Excel file
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename=fees-payment-form.xlsx'
        wb.save(response)

        return response
