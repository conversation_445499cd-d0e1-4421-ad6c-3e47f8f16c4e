from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone


class CustomUser(AbstractUser):
    gender = models.CharField(max_length=6, choices=[
        ("Male", "Male"),
        ("Female", "Female"),
    ])
    phone_number = models.CharField(max_length=15, blank=True, null=True)

    def __str__(self):
        return self.username

    # RBAC Methods
    def get_active_roles(self):
        """Get all active roles for this user"""
        from .rbac import UserRole
        return UserRole.objects.filter(
            user=self,
            is_active=True,
            role__is_active=True
        ).select_related('role')

    def get_all_permissions(self):
        """Get all permissions from all active roles"""
        permissions = set()
        for user_role in self.get_active_roles():
            if not user_role.is_expired():
                permissions.update(user_role.role.get_all_permissions())
        return list(permissions)

    def has_role(self, role_name):
        """Check if user has a specific role"""
        # Superusers have all roles except for specific role checks
        if self.is_superuser and role_name != 'any_role':
            return True

        active_roles = self.get_active_roles()
        for user_role in active_roles:
            if user_role.role.name == role_name and not user_role.is_expired():
                return True
        return False

    def has_permission(self, permission_name):
        """Check if user has a specific permission"""
        # Superusers have all permissions
        if self.is_superuser:
            return True

        for user_role in self.get_active_roles():
            if not user_role.is_expired():
                if user_role.role.has_permission(permission_name):
                    return True
        return False

    def get_highest_role_level(self):
        """Get the highest role level for this user"""
        # Superusers get the highest possible level
        if self.is_superuser:
            return 6

        active_roles = self.get_active_roles()
        if not active_roles:
            return 0

        # Get non-expired role levels
        role_levels = [ur.role.level for ur in active_roles if not ur.is_expired()]

        # Return highest level or 0 if all roles are expired
        return max(role_levels) if role_levels else 0

    def assign_role(self, role, assigned_by=None, expires_at=None, notes=""):
        """Assign a role to this user"""
        from .rbac import UserRole, Role

        if isinstance(role, str):
            role = Role.objects.get(name=role)

        user_role, created = UserRole.objects.get_or_create(
            user=self,
            role=role,
            defaults={
                'assigned_by': assigned_by,
                'expires_at': expires_at,
                'notes': notes,
                'is_active': True
            }
        )

        if not created and not user_role.is_active:
            user_role.is_active = True
            user_role.assigned_by = assigned_by
            user_role.expires_at = expires_at
            user_role.notes = notes
            user_role.save()

        return user_role

    def remove_role(self, role):
        """Remove a role from this user"""
        from .rbac import Role

        if isinstance(role, str):
            role = Role.objects.get(name=role)

        self.user_roles.filter(role=role).update(is_active=False)

    def is_student(self):
        """Check if user is a student"""
        return self.has_role('student')

    def is_teacher(self):
        """Check if user is a teacher"""
        return self.has_role('teacher')

    def is_admin(self):
        """Check if user is an administrator"""
        return self.has_role('administrator') or self.has_role('super_administrator')

    def can_manage_students(self):
        """Check if user can manage students"""
        return self.has_permission('manage_students')

    def can_manage_finances(self):
        """Check if user can manage finances"""
        return self.has_permission('manage_finances')

    def can_manage_academics(self):
        """Check if user can manage academics"""
        return self.has_permission('manage_academics')
