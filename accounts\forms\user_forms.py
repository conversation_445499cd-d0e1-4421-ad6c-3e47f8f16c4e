from django import forms
from django.contrib.auth.forms import User<PERSON>reationForm, UserChangeForm, PasswordChangeForm
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime, timedelta

from accounts.models import CustomUser, Role, UserRole
from accounts.utils import RBACManager


class CreateUserForm(UserCreationForm):
    """
    Enhanced user creation form with role assignment capabilities
    """
    
    # Basic user fields
    first_name = forms.CharField(
        max_length=150,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter first name'
        })
    )
    
    last_name = forms.CharField(
        max_length=150,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter last name'
        })
    )

    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter email address'
        })
    )

    phone_number = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter phone number (optional)'
        })
    )

    gender = forms.ChoiceField(
        choices=[('', 'Select Gender'), ('Male', 'Male'), ('Female', 'Female')],
        required=True,
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        })
    )
    
    # Role assignment fields
    roles = forms.ModelMultipleChoiceField(
        queryset=Role.objects.filter(is_active=True).order_by('level'),
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'role-checkbox'
        }),
        help_text="Select one or more roles for this user"
    )
    
    role_expires_at = forms.DateTimeField(
        required=False,
        widget=forms.DateTimeInput(attrs={
            'type': 'datetime-local',
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        }),
        help_text="Optional: Set when role assignments expire"
    )

    role_notes = forms.CharField(
        max_length=500,
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'rows': 3,
            'placeholder': 'Optional notes about role assignments'
        })
    )
    
    # Account status fields
    is_active = forms.BooleanField(
        initial=True,
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-5 h-5 text-[#7AB2D3] border-[#B9D8EB] rounded focus:ring-[#7AB2D3]'
        }),
        help_text="User can log in and access the system"
    )
    
    send_welcome_email = forms.BooleanField(
        initial=True,
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-5 h-5 text-[#7AB2D3] border-[#B9D8EB] rounded focus:ring-[#7AB2D3]'
        }),
        help_text="Send welcome email with login credentials"
    )

    class Meta:
        model = CustomUser
        fields = [
            'username', 'first_name', 'last_name', 'email', 'phone_number', 
            'gender', 'password1', 'password2', 'is_active'
        ]
        
    def __init__(self, *args, **kwargs):
        self.created_by = kwargs.pop('created_by', None)
        super().__init__(*args, **kwargs)
        
        # Style the inherited fields
        self.fields['username'].widget.attrs.update({
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter username'
        })

        self.fields['password1'].widget.attrs.update({
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter password'
        })

        self.fields['password2'].widget.attrs.update({
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Confirm password'
        })
        
        # Update field labels
        self.fields['username'].help_text = "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
        self.fields['password1'].help_text = "Your password must contain at least 8 characters."
        self.fields['password2'].help_text = "Enter the same password as before, for verification."

    def clean_email(self):
        """Validate email uniqueness"""
        email = self.cleaned_data.get('email')
        if email and CustomUser.objects.filter(email=email).exists():
            raise ValidationError("A user with this email already exists.")
        return email

    def clean_roles(self):
        """Validate role assignments"""
        roles = self.cleaned_data.get('roles')
        if not roles:
            return roles
        
        # Check if user has permission to assign these roles
        if self.created_by:
            user_max_level = self.created_by.get_highest_role_level()
            for role in roles:
                if role.level > user_max_level:
                    raise ValidationError(
                        f"You cannot assign the '{role.display_name}' role "
                        f"(level {role.level}) as it's higher than your level ({user_max_level})."
                    )
        
        return roles

    def clean_role_expires_at(self):
        """Validate expiration date"""
        expires_at = self.cleaned_data.get('role_expires_at')
        if expires_at and expires_at <= timezone.now():
            raise ValidationError("Expiration date must be in the future.")
        return expires_at

    def save(self, commit=True):
        """Save user and assign roles"""
        user = super().save(commit=False)
        
        if commit:
            user.save()
            
            # Assign roles
            roles = self.cleaned_data.get('roles', [])
            expires_at = self.cleaned_data.get('role_expires_at')
            notes = self.cleaned_data.get('role_notes', '')
            
            for role in roles:
                RBACManager.assign_role_to_user(
                    user=user,
                    role=role,
                    assigned_by=self.created_by,
                    expires_at=expires_at,
                    notes=notes
                )
            
            # TODO: Send welcome email if requested
            # if self.cleaned_data.get('send_welcome_email'):
            #     self.send_welcome_email(user)
        
        return user


class EditUserForm(UserChangeForm):
    """
    Form for editing existing users
    """
    
    first_name = forms.CharField(
        max_length=150,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        })
    )

    last_name = forms.CharField(
        max_length=150,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        })
    )

    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        })
    )

    phone_number = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        })
    )

    gender = forms.ChoiceField(
        choices=[('Male', 'Male'), ('Female', 'Female')],
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        })
    )
    
    is_active = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-5 h-5 text-[#7AB2D3] border-[#B9D8EB] rounded focus:ring-[#7AB2D3]'
        })
    )

    class Meta:
        model = CustomUser
        fields = [
            'username', 'first_name', 'last_name', 'email', 
            'phone_number', 'gender', 'is_active'
        ]
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Remove password field
        if 'password' in self.fields:
            del self.fields['password']
        
        # Style username field
        self.fields['username'].widget.attrs.update({
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        })

    def clean_email(self):
        """Validate email uniqueness"""
        email = self.cleaned_data.get('email')
        if email and CustomUser.objects.filter(email=email).exclude(pk=self.instance.pk).exists():
            raise ValidationError("A user with this email already exists.")
        return email


class AssignRoleForm(forms.Form):
    """
    Form for assigning roles to existing users
    """
    
    role = forms.ModelChoiceField(
        queryset=Role.objects.filter(is_active=True).order_by('level'),
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        })
    )

    expires_at = forms.DateTimeField(
        required=False,
        widget=forms.DateTimeInput(attrs={
            'type': 'datetime-local',
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        }),
        help_text="Optional: Set when this role assignment expires"
    )

    notes = forms.CharField(
        max_length=500,
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'rows': 3,
            'placeholder': 'Optional notes about this role assignment'
        })
    )
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.assigned_by = kwargs.pop('assigned_by', None)
        super().__init__(*args, **kwargs)
    
    def clean_role(self):
        """Validate role assignment"""
        role = self.cleaned_data.get('role')
        
        if self.user and role:
            # Check if user already has this role
            if self.user.has_role(role.name):
                raise ValidationError(f"User already has the '{role.display_name}' role.")
            
            # Check if assigner has permission to assign this role
            if self.assigned_by:
                assigner_max_level = self.assigned_by.get_highest_role_level()
                if role.level > assigner_max_level:
                    raise ValidationError(
                        f"You cannot assign the '{role.display_name}' role "
                        f"(level {role.level}) as it's higher than your level ({assigner_max_level})."
                    )
        
        return role
    
    def clean_expires_at(self):
        """Validate expiration date"""
        expires_at = self.cleaned_data.get('expires_at')
        if expires_at and expires_at <= timezone.now():
            raise ValidationError("Expiration date must be in the future.")
        return expires_at


class CustomPasswordChangeForm(PasswordChangeForm):
    """Custom password change form with styled fields"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Style the form fields
        self.fields['old_password'].widget.attrs.update({
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter current password'
        })

        self.fields['new_password1'].widget.attrs.update({
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter new password'
        })

        self.fields['new_password2'].widget.attrs.update({
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Confirm new password'
        })


class UserProfileForm(forms.ModelForm):
    """Form for users to update their own profile information"""

    class Meta:
        model = CustomUser
        fields = ['first_name', 'last_name', 'email', 'phone_number', 'gender']

    first_name = forms.CharField(
        max_length=150,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter first name'
        })
    )

    last_name = forms.CharField(
        max_length=150,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter last name'
        })
    )

    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter email address'
        })
    )

    phone_number = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white',
            'placeholder': 'Enter phone number (optional)'
        })
    )

    gender = forms.ChoiceField(
        choices=[('', 'Select Gender'), ('Male', 'Male'), ('Female', 'Female')],
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-300 text-[#2C3E50] bg-white'
        })
    )

    def clean_email(self):
        """Validate email uniqueness (excluding current user)"""
        email = self.cleaned_data.get('email')
        if email:
            existing = CustomUser.objects.filter(email=email).exclude(pk=self.instance.pk)
            if existing.exists():
                raise ValidationError("A user with this email already exists.")
        return email
