{% extends 'base.html' %} {% load static %} {% load humanize %}
<!-- Title -->
{% block title %}Term Fee Report | {% endblock %}
<!-- Body -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-graduation-cap text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Term Fee Report
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Complete term fee payment history and analysis
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#40657F] to-[#2C3E50] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <a
        href="?export=true"
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group button-slide-in"
      >
        <i
          class="fas fa-download text-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
        ></i>
        <span>Download Excel</span>
      </a>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 summary-cards-fade-in">
    <!-- Total Students Card -->
    <div
      class="summary-card group relative overflow-hidden bg-gradient-to-br from-[#40657F]/20 via-white to-[#E2F1F9] border-l-4 border-[#40657F] hover:shadow-xl hover:-translate-y-1 transition-all duration-400 p-6 rounded-2xl"
    >
      <div
        class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-[#40657F]/20 to-[#2C3E50]/10 rounded-full -translate-y-8 translate-x-8 group-hover:scale-125 transition-transform duration-700"
      ></div>

      <div class="flex items-center gap-3 mb-4 relative z-10">
        <div
          class="w-10 h-10 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-lg flex items-center justify-center shadow-md"
        >
          <i class="fas fa-users text-white text-lg"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-sm">Total Accounts</h3>
        </div>
      </div>
      <p class="text-2xl font-bold text-[#40657F] font-display counter-animate">
        {{ receipts|length }}
      </p>
    </div>

    <!-- Completed Payments Card -->
    <div
      class="summary-card group relative overflow-hidden bg-gradient-to-br from-[#74C69D]/20 via-white to-[#E2F1F9] border-l-4 border-[#74C69D] hover:shadow-xl hover:-translate-y-1 transition-all duration-400 p-6 rounded-2xl"
    >
      <div
        class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-[#74C69D]/20 to-[#5fb085]/10 rounded-full -translate-y-8 translate-x-8 group-hover:scale-125 transition-transform duration-700"
      ></div>

      <div class="flex items-center gap-3 mb-4 relative z-10">
        <div
          class="w-10 h-10 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-lg flex items-center justify-center shadow-md"
        >
          <i class="fas fa-check-circle text-white text-lg"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-sm">Completed</h3>
        </div>
      </div>
      <p class="text-2xl font-bold text-[#74C69D] font-display counter-animate">
        {{ accounts_paid }}
      </p>
    </div>

    <!-- Pending Payments Card -->
    <div
      class="summary-card group relative overflow-hidden bg-gradient-to-br from-[#F28C8C]/20 via-white to-[#E2F1F9] border-l-4 border-[#F28C8C] hover:shadow-xl hover:-translate-y-1 transition-all duration-400 p-6 rounded-2xl"
    >
      <div
        class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-[#F28C8C]/20 to-[#e07575]/10 rounded-full -translate-y-8 translate-x-8 group-hover:scale-125 transition-transform duration-700"
      ></div>

      <div class="flex items-center gap-3 mb-4 relative z-10">
        <div
          class="w-10 h-10 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-lg flex items-center justify-center shadow-md"
        >
          <i class="fas fa-clock text-white text-lg"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-sm">Pending</h3>
        </div>
      </div>
      <p class="text-2xl font-bold text-[#F28C8C] font-display counter-animate">
        {{ accounts_pending }}
      </p>
    </div>

    <!-- Total Amount Card -->
    <div
      class="summary-card group relative overflow-hidden bg-gradient-to-br from-[#7AB2D3]/20 via-white to-[#E2F1F9] border-l-4 border-[#7AB2D3] hover:shadow-xl hover:-translate-y-1 transition-all duration-400 p-6 rounded-2xl"
    >
      <div
        class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 rounded-full -translate-y-8 translate-x-8 group-hover:scale-125 transition-transform duration-700"
      ></div>

      <div class="flex items-center gap-3 mb-4 relative z-10">
        <div
          class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center shadow-md"
        >
          <i class="fas fa-money-bill-wave text-white text-lg"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-sm">Total Paid</h3>
        </div>
      </div>
      <p class="text-lg font-bold text-[#7AB2D3] font-display counter-animate">
        MWK {{ total_amount|intcomma }}
      </p>
    </div>
  </div>

  <!-- Fee Payment Table -->
  <div class="card-modern p-8 table-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg section-icon-float"
      >
        <i class="fas fa-table text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Term Payment Details
        </h3>
        <p class="text-[#40657F] text-sm">
          Complete student payment records for the term
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div
      class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg"
    >
      <table class="min-w-full bg-white">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-user text-[#40657F]"></i>
                Student Name
              </div>
            </th>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-tag text-[#74C69D]"></i>
                Category
              </div>
            </th>
            <th
              class="px-6 py-4 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-end gap-2">
                <i class="fas fa-money-bill text-[#7AB2D3]"></i>
                Amount Paid
              </div>
            </th>
            <th
              class="px-6 py-4 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-end gap-2">
                <i class="fas fa-balance-scale text-[#F28C8C]"></i>
                Balance
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-check-circle text-[#74C69D]"></i>
                Status
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#B9D8EB]/30">
          {% for receipt in receipts %}
          <tr
            class="payment-row hover:bg-[#E2F1F9]/50 transition-colors duration-200"
          >
            <td class="px-6 py-4 text-[#2C3E50] font-medium">
              <div class="flex items-center gap-3">
                <div
                  class="w-8 h-8 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-full flex items-center justify-center text-white text-xs font-bold"
                >
                  {{ receipt.student.name|first }}
                </div>
                {{ receipt.student.name }}
              </div>
            </td>
            <td class="px-6 py-4 text-[#40657F] font-medium">
              <span
                class="bg-[#74C69D]/20 text-[#74C69D] px-3 py-1 rounded-full text-sm font-semibold border border-[#74C69D]/30"
              >
                {{ receipt.category.name }}
              </span>
            </td>
            <td class="px-6 py-4 text-right font-bold text-[#7AB2D3] text-lg">
              MWK {{ receipt.amount|intcomma }}
            </td>
            <td class="px-6 py-4 text-right font-bold text-[#F28C8C] text-lg">
              MWK {{ receipt.current_balance|intcomma }}
            </td>
            <td class="px-6 py-4 text-center">
              {% if receipt.is_paid %}
              <span
                class="inline-flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full text-sm font-bold border border-[#74C69D]/30"
              >
                <i class="fas fa-check-circle"></i>
                Completed
              </span>
              {% else %}
              <span
                class="inline-flex items-center gap-2 bg-[#F28C8C]/20 text-[#F28C8C] px-4 py-2 rounded-full text-sm font-bold border border-[#F28C8C]/30"
              >
                <i class="fas fa-clock"></i>
                Incomplete
              </span>
              {% endif %}
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .button-slide-in {
    opacity: 0;
    transform: translateX(50px);
    animation: buttonSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Summary Cards Animations */
  .summary-cards-fade-in {
    opacity: 0;
    animation: summaryCardsFadeIn 0.8s ease-out 1s forwards;
  }

  .summary-card {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    animation: summaryCardSlideUp 0.6s ease-out forwards;
  }

  .summary-card:nth-child(1) {
    animation-delay: 1.1s;
  }
  .summary-card:nth-child(2) {
    animation-delay: 1.2s;
  }
  .summary-card:nth-child(3) {
    animation-delay: 1.3s;
  }
  .summary-card:nth-child(4) {
    animation-delay: 1.4s;
  }

  .counter-animate {
    opacity: 0;
    animation: counterFadeIn 1s ease-out 1.6s forwards;
  }

  /* Table Animations */
  .table-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: tableFadeIn 0.8s ease-out 1.5s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 4s ease-in-out infinite;
  }

  .payment-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: paymentRowSlideIn 0.4s ease-out forwards;
  }

  .payment-row:nth-child(1) {
    animation-delay: 1.8s;
  }
  .payment-row:nth-child(2) {
    animation-delay: 1.9s;
  }
  .payment-row:nth-child(3) {
    animation-delay: 2s;
  }
  .payment-row:nth-child(4) {
    animation-delay: 2.1s;
  }
  .payment-row:nth-child(5) {
    animation-delay: 2.2s;
  }
  .payment-row:nth-child(6) {
    animation-delay: 2.3s;
  }
  .payment-row:nth-child(7) {
    animation-delay: 2.4s;
  }
  .payment-row:nth-child(8) {
    animation-delay: 2.5s;
  }
  .payment-row:nth-child(9) {
    animation-delay: 2.6s;
  }
  .payment-row:nth-child(10) {
    animation-delay: 2.7s;
  }
  .payment-row:nth-child(11) {
    animation-delay: 2.8s;
  }
  .payment-row:nth-child(12) {
    animation-delay: 2.9s;
  }
  .payment-row:nth-child(13) {
    animation-delay: 3s;
  }
  .payment-row:nth-child(14) {
    animation-delay: 3.1s;
  }
  .payment-row:nth-child(15) {
    animation-delay: 3.2s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes buttonSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes summaryCardsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes summaryCardSlideUp {
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes counterFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes tableFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes sectionIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(3deg);
    }
  }

  @keyframes paymentRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Hover Enhancements */
  .summary-card:hover .counter-animate {
    animation: counterPulse 0.6s ease-in-out;
    opacity: 1 !important;
  }

  @keyframes counterPulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 1;
    }
  }

  /* Status Badge Animations */
  .payment-row:hover .bg-\[#74C69D\]\/20 {
    background-color: rgba(116, 198, 157, 0.3);
    transform: scale(1.05);
    transition: all 0.3s ease;
  }

  .payment-row:hover .bg-\[#F28C8C\]\/20 {
    background-color: rgba(242, 140, 140, 0.3);
    transform: scale(1.05);
    transition: all 0.3s ease;
  }

  /* Term-specific Enhancements */
  .graduation-cap-spin {
    animation: graduationCapSpin 6s linear infinite;
  }

  @keyframes graduationCapSpin {
    0% {
      transform: rotate(0deg);
    }
    25% {
      transform: rotate(5deg);
    }
    50% {
      transform: rotate(0deg);
    }
    75% {
      transform: rotate(-5deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .summary-card {
      animation-delay: 0.8s;
    }

    .summary-card:nth-child(n) {
      animation-delay: calc(0.8s + 0.1s * var(--card-index, 1));
    }

    .payment-row {
      animation-delay: 1.2s;
    }

    .payment-row:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--row-index, 1));
    }
  }

  /* Term Report Specific Styling */
  .term-highlight {
    background: linear-gradient(45deg, #40657f, #2c3e50);
    background-size: 200% 200%;
    animation: termGradientShift 4s ease-in-out infinite;
  }

  @keyframes termGradientShift {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
</style>

{% endblock %}
