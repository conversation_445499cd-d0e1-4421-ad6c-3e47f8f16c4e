from django.contrib import admin

from finances.fee_management.models import FeesWaiver, Receipt, FeeAccount, FeeCategory
# from finances.income import Income  # REMOVED - deprecated model

# from dateutil.relativedelta import relativedelta  # No longer needed
from django.utils.html import format_html
# Register your models here.


admin.site.register(FeesWaiver)


@admin.action(description='Seed Journal Entries for selected Receipts')
def seed_journal_entries(modeladmin, request, queryset):
    created_count = 0

    for receipt in queryset:
        receipt.save()
        created_count += 1

    modeladmin.message_user(
        request, f"{created_count} journal entries created.")


@admin.register(Receipt)
class ReceiptAdmin(admin.ModelAdmin):
    search_fields = ["student__name", "receipt_number"]
    list_display = ["receipt_number", "student",
                    "amount_paid", "date", "amount_paid"]
    list_filter = ["fee_account__category", "fee_account__term"]
    actions = [seed_journal_entries]


@admin.register(FeeAccount)
class FeeAccountAdmin(admin.ModelAdmin):
    search_fields = ["student__name", "term__term_name"]
    list_display = [
        "student", "term", "category", "total_due",
        "amount", "colored_status"
    ]
    list_filter = ["term", "category", ]

    def colored_status(self, obj):
        if obj.is_fully_paid:
            return format_html('<span style="color: green;">{}</span>', "Paid")
        elif obj.amount > 0:
            return format_html('<span style="color: orange;">{}</span>', "Pending")
        else:
            return format_html('<span style="color: red;">{}</span>', "Not Paid")
    colored_status.short_description = "Status"


@admin.action(description='DEPRECATED: Income seeding (use Budget/Ledger system)')
def seed_income(modeladmin, request, queryset):
    """
    DEPRECATED: Income model has been removed.
    Income tracking is now handled through the modern Budget/Ledger system.

    Use the Budget/BudgetLine system for income planning and tracking instead.
    """
    modeladmin.message_user(
        request,
        "DEPRECATED: Income model has been removed. Use Budget/BudgetLine system for income tracking.",
        level='warning'
    )


@admin.register(FeeCategory)
class FeeCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'amount')
    actions = [seed_income]
