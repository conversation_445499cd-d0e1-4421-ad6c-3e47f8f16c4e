{% extends 'base.html' %} {% load humanize %} {% load static %}
<!--  -->
{% block title %}{{ student.name }} - Student Details | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-lg icon-float"
      >
        <i class="fas fa-user text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in"
        >
          Student Details
        </h1>
        <div
          class="w-20 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full accent-line-grow"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Student Details</span>
    </nav>
  </div>

  <!-- Main Content -->
  <section class="card-modern p-8 main-content-fade-in">
    <div class="flex flex-col gap-8 w-full">
      <!-- Student Header -->
      <div class="flex flex-col gap-6 w-full">
        <div
          class="flex flex-col md:flex-row justify-between items-center w-full gap-6 py-4 student-header-slide-in"
        >
          <div class="flex items-center gap-4">
            <div
              class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg"
            >
              {{ student.name|first }}
            </div>
            <div>
              <h1
                class="font-bold text-2xl md:text-3xl text-[#2C3E50] font-display"
              >
                {{ student.name }}
              </h1>
              <p class="text-[#40657F] font-medium">
                Student ID: {{ student.student_id }}
              </p>
            </div>
          </div>
          <div class="flex gap-3 action-buttons-slide-in">
            <a
              href="{% url 'students:edit_student' student.student_id %}"
              class="inline-flex items-center gap-2 bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white font-bold py-3 px-6 rounded-xl hover:from-[#2C3E50] hover:to-[#40657F] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl group"
            >
              <i
                class="fas fa-user-edit group-hover:scale-110 transition-transform duration-300"
              ></i>
              <span>Edit Student</span>
            </a>
            <a
              href="{% url 'finances:waive_fees' student.student_id %}"
              class="inline-flex items-center gap-2 bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-bold py-3 px-6 rounded-xl hover:from-[#e07575] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl group"
            >
              <i
                class="fas fa-hand-holding-usd group-hover:scale-110 transition-transform duration-300"
              ></i>
              <span>Waive Fees</span>
            </a>
            <a
              href="{% url 'finances:add_receipt' student.student_id %}"
              class="inline-flex items-center gap-2 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl group"
            >
              <i
                class="fas fa-plus group-hover:scale-110 group-hover:rotate-90 transition-all duration-300"
              ></i>
              <span>Add Payment</span>
            </a>
          </div>
        </div>

        <div
          class="w-full h-px bg-gradient-to-r from-[#B9D8EB] via-[#7AB2D3] to-[#B9D8EB] divider-grow"
        ></div>

        <!-- Fee Account Tabs -->
        <div
          class="flex flex-col md:flex-row items-stretch justify-center gap-4 w-full fee-tabs-fade-in"
        >
          {% for fee_account in fee_accounts %}
          <button
            class="fee-btn group relative overflow-hidden border-2 {% if forloop.first %}border-[#7AB2D3] bg-[#7AB2D3]/10 active{% else %}border-[#B9D8EB] bg-white{% endif %} py-4 px-6 rounded-xl font-semibold w-full transition-all duration-300 focus:outline-none focus:border-[#7AB2D3] hover:shadow-lg hover:-translate-y-1"
            id="feeaccount-{{forloop.counter}}"
            type="button"
            onclick="showDetails({{forloop.counter}})"
            {%
            if
            forloop.first
            %}autofocus{%
            endif
            %}
          >
            <!-- Background Pattern -->
            <div
              class="absolute top-0 right-0 w-12 h-12 bg-gradient-to-br from-[#7AB2D3]/20 to-[#40657F]/10 rounded-full -translate-y-6 translate-x-6 group-hover:scale-125 transition-transform duration-500"
            ></div>

            <div class="relative z-10">
              {% if fee_account.month is None %}
              <div class="flex items-center justify-center gap-2 mb-2">
                <i
                  class="fas fa-graduation-cap transition-colors duration-300"
                ></i>
                <span class="text-lg font-bold transition-colors duration-300"
                  >{{ fee_account.category }}</span
                >
              </div>
              <span class="text-sm font-medium transition-colors duration-300"
                >{{ fee_account.term.term_name }}</span
              >
              {% else %}
              <div class="flex items-center justify-center gap-2 mb-2">
                <i class="fas fa-calendar transition-colors duration-300"></i>
                <span class="text-lg font-bold transition-colors duration-300"
                  >{{ fee_account.category}}</span
                >
              </div>
              <span class="text-sm font-medium transition-colors duration-300"
                >{{ fee_account.month|date:"F"}}</span
              >
              {% endif %}
            </div>
          </button>
          {% endfor %}
        </div>
      </div>

      <!-- Fee Details Sections -->
      {% for fee_account in fee_accounts %}
      <div
        class="fee-details flex flex-col lg:flex-row gap-8 {% if forloop.first %}flex{% else %}hidden{% endif %} transition-all duration-500 fee-details-fade-in"
        id="details-{{forloop.counter}}"
      >
        <!-- Student Photo -->
        <div class="flex justify-center items-start lg:items-center">
          <div class="relative group">
            <img
              width="200"
              class="rounded-2xl shadow-xl border-4 border-[#E2F1F9] group-hover:shadow-2xl transition-all duration-300"
              src="{% static 'img/student1.jpg' %}"
              alt="{{ student.name }}"
            />
            <div
              class="absolute inset-0 bg-gradient-to-t from-[#40657F]/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            ></div>
          </div>
        </div>

        <!-- Student Information -->
        <div
          class="flex-1 bg-gradient-to-br from-[#F7FAFC] to-[#E2F1F9] rounded-2xl p-8 border border-[#B9D8EB]/50"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Personal Information -->
            <div class="space-y-4">
              <h3
                class="text-xl font-bold text-[#2C3E50] mb-4 flex items-center gap-2"
              >
                <i class="fas fa-user text-[#7AB2D3]"></i>
                Personal Information
              </h3>

              <div class="space-y-3">
                <div class="flex items-center gap-3">
                  <span class="info-label w-20">ID:</span>
                  <span class="info-value bg-[#7AB2D3]/20 px-3 py-1 rounded-lg"
                    >{{ student.student_id}}</span
                  >
                </div>
                <div class="flex items-center gap-3">
                  <span class="info-label w-20">Name:</span>
                  <span class="info-value">{{ student.name }}</span>
                </div>
                <div class="flex items-center gap-3">
                  <span class="info-label w-20">Gender:</span>
                  <span
                    class="info-value bg-{% if student.gender == 'Male' %}[#7AB2D3]{% else %}[#F28C8C]{% endif %}/20 px-3 py-1 rounded-lg"
                    >{{ student.gender}}</span
                  >
                </div>
                <div class="flex items-center gap-3">
                  <span class="info-label w-20">Class:</span>
                  <span class="info-value bg-[#74C69D]/20 px-3 py-1 rounded-lg"
                    >{{ student.level}}</span
                  >
                </div>
              </div>
            </div>

            <!-- Fee Information -->
            <div class="space-y-4">
              <h3
                class="text-xl font-bold text-[#2C3E50] mb-4 flex items-center gap-2"
              >
                <i class="fas fa-money-bill-wave text-[#74C69D]"></i>
                Fee Information
              </h3>

              <div class="space-y-4">
                <div
                  class="bg-white rounded-xl p-4 border border-[#B9D8EB]/30 shadow-sm"
                >
                  <div class="flex justify-between items-center mb-2">
                    <span class="fee-card-label">
                      {% if "Tuition" in fee_account.category.name %}
                      <i class="fas fa-graduation-cap mr-2"></i>
                      Tuition Fees
                      <!--  -->
                      {% elif "Food" in fee_account.category.name %}
                      <i class="fas fa-utensils mr-2"></i>Food Fees {% else %}
                      <i class="fas fa-tag mr-2"></i>
                      {{ fee_account.category.name}} {% endif %}
                    </span>
                  </div>
                  <p class="fee-amount font-display">
                    MWK {{fee_account.total_due|intcomma}}
                  </p>
                </div>

                <div
                  class="bg-white rounded-xl p-4 border border-[#B9D8EB]/30 shadow-sm"
                >
                  <div class="flex justify-between items-center mb-2">
                    <span class="fee-card-label">
                      <i class="fas fa-check-circle mr-2 text-[#74C69D]"></i
                      >Amount Paid
                    </span>
                  </div>
                  <p class="fee-amount paid font-display">
                    MWK {{ fee_account.amount|intcomma }}
                  </p>
                </div>

                {% comment %}
                Overpayments are handled at the group level through receipt distribution,
                individual accounts should not show overpayments
                {% endcomment %}

                <div
                  class="bg-gradient-to-r from-[#F28C8C]/10 to-[#e07575]/10 rounded-xl p-4 border border-[#F28C8C]/30 shadow-sm"
                >
                  <div class="flex justify-between items-center mb-2">
                    <span class="fee-card-label">
                      <i class="fas fa-balance-scale mr-2 text-[#F28C8C]"></i
                      >Outstanding Balance
                    </span>
                  </div>
                  <p class="fee-amount balance font-display">
                    MWK {{ fee_account.current_balance|intcomma }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- Payment History Section -->
    <div
      class="w-full h-px bg-gradient-to-r from-[#B9D8EB] via-[#7AB2D3] to-[#B9D8EB] divider-grow-2"
    ></div>

    <div class="w-full payment-history-fade-in">
      <div class="flex items-center gap-4 mb-6">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg section-icon-float"
        >
          <i class="fas fa-receipt text-white text-lg"></i>
        </div>
        <div>
          <h3 class="section-header font-display">Payment History</h3>
          <p class="section-description">
            Complete record of all payments made
          </p>
        </div>
        <div
          class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
        ></div>
      </div>

      <!-- Payment Search Section -->
      <div class="card-modern p-6 mb-6 search-fade-in">
        <form method="GET" class="flex flex-col lg:flex-row gap-4">
          <div class="flex-1">
            <div class="relative">
              <input
                type="text"
                name="payment_search"
                value="{{ request.GET.payment_search }}"
                placeholder="Search payments by receipt number, category, or amount..."
                class="w-full px-4 py-3 pl-12 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]/60"
              />
              <i
                class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#7AB2D3]"
              ></i>
            </div>
          </div>
          <div class="flex gap-3">
            <button
              type="submit"
              class="px-6 py-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] transition-all duration-200 font-semibold"
            >
              <i class="fas fa-search mr-2"></i>
              Search
            </button>
            {% if request.GET.payment_search %}
            <a
              href="{% url 'students:student_details' student.student_id %}"
              class="px-6 py-3 bg-[#B9D8EB] text-[#40657F] rounded-xl hover:bg-[#E2F1F9] transition-all duration-200 font-semibold"
            >
              <i class="fas fa-times mr-2"></i>
              Clear
            </a>
            {% endif %}
          </div>
        </form>
      </div>

      <div
        class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg"
      >
        <table class="min-w-full bg-white">
          <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
            <tr>
              <th
                class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center gap-2">
                  <i class="fas fa-hashtag text-[#7AB2D3]"></i>
                  Receipt No
                </div>
              </th>
              <th
                class="px-6 py-4 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center justify-end gap-2">
                  <i class="fas fa-money-bill text-[#74C69D]"></i>
                  Amount Paid
                </div>
              </th>
              <th
                class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center gap-2">
                  <i class="fas fa-tag text-[#40657F]"></i>
                  Category
                </div>
              </th>
              <th
                class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center justify-center gap-2">
                  <i class="fas fa-calendar text-[#F28C8C]"></i>
                  Date
                </div>
              </th>
              <th
                class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
              >
                <div class="flex items-center justify-center gap-2">
                  <i class="fas fa-cog text-[#7AB2D3]"></i>
                  Actions
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#B9D8EB]/30">
            {% for receipt in receipt %}
            <tr
              class="payment-row hover:bg-[#E2F1F9]/50 transition-colors duration-200"
            >
              <td class="px-6 py-4">
                <a
                  href="{% url 'finances:generate_pdf' receipt.student.student_id receipt.receipt_number %}"
                  class="receipt-link inline-flex items-center gap-2 hover:underline transition-colors duration-200"
                >
                  <i class="fas fa-file-pdf"></i>
                  {{ receipt.receipt_number }}
                </a>
              </td>
              <td class="px-6 py-4 text-right">
                <span class="payment-amount font-display"
                  >MWK {{ receipt.amount_paid|intcomma }}</span
                >
              </td>
              <td class="px-6 py-4">
                <span class="category-badge px-3 py-1 rounded-full text-sm">
                  {{ receipt.fee_account.category }}
                </span>
              </td>
              <td class="px-6 py-4 text-center">
                <div class="flex items-center justify-center gap-2">
                  <i class="fas fa-calendar-day text-[#F28C8C]"></i>
                  <span class="date-display">{{ receipt.date }}</span>
                </div>
              </td>
              <td class="px-6 py-4 text-center">
                {% if not receipt.is_reversed %}
                <a
                  href="{% url 'finances:reverse_receipt' receipt.slug %}"
                  class="inline-flex items-center gap-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-bold py-1 px-3 rounded-lg hover:from-[#e07575] hover:to-[#F28C8C] focus:ring-2 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 text-xs group"
                  onclick="return confirm('Are you sure you want to reverse this receipt? This action cannot be undone.')"
                  title="Reverse Receipt"
                >
                  <i
                    class="fas fa-undo group-hover:rotate-180 transition-all duration-300"
                  ></i>
                  <span>Reverse</span>
                </a>
                {% else %}
                <span
                  class="inline-flex items-center gap-1 bg-[#F28C8C]/20 text-[#F28C8C] py-1 px-3 rounded-lg text-xs font-bold"
                >
                  <i class="fas fa-undo"></i>
                  <span>Reversed</span>
                </span>
                {% endif %}
              </td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="5" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center gap-4">
                  <div
                    class="empty-state-icon w-16 h-16 rounded-full flex items-center justify-center"
                  >
                    <i class="fas fa-receipt text-[#B9D8EB] text-2xl"></i>
                  </div>
                  <div>
                    <h4 class="empty-state-title mb-2">No Payment History</h4>
                    <p class="empty-state-description">
                      No payments have been recorded for this student yet.
                    </p>
                  </div>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </section>
</section>
{% endblock %}

<!--  -->
{% block style %}
<style>
  /* Header Animations */
  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: breadcrumbSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Main Content Animations */
  .main-content-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: mainContentFadeIn 0.8s ease-out 0.8s forwards;
  }

  .student-header-slide-in {
    opacity: 0;
    transform: translateX(-30px);
    animation: studentHeaderSlideIn 0.8s ease-out 1s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 1.2s forwards;
  }

  .divider-grow {
    width: 0;
    animation: dividerGrow 0.8s ease-out 1.4s forwards;
  }

  .divider-grow-2 {
    width: 0;
    animation: dividerGrow 0.8s ease-out 2.2s forwards;
  }

  .fee-tabs-fade-in {
    opacity: 0;
    animation: feeTabsFadeIn 0.8s ease-out 1.6s forwards;
  }

  .fee-details-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: feeDetailsFadeIn 0.8s ease-out 1.8s forwards;
  }

  .payment-history-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: paymentHistoryFadeIn 0.8s ease-out 2.4s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 4s ease-in-out infinite;
  }

  .payment-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: paymentRowSlideIn 0.4s ease-out forwards;
  }

  .payment-row:nth-child(1) {
    animation-delay: 2.6s;
  }
  .payment-row:nth-child(2) {
    animation-delay: 2.7s;
  }
  .payment-row:nth-child(3) {
    animation-delay: 2.8s;
  }
  .payment-row:nth-child(4) {
    animation-delay: 2.9s;
  }
  .payment-row:nth-child(5) {
    animation-delay: 3s;
  }

  /* Keyframe Definitions */
  @keyframes breadcrumbSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes mainContentFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes studentHeaderSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes dividerGrow {
    to {
      width: 100%;
    }
  }

  @keyframes feeTabsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes feeDetailsFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes paymentHistoryFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes sectionIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(3deg);
    }
  }

  @keyframes paymentRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Fee Tab Enhancements */
  .fee-btn {
    transition: all 0.3s ease;
  }

  .fee-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(122, 178, 211, 0.3);
  }

  .fee-btn.active {
    background: linear-gradient(135deg, #7ab2d3 0%, #40657f 100%);
    color: white !important;
    border-color: #7ab2d3;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(122, 178, 211, 0.4);
  }

  .fee-btn.active .text-lg,
  .fee-btn.active .text-sm {
    color: white !important;
  }

  .fee-btn.active i {
    color: white !important;
  }

  /* Enhanced text contrast and readability */
  .fee-btn:not(.active) .text-lg {
    color: #2c3e50 !important;
    font-weight: 700;
  }

  .fee-btn:not(.active) .text-sm {
    color: #40657f !important;
    font-weight: 500;
  }

  .fee-btn:not(.active) i {
    color: #7ab2d3 !important;
  }

  /* Improved hover states for better UX */
  .fee-btn:hover:not(.active) {
    background: linear-gradient(135deg, #e2f1f9 0%, #b9d8eb 50%);
    border-color: #7ab2d3;
  }

  .fee-btn:hover:not(.active) .text-lg {
    color: #2c3e50 !important;
  }

  .fee-btn:hover:not(.active) .text-sm {
    color: #40657f !important;
  }

  /* Enhanced empty state styling */
  .empty-state-icon {
    background: linear-gradient(135deg, #b9d8eb 0%, #e2f1f9 100%);
  }

  .empty-state-title {
    color: #2c3e50 !important;
    font-weight: 600;
  }

  .empty-state-description {
    color: #40657f !important;
    font-weight: 400;
  }

  /* Improved badge contrast */
  .category-badge {
    background: rgba(64, 101, 127, 0.15) !important;
    color: #2c3e50 !important;
    border: 1px solid rgba(64, 101, 127, 0.3) !important;
    font-weight: 600;
  }

  /* Enhanced payment amount styling */
  .payment-amount {
    color: #74c69d !important;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(116, 198, 157, 0.1);
  }

  /* Better link contrast */
  .receipt-link {
    color: #40657f !important;
    font-weight: 700;
  }

  .receipt-link:hover {
    color: #2c3e50 !important;
    text-decoration: underline;
  }

  /* Improved date styling */
  .date-display {
    color: #2c3e50 !important;
    font-weight: 500;
  }

  /* Enhanced section headers */
  .section-header {
    color: #2c3e50 !important;
    font-weight: 700;
  }

  .section-description {
    color: #40657f !important;
    font-weight: 500;
  }

  /* Better personal info contrast */
  .info-label {
    color: #40657f !important;
    font-weight: 600;
  }

  .info-value {
    color: #2c3e50 !important;
    font-weight: 700;
  }

  /* Enhanced fee information cards */
  .fee-card-label {
    color: #40657f !important;
    font-weight: 600;
  }

  .fee-amount {
    color: #2c3e50 !important;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(44, 62, 80, 0.1);
  }

  .fee-amount.paid {
    color: #74c69d !important;
  }

  .fee-amount.carry-over {
    color: #7ab2d3 !important;
  }

  .fee-amount.balance {
    color: #f28c8c !important;
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .payment-row {
      animation-delay: 2s;
    }

    .payment-row:nth-child(n) {
      animation-delay: calc(2s + 0.1s * var(--row-index, 1));
    }
  }
</style>
{% endblock %} {% block scripts %}
<script>
  function showDetails(index) {
    // Hide all fee detail sections with animation
    document.querySelectorAll(".fee-details").forEach((element) => {
      element.style.opacity = "0";
      element.style.transform = "translateY(20px)";
      setTimeout(() => {
        element.classList.add("hidden");
        element.classList.remove("flex");
      }, 300);
    });

    // Update button states
    document.querySelectorAll(".fee-btn").forEach((element) => {
      element.classList.remove("border-[#7AB2D3]", "bg-[#7AB2D3]/10", "active");
      element.classList.add("border-[#B9D8EB]", "bg-white");
    });

    // Show the selected fee detail section with animation
    setTimeout(() => {
      const selectedDetail = document.getElementById(`details-${index}`);
      const selectedButton = document.getElementById(`feeaccount-${index}`);

      selectedDetail.classList.remove("hidden");
      selectedDetail.classList.add("flex");

      selectedButton.classList.remove("border-[#B9D8EB]", "bg-white");
      selectedButton.classList.add(
        "border-[#7AB2D3]",
        "bg-[#7AB2D3]/10",
        "active"
      );

      // Animate in
      setTimeout(() => {
        selectedDetail.style.opacity = "1";
        selectedDetail.style.transform = "translateY(0)";
      }, 50);
    }, 300);
  }

  // Initialize first tab as active
  document.addEventListener("DOMContentLoaded", function () {
    const firstButton = document.getElementById("feeaccount-1");
    if (firstButton) {
      firstButton.classList.add("active");
    }
  });
</script>
{% endblock %}
