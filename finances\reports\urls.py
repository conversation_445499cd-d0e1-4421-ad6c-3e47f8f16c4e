from django.urls import path

from . import views

urlpatterns = [
    path('fee_reports', views.fee_reports, name="fee_reports"),
    path('monthly-reports/<slug:month>',
         views.monthly_reports, name='monthly_reports'),
    path('fee-reports/<slug:month>',
         views.fee_monthly_report, name='fee_monthly_report'),
    path('fee-term-reports',
         views.fee_term_report, name='fee_term_report'),
    path('expenditure-statement',
         views.expenditure_statement, name='expenditure_statement'),
    path('expenditure-report/',
         views.generate_expenditure_statement, name="expenditure_report"),
    path('expenditure-report/<slug:start_date>/<slug:end_date>/',
         views.generate_expenditure_statement, name="expenditure_pdf"),
    path('income-statement', views.income_statement, name="income_statement"),
    path('generate-income-statement', views.generate_income_statement,
         name="generate_income_statement"),
]
