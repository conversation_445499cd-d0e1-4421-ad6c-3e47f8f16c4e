# Backup & Recovery Guide

## 🛡️ Overview

This guide provides comprehensive instructions for implementing robust backup and recovery procedures for the Receipt Generator system, ensuring data protection and business continuity.

## 🗄️ Database Backup Strategies

### PostgreSQL Backup Procedures

#### Automated Daily Backups
```bash
#!/bin/bash
# backup_database.sh - Daily database backup script

# Configuration
DB_NAME="receipt_gen"
DB_USER="postgres"
BACKUP_DIR="/var/backups/receipt_gen"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/db_backup_$DATE.sql"
LOG_FILE="/var/log/receipt_gen_backup.log"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> $LOG_FILE
}

log_message "Starting database backup"

# Create database dump
pg_dump -U $DB_USER -h localhost $DB_NAME > $BACKUP_FILE

if [ $? -eq 0 ]; then
    log_message "Database backup completed successfully: $BACKUP_FILE"
    
    # Compress the backup
    gzip $BACKUP_FILE
    log_message "Backup compressed: $BACKUP_FILE.gz"
    
    # Remove backups older than 30 days
    find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete
    log_message "Old backups cleaned up"
    
    # Upload to cloud storage (optional)
    # aws s3 cp $BACKUP_FILE.gz s3://your-backup-bucket/database/
    
else
    log_message "ERROR: Database backup failed"
    exit 1
fi

log_message "Backup process completed"
```

#### Custom Backup with Schema and Data Separation
```bash
#!/bin/bash
# advanced_backup.sh - Separate schema and data backups

DB_NAME="receipt_gen"
DB_USER="postgres"
BACKUP_DIR="/var/backups/receipt_gen"
DATE=$(date +%Y%m%d_%H%M%S)

# Schema-only backup
pg_dump -U $DB_USER -h localhost --schema-only $DB_NAME > "$BACKUP_DIR/schema_$DATE.sql"

# Data-only backup
pg_dump -U $DB_USER -h localhost --data-only $DB_NAME > "$BACKUP_DIR/data_$DATE.sql"

# Full backup with custom format (faster restore)
pg_dump -U $DB_USER -h localhost -Fc $DB_NAME > "$BACKUP_DIR/full_backup_$DATE.dump"

# Backup specific tables (critical data)
pg_dump -U $DB_USER -h localhost -t students_student -t finances_receipt -t finances_feeaccount $DB_NAME > "$BACKUP_DIR/critical_data_$DATE.sql"

echo "Advanced backup completed: $DATE"
```

### SQLite Backup (Development)

#### SQLite Backup Script
```bash
#!/bin/bash
# sqlite_backup.sh - Development database backup

DB_FILE="db.sqlite3"
BACKUP_DIR="backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Create backup copy
cp $DB_FILE "$BACKUP_DIR/db_backup_$DATE.sqlite3"

# Create SQL dump
sqlite3 $DB_FILE .dump > "$BACKUP_DIR/db_dump_$DATE.sql"

# Compress backups
gzip "$BACKUP_DIR/db_backup_$DATE.sqlite3"
gzip "$BACKUP_DIR/db_dump_$DATE.sql"

echo "SQLite backup completed: $DATE"
```

## 📁 File System Backup

### Application Files Backup

#### Complete Application Backup
```bash
#!/bin/bash
# app_backup.sh - Complete application and media files backup

APP_DIR="/var/www/receipt_gen"
MEDIA_DIR="/var/www/receipt_gen/media"
STATIC_DIR="/var/www/receipt_gen/static"
BACKUP_DIR="/var/backups/receipt_gen"
DATE=$(date +%Y%m%d_%H%M%S)
LOG_FILE="/var/log/receipt_gen_backup.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> $LOG_FILE
}

log_message "Starting application backup"

# Create backup directory
mkdir -p $BACKUP_DIR/app_backups

# Backup application code (excluding sensitive files)
tar -czf "$BACKUP_DIR/app_backups/app_code_$DATE.tar.gz" \
    --exclude='*.pyc' \
    --exclude='__pycache__' \
    --exclude='.git' \
    --exclude='venv' \
    --exclude='node_modules' \
    --exclude='.env' \
    --exclude='db.sqlite3' \
    -C $(dirname $APP_DIR) $(basename $APP_DIR)

if [ $? -eq 0 ]; then
    log_message "Application code backup completed"
else
    log_message "ERROR: Application code backup failed"
fi

# Backup media files
if [ -d "$MEDIA_DIR" ]; then
    tar -czf "$BACKUP_DIR/app_backups/media_files_$DATE.tar.gz" -C $(dirname $MEDIA_DIR) $(basename $MEDIA_DIR)
    log_message "Media files backup completed"
fi

# Backup static files
if [ -d "$STATIC_DIR" ]; then
    tar -czf "$BACKUP_DIR/app_backups/static_files_$DATE.tar.gz" -C $(dirname $STATIC_DIR) $(basename $STATIC_DIR)
    log_message "Static files backup completed"
fi

# Clean up old backups (keep 14 days)
find $BACKUP_DIR/app_backups -name "*.tar.gz" -mtime +14 -delete
log_message "Old application backups cleaned up"

log_message "Application backup process completed"
```

### Configuration Files Backup

#### System Configuration Backup
```bash
#!/bin/bash
# config_backup.sh - Backup system configuration files

BACKUP_DIR="/var/backups/receipt_gen/config"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup nginx configuration
if [ -f "/etc/nginx/sites-available/receipt_gen" ]; then
    cp /etc/nginx/sites-available/receipt_gen "$BACKUP_DIR/nginx_config_$DATE"
fi

# Backup systemd service file
if [ -f "/etc/systemd/system/receipt_gen.service" ]; then
    cp /etc/systemd/system/receipt_gen.service "$BACKUP_DIR/systemd_service_$DATE"
fi

# Backup environment file (without sensitive data)
if [ -f "/var/www/receipt_gen/.env" ]; then
    # Create sanitized version
    grep -v -E "(SECRET_KEY|DATABASE_URL|API_KEY)" /var/www/receipt_gen/.env > "$BACKUP_DIR/env_template_$DATE"
fi

# Backup crontab
crontab -l > "$BACKUP_DIR/crontab_$DATE" 2>/dev/null

echo "Configuration backup completed: $DATE"
```

## ☁️ Cloud Storage Integration

### AWS S3 Backup Integration

#### S3 Upload Script
```bash
#!/bin/bash
# s3_backup.sh - Upload backups to AWS S3

BACKUP_DIR="/var/backups/receipt_gen"
S3_BUCKET="your-backup-bucket"
AWS_PROFILE="backup-user"

# Upload database backups
aws s3 sync "$BACKUP_DIR/" "s3://$S3_BUCKET/receipt_gen/" \
    --profile $AWS_PROFILE \
    --exclude "*" \
    --include "*.sql.gz" \
    --include "*.dump" \
    --storage-class STANDARD_IA

# Upload application backups
aws s3 sync "$BACKUP_DIR/app_backups/" "s3://$S3_BUCKET/receipt_gen/app_backups/" \
    --profile $AWS_PROFILE \
    --storage-class STANDARD_IA

echo "Cloud backup upload completed"
```

#### S3 Lifecycle Policy (JSON)
```json
{
    "Rules": [
        {
            "ID": "ReceiptGenBackupLifecycle",
            "Status": "Enabled",
            "Filter": {
                "Prefix": "receipt_gen/"
            },
            "Transitions": [
                {
                    "Days": 30,
                    "StorageClass": "STANDARD_IA"
                },
                {
                    "Days": 90,
                    "StorageClass": "GLACIER"
                },
                {
                    "Days": 365,
                    "StorageClass": "DEEP_ARCHIVE"
                }
            ],
            "Expiration": {
                "Days": 2555
            }
        }
    ]
}
```

## 🔄 Recovery Procedures

### Database Recovery

#### PostgreSQL Recovery
```bash
#!/bin/bash
# restore_database.sh - Restore PostgreSQL database

DB_NAME="receipt_gen"
DB_USER="postgres"
BACKUP_FILE="$1"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    echo "Example: $0 /var/backups/receipt_gen/db_backup_20231201_120000.sql.gz"
    exit 1
fi

echo "WARNING: This will replace the current database!"
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "Recovery cancelled"
    exit 0
fi

# Stop application services
systemctl stop receipt_gen
systemctl stop nginx

# Drop existing database
dropdb -U $DB_USER $DB_NAME

# Create new database
createdb -U $DB_USER $DB_NAME

# Restore from backup
if [[ $BACKUP_FILE == *.gz ]]; then
    gunzip -c $BACKUP_FILE | psql -U $DB_USER $DB_NAME
else
    psql -U $DB_USER $DB_NAME < $BACKUP_FILE
fi

if [ $? -eq 0 ]; then
    echo "Database restored successfully"
    
    # Restart services
    systemctl start receipt_gen
    systemctl start nginx
    
    echo "Services restarted"
else
    echo "ERROR: Database restoration failed"
    exit 1
fi
```

#### Point-in-Time Recovery
```bash
#!/bin/bash
# point_in_time_recovery.sh - Restore to specific point in time

TARGET_TIME="$1"  # Format: 2023-12-01 12:00:00
BACKUP_FILE="$2"

if [ -z "$TARGET_TIME" ] || [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 'YYYY-MM-DD HH:MM:SS' <backup_file>"
    exit 1
fi

echo "Performing point-in-time recovery to: $TARGET_TIME"

# Restore base backup
./restore_database.sh $BACKUP_FILE

# Apply WAL files up to target time
pg_ctl start -D /var/lib/postgresql/data -o "-r $TARGET_TIME"

echo "Point-in-time recovery completed"
```

### Application Recovery

#### Complete Application Restore
```bash
#!/bin/bash
# restore_application.sh - Restore complete application

APP_DIR="/var/www/receipt_gen"
BACKUP_DIR="/var/backups/receipt_gen"
RESTORE_DATE="$1"

if [ -z "$RESTORE_DATE" ]; then
    echo "Usage: $0 <backup_date>"
    echo "Example: $0 20231201_120000"
    exit 1
fi

echo "Restoring application from backup: $RESTORE_DATE"

# Stop services
systemctl stop receipt_gen
systemctl stop nginx

# Backup current application (safety measure)
mv $APP_DIR "${APP_DIR}_backup_$(date +%Y%m%d_%H%M%S)"

# Restore application code
tar -xzf "$BACKUP_DIR/app_backups/app_code_$RESTORE_DATE.tar.gz" -C $(dirname $APP_DIR)

# Restore media files
if [ -f "$BACKUP_DIR/app_backups/media_files_$RESTORE_DATE.tar.gz" ]; then
    tar -xzf "$BACKUP_DIR/app_backups/media_files_$RESTORE_DATE.tar.gz" -C $APP_DIR
fi

# Restore static files
if [ -f "$BACKUP_DIR/app_backups/static_files_$RESTORE_DATE.tar.gz" ]; then
    tar -xzf "$BACKUP_DIR/app_backups/static_files_$RESTORE_DATE.tar.gz" -C $APP_DIR
fi

# Set proper permissions
chown -R www-data:www-data $APP_DIR
chmod -R 755 $APP_DIR

# Restore environment file (manual step required)
echo "IMPORTANT: Restore .env file manually with proper credentials"

# Restart services
systemctl start receipt_gen
systemctl start nginx

echo "Application restoration completed"
echo "Remember to:"
echo "1. Restore .env file with proper credentials"
echo "2. Run database migrations if needed"
echo "3. Collect static files if needed"
```

## 📋 Backup Monitoring and Alerts

### Backup Verification Script

#### Backup Integrity Check
```bash
#!/bin/bash
# verify_backups.sh - Verify backup integrity

BACKUP_DIR="/var/backups/receipt_gen"
LOG_FILE="/var/log/backup_verification.log"
EMAIL_ALERT="<EMAIL>"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> $LOG_FILE
}

send_alert() {
    echo "$1" | mail -s "Backup Alert - Receipt Generator" $EMAIL_ALERT
}

# Check if recent backups exist
RECENT_BACKUP=$(find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime -1 | head -1)

if [ -z "$RECENT_BACKUP" ]; then
    log_message "ERROR: No recent database backup found"
    send_alert "ERROR: No database backup found in the last 24 hours"
    exit 1
fi

# Test backup integrity
gunzip -t $RECENT_BACKUP

if [ $? -eq 0 ]; then
    log_message "Backup integrity check passed: $RECENT_BACKUP"
else
    log_message "ERROR: Backup integrity check failed: $RECENT_BACKUP"
    send_alert "ERROR: Backup file is corrupted: $RECENT_BACKUP"
    exit 1
fi

# Check backup size (should be reasonable)
BACKUP_SIZE=$(stat -c%s $RECENT_BACKUP)
MIN_SIZE=1048576  # 1MB minimum

if [ $BACKUP_SIZE -lt $MIN_SIZE ]; then
    log_message "WARNING: Backup file seems too small: $BACKUP_SIZE bytes"
    send_alert "WARNING: Backup file is suspiciously small: $BACKUP_SIZE bytes"
fi

log_message "Backup verification completed successfully"
```

### Automated Backup Schedule

#### Crontab Configuration
```bash
# /etc/crontab - Backup schedule

# Daily database backup at 2 AM
0 2 * * * root /usr/local/bin/backup_database.sh

# Weekly application backup on Sundays at 3 AM
0 3 * * 0 root /usr/local/bin/app_backup.sh

# Daily backup verification at 6 AM
0 6 * * * root /usr/local/bin/verify_backups.sh

# Monthly configuration backup on 1st day at 4 AM
0 4 1 * * root /usr/local/bin/config_backup.sh

# Daily cloud upload at 5 AM
0 5 * * * root /usr/local/bin/s3_backup.sh
```

## 🚨 Disaster Recovery Plan

### Recovery Time Objectives (RTO) and Recovery Point Objectives (RPO)

#### Service Level Targets
- **RPO (Recovery Point Objective)**: Maximum 24 hours of data loss
- **RTO (Recovery Time Objective)**: Maximum 4 hours to restore service
- **Critical Data**: Student records, financial transactions, academic records
- **Non-Critical Data**: Logs, temporary files, cache data

#### Emergency Recovery Procedures

```bash
#!/bin/bash
# emergency_recovery.sh - Emergency disaster recovery

echo "=== EMERGENCY RECOVERY PROCEDURE ==="
echo "This script will guide you through disaster recovery"

# Step 1: Assess damage
echo "Step 1: Assessing system status..."
systemctl status receipt_gen
systemctl status postgresql
systemctl status nginx

# Step 2: Restore database
echo "Step 2: Database recovery"
read -p "Do you need to restore the database? (y/n): " restore_db

if [ "$restore_db" = "y" ]; then
    echo "Available database backups:"
    ls -la /var/backups/receipt_gen/db_backup_*.sql.gz | tail -5
    read -p "Enter backup filename to restore: " backup_file
    ./restore_database.sh "/var/backups/receipt_gen/$backup_file"
fi

# Step 3: Restore application
echo "Step 3: Application recovery"
read -p "Do you need to restore application files? (y/n): " restore_app

if [ "$restore_app" = "y" ]; then
    echo "Available application backups:"
    ls -la /var/backups/receipt_gen/app_backups/app_code_*.tar.gz | tail -5
    read -p "Enter backup date (YYYYMMDD_HHMMSS): " backup_date
    ./restore_application.sh "$backup_date"
fi

# Step 4: Verify services
echo "Step 4: Verifying services..."
systemctl start postgresql
systemctl start receipt_gen
systemctl start nginx

sleep 10

curl -f http://localhost/ > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ Application is responding"
else
    echo "✗ Application is not responding - manual intervention required"
fi

echo "=== RECOVERY PROCEDURE COMPLETED ==="
echo "Please verify all functionality manually"
```

## 🔧 Best Practices

### Backup Strategy Guidelines
1. **Follow the 3-2-1 rule**: 3 copies of data, 2 different media types, 1 offsite
2. **Test backups regularly** with restoration procedures
3. **Automate backup processes** to ensure consistency
4. **Monitor backup success** and set up alerts for failures
5. **Document recovery procedures** and keep them updated
6. **Encrypt sensitive backups** both in transit and at rest
7. **Implement backup retention policies** to manage storage costs
8. **Train staff** on recovery procedures

### Security Considerations
- Store backup credentials securely
- Encrypt backups containing sensitive data
- Limit access to backup files and systems
- Use secure transfer methods for cloud uploads
- Regularly audit backup access logs
- Implement backup file integrity checking

### Performance Optimization
- Schedule backups during low-usage periods
- Use incremental backups for large datasets
- Compress backups to save storage space
- Use parallel processing for large backup operations
- Monitor backup performance and optimize as needed

This comprehensive backup and recovery guide ensures data protection and business continuity for the Receipt Generator system.
