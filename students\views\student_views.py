from datetime import date

from django.shortcuts import redirect, render
from django.contrib import messages
from django.db.models import Q

from finances.fee_management.models import FeeAccount, Receipt
from students.models import Student
from students.forms import AdmissionForm, ExcelAdmissionForm, EditStudentForm
from accounts.decorators import staff_or_permission_required, require_permission


current_month = date.today()


@staff_or_permission_required('view_students', 'manage_system')
def students(request):
    """
    Enhanced student search and listing view
    Requires view_students permission or staff status
    """
    # Get search query from request
    search_query = request.GET.get('q', '').strip()

    # Base queryset with optimized select_related
    students = Student.objects.filter(
        is_active=True
    ).select_related('level')

    # Apply search filters if search query exists
    if search_query:
        students = students.filter(
            Q(name__icontains=search_query) |
            Q(student_id__icontains=search_query) |
            Q(level__level_name__icontains=search_query)
        )

    # Order results
    students = students.order_by('name')

    context = {
        'students': students,
        'search_query': search_query,
        'total_count': students.count(),
    }
    return render(request, 'students/students.html', context)


@staff_or_permission_required('view_students')
def student_details(request, slug):
    # TODO: return the term=active_term()
    student = Student.objects.get(student_id=slug)
    fee_accounts = FeeAccount.objects.filter(
        student=student, term__is_active=True).order_by('month')

    # Get payment search query
    payment_search = request.GET.get('payment_search', '').strip()

    # Base receipt queryset
    receipt = Receipt.objects.filter(
        student=student, fee_account__term__is_active=True, is_reversed=False
    ).select_related('fee_account', 'fee_account__category')

    # Apply payment search filter if search query exists
    if payment_search:
        receipt = receipt.filter(
            Q(receipt_number__icontains=payment_search) |
            Q(fee_account__category__name__icontains=payment_search) |
            Q(amount_paid__icontains=payment_search)
        )

    # Order results
    receipt = receipt.order_by("-receipt_number", "-date")

    context = {
        'student': student,
        'fee_accounts': fee_accounts,
        'receipt': receipt,
        'payment_search': payment_search,
    }
    return render(request, 'students/student_details.html', context)


@staff_or_permission_required('add_student')
def admission(request):
    form = AdmissionForm()
    if request.method == 'POST':
        form = AdmissionForm(request.POST)
        if form.is_valid():
            student = form.save()
            student.save()
            messages.success(request, f"{student.name} created successfully")
            return redirect('students:admission')

    context = {'form': form}
    return render(request, 'students/admission.html', context)


@staff_or_permission_required('add_student')
def admission_by_excel(request):
    form = ExcelAdmissionForm()
    if request.method == 'POST':
        form = ExcelAdmissionForm(request.POST, request.FILES)
        if form.is_valid():
            form.save(request, commit=True)
            return redirect('students:admission')

    context = {'form': form}
    return render(request, 'students/admission_by_excel.html', context)


@staff_or_permission_required('edit_student')
def edit_student(request, slug):
    """Edit student details including activation/deactivation"""
    student = Student.objects.get(student_id=slug)
    original_active_status = student.is_active

    if request.method == 'POST':
        form = EditStudentForm(request.POST, instance=student)
        if form.is_valid():
            updated_student = form.save()

            # Check if student was deactivated
            if original_active_status and not updated_student.is_active:
                # Student was deactivated - remove current term fee accounts
                from finances.fee_management.models.fee_account import FeeAccount
                from students.models import Term

                current_term = Term.objects.filter(is_active=True).first()
                if current_term:
                    fee_accounts_removed = FeeAccount.objects.filter(
                        student=updated_student,
                        term=current_term
                    ).count()

                    FeeAccount.objects.filter(
                        student=updated_student,
                        term=current_term
                    ).delete()

                    messages.warning(
                        request,
                        f"{updated_student.name} has been deactivated. {fee_accounts_removed} fee account(s) for the current term have been removed."
                    )
                else:
                    messages.warning(
                        request,
                        f"{updated_student.name} has been deactivated."
                    )
            elif not original_active_status and updated_student.is_active:
                # Student was reactivated
                messages.success(
                    request,
                    f"{updated_student.name} has been reactivated and can now access academy services."
                )
            else:
                # Just regular update
                messages.success(
                    request,
                    f"{updated_student.name}'s information has been updated successfully."
                )

            return redirect('students:student_details', slug=updated_student.student_id)
    else:
        form = EditStudentForm(instance=student)

    context = {
        'form': form,
        'student': student
    }
    return render(request, 'students/edit_student.html', context)
