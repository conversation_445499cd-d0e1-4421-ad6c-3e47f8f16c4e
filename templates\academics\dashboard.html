{% extends 'academics/base.html' %} {% load static %} {% block title %}Teacher's
Dashboard | {% endblock %} {% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex items-center gap-6 mb-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-2xl flex items-center justify-center shadow-xl icon-float"
      >
        <i class="fas fa-chalkboard-teacher text-white text-2xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
        >
          Teacher's Dashboard
        </h1>
        <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
          Academic management overview and insights
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#74C69D] to-[#5fb085] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#74C69D] font-semibold">Teacher Dashboard</span>
    </nav>
  </div>

  <!-- Statistics Cards -->
  <div
    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 stats-cards-fade-in"
  >
    <!-- Total Classes Card -->
    <div
      class="stat-card bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10 rounded-2xl p-6 border border-[#74C69D]/20 hover:shadow-xl transition-all duration-300 group"
    >
      <div class="flex items-center justify-between mb-4">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
        >
          <i class="fas fa-chalkboard-user text-white text-lg"></i>
        </div>
        <div class="w-3 h-3 bg-[#74C69D] rounded-full animate-pulse"></div>
      </div>
      <div class="space-y-2">
        <span
          class="text-sm font-bold text-[#40657F] uppercase tracking-wider block"
          >Total Classes</span
        >
        <span class="text-3xl font-bold text-[#74C69D] font-display block"
          >{{level_count}}</span
        >
        <span class="text-sm text-[#40657F] font-medium">Active classes</span>
      </div>
    </div>

    <!-- Total Students Card -->
    <div
      class="stat-card bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 rounded-2xl p-6 border border-[#7AB2D3]/20 hover:shadow-xl transition-all duration-300 group"
    >
      <div class="flex items-center justify-between mb-4">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
        >
          <i class="fas fa-graduation-cap text-white text-lg"></i>
        </div>
        <div class="w-3 h-3 bg-[#7AB2D3] rounded-full animate-pulse"></div>
      </div>
      <div class="space-y-2">
        <span
          class="text-sm font-bold text-[#40657F] uppercase tracking-wider block"
          >Total Students</span
        >
        <span class="text-3xl font-bold text-[#7AB2D3] font-display block"
          >{{student_count}}</span
        >
        <span class="text-sm text-[#40657F] font-medium"
          >Enrolled students</span
        >
      </div>
    </div>

    <!-- Total Subjects Card -->
    <div
      class="stat-card bg-gradient-to-br from-[#F28C8C]/10 to-[#e07575]/10 rounded-2xl p-6 border border-[#F28C8C]/20 hover:shadow-xl transition-all duration-300 group"
    >
      <div class="flex items-center justify-between mb-4">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
        >
          <i class="fas fa-book text-white text-lg"></i>
        </div>
        <div class="w-3 h-3 bg-[#F28C8C] rounded-full animate-pulse"></div>
      </div>
      <div class="space-y-2">
        <span
          class="text-sm font-bold text-[#40657F] uppercase tracking-wider block"
          >Total Subjects</span
        >
        <span class="text-3xl font-bold text-[#F28C8C] font-display block"
          >{{subject_count}}</span
        >
        <span class="text-sm text-[#40657F] font-medium"
          >Available subjects</span
        >
      </div>
    </div>

    <!-- Pass Percentage Card -->
    <div
      class="stat-card bg-gradient-to-br from-[#B9D8EB]/20 to-[#E2F1F9]/20 rounded-2xl p-6 border border-[#B9D8EB]/30 hover:shadow-xl transition-all duration-300 group"
    >
      <div class="flex items-center justify-between mb-4">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
        >
          <i class="fas fa-chart-simple text-white text-lg"></i>
        </div>
        <div class="w-3 h-3 bg-[#40657F] rounded-full animate-pulse"></div>
      </div>
      <div class="space-y-2">
        <span
          class="text-sm font-bold text-[#40657F] uppercase tracking-wider block"
          >Pass Rate</span
        >
        <span class="text-3xl font-bold text-[#40657F] font-display block"
          >{{pass_percentage}}%</span
        >
        <span class="text-sm text-[#40657F] font-medium">Success rate</span>
      </div>
    </div>
  </div>

  <!-- Leaderboard Section -->
  <div class="leaderboard-header-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center shadow-lg leaderboard-icon-float"
      >
        <i class="fas fa-trophy text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-3xl font-bold text-[#2C3E50] font-display">
          Class Leaderboards
        </h2>
        <p class="text-[#40657F] text-sm">Top performing students by class</p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 leaderboard-cards-fade-in">
    {% for class, students in class_leaderboards.items %}
    <div
      class="leaderboard-card card-modern p-8 hover:shadow-2xl transition-all duration-300"
    >
      <!-- Class Header -->
      <div
        class="flex items-center gap-4 mb-6 pb-4 border-b border-[#B9D8EB]/30"
      >
        <div
          class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg"
        >
          <i class="fas fa-chalkboard text-white text-sm"></i>
        </div>
        <div>
          <h3 class="text-xl font-bold text-[#2C3E50] font-display">
            {{class}}
          </h3>
          <p class="text-[#40657F] text-sm">
            {{students|length}} student{{students|length|pluralize}}
          </p>
        </div>
      </div>

      <!-- Students List -->
      <div class="space-y-3">
        {% for student in students %}
        <div
          class="student-row flex items-center gap-4 p-4 rounded-xl hover:bg-[#E2F1F9]/50 transition-all duration-200 group"
        >
          <!-- Rank Badge -->
          <div class="flex-shrink-0">
            {% if forloop.counter == 1 %}
            <div
              class="w-10 h-10 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-full flex items-center justify-center shadow-lg"
            >
              <i class="fas fa-crown text-white text-sm"></i>
            </div>
            {% elif forloop.counter == 2 %}
            <div
              class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-full flex items-center justify-center shadow-lg"
            >
              <i class="fas fa-medal text-white text-sm"></i>
            </div>
            {% elif forloop.counter == 3 %}
            <div
              class="w-10 h-10 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-full flex items-center justify-center shadow-lg"
            >
              <i class="fas fa-award text-white text-sm"></i>
            </div>
            {% else %}
            <div
              class="w-10 h-10 bg-gradient-to-br from-[#B9D8EB] to-[#E2F1F9] rounded-full flex items-center justify-center border border-[#B9D8EB]/50"
            >
              <span class="text-[#40657F] font-bold text-sm"
                >{{ forloop.counter }}</span
              >
            </div>
            {% endif %}
          </div>

          <!-- Student Info -->
          <div class="flex-1 min-w-0">
            <h4 class="font-bold text-[#2C3E50] capitalize truncate">
              {{ student.name }}
            </h4>
            <p class="text-sm text-[#40657F]">
              {% if forloop.counter == 1 %} 🏆 Top Performer {% elif
              forloop.counter == 2 %} 🥈 Excellent Work {% elif forloop.counter
              == 3 %} 🥉 Great Progress {% else %} 📚 Good Student {% endif %}
            </p>
          </div>

          <!-- Score -->
          <div class="flex-shrink-0">
            <div class="text-right">
              <div class="text-xl font-bold text-[#74C69D] font-display">
                {{ student.score|default:'—' }}
              </div>
              <div class="text-xs text-[#40657F] uppercase tracking-wider">
                Score
              </div>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
    {% empty %}
    <div class="col-span-full text-center py-12">
      <div class="flex flex-col items-center gap-4">
        <div
          class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
        >
          <i class="fas fa-trophy text-[#B9D8EB] text-2xl"></i>
        </div>
        <div>
          <h4 class="text-lg font-bold text-[#2C3E50] mb-2">
            No Leaderboard Data
          </h4>
          <p class="text-[#40657F]">
            Student performance data will appear here once available.
          </p>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>

  <!-- Quick Access Section -->
  <div class="card-modern p-8 quick-access-fade-in">
    <div class="flex items-center gap-4 mb-6">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg quick-access-icon-float"
      >
        <i class="fas fa-bolt text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
          Quick Access
        </h3>
        <p class="text-[#40657F] text-sm">
          Frequently used management tools
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Academic Management -->
      <a
        href="{% url 'academics:academic_management_dashboard' %}"
        class="quick-access-card group bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 border border-[#7AB2D3]/20 rounded-xl p-4 hover:shadow-lg hover:-translate-y-1 transition-all duration-300"
      >
        <div class="flex items-center gap-3">
          <div
            class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
          >
            <i class="fas fa-graduation-cap text-white text-sm"></i>
          </div>
          <div>
            <h4 class="font-bold text-[#2C3E50] text-sm">Academic Management</h4>
            <p class="text-xs text-[#40657F]">Subjects & Activities</p>
          </div>
        </div>
      </a>

      <!-- Activity Types -->
      <a
        href="{% url 'academics:activity_type_management' %}"
        class="quick-access-card group bg-gradient-to-br from-[#40657F]/10 to-[#2C3E50]/10 border border-[#40657F]/20 rounded-xl p-4 hover:shadow-lg hover:-translate-y-1 transition-all duration-300"
      >
        <div class="flex items-center gap-3">
          <div
            class="w-10 h-10 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
          >
            <i class="fas fa-tags text-white text-sm"></i>
          </div>
          <div>
            <h4 class="font-bold text-[#2C3E50] text-sm">Activity Types</h4>
            <p class="text-xs text-[#40657F]">Manage Categories</p>
          </div>
        </div>
      </a>

      <!-- Classes -->
      <a
        href="{% url 'academics:levels' %}"
        class="quick-access-card group bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10 border border-[#74C69D]/20 rounded-xl p-4 hover:shadow-lg hover:-translate-y-1 transition-all duration-300"
      >
        <div class="flex items-center gap-3">
          <div
            class="w-10 h-10 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
          >
            <i class="fas fa-chalkboard text-white text-sm"></i>
          </div>
          <div>
            <h4 class="font-bold text-[#2C3E50] text-sm">Classes</h4>
            <p class="text-xs text-[#40657F]">View All Levels</p>
          </div>
        </div>
      </a>

      <!-- Performance -->
      <a
        href="{% url 'academics:enter_by_level' %}"
        class="quick-access-card group bg-gradient-to-br from-[#F28C8C]/10 to-[#e74c3c]/10 border border-[#F28C8C]/20 rounded-xl p-4 hover:shadow-lg hover:-translate-y-1 transition-all duration-300"
      >
        <div class="flex items-center gap-3">
          <div
            class="w-10 h-10 bg-gradient-to-br from-[#F28C8C] to-[#e74c3c] rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
          >
            <i class="fas fa-chart-bar text-white text-sm"></i>
          </div>
          <div>
            <h4 class="font-bold text-[#2C3E50] text-sm">Enter Grades</h4>
            <p class="text-xs text-[#40657F]">Exam Results</p>
          </div>
        </div>
      </a>
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Stats Cards Animations */
  .stats-cards-fade-in {
    opacity: 0;
    animation: statsCardsFadeIn 0.8s ease-out 0.8s forwards;
  }

  .stat-card {
    opacity: 0;
    transform: translateY(30px);
    animation: statCardSlideIn 0.6s ease-out forwards;
  }

  .stat-card:nth-child(1) {
    animation-delay: 1s;
  }
  .stat-card:nth-child(2) {
    animation-delay: 1.1s;
  }
  .stat-card:nth-child(3) {
    animation-delay: 1.2s;
  }
  .stat-card:nth-child(4) {
    animation-delay: 1.3s;
  }

  /* Leaderboard Animations */
  .leaderboard-header-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: leaderboardHeaderFadeIn 0.8s ease-out 1.4s forwards;
  }

  .leaderboard-icon-float {
    animation: leaderboardIconFloat 4s ease-in-out infinite;
  }

  .leaderboard-cards-fade-in {
    opacity: 0;
    animation: leaderboardCardsFadeIn 0.8s ease-out 1.6s forwards;
  }

  .leaderboard-card {
    opacity: 0;
    transform: translateX(-30px);
    animation: leaderboardCardSlideIn 0.6s ease-out forwards;
  }

  .leaderboard-card:nth-child(odd) {
    animation-delay: 1.8s;
  }
  .leaderboard-card:nth-child(even) {
    animation-delay: 1.9s;
  }

  .student-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: studentRowSlideIn 0.4s ease-out forwards;
  }

  .student-row:nth-child(1) {
    animation-delay: 2s;
  }
  .student-row:nth-child(2) {
    animation-delay: 2.1s;
  }
  .student-row:nth-child(3) {
    animation-delay: 2.2s;
  }
  .student-row:nth-child(4) {
    animation-delay: 2.3s;
  }
  .student-row:nth-child(5) {
    animation-delay: 2.4s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes statsCardsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes statCardSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes leaderboardHeaderFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes leaderboardIconFloat {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(-5deg);
    }
  }

  @keyframes leaderboardCardsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes leaderboardCardSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes studentRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Quick Access Animations */
  .quick-access-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: quickAccessFadeIn 0.8s ease-out 1.8s forwards;
  }

  .quick-access-icon-float {
    animation: quickAccessIconFloat 4s ease-in-out infinite;
  }

  .quick-access-card {
    opacity: 0;
    transform: translateY(20px);
    animation: quickAccessCardSlideIn 0.4s ease-out forwards;
  }

  .quick-access-card:nth-child(1) { animation-delay: 2s; }
  .quick-access-card:nth-child(2) { animation-delay: 2.1s; }
  .quick-access-card:nth-child(3) { animation-delay: 2.2s; }
  .quick-access-card:nth-child(4) { animation-delay: 2.3s; }

  @keyframes quickAccessFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes quickAccessIconFloat {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-6px) rotate(5deg);
    }
  }

  @keyframes quickAccessCardSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .stat-card {
      animation-delay: 0.8s;
    }

    .stat-card:nth-child(n) {
      animation-delay: calc(0.8s + 0.1s * var(--card-index, 1));
    }

    .student-row {
      animation-delay: 1.5s;
    }

    .student-row:nth-child(n) {
      animation-delay: calc(1.5s + 0.1s * var(--row-index, 1));
    }
  }
</style>

{% endblock %}

<!--  -->
{% block scripts %}
<script src="{% static 'js/main_chart.js' %}"></script>
{% endblock %}
