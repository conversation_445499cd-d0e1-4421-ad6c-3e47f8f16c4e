from datetime import date

from django.http import HttpResponse
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from openpyxl import Workbook


from students.models import Level, Student
from students.utils import get_levels_student_count


current_month = date.today()


@login_required(login_url="accounts:login")
def classes(request):
    levels = Level.objects.all()
    levels_student_count = get_levels_student_count(levels)
    context = {
        'levels': levels,
        'level_student_count': levels_student_count
    }
    return render(request, 'students/classes.html', context)


@login_required(login_url="accounts:login")
def class_details(request, slug):
    levels = Level.objects.all()
    level = Level.objects.get(slug=slug)
    students = Student.objects.filter(
        level=level, is_active=True).order_by('name')
    
    if f'students' in request.GET:
        return export_to_excel(level, students)
    
    context = {
        'levels': levels,
        'level': level,
        'students': students
    }
    return render(request, 'students/class_details.html', context)


def export_to_excel(level, students):
    wb = Workbook()
    ws = wb.active
    ws.title = f"{level} student list"

    headers = ["Student ID", "Student Name",
               "Gender", "level"]
    ws.append(headers)

    for student in students:
        ws.append([
            student.student_id,
            student.name,
            student.gender,
            level.level_name,
        ])

    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename="{level} students.xlsx"'

    wb.save(response)

    return response
