{% extends 'base.html' %}
{% load static %}
{% load rbac_tags %}

{% block title %}Create New User | {% endblock %}

{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-2xl flex items-center justify-center shadow-lg icon-float">
        <i class="fas fa-user-plus text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in">
          Create New User
        </h1>
        <div class="w-20 h-1 bg-gradient-to-r from-[#74C69D] to-[#5fb085] rounded-full accent-line-grow"></div>
      </div>
    </div>
    
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <a href="{% url 'accounts:user_management' %}" class="text-[#40657F] hover:text-[#7AB2D3]">User Management</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Create User</span>
    </nav>
  </div>

  <!-- Form -->
  <div class="card-modern p-8">
    <form method="post" class="space-y-8">
      {% csrf_token %}
      
      <!-- Basic Information -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-user text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">Basic Information</h3>
            <p class="text-[#40657F] text-sm">Enter the user's personal details</p>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="{{ form.username.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Username <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.username }}
            {% if form.username.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.username.errors.0 }}</div>
            {% endif %}
            {% if form.username.help_text %}
              <div class="mt-1 text-xs text-[#40657F]">{{ form.username.help_text }}</div>
            {% endif %}
          </div>
          
          <div>
            <label for="{{ form.email.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Email Address <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.email }}
            {% if form.email.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.email.errors.0 }}</div>
            {% endif %}
          </div>
          
          <div>
            <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              First Name <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.first_name }}
            {% if form.first_name.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.first_name.errors.0 }}</div>
            {% endif %}
          </div>
          
          <div>
            <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Last Name <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.last_name }}
            {% if form.last_name.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.last_name.errors.0 }}</div>
            {% endif %}
          </div>
          
          <div>
            <label for="{{ form.gender.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Gender <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.gender }}
            {% if form.gender.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.gender.errors.0 }}</div>
            {% endif %}
          </div>
          
          <div>
            <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Phone Number
            </label>
            {{ form.phone_number }}
            {% if form.phone_number.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.phone_number.errors.0 }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Password Information -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-lock text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">Password</h3>
            <p class="text-[#40657F] text-sm">Set a secure password for the user</p>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="{{ form.password1.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Password <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.password1 }}
            {% if form.password1.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.password1.errors.0 }}</div>
            {% endif %}
            {% if form.password1.help_text %}
              <div class="mt-1 text-xs text-[#40657F]">{{ form.password1.help_text }}</div>
            {% endif %}
          </div>
          
          <div>
            <label for="{{ form.password2.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Confirm Password <span class="text-[#F28C8C]">*</span>
            </label>
            {{ form.password2 }}
            {% if form.password2.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.password2.errors.0 }}</div>
            {% endif %}
            {% if form.password2.help_text %}
              <div class="mt-1 text-xs text-[#40657F]">{{ form.password2.help_text }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Role Assignment -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-[#FFB84D] to-[#e6a43d] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-user-shield text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">Role Assignment</h3>
            <p class="text-[#40657F] text-sm">Select roles for this user (optional)</p>
          </div>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-semibold text-[#2C3E50] mb-3">Available Roles</label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              {% for role in available_roles %}
                {% if role.level <= user_max_level %}
                <label class="flex items-center p-4 border border-[#B9D8EB] rounded-xl hover:bg-[#F7FAFC] cursor-pointer transition-colors">
                  <input type="checkbox" name="roles" value="{{ role.id }}" 
                         class="w-4 h-4 text-[#7AB2D3] border-[#B9D8EB] rounded focus:ring-[#7AB2D3] mr-3">
                  <div class="flex-1">
                    <div class="font-semibold text-[#2C3E50]">{{ role.display_name }}</div>
                    <div class="text-sm text-[#40657F]">Level {{ role.level }} • {{ role.description|truncatechars:50 }}</div>
                  </div>
                </label>
                {% endif %}
              {% endfor %}
            </div>
            {% if form.roles.errors %}
              <div class="mt-2 text-sm text-[#F28C8C]">{{ form.roles.errors.0 }}</div>
            {% endif %}
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="{{ form.role_expires_at.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
                Role Expiration (Optional)
              </label>
              {{ form.role_expires_at }}
              {% if form.role_expires_at.errors %}
                <div class="mt-1 text-sm text-[#F28C8C]">{{ form.role_expires_at.errors.0 }}</div>
              {% endif %}
              {% if form.role_expires_at.help_text %}
                <div class="mt-1 text-xs text-[#40657F]">{{ form.role_expires_at.help_text }}</div>
              {% endif %}
            </div>
          </div>
          
          <div>
            <label for="{{ form.role_notes.id_for_label }}" class="block text-sm font-semibold text-[#2C3E50] mb-2">
              Role Assignment Notes (Optional)
            </label>
            {{ form.role_notes }}
            {% if form.role_notes.errors %}
              <div class="mt-1 text-sm text-[#F28C8C]">{{ form.role_notes.errors.0 }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Account Settings -->
      <div class="space-y-6">
        <div class="flex items-center gap-4 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-[#9B59B6] to-[#8e44ad] rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-cog text-white text-sm"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-[#2C3E50] font-display">Account Settings</h3>
            <p class="text-[#40657F] text-sm">Configure account status and notifications</p>
          </div>
        </div>
        
        <div class="space-y-4">
          <label class="flex items-center p-4 border border-[#B9D8EB] rounded-xl hover:bg-[#F7FAFC] cursor-pointer transition-colors">
            {{ form.is_active }}
            <div class="ml-3">
              <div class="font-semibold text-[#2C3E50]">Active Account</div>
              <div class="text-sm text-[#40657F]">User can log in and access the system</div>
            </div>
          </label>
          
          <label class="flex items-center p-4 border border-[#B9D8EB] rounded-xl hover:bg-[#F7FAFC] cursor-pointer transition-colors">
            {{ form.send_welcome_email }}
            <div class="ml-3">
              <div class="font-semibold text-[#2C3E50]">Send Welcome Email</div>
              <div class="text-sm text-[#40657F]">Send login credentials to the user's email</div>
            </div>
          </label>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-between pt-6 border-t border-[#E2F1F9]">
        <a href="{% url 'accounts:user_management' %}" 
           class="px-6 py-3 border border-[#B9D8EB] text-[#40657F] rounded-xl font-semibold hover:bg-[#F7FAFC] transition-colors">
          <i class="fas fa-arrow-left mr-2"></i>Cancel
        </a>
        
        <button type="submit" 
                class="px-8 py-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
          <i class="fas fa-user-plus mr-2"></i>Create User
        </button>
      </div>
    </form>
  </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form validation feedback
    const form = document.querySelector('form');
    const submitButton = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating User...';
        submitButton.disabled = true;
    });
    
    // Password strength indicator (basic)
    const password1 = document.getElementById('{{ form.password1.id_for_label }}');
    if (password1) {
        password1.addEventListener('input', function() {
            const strength = this.value.length >= 8 ? 'strong' : 'weak';
            // You could add visual feedback here
        });
    }
});
</script>

{% endblock %}
