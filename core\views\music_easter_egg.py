"""
Music Easter Egg View
A delightful surprise for music lovers who search for music-related terms
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
import json

from core.music_quotes import get_random_music_quote, is_music_search, get_music_quote_by_genre, get_music_quotes_by_author


@login_required(login_url="accounts:login")
def music_easter_egg(request):
    """
    Display a random music quote when triggered by music search terms
    """
    # Get the search term that triggered this easter egg
    search_term = request.GET.get('search', '')
    quote_data = get_random_music_quote()
    
    # Add some context about how they got here
    trigger_context = {
        'search_term': search_term,
        'message': f'Your search for "{search_term}" has awakened the musical spirit within the system...'
    }
    
    context = {
        'quote': quote_data,
        'trigger': trigger_context,
        'page_title': 'A Moment of Musical Inspiration'
    }
    
    return render(request, 'core/music_easter_egg.html', context)


@login_required(login_url="accounts:login")
@csrf_exempt
@require_http_methods(["POST"])
def get_new_music_quote(request):
    """
    AJAX endpoint to get a new random music quote
    """
    try:
        data = json.loads(request.body)
        genre = data.get('genre', None)
        author = data.get('author', None)
        
        if author:
            quotes = get_music_quotes_by_author(author)
            if quotes:
                import random
                quote_data = random.choice(quotes)
            else:
                quote_data = get_random_music_quote()
        elif genre:
            quote_data = get_music_quote_by_genre(genre)
        else:
            quote_data = get_random_music_quote()
        
        return JsonResponse({
            'success': True,
            'quote': quote_data
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def check_music_search(search_term):
    """
    Helper function to check if a search term should trigger the music easter egg
    This can be imported and used in other views
    """
    return is_music_search(search_term)
