from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db import transaction
from django.core.paginator import Paginator
from django.db.models import Q, Count

from accounts.decorators import require_permission

from academics.models import Subject, AssessmentType, Activity, ActivityType
from academics.forms.academic_management_forms import (
    SubjectForm, AssessmentTypeForm, ActivityTypeForm, ActivityForm,
    BulkSubjectDeleteForm, BulkAssessmentTypeDeleteForm, BulkActivityDeleteForm,
    BulkActivityTypeDeleteForm
)


@login_required(login_url="accounts:login")
@require_permission('view_academics', 'manage_academics')
def academic_management_dashboard(request):
    """
    Main academic management dashboard
    """
    # Get search queries
    subject_search = request.GET.get('subject_search', '').strip()
    assessment_search = request.GET.get('assessment_search', '').strip()
    activity_search = request.GET.get('activity_search', '').strip()
    
    # Base querysets
    subjects = Subject.objects.all()
    assessment_types = AssessmentType.objects.all()
    activities = Activity.objects.all().select_related('activity_type', 'subject', 'term', 'class_assigned')
    
    # Apply subject filters
    if subject_search:
        subjects = subjects.filter(
            Q(name__icontains=subject_search) |
            Q(abbrv__icontains=subject_search) |
            Q(category__icontains=subject_search) |
            Q(level__icontains=subject_search)
        )
    
    # Apply assessment type filters
    if assessment_search:
        assessment_types = assessment_types.filter(
            Q(name__icontains=assessment_search) |
            Q(description__icontains=assessment_search)
        )
    
    # Apply activity filters
    if activity_search:
        activities = activities.filter(
            Q(activity_type__name__icontains=activity_search) |
            Q(subject__name__icontains=activity_search) |
            Q(notes__icontains=activity_search)
        )
    
    # Order results
    subjects = subjects.order_by('name')
    assessment_types = assessment_types.order_by('sequence', 'name')
    activities = activities.order_by('-date', 'activity_type__name')
    
    # Pagination
    subject_paginator = Paginator(subjects, 10)
    assessment_paginator = Paginator(assessment_types, 10)
    activity_paginator = Paginator(activities, 10)
    
    subject_page = request.GET.get('subject_page')
    assessment_page = request.GET.get('assessment_page')
    activity_page = request.GET.get('activity_page')
    
    subject_page_obj = subject_paginator.get_page(subject_page)
    assessment_page_obj = assessment_paginator.get_page(assessment_page)
    activity_page_obj = activity_paginator.get_page(activity_page)
    
    # Statistics
    stats = {
        'total_subjects': Subject.objects.count(),
        'total_assessment_types': AssessmentType.objects.count(),
        'total_activities': Activity.objects.count(),
        'total_activity_types': ActivityType.objects.count(),
    }
    
    context = {
        'subjects': subject_page_obj,
        'assessment_types': assessment_page_obj,
        'activities': activity_page_obj,
        'subject_search': subject_search,
        'assessment_search': assessment_search,
        'activity_search': activity_search,
        'stats': stats,
        'subject_total_count': subjects.count(),
        'assessment_total_count': assessment_types.count(),
        'activity_total_count': activities.count(),
    }
    return render(request, 'academics/academic_management_dashboard.html', context)


# Subject Management Views
@login_required(login_url="accounts:login")
@require_permission('add_subject', 'manage_academics')
def add_subject(request):
    """Add a new subject"""
    if request.method == 'POST':
        form = SubjectForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    subject = form.save()
                    messages.success(
                        request, 
                        f'Subject "{subject.name}" has been created successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error creating subject: {str(e)}')
    else:
        form = SubjectForm()
    
    context = {
        'form': form,
        'title': 'Add New Subject',
        'submit_text': 'Create Subject'
    }
    return render(request, 'academics/subject_form.html', context)


@login_required(login_url="accounts:login")
@require_permission('edit_subject', 'manage_academics')
def edit_subject(request, pk):
    """Edit an existing subject"""
    subject = get_object_or_404(Subject, pk=pk)
    
    if request.method == 'POST':
        form = SubjectForm(request.POST, instance=subject)
        if form.is_valid():
            try:
                with transaction.atomic():
                    subject = form.save()
                    messages.success(
                        request, 
                        f'Subject "{subject.name}" has been updated successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error updating subject: {str(e)}')
    else:
        form = SubjectForm(instance=subject)
    
    context = {
        'form': form,
        'subject': subject,
        'title': f'Edit Subject: {subject.name}',
        'submit_text': 'Update Subject'
    }
    return render(request, 'academics/subject_form.html', context)


# Assessment Type Management Views
@login_required(login_url="accounts:login")
def add_assessment_type(request):
    """Add a new assessment type"""
    if request.method == 'POST':
        form = AssessmentTypeForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    assessment_type = form.save()
                    messages.success(
                        request, 
                        f'Assessment type "{assessment_type.name}" has been created successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error creating assessment type: {str(e)}')
    else:
        form = AssessmentTypeForm()
    
    context = {
        'form': form,
        'title': 'Add New Assessment Type',
        'submit_text': 'Create Assessment Type'
    }
    return render(request, 'academics/assessment_type_form.html', context)


@login_required(login_url="accounts:login")
def edit_assessment_type(request, pk):
    """Edit an existing assessment type"""
    assessment_type = get_object_or_404(AssessmentType, pk=pk)
    
    if request.method == 'POST':
        form = AssessmentTypeForm(request.POST, instance=assessment_type)
        if form.is_valid():
            try:
                with transaction.atomic():
                    assessment_type = form.save()
                    messages.success(
                        request, 
                        f'Assessment type "{assessment_type.name}" has been updated successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error updating assessment type: {str(e)}')
    else:
        form = AssessmentTypeForm(instance=assessment_type)
    
    context = {
        'form': form,
        'assessment_type': assessment_type,
        'title': f'Edit Assessment Type: {assessment_type.name}',
        'submit_text': 'Update Assessment Type'
    }
    return render(request, 'academics/assessment_type_form.html', context)


# Activity Type Management Views
@login_required(login_url="accounts:login")
def add_activity_type(request):
    """Add a new activity type"""
    if request.method == 'POST':
        form = ActivityTypeForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    activity_type = form.save()
                    messages.success(
                        request, 
                        f'Activity type "{activity_type.name}" has been created successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error creating activity type: {str(e)}')
    else:
        form = ActivityTypeForm()
    
    context = {
        'form': form,
        'title': 'Add New Activity Type',
        'submit_text': 'Create Activity Type'
    }
    return render(request, 'academics/activity_type_form.html', context)


@login_required(login_url="accounts:login")
def edit_activity_type(request, pk):
    """Edit an existing activity type"""
    activity_type = get_object_or_404(ActivityType, pk=pk)
    
    if request.method == 'POST':
        form = ActivityTypeForm(request.POST, instance=activity_type)
        if form.is_valid():
            try:
                with transaction.atomic():
                    activity_type = form.save()
                    messages.success(
                        request, 
                        f'Activity type "{activity_type.name}" has been updated successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error updating activity type: {str(e)}')
    else:
        form = ActivityTypeForm(instance=activity_type)
    
    context = {
        'form': form,
        'activity_type': activity_type,
        'title': f'Edit Activity Type: {activity_type.name}',
        'submit_text': 'Update Activity Type'
    }
    return render(request, 'academics/activity_type_form.html', context)


@login_required(login_url="accounts:login")
def activity_type_management(request):
    """
    Activity type management dashboard
    """
    # Get search query
    search_query = request.GET.get('search', '').strip()

    # Base queryset
    activity_types = ActivityType.objects.all()

    # Apply search filter
    if search_query:
        activity_types = activity_types.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Order results
    activity_types = activity_types.order_by('name')

    # Pagination
    paginator = Paginator(activity_types, 12)
    page = request.GET.get('page')
    page_obj = paginator.get_page(page)

    # Statistics
    stats = {
        'total_activity_types': ActivityType.objects.count(),
        'total_activities': Activity.objects.count(),
    }

    context = {
        'activity_types': page_obj,
        'search_query': search_query,
        'stats': stats,
        'total_count': activity_types.count(),
    }
    return render(request, 'academics/activity_type_management.html', context)


@login_required(login_url="accounts:login")
def activity_type_details(request, pk):
    """View activity type details"""
    activity_type = get_object_or_404(ActivityType, pk=pk)

    # Get related activities
    activities = activity_type.activities.all().select_related('subject', 'term', 'class_assigned')[:10]

    context = {
        'activity_type': activity_type,
        'activities': activities,
        'activity_count': activity_type.activities.count(),
    }
    return render(request, 'academics/activity_type_details.html', context)


@login_required(login_url="accounts:login")
def bulk_delete_activity_types(request):
    """Bulk delete activity types"""
    if request.method == 'POST':
        form = BulkActivityTypeDeleteForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    activity_types = form.cleaned_data['activity_types']
                    count = activity_types.count()
                    activity_types.delete()
                    messages.success(
                        request,
                        f'{count} activity type{"s" if count != 1 else ""} deleted successfully!'
                    )
                    return redirect('academics:activity_type_management')
            except Exception as e:
                messages.error(request, f'Error deleting activity types: {str(e)}')
    else:
        form = BulkActivityTypeDeleteForm()

    context = {
        'form': form,
        'title': 'Delete Activity Types',
        'submit_text': 'Delete Selected Activity Types'
    }
    return render(request, 'academics/bulk_delete_activity_types.html', context)


# Activity Management Views
@login_required(login_url="accounts:login")
def add_activity(request):
    """Add a new activity"""
    if request.method == 'POST':
        form = ActivityForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    activity = form.save()
                    messages.success(
                        request, 
                        f'Activity "{activity.activity_type.name}" has been created successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error creating activity: {str(e)}')
    else:
        form = ActivityForm()
    
    context = {
        'form': form,
        'title': 'Add New Activity',
        'submit_text': 'Create Activity'
    }
    return render(request, 'academics/activity_form.html', context)


@login_required(login_url="accounts:login")
def edit_activity(request, pk):
    """Edit an existing activity"""
    activity = get_object_or_404(Activity, pk=pk)
    
    if request.method == 'POST':
        form = ActivityForm(request.POST, instance=activity)
        if form.is_valid():
            try:
                with transaction.atomic():
                    activity = form.save()
                    messages.success(
                        request, 
                        f'Activity "{activity.activity_type.name}" has been updated successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error updating activity: {str(e)}')
    else:
        form = ActivityForm(instance=activity)
    
    context = {
        'form': form,
        'activity': activity,
        'title': f'Edit Activity: {activity.activity_type.name}',
        'submit_text': 'Update Activity'
    }
    return render(request, 'academics/activity_form.html', context)


# Bulk Delete Views
@login_required(login_url="accounts:login")
@require_permission('edit_subject', 'manage_academics')
def bulk_delete_subjects(request):
    """Bulk delete subjects"""
    if request.method == 'POST':
        form = BulkSubjectDeleteForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    subjects = form.cleaned_data['subjects']
                    count = subjects.count()
                    subjects.delete()
                    messages.success(
                        request, 
                        f'{count} subject{"s" if count != 1 else ""} deleted successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error deleting subjects: {str(e)}')
    else:
        form = BulkSubjectDeleteForm()
    
    context = {
        'form': form,
        'title': 'Delete Subjects',
        'submit_text': 'Delete Selected Subjects'
    }
    return render(request, 'academics/bulk_delete_subjects.html', context)


@login_required(login_url="accounts:login")
def bulk_delete_assessment_types(request):
    """Bulk delete assessment types"""
    if request.method == 'POST':
        form = BulkAssessmentTypeDeleteForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    assessment_types = form.cleaned_data['assessment_types']
                    count = assessment_types.count()
                    assessment_types.delete()
                    messages.success(
                        request,
                        f'{count} assessment type{"s" if count != 1 else ""} deleted successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error deleting assessment types: {str(e)}')
    else:
        form = BulkAssessmentTypeDeleteForm()

    context = {
        'form': form,
        'title': 'Delete Assessment Types',
        'submit_text': 'Delete Selected Assessment Types'
    }
    return render(request, 'academics/bulk_delete_assessment_types.html', context)


@login_required(login_url="accounts:login")
def bulk_delete_activities(request):
    """Bulk delete activities"""
    if request.method == 'POST':
        form = BulkActivityDeleteForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    activities = form.cleaned_data['activities']
                    count = activities.count()
                    activities.delete()
                    messages.success(
                        request,
                        f'{count} activit{"ies" if count != 1 else "y"} deleted successfully!'
                    )
                    return redirect('academics:academic_management_dashboard')
            except Exception as e:
                messages.error(request, f'Error deleting activities: {str(e)}')
    else:
        form = BulkActivityDeleteForm()

    context = {
        'form': form,
        'title': 'Delete Activities',
        'submit_text': 'Delete Selected Activities'
    }
    return render(request, 'academics/bulk_delete_activities.html', context)


@login_required(login_url="accounts:login")
@require_permission('view_academics', 'manage_academics')
def subject_details(request, pk):
    """View subject details"""
    subject = get_object_or_404(Subject, pk=pk)

    # Get related data
    enrollments = subject.enrollment_set.all().select_related('student')[:10]
    assessments = subject.assessment_set.all().select_related('assessment_type')[:10]

    context = {
        'subject': subject,
        'enrollments': enrollments,
        'assessments': assessments,
        'enrollment_count': subject.enrollment_set.count(),
        'assessment_count': subject.assessment_set.count(),
    }
    return render(request, 'academics/subject_details.html', context)


@login_required(login_url="accounts:login")
def activity_details(request, pk):
    """View activity details"""
    activity = get_object_or_404(Activity, pk=pk)

    # Get participation records
    participations = activity.activityparticipation_set.all().select_related('student')

    context = {
        'activity': activity,
        'participations': participations,
        'participation_count': participations.count(),
    }
    return render(request, 'academics/activity_details.html', context)
