from django.shortcuts import redirect, render
from django.contrib.auth.decorators import login_required


from academics.models import Activity, ActivityType, StudentActivityParticipation
from accounts.models import TeacherAssignment

from students.models import Student, Level


@login_required(login_url="accounts:login")
def activities(request):
    """
    View to handle activities related to subjects and students.
    """
    # Filter activities based on user permissions
    if request.user.is_staff or request.user.is_superuser:
        # Staff/admin can see all activities
        activities = Activity.objects.filter(term__is_active=True).order_by('-date')
    else:
        # Teachers can only see activities for classes they are assigned to
        teacher_assignments = TeacherAssignment.objects.filter(
            teacher=request.user,
            term__is_active=True
        ).select_related('class_assigned')

        # Get unique class IDs from assignments
        class_ids = teacher_assignments.values_list('class_assigned_id', flat=True).distinct()

        # Filter activities by assigned classes
        activities = Activity.objects.filter(
            term__is_active=True,
            class_assigned_id__in=class_ids
        ).order_by('-date')

    context = {
        'activities': activities,
    }

    return render(request, 'academics/activities/activities.html', context)


@login_required(login_url="accounts:login")
def enter_results(request, id):
    """
    View to display details of a specific activity.
    """
    activity = Activity.objects.get(id=id)

    # Check if teacher has permission to enter results for this activity
    if not (request.user.is_staff or request.user.is_superuser):
        teacher_assignment = TeacherAssignment.objects.filter(
            teacher=request.user,
            class_assigned=activity.class_assigned,
            term__is_active=True
        ).first()

        if not teacher_assignment:
            from django.contrib import messages
            messages.error(request, "You don't have permission to enter results for this activity.")
            return redirect('academics:activities')

    participants = Student.objects.filter(
        level=activity.class_assigned, is_active=True).order_by('name')
    if request.method == "POST":
        for key, value in request.POST.items():
            if key == "csrfmiddlewaretoken":
                continue

            status = "attended"
            if value == "":
                score = 0
                status = "Absent"

            student = Student.objects.get(id=key)
            score = int(value) if value else 0
            results = StudentActivityParticipation.objects.create(
                student=student,
                activity=activity,
                score=score,
                participation_status=status,
            )
            results.save()

        return redirect('academics:activities')

    context = {
        'activity': activity,
        'participants': participants,
    }

    return render(request, 'academics/activities/enter_results.html', context)


@login_required(login_url="accounts:login")
def view_activity_results(request):
    """
    View to display results of a specific activity.
    """
    from students.models import Term
    level_id = None
    type_id = None
    term_id = None

    activities = ActivityType.objects.all()
    levels = Level.objects.all()
    terms = Term.objects.all()
    results = []

    if request.method == "POST":
        level_id = request.POST.get('level')
        type_id = request.POST.get('activity_type')
        term_id = request.POST.get('term')

    if level_id and type_id and term_id:
        activity = Activity.objects.get(
            activity_type__id=type_id, class_assigned__id=level_id, term__id=term_id
        )
        results = StudentActivityParticipation.objects.filter(
            activity=activity
        ).order_by('-score')

    context = {
        'activities': activities,
        'levels': levels,
        'terms': terms,
        'results': results,

    }

    return render(request, 'academics/activities/view_results.html', context)
