"""
Space Easter Egg View
A delightful surprise for space enthusiasts and science lovers
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
import json

from core.space_quotes import get_random_space_quote, is_space_search, get_space_quote_by_category, get_space_quotes_by_author


@login_required(login_url="accounts:login")
def space_easter_egg(request):
    """
    Display a random space/science quote when triggered by space search terms
    """
    # Get the search term that triggered this easter egg
    search_term = request.GET.get('search', '')
    quote_data = get_random_space_quote()
    
    # Add some context about how they got here
    trigger_context = {
        'search_term': search_term,
        'message': f'Your search for "{search_term}" has launched you into the cosmic depths of wonder...'
    }
    
    context = {
        'quote': quote_data,
        'trigger': trigger_context,
        'page_title': 'A Cosmic Moment of Wonder'
    }
    
    return render(request, 'core/space_easter_egg.html', context)


@login_required(login_url="accounts:login")
@csrf_exempt
@require_http_methods(["POST"])
def get_new_space_quote(request):
    """
    AJAX endpoint to get a new random space quote
    """
    try:
        data = json.loads(request.body)
        category = data.get('category', None)
        author = data.get('author', None)
        
        if author:
            quotes = get_space_quotes_by_author(author)
            if quotes:
                import random
                quote_data = random.choice(quotes)
            else:
                quote_data = get_random_space_quote()
        elif category:
            quote_data = get_space_quote_by_category(category)
        else:
            quote_data = get_random_space_quote()
        
        return JsonResponse({
            'success': True,
            'quote': quote_data
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def check_space_search(search_term):
    """
    Helper function to check if a search term should trigger the space easter egg
    This can be imported and used in other views
    """
    return is_space_search(search_term)
