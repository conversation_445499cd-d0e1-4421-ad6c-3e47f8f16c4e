"""
Student progression utilities for advancing students to the next class level
when a new academic year is created.
"""

from django.db import transaction
from django.contrib.auth import get_user_model
from students.models import Student, Level, Term, AcademicYear
from academics.models import Enrollment
from finances.fee_management.models.fee_account import FeeAccount

User = get_user_model()


def get_next_level(current_level):
    """
    Get the next level for student progression.
    Returns None if student is at the highest level.
    """
    # Define level progression mapping
    level_progression = {
        'Baby Class': 'Toddler Class',
        'Toddler Class': 'Reception',
        'Reception': 'Primary 1',
        'Primary 1': 'Primary 2',
        'Primary 2': 'Primary 3',
        'Primary 3': 'Primary 4',
        'Primary 4': "Primary 5",
    }
    
    next_level_name = level_progression.get(current_level.level_name)
    
    if next_level_name:
        try:
            return Level.objects.get(level_name=next_level_name)
        except Level.DoesNotExist:
            # Create the level if it doesn't exist
            return Level.objects.create(level_name=next_level_name)
    
    return None


def progress_student_to_next_level(student, new_academic_year, user=None):
    """
    Progress a single student to the next level for the new academic year.
    
    Args:
        student: Student instance to progress
        new_academic_year: AcademicYear instance for the new year
        user: User performing the action (optional)
    
    Returns:
        dict: Result of the progression with status and message
    """
    try:
        with transaction.atomic():
            current_level = student.level
            next_level = get_next_level(current_level)
            
            if next_level is None:
                return {
                    'status': 'graduated',
                    'message': f'{student.name} has completed the highest level ({current_level.level_name}) and is ready for graduation.',
                    'old_level': current_level.level_name,
                    'new_level': None
                }
            
            # Check if student has passed the current level
            # Get the most recent term from the previous academic year
            previous_terms = Term.objects.filter(
                academic_year=student.level.academic_year if hasattr(student.level, 'academic_year') else None
            ).order_by('-id')
            
            has_passed = True  # Default to pass if no academic records
            
            if previous_terms.exists():
                latest_term = previous_terms.first()
                enrollments = Enrollment.objects.filter(
                    student=student,
                    term=latest_term
                )
                
                if enrollments.exists():
                    # Check if student passed majority of subjects
                    total_subjects = enrollments.count()
                    passed_subjects = enrollments.filter(has_passed=True).count()
                    pass_rate = passed_subjects / total_subjects if total_subjects > 0 else 0
                    
                    has_passed = pass_rate >= 0.6  # 60% pass rate required
            
            if not has_passed:
                return {
                    'status': 'repeat',
                    'message': f'{student.name} did not meet the requirements to progress. Student will repeat {current_level.level_name}.',
                    'old_level': current_level.level_name,
                    'new_level': current_level.level_name
                }
            
            # Progress the student
            old_level_name = current_level.level_name
            student.level = next_level
            student.save()
            
            # Log the progression
            if user:
                # You can add logging here if needed
                pass
            
            return {
                'status': 'progressed',
                'message': f'{student.name} has been progressed from {old_level_name} to {next_level.level_name}.',
                'old_level': old_level_name,
                'new_level': next_level.level_name
            }
            
    except Exception as e:
        return {
            'status': 'error',
            'message': f'Error progressing {student.name}: {str(e)}',
            'old_level': student.level.level_name if student.level else 'Unknown',
            'new_level': None
        }


def progress_all_students_to_new_year(new_academic_year, user=None):
    """
    Progress all active students to the next level for a new academic year.
    
    Args:
        new_academic_year: AcademicYear instance for the new year
        user: User performing the action (optional)
    
    Returns:
        dict: Summary of the progression results
    """
    results = {
        'total_students': 0,
        'progressed': 0,
        'repeated': 0,
        'graduated': 0,
        'errors': 0,
        'details': []
    }
    
    try:
        with transaction.atomic():
            # Get all active students
            active_students = Student.objects.filter(is_active=True)
            results['total_students'] = active_students.count()
            
            for student in active_students:
                result = progress_student_to_next_level(student, new_academic_year, user)
                
                if result['status'] == 'progressed':
                    results['progressed'] += 1
                elif result['status'] == 'repeat':
                    results['repeated'] += 1
                elif result['status'] == 'graduated':
                    results['graduated'] += 1
                else:
                    results['errors'] += 1
                
                results['details'].append({
                    'student': student.name,
                    'student_id': student.student_id,
                    'result': result
                })
            
            return results
            
    except Exception as e:
        results['errors'] = results['total_students']
        results['details'] = [{
            'error': f'Bulk progression failed: {str(e)}'
        }]
        return results


def create_fee_accounts_for_new_year(new_academic_year, user=None):
    """
    Create fee accounts for all students in the new academic year.
    This should be called after student progression.
    
    Args:
        new_academic_year: AcademicYear instance for the new year
        user: User performing the action (optional)
    
    Returns:
        dict: Summary of fee account creation
    """
    from finances.fee_management.models.fee_account import FeeCategory
    
    results = {
        'total_students': 0,
        'accounts_created': 0,
        'errors': 0,
        'details': []
    }
    
    try:
        # Get the first term of the new academic year
        first_term = Term.objects.filter(academic_year=new_academic_year).first()
        
        if not first_term:
            return {
                'total_students': 0,
                'accounts_created': 0,
                'errors': 1,
                'details': [{'error': 'No terms found for the new academic year'}]
            }
        
        # Get all active students
        active_students = Student.objects.filter(is_active=True)
        results['total_students'] = active_students.count()
        
        # Get default fee categories
        fee_categories = FeeCategory.objects.all()
        
        for student in active_students:
            try:
                for category in fee_categories:
                    # Check if fee account already exists
                    existing_account = FeeAccount.objects.filter(
                        student=student,
                        term=first_term,
                        category=category
                    ).first()
                    
                    if not existing_account:
                        # Create new fee account
                        FeeAccount.objects.create(
                            student=student,
                            term=first_term,
                            category=category,
                            total_due=category.default_amount if hasattr(category, 'default_amount') else 0,
                            billing_cycle='Termly'
                        )
                        results['accounts_created'] += 1
                
                results['details'].append({
                    'student': student.name,
                    'student_id': student.student_id,
                    'status': 'success'
                })
                
            except Exception as e:
                results['errors'] += 1
                results['details'].append({
                    'student': student.name,
                    'student_id': student.student_id,
                    'status': 'error',
                    'message': str(e)
                })
        
        return results
        
    except Exception as e:
        results['errors'] = results['total_students']
        results['details'] = [{
            'error': f'Fee account creation failed: {str(e)}'
        }]
        return results


def progress_students_and_setup_new_year(new_academic_year, user=None):
    """
    Complete workflow to progress students and set up the new academic year.
    
    Args:
        new_academic_year: AcademicYear instance for the new year
        user: User performing the action (optional)
    
    Returns:
        dict: Complete summary of the progression and setup
    """
    # Step 1: Progress all students
    progression_results = progress_all_students_to_new_year(new_academic_year, user)
    
    # Step 2: Create fee accounts for the new year
    fee_account_results = create_fee_accounts_for_new_year(new_academic_year, user)
    
    return {
        'academic_year': new_academic_year.name,
        'progression': progression_results,
        'fee_accounts': fee_account_results,
        'summary': {
            'total_students': progression_results['total_students'],
            'progressed': progression_results['progressed'],
            'repeated': progression_results['repeated'],
            'graduated': progression_results['graduated'],
            'progression_errors': progression_results['errors'],
            'fee_accounts_created': fee_account_results['accounts_created'],
            'fee_account_errors': fee_account_results['errors']
        }
    }
