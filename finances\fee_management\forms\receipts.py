from datetime import datetime
import pandas as pd

from django import forms

from finances.fee_management.models import FeeAccount, Receipt
from students.models.student import Student


class ReceiptForm(forms.ModelForm):
    category = forms.ChoiceField(
        choices=[
            ("Tuition Fees", "Tuition Fees"),
            ("Food Fees", "Food Fees"),
        ],
        widget=forms.Select(attrs={'class': 'django-form'}),
    )

    class Meta:
        model = Receipt
        fields = ['amount_paid', 'date']
        widgets = {
            'amount_paid': forms.TextInput(attrs={
                'class': 'django-form',
                'placeholder': 'Amount'
            }),
            'date': forms.DateInput(attrs={
                'class': 'django-form',
                'type': 'date',
            }),
            'category': forms.Select(attrs={
                'class': 'django-form',
                'placeholder': 'Category'
            }),
        }

    def save(self, commit=True, student=None):
        receipt = super().save(commit=False)
        fee_category = self.cleaned_data['category']

        if "Food" in fee_category:
            fee_account = FeeAccount.objects.get(
                student=student, category__name__icontains=fee_category, month__month=receipt.date.month)
        else:
            fee_account = FeeAccount.objects.get(
                student=student, category__name__icontains=fee_category, term__is_active=True
            )
        if commit:
            receipt.student = student
            receipt.fee_account = fee_account

        return receipt


class ExcelPaymentForm(forms.Form):
    excel_file_students = forms.FileField(
        label="Upload Excel File",
        widget=forms.FileInput(attrs={'class': 'django-file'}),
        required=False
    )

    def save(self):
        upload_student_file = self.cleaned_data.get('excel_file_students')

        if upload_student_file:
            df_students = pd.read_excel(upload_student_file)

            for index, row in df_students.iterrows():
                student_id = row['Student ID']

                # Extract and convert dates
                tuition_date = self._convert_to_date(row['Tuition Date'])
                food_date = self._convert_to_date(row['Food Date'])
                pta_date = self._convert_to_date(row['PTA Date'])

                try:
                    student = Student.objects.get(
                        student_id=student_id, is_active=True)
                    tuition_fee_account = self._get_fee_account(
                        student, "Tuition", tuition_date)
                    food_fee_account = self._get_fee_account(
                        student, "Food", food_date)
                    pta_fee_account = self._get_fee_account(
                        student, "PTA", pta_date)

                except Student.DoesNotExist:
                    raise ValueError(
                        f'Student with ID: {student_id} does not exist'
                    )

                self._process_payment(
                    student, tuition_fee_account, row['Tuition Payment'], tuition_date)
                self._process_payment(
                    student, food_fee_account, row['Food Payment'], food_date)
                self._process_payment(
                    student, pta_fee_account, row['PTA'], pta_date)

    def _convert_to_date(self, date_value):
        if pd.notna(date_value) and pd.notnull(date_value):
            return date_value.date()
        return None

    def _get_fee_account(self, student, category_name, date):
        month = date.month if date else datetime.now().month

        if "Food" in category_name:
            return FeeAccount.objects.get(
                student=student, category__name__contains=category_name, term__is_active=True, month__month=month
            )
        else:
            return FeeAccount.objects.get(
                student=student,
                category__name__contains=category_name,
                term__is_active=True,
            )

    def _get_or_create_fee_account(self, student, category_name):
        return FeeAccount.objects.get_or_create(
            student=student,
            category__name__contains=category_name,
            term__is_active=True,
        )[0]

    def _process_payment(self, student, fee_account, amount, date):
        if pd.notna(amount) and pd.notnull(amount):
            current_balance = fee_account.current_balance
            if current_balance > 0:
                receipt = Receipt(
                    student=student,
                    fee_account=fee_account,
                    amount_paid=amount,
                    date=date
                )
                receipt.save()
