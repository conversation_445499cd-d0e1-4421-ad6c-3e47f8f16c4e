from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db import transaction
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.forms import inlineformset_factory

from finances.book_keeping import Budget, BudgetLine, Ledger
from finances.forms.budget_ledger_forms import (
    LedgerForm, BudgetForm, BudgetLineForm, BulkLedgerDeleteForm, 
    BulkBudgetDeleteForm, LedgerSearchForm, BudgetSearchForm
)


@login_required(login_url="accounts:login")
def budget_ledger_management(request):
    """
    Main budget and ledger management dashboard
    """
    # Get search queries
    ledger_search = request.GET.get('ledger_search', '').strip()
    budget_search = request.GET.get('budget_search', '').strip()
    ledger_type_filter = request.GET.get('ledger_type', '')
    term_filter = request.GET.get('term', '')
    
    # Base querysets
    ledgers = Ledger.objects.all()
    budgets = Budget.objects.all().select_related('term')
    
    # Apply ledger filters
    if ledger_search:
        ledgers = ledgers.filter(
            Q(code__icontains=ledger_search) |
            Q(name__icontains=ledger_search) |
            Q(ledger_type__icontains=ledger_search)
        )
    
    if ledger_type_filter:
        ledgers = ledgers.filter(ledger_type=ledger_type_filter)
    
    # Apply budget filters
    if budget_search:
        budgets = budgets.filter(
            Q(term__term_name__icontains=budget_search) |
            Q(term__academic_year__name__icontains=budget_search) |
            Q(description__icontains=budget_search)
        )
    
    if term_filter:
        budgets = budgets.filter(term_id=term_filter)
    
    # Order results
    ledgers = ledgers.order_by('ledger_type', 'code')
    budgets = budgets.order_by('-term__start_date')
    
    # Pagination
    ledger_paginator = Paginator(ledgers, 15)
    budget_paginator = Paginator(budgets, 10)
    
    ledger_page = request.GET.get('ledger_page')
    budget_page = request.GET.get('budget_page')
    
    ledger_page_obj = ledger_paginator.get_page(ledger_page)
    budget_page_obj = budget_paginator.get_page(budget_page)
    
    # Statistics
    ledger_stats = {
        'total_ledgers': Ledger.objects.count(),
        'asset_count': Ledger.objects.filter(ledger_type='Asset').count(),
        'revenue_count': Ledger.objects.filter(ledger_type='Revenue').count(),
        'expense_count': Ledger.objects.filter(ledger_type='Expense').count(),
    }
    
    budget_stats = {
        'total_budgets': Budget.objects.count(),
        'active_budgets': Budget.objects.filter(term__is_active=True).count(),
        'total_budget_lines': BudgetLine.objects.count(),
    }
    
    # Search forms
    ledger_search_form = LedgerSearchForm(initial={
        'search': ledger_search,
        'ledger_type': ledger_type_filter
    })
    
    budget_search_form = BudgetSearchForm(initial={
        'search': budget_search,
        'term': term_filter
    })
    
    context = {
        'ledgers': ledger_page_obj,
        'budgets': budget_page_obj,
        'ledger_search': ledger_search,
        'budget_search': budget_search,
        'ledger_type_filter': ledger_type_filter,
        'term_filter': term_filter,
        'ledger_stats': ledger_stats,
        'budget_stats': budget_stats,
        'ledger_search_form': ledger_search_form,
        'budget_search_form': budget_search_form,
        'ledger_total_count': ledgers.count(),
        'budget_total_count': budgets.count(),
    }
    return render(request, 'finances/budget_ledger_management.html', context)


@login_required(login_url="accounts:login")
def add_ledger(request):
    """
    Add a new ledger account
    """
    if request.method == 'POST':
        form = LedgerForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    ledger = form.save()
                    messages.success(
                        request, 
                        f'Ledger account "{ledger.name}" has been created successfully!'
                    )
                    return redirect('finances:budget_ledger_management')
            except Exception as e:
                messages.error(request, f'Error creating ledger: {str(e)}')
    else:
        form = LedgerForm()
    
    context = {
        'form': form,
        'title': 'Add New Ledger Account',
        'submit_text': 'Create Ledger'
    }
    return render(request, 'finances/ledger_form.html', context)


@login_required(login_url="accounts:login")
def edit_ledger(request, pk):
    """
    Edit an existing ledger account
    """
    ledger = get_object_or_404(Ledger, pk=pk)
    
    if request.method == 'POST':
        form = LedgerForm(request.POST, instance=ledger)
        if form.is_valid():
            try:
                with transaction.atomic():
                    ledger = form.save()
                    messages.success(
                        request, 
                        f'Ledger account "{ledger.name}" has been updated successfully!'
                    )
                    return redirect('finances:budget_ledger_management')
            except Exception as e:
                messages.error(request, f'Error updating ledger: {str(e)}')
    else:
        form = LedgerForm(instance=ledger)
    
    context = {
        'form': form,
        'ledger': ledger,
        'title': f'Edit Ledger: {ledger.name}',
        'submit_text': 'Update Ledger'
    }
    return render(request, 'finances/ledger_form.html', context)


@login_required(login_url="accounts:login")
def add_budget(request):
    """
    Add a new budget
    """
    if request.method == 'POST':
        form = BudgetForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    budget = form.save()
                    messages.success(
                        request, 
                        f'Budget for "{budget.term}" has been created successfully!'
                    )
                    return redirect('finances:budget_details', pk=budget.pk)
            except Exception as e:
                messages.error(request, f'Error creating budget: {str(e)}')
    else:
        form = BudgetForm()
    
    context = {
        'form': form,
        'title': 'Add New Budget',
        'submit_text': 'Create Budget'
    }
    return render(request, 'finances/budget_form.html', context)


@login_required(login_url="accounts:login")
def edit_budget(request, pk):
    """
    Edit an existing budget
    """
    budget = get_object_or_404(Budget, pk=pk)
    
    if request.method == 'POST':
        form = BudgetForm(request.POST, instance=budget)
        if form.is_valid():
            try:
                with transaction.atomic():
                    budget = form.save()
                    messages.success(
                        request, 
                        f'Budget for "{budget.term}" has been updated successfully!'
                    )
                    return redirect('finances:budget_details', pk=budget.pk)
            except Exception as e:
                messages.error(request, f'Error updating budget: {str(e)}')
    else:
        form = BudgetForm(instance=budget)
    
    context = {
        'form': form,
        'budget': budget,
        'title': f'Edit Budget: {budget.term}',
        'submit_text': 'Update Budget'
    }
    return render(request, 'finances/budget_form.html', context)


@login_required(login_url="accounts:login")
def budget_details(request, pk):
    """
    View budget details and manage budget lines
    """
    budget = get_object_or_404(Budget, pk=pk)
    budget_lines = BudgetLine.objects.filter(budget=budget).select_related('account')
    
    # Create formset for budget lines
    BudgetLineFormSet = inlineformset_factory(
        Budget, BudgetLine, form=BudgetLineForm, extra=1, can_delete=True
    )
    
    if request.method == 'POST':
        formset = BudgetLineFormSet(request.POST, instance=budget)
        if formset.is_valid():
            try:
                with transaction.atomic():
                    formset.save()
                    messages.success(request, 'Budget lines have been updated successfully!')
                    return redirect('finances:budget_details', pk=budget.pk)
            except Exception as e:
                messages.error(request, f'Error updating budget lines: {str(e)}')
    else:
        formset = BudgetLineFormSet(instance=budget)
    
    # Calculate totals
    total_budget = budget_lines.aggregate(total=Sum('amount'))['total'] or 0
    
    context = {
        'budget': budget,
        'budget_lines': budget_lines,
        'formset': formset,
        'total_budget': total_budget,
    }
    return render(request, 'finances/budget_details.html', context)


@login_required(login_url="accounts:login")
def bulk_delete_ledgers(request):
    """
    Bulk delete ledgers
    """
    if request.method == 'POST':
        form = BulkLedgerDeleteForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    ledgers = form.cleaned_data['ledgers']
                    count = ledgers.count()
                    ledgers.delete()
                    messages.success(
                        request, 
                        f'{count} ledger account{"s" if count != 1 else ""} deleted successfully!'
                    )
                    return redirect('finances:budget_ledger_management')
            except Exception as e:
                messages.error(request, f'Error deleting ledgers: {str(e)}')
    else:
        form = BulkLedgerDeleteForm()
    
    context = {
        'form': form,
        'title': 'Delete Ledger Accounts',
        'submit_text': 'Delete Selected Ledgers'
    }
    return render(request, 'finances/bulk_delete_ledgers.html', context)


@login_required(login_url="accounts:login")
def bulk_delete_budgets(request):
    """
    Bulk delete budgets
    """
    if request.method == 'POST':
        form = BulkBudgetDeleteForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    budgets = form.cleaned_data['budgets']
                    count = budgets.count()
                    budgets.delete()
                    messages.success(
                        request, 
                        f'{count} budget{"s" if count != 1 else ""} deleted successfully!'
                    )
                    return redirect('finances:budget_ledger_management')
            except Exception as e:
                messages.error(request, f'Error deleting budgets: {str(e)}')
    else:
        form = BulkBudgetDeleteForm()
    
    context = {
        'form': form,
        'title': 'Delete Budgets',
        'submit_text': 'Delete Selected Budgets'
    }
    return render(request, 'finances/bulk_delete_budgets.html', context)


@login_required(login_url="accounts:login")
def ledger_details(request, pk):
    """
    View ledger account details and transactions
    """
    ledger = get_object_or_404(Ledger, pk=pk)
    
    # Get recent transactions
    from finances.book_keeping.ledger import JournalLine
    recent_transactions = JournalLine.objects.filter(
        account=ledger
    ).select_related('journal_entry').order_by('-journal_entry__date')[:20]
    
    # Calculate balance
    total_amount = ledger.total_amount
    
    context = {
        'ledger': ledger,
        'recent_transactions': recent_transactions,
        'total_amount': total_amount,
    }
    return render(request, 'finances/ledger_details.html', context)
