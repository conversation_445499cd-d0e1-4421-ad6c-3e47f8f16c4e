# Generated by Django 5.1.2 on 2025-07-05 11:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_alter_customuser_options_alter_customuser_table_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Unique permission name (e.g., 'view_student', 'edit_finance')", max_length=100, unique=True)),
                ('display_name', models.CharField(help_text='Human-readable permission name', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Detailed description of what this permission allows')),
                ('category', models.CharField(help_text="Permission category (e.g., 'students', 'finances', 'academics')", max_length=50)),
                ('is_active', models.<PERSON>oleanField(default=True, help_text='Whether this permission is currently active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Permission',
                'verbose_name_plural': 'Permissions',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Unique role name', max_length=100, unique=True)),
                ('display_name', models.CharField(help_text='Human-readable role name', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Description of the role and its responsibilities')),
                ('level', models.IntegerField(choices=[(1, 'Student'), (2, 'Teacher'), (3, 'Academic Coordinator'), (4, 'Finance Manager'), (5, 'Administrator'), (6, 'Super Administrator')], default=1, help_text='Role hierarchy level (higher numbers have more privileges)')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this role is currently active')),
                ('is_default', models.BooleanField(default=False, help_text='Whether this is a default role for new users')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('permissions', models.ManyToManyField(blank=True, help_text='Permissions granted to this role', related_name='roles', to='accounts.permission')),
            ],
            options={
                'verbose_name': 'Role',
                'verbose_name_plural': 'Roles',
                'ordering': ['level', 'name'],
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this role assignment is currently active')),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='When this role assignment expires (optional)', null=True)),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this role assignment')),
                ('assigned_by', models.ForeignKey(blank=True, help_text='User who assigned this role', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='role_assignments_made', to=settings.AUTH_USER_MODEL)),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_assignments', to='accounts.role')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Role Assignment',
                'verbose_name_plural': 'User Role Assignments',
                'ordering': ['-assigned_at'],
                'unique_together': {('user', 'role')},
            },
        ),
    ]
