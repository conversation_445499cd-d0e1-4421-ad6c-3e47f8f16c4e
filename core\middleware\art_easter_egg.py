"""
Art Easter Egg Middleware
Detects art/creativity-related search terms and redirects to the art easter egg page
"""

from django.shortcuts import redirect
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin
from urllib.parse import urlencode

from core.art_quotes import is_art_search


class ArtEasterEggMiddleware(MiddlewareMixin):
    """
    Middleware to detect art/creativity search terms and redirect to art easter egg
    """
    
    def process_request(self, request):
        """
        Check if the request contains art/creativity search terms
        """
        # Skip if already on any easter egg page
        if any(request.path.startswith(path) for path in [
            '/core/artistic-moment/', 
            '/core/cosmic-moment/', 
            '/core/philosophical-moment/', 
            '/core/musical-moment/',
            '/coffee/', 
            '/teapot/'
        ]):
            return None
        
        # Skip for admin, API, and static files
        if any(request.path.startswith(path) for path in ['/admin/', '/api/', '/static/', '/media/']):
            return None
        
        # Check GET parameters for search terms
        search_params = [
            'search', 'q', 'query', 'search_query', 'term', 
            'subject_search', 'assessment_search', 'activity_search',
            'student_search', 'teacher_search', 'finance_search'
        ]
        
        for param in search_params:
            search_term = request.GET.get(param, '').strip()
            if search_term and is_art_search(search_term):
                # Redirect to art easter egg with the search term
                easter_egg_url = reverse('core:art_easter_egg')
                query_params = urlencode({'search': search_term})
                return redirect(f'{easter_egg_url}?{query_params}')
        
        # Check POST data for search terms (for forms)
        if request.method == 'POST':
            for param in search_params:
                search_term = request.POST.get(param, '').strip()
                if search_term and is_art_search(search_term):
                    # For POST requests, redirect with GET parameters
                    easter_egg_url = reverse('core:art_easter_egg')
                    query_params = urlencode({'search': search_term})
                    return redirect(f'{easter_egg_url}?{query_params}')
        
        return None
