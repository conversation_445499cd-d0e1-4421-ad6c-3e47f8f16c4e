from django import template
from django.contrib.auth import get_user_model

register = template.Library()
User = get_user_model()


@register.filter
def has_permission(user, permission_name):
    """
    Template filter to check if user has a specific permission.
    
    Usage in templates:
    {% if user|has_permission:"view_students" %}
        <!-- Show content for users with view_students permission -->
    {% endif %}
    """
    if not user.is_authenticated:
        return False
    return user.has_permission(permission_name)


@register.filter
def has_role(user, role_name):
    """
    Template filter to check if user has a specific role.
    
    Usage in templates:
    {% if user|has_role:"teacher" %}
        <!-- Show content for teachers -->
    {% endif %}
    """
    if not user.is_authenticated:
        return False
    return user.has_role(role_name)


@register.filter
def role_level_at_least(user, min_level):
    """
    Template filter to check if user has at least the specified role level.
    
    Usage in templates:
    {% if user|role_level_at_least:3 %}
        <!-- Show content for users with role level 3 or higher -->
    {% endif %}
    """
    if not user.is_authenticated:
        return False
    try:
        min_level = int(min_level)
        return user.get_highest_role_level() >= min_level
    except (ValueError, TypeError):
        return False


@register.simple_tag
def user_permissions(user):
    """
    Template tag to get all user permissions.
    
    Usage in templates:
    {% user_permissions user as permissions %}
    {% for permission in permissions %}
        <li>{{ permission.display_name }}</li>
    {% endfor %}
    """
    if not user.is_authenticated:
        return []
    return user.get_all_permissions()


@register.simple_tag
def user_roles(user):
    """
    Template tag to get all user roles.
    
    Usage in templates:
    {% user_roles user as roles %}
    {% for role in roles %}
        <li>{{ role.role.display_name }}</li>
    {% endfor %}
    """
    if not user.is_authenticated:
        return []
    return user.get_active_roles()


@register.inclusion_tag('accounts/rbac_debug.html')
def rbac_debug(user):
    """
    Inclusion tag to show RBAC debug information.
    Only shows in DEBUG mode.
    
    Usage in templates:
    {% rbac_debug user %}
    """
    from django.conf import settings
    
    if not settings.DEBUG or not user.is_authenticated:
        return {'show_debug': False}
    
    return {
        'show_debug': True,
        'user': user,
        'roles': user.get_active_roles(),
        'permissions': user.get_all_permissions(),
        'role_level': user.get_highest_role_level(),
    }


@register.simple_tag
def can_manage_students(user):
    """Check if user can manage students"""
    if not user.is_authenticated:
        return False
    return user.can_manage_students()


@register.simple_tag
def can_manage_finances(user):
    """Check if user can manage finances"""
    if not user.is_authenticated:
        return False
    return user.can_manage_finances()


@register.simple_tag
def can_manage_academics(user):
    """Check if user can manage academics"""
    if not user.is_authenticated:
        return False
    return user.can_manage_academics()


@register.filter
def has_any_permission(user, permission_list):
    """
    Template filter to check if user has any of the specified permissions.
    
    Usage in templates:
    {% if user|has_any_permission:"view_students,edit_students,manage_students" %}
        <!-- Show content for users with any of these permissions -->
    {% endif %}
    """
    if not user.is_authenticated:
        return False
    
    if isinstance(permission_list, str):
        permissions = [p.strip() for p in permission_list.split(',')]
    else:
        permissions = permission_list
    
    return any(user.has_permission(perm) for perm in permissions)


@register.filter
def has_any_role(user, role_list):
    """
    Template filter to check if user has any of the specified roles.
    
    Usage in templates:
    {% if user|has_any_role:"teacher,administrator" %}
        <!-- Show content for teachers or administrators -->
    {% endif %}
    """
    if not user.is_authenticated:
        return False
    
    if isinstance(role_list, str):
        roles = [r.strip() for r in role_list.split(',')]
    else:
        roles = role_list
    
    return any(user.has_role(role) for role in roles)


@register.simple_tag(takes_context=True)
def rbac_context(context):
    """
    Add RBAC context variables to template context.
    
    Usage in templates:
    {% rbac_context %}
    <!-- Now you can use variables like can_manage_students, user_role_level, etc. -->
    """
    request = context.get('request')
    if not request or not request.user.is_authenticated:
        return ''
    
    user = request.user
    context.update({
        'user_roles': user.get_active_roles(),
        'user_permissions': user.get_all_permissions(),
        'user_role_level': user.get_highest_role_level(),
        'is_student': user.is_student(),
        'is_teacher': user.is_teacher(),
        'is_admin': user.is_admin(),
        'can_manage_students': user.can_manage_students(),
        'can_manage_finances': user.can_manage_finances(),
        'can_manage_academics': user.can_manage_academics(),
    })
    
    return ''
