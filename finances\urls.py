from django.urls import path, include

# Import existing views from main views.py
from finances.views import (
    add_expenditure, expenditure_detail, expenditure_list, recalculate_expenditure,
    reverse_outflow, reverse_receipt, reversal_list, reversal_detail
)

# Import budget and ledger views from view_modules directory
from .view_modules.budget_ledger_views import (
    budget_ledger_management, add_ledger, edit_ledger, add_budget,
    edit_budget, budget_details, bulk_delete_ledgers, bulk_delete_budgets,
    ledger_details
)

app_name = "finances"

urlpatterns = [
    path('reports/', include('finances.reports.urls')),
    path('fee/', include('finances.fee_management.urls')),
    path('', expenditure_list, name="expenditures"),
    path('<slug:slug>', expenditure_detail, name="expenditure_detail"),
    path('add-expenditure/', add_expenditure, name="add_expenditure"),
    path('administration/rework-expenditure',
         recalculate_expenditure, name="recalculate_expenditure"),

    # Reversal URLs
    path('reverse-outflow/<slug:slug>/',
         reverse_outflow, name="reverse_outflow"),
    path('reverse-receipt/<slug:slug>/',
         reverse_receipt, name="reverse_receipt"),
    path('reversals/', reversal_list, name="reversal_list"),
    path('reversals/<str:voucher>/', reversal_detail, name="reversal_detail"),

    # Budget and Ledger Management URLs
    path('budget-ledger/', budget_ledger_management, name="budget_ledger_management"),

    # Ledger URLs
    path('ledgers/add/', add_ledger, name="add_ledger"),
    path('ledgers/<int:pk>/edit/', edit_ledger, name="edit_ledger"),
    path('ledgers/<int:pk>/details/', ledger_details, name="ledger_details"),
    path('ledgers/bulk-delete/', bulk_delete_ledgers, name="bulk_delete_ledgers"),

    # Budget URLs
    path('budgets/add/', add_budget, name="add_budget"),
    path('budgets/<int:pk>/edit/', edit_budget, name="edit_budget"),
    path('budgets/<int:pk>/details/', budget_details, name="budget_details"),
    path('budgets/bulk-delete/', bulk_delete_budgets, name="bulk_delete_budgets"),
]
