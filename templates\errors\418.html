{% load static %}
<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>I'm a Teapot - Tiny Feet Academy</title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
    />

    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-color: #7ab2d3;
        --primary-dark: #5a9bd4;
        --gradient-primary: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--primary-dark) 100%
        );
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .font-display {
        font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .floating {
        animation: float 6s ease-in-out infinite;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        33% {
          transform: translateY(-15px) rotate(2deg);
        }
        66% {
          transform: translateY(-5px) rotate(-2deg);
        }
      }

      .teapot-steam {
        animation: steam 3s ease-in-out infinite;
      }

      @keyframes steam {
        0%,
        100% {
          opacity: 0.6;
          transform: translateY(0px) scale(1);
        }
        50% {
          opacity: 0.9;
          transform: translateY(-20px) scale(1.1);
        }
      }

      .pulse-tea {
        animation: pulse-tea 4s ease-in-out infinite;
      }

      @keyframes pulse-tea {
        0%,
        100% {
          box-shadow: 0 0 20px rgba(139, 69, 19, 0.3);
        }
        50% {
          box-shadow: 0 0 40px rgba(139, 69, 19, 0.6);
        }
      }

      .slide-in {
        animation: slideIn 0.8s ease-out forwards;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .wiggle {
        animation: wiggle 2s ease-in-out infinite;
      }

      @keyframes wiggle {
        0%,
        100% {
          transform: rotate(0deg);
        }
        25% {
          transform: rotate(5deg);
        }
        75% {
          transform: rotate(-5deg);
        }
      }
    </style>
  </head>
  <body
    class="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 flex flex-col items-center justify-center p-4"
  >
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- Steam clouds -->
      <div
        class="absolute top-20 left-1/4 w-16 h-16 bg-white opacity-20 rounded-full teapot-steam"
      ></div>
      <div
        class="absolute top-32 right-1/3 w-12 h-12 bg-white opacity-15 rounded-full teapot-steam"
        style="animation-delay: -1s"
      ></div>
      <div
        class="absolute top-16 right-1/4 w-20 h-20 bg-white opacity-10 rounded-full teapot-steam"
        style="animation-delay: -2s"
      ></div>

      <!-- Floating tea elements -->
      <div
        class="absolute -top-40 -right-40 w-80 h-80 bg-amber-100 opacity-20 rounded-full floating"
      ></div>
      <div
        class="absolute -bottom-40 -left-40 w-96 h-96 bg-orange-100 opacity-15 rounded-full floating"
        style="animation-delay: -3s"
      ></div>
      <div
        class="absolute top-1/2 left-1/4 w-32 h-32 bg-yellow-100 opacity-25 rounded-full floating"
        style="animation-delay: -1s"
      ></div>
    </div>

    <!-- Error Content -->
    <div class="relative z-10 max-w-2xl mx-auto text-center slide-in">
      <!-- Teapot Icon -->
      <div class="mb-8">
        <div
          class="w-32 h-32 mx-auto bg-gradient-to-br from-amber-600 to-orange-700 rounded-full flex items-center justify-center shadow-2xl pulse-tea relative overflow-hidden"
        >
          <!-- Teapot body -->
          <div
            class="absolute inset-0 bg-gradient-to-br from-amber-500/20 to-transparent rounded-full"
          ></div>
          <div class="relative z-10 wiggle">
            <i class="fas fa-coffee text-white text-4xl"></i>
          </div>
          <!-- Steam effect -->
          <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
            <div
              class="w-2 h-6 bg-white opacity-60 rounded-full teapot-steam"
            ></div>
            <div
              class="w-1 h-4 bg-white opacity-40 rounded-full ml-1 teapot-steam"
              style="animation-delay: -0.5s"
            ></div>
            <div
              class="w-1.5 h-5 bg-white opacity-50 rounded-full -ml-2 teapot-steam"
              style="animation-delay: -1s"
            ></div>
          </div>
        </div>
      </div>

      <!-- Error Code -->
      <div class="mb-6">
        <h1
          class="font-display font-bold text-8xl md:text-9xl text-gray-800 mb-2"
        >
          418
        </h1>
        <div
          class="w-24 h-1 bg-gradient-to-r from-amber-600 to-orange-700 rounded-full mx-auto"
        ></div>
      </div>

      <!-- Error Message -->
      <div class="mb-8">
        <h2
          class="font-display font-bold text-2xl md:text-3xl text-gray-800 mb-4"
        >
          I'm a Teapot! ☕
        </h2>
        <p class="text-gray-600 text-lg leading-relaxed max-w-lg mx-auto mb-4">
          Well, this is awkward... You've stumbled upon the most famous joke in
          HTTP history! Our server is politely declining to brew coffee because,
          well, it's a teapot. And teapots have standards, you know!
        </p>
        <div
          class="bg-amber-50 border-l-4 border-amber-400 p-4 rounded-r-xl max-w-md mx-auto"
        >
          <p class="text-amber-800 text-sm font-medium">
            <i class="fas fa-lightbulb mr-2"></i>
            <strong>Fun Fact:</strong> HTTP 418 "I'm a teapot" was created as an
            April Fools' joke in 1998! It's like the class clown of HTTP status
            codes - not very useful, but everyone loves it! 🎭
          </p>
        </div>
      </div>

      <!-- Tea Time Message -->
      <div
        class="mb-8 p-6 bg-gradient-to-r from-amber-100 to-orange-100 rounded-2xl border border-amber-200 shadow-lg"
      >
        <h3
          class="font-semibold text-amber-800 mb-3 flex items-center justify-center gap-2"
        >
          <i class="fas fa-clock text-amber-600"></i>
          It's Tea Time!
        </h3>
        <p class="text-amber-700 text-sm leading-relaxed">
          Since you're here, why not take a moment to appreciate this
          delightfully useless error code? It's a reminder that even in the
          serious world of web development, there's always room for a little
          humor. Maybe grab some actual tea while you're at it! 🫖
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <a
          href="/"
          class="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] text-white font-semibold py-3 px-8 rounded-xl hover:from-[var(--primary-dark)] hover:to-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-home mr-2"></i>
          Go Home
        </a>
        <button
          onclick="history.back()"
          class="bg-white text-gray-700 font-semibold py-3 px-8 rounded-xl border-2 border-gray-200 hover:border-amber-500 hover:text-amber-600 focus:ring-4 focus:ring-amber-500/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-arrow-left mr-2"></i>
          Go Back
        </button>
        <button
          onclick="location.reload()"
          class="bg-gradient-to-r from-amber-500 to-orange-600 text-white font-semibold py-3 px-8 rounded-xl hover:from-orange-600 hover:to-amber-500 focus:ring-4 focus:ring-amber-500/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-coffee mr-2"></i>
          Try Coffee Instead?
        </button>
      </div>

      <!-- Developer Easter Egg -->
      <div
        class="mt-12 p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-amber-200/50 shadow-lg"
      >
        <h3
          class="font-semibold text-gray-800 mb-3 flex items-center justify-center gap-2"
        >
          <i class="fas fa-code text-amber-600"></i>
          Developer's Secret
        </h3>
        <p class="text-sm text-gray-600 leading-relaxed mb-3">
          Congratulations! You've found one of the internet's most beloved
          easter eggs. RFC 2324 (Hyper Text Coffee Pot Control Protocol) was
          published on April 1st, 1998, and HTTP 418 has been making developers
          smile ever since.
        </p>
        <div
          class="flex items-center justify-center gap-4 text-xs text-gray-500"
        >
          <span class="flex items-center gap-1">
            <i class="fas fa-calendar"></i>
            Since: April 1, 1998
          </span>
          <span class="flex items-center gap-1">
            <i class="fas fa-heart text-red-400"></i>
            Status: Still Beloved
          </span>
          <span class="flex items-center gap-1">
            <i class="fas fa-coffee"></i>
            Coffee Level: Zero
          </span>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="mt-8 text-center">
      <p class="text-sm text-gray-500 flex items-center justify-center gap-2">
        <i class="fas fa-graduation-cap text-[var(--primary-color)]"></i>
        Tiny Feet Academy MIS - Now with 100% more tea! 🫖
      </p>
    </footer>

    <!-- Floating tea cups -->
    <div class="fixed bottom-10 right-10 text-4xl opacity-20 floating">☕</div>
    <div
      class="fixed top-20 left-10 text-3xl opacity-15 floating"
      style="animation-delay: -2s"
    >
      🫖
    </div>
    <div
      class="fixed top-1/2 right-20 text-2xl opacity-10 floating"
      style="animation-delay: -4s"
    >
      🍵
    </div>
  </body>
</html>
