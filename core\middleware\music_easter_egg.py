"""
Music Easter Egg Middleware
Detects music-related search terms and redirects to the music easter egg page
"""

from django.shortcuts import redirect
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin
from urllib.parse import urlencode

from core.music_quotes import is_music_search


class MusicEasterEggMiddleware(MiddlewareMixin):
    """
    Middleware to detect music search terms and redirect to music easter egg
    """
    
    def process_request(self, request):
        """
        Check if the request contains music search terms
        """
        # Skip if already on the music easter egg page
        if request.path.startswith('/core/musical-moment/'):
            return None
        
        # Skip for admin, API, and static files
        if any(request.path.startswith(path) for path in ['/admin/', '/api/', '/static/', '/media/']):
            return None
        
        # Check GET parameters for search terms
        search_params = [
            'search', 'q', 'query', 'search_query', 'term', 
            'subject_search', 'assessment_search', 'activity_search',
            'student_search', 'teacher_search', 'finance_search'
        ]
        
        for param in search_params:
            search_term = request.GET.get(param, '').strip()
            if search_term and is_music_search(search_term):
                # Redirect to music easter egg with the search term
                easter_egg_url = reverse('core:music_easter_egg')
                query_params = urlencode({'search': search_term})
                return redirect(f'{easter_egg_url}?{query_params}')
        
        # Check POST data for search terms (for forms)
        if request.method == 'POST':
            for param in search_params:
                search_term = request.POST.get(param, '').strip()
                if search_term and is_music_search(search_term):
                    # For POST requests, redirect with GET parameters
                    easter_egg_url = reverse('core:music_easter_egg')
                    query_params = urlencode({'search': search_term})
                    return redirect(f'{easter_egg_url}?{query_params}')
        
        return None
