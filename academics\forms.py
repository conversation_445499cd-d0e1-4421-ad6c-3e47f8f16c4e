from django import forms


class StudentMarkForm(forms.Form):
    student_id = forms.IntegerField(widget=forms.HiddenInput())
    name = forms.CharField(
        required=False,
        disabled=True,
        widget=forms.TextInput(
            attrs={
                'class': 'w-full px-3 py-1 text-sm text-gray-700'
            }
        )
    )
    marks = forms.IntegerField(
        min_value=0, max_value=100, required=True,
        widget=forms.NumberInput(attrs={
            'placeholder': 'Enter grade',
            'class': 'w-40 px-3 py-1 border border-[#cbd5e1] rounded focus:ring-2 focus:ring-[#557d94] focus:outline-none transition'
        })
    )
