# Testing Guide

## 🧪 Overview

This guide provides comprehensive instructions for testing the Receipt Generator system, including unit tests, integration tests, and best practices for maintaining code quality.

## 🏗️ Testing Architecture

### Testing Pyramid
```
    /\
   /  \     E2E Tests (Few)
  /____\    
 /      \   Integration Tests (Some)
/__________\ Unit Tests (Many)
```

### Test Types
- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test component interactions
- **Model Tests**: Test database models and relationships
- **View Tests**: Test HTTP responses and templates
- **Form Tests**: Test form validation and processing
- **API Tests**: Test API endpoints and responses

## 🛠️ Test Setup

### Test Configuration
```python
# settings/test.py
from .base import *

# Test database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

# Disable migrations for faster tests
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Test-specific settings
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',  # Faster for tests
]

EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# Disable logging during tests
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'root': {
        'handlers': ['null'],
    },
}
```

### Test Factories
```python
# tests/factories.py
import factory
from django.contrib.auth import get_user_model
from students.models import Student, Level, AcademicYear, Term
from finances.models import FeeAccount, Receipt

User = get_user_model()

class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User
    
    username = factory.Sequence(lambda n: f'user{n}')
    email = factory.LazyAttribute(lambda obj: f'{obj.username}@example.com')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    is_active = True

class LevelFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Level
    
    name = factory.Sequence(lambda n: f'Level {n}')
    abbrv = factory.Sequence(lambda n: f'L{n}')
    is_active = True

class AcademicYearFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = AcademicYear
    
    name = factory.Sequence(lambda n: f'Academic Year {2020 + n}')
    start_date = factory.Faker('date_this_year')
    end_date = factory.LazyAttribute(
        lambda obj: obj.start_date.replace(year=obj.start_date.year + 1)
    )
    is_active = False

class TermFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Term
    
    name = factory.Sequence(lambda n: f'Term {n}')
    academic_year = factory.SubFactory(AcademicYearFactory)
    start_date = factory.Faker('date_this_year')
    end_date = factory.LazyAttribute(
        lambda obj: obj.start_date.replace(month=obj.start_date.month + 3)
    )
    is_active = False

class StudentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Student
    
    name = factory.Faker('name')
    gender = factory.Iterator(['Male', 'Female'])
    level = factory.SubFactory(LevelFactory)
    is_active = True

class FeeAccountFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = FeeAccount
    
    student = factory.SubFactory(StudentFactory)
    term = factory.SubFactory(TermFactory)
    amount_due = factory.Faker('random_int', min=10000, max=50000)
    amount_paid = 0

class ReceiptFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Receipt
    
    student = factory.SubFactory(StudentFactory)
    fee_account = factory.SubFactory(FeeAccountFactory)
    amount_paid = factory.Faker('random_int', min=1000, max=10000)
    date = factory.Faker('date_this_year')
```

## 📋 Model Testing

### Student Model Tests
```python
# students/tests/test_models.py
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from students.models import Student, Level
from tests.factories import StudentFactory, LevelFactory

class StudentModelTest(TestCase):
    def setUp(self):
        self.level = LevelFactory()
    
    def test_student_creation(self):
        """Test basic student creation"""
        student = StudentFactory(level=self.level)
        
        self.assertIsNotNone(student.id)
        self.assertIsNotNone(student.student_id)
        self.assertIsNotNone(student.slug)
        self.assertTrue(student.is_active)
    
    def test_student_id_generation(self):
        """Test automatic student ID generation"""
        student = StudentFactory(level=self.level)
        
        self.assertTrue(student.student_id.startswith(self.level.abbrv))
        self.assertRegex(student.student_id, rf'^{self.level.abbrv}\d+$')
    
    def test_student_id_uniqueness(self):
        """Test student ID uniqueness"""
        student1 = StudentFactory(level=self.level)
        student2 = StudentFactory(level=self.level)
        
        self.assertNotEqual(student1.student_id, student2.student_id)
    
    def test_slug_generation(self):
        """Test automatic slug generation"""
        student = StudentFactory(name="John Doe", level=self.level)
        
        self.assertIsNotNone(student.slug)
        self.assertIn('john', student.slug.lower())
    
    def test_fee_account_creation_on_activation(self):
        """Test fee account creation when student is activated"""
        from students.models import Term
        from finances.models import FeeAccount
        
        # Create active term
        term = TermFactory(is_active=True)
        
        # Create student (should trigger fee account creation)
        student = StudentFactory(level=self.level, is_active=True)
        
        # Check fee account was created
        fee_account = FeeAccount.objects.filter(
            student=student, term=term
        ).first()
        self.assertIsNotNone(fee_account)
    
    def test_string_representation(self):
        """Test string representation"""
        student = StudentFactory(name="Jane Smith", level=self.level)
        self.assertEqual(str(student), "Jane Smith")

class LevelModelTest(TestCase):
    def test_level_creation(self):
        """Test basic level creation"""
        level = LevelFactory()
        
        self.assertIsNotNone(level.id)
        self.assertIsNotNone(level.slug)
        self.assertTrue(level.is_active)
    
    def test_abbreviation_uniqueness(self):
        """Test level abbreviation uniqueness"""
        LevelFactory(abbrv="PRI")
        
        with self.assertRaises(IntegrityError):
            LevelFactory(abbrv="PRI")
```

### Receipt Model Tests
```python
# finances/tests/test_models.py
from django.test import TestCase
from decimal import Decimal

from finances.models import Receipt, FeeAccount
from tests.factories import ReceiptFactory, FeeAccountFactory, StudentFactory

class ReceiptModelTest(TestCase):
    def setUp(self):
        self.student = StudentFactory()
        self.fee_account = FeeAccountFactory(
            student=self.student,
            amount_due=10000,
            amount_paid=0
        )
    
    def test_receipt_creation(self):
        """Test basic receipt creation"""
        receipt = ReceiptFactory(
            student=self.student,
            fee_account=self.fee_account,
            amount_paid=5000
        )
        
        self.assertIsNotNone(receipt.id)
        self.assertIsNotNone(receipt.receipt_number)
        self.assertIsNotNone(receipt.slug)
    
    def test_receipt_number_generation(self):
        """Test automatic receipt number generation"""
        receipt = ReceiptFactory(
            student=self.student,
            fee_account=self.fee_account
        )
        
        expected_prefix = f"REC{self.student.level.abbrv}"
        self.assertTrue(receipt.receipt_number.startswith(expected_prefix))
    
    def test_receipt_number_uniqueness(self):
        """Test receipt number uniqueness"""
        receipt1 = ReceiptFactory(
            student=self.student,
            fee_account=self.fee_account
        )
        receipt2 = ReceiptFactory(
            student=self.student,
            fee_account=self.fee_account
        )
        
        self.assertNotEqual(receipt1.receipt_number, receipt2.receipt_number)
    
    def test_fee_account_update_on_receipt_save(self):
        """Test fee account amount_paid update when receipt is saved"""
        initial_paid = self.fee_account.amount_paid
        
        receipt = ReceiptFactory(
            student=self.student,
            fee_account=self.fee_account,
            amount_paid=3000
        )
        
        # Refresh fee account from database
        self.fee_account.refresh_from_db()
        
        self.assertEqual(
            self.fee_account.amount_paid,
            initial_paid + receipt.amount_paid
        )
```

## 🌐 View Testing

### Function-Based View Tests
```python
# students/tests/test_views.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model

from tests.factories import UserFactory, StudentFactory, LevelFactory

User = get_user_model()

class StudentViewTest(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.client = Client()
        self.client.login(username=self.user.username, password='password')
        
        self.level = LevelFactory()
        self.student = StudentFactory(level=self.level)
    
    def test_student_list_view(self):
        """Test student list view"""
        response = self.client.get(reverse('students:student_list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.student.name)
        self.assertContains(response, 'Students')
    
    def test_student_detail_view(self):
        """Test student detail view"""
        response = self.client.get(
            reverse('students:student_detail', kwargs={'slug': self.student.slug})
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.student.name)
        self.assertContains(response, self.student.student_id)
    
    def test_student_create_view_get(self):
        """Test student create view GET request"""
        response = self.client.get(reverse('students:student_create'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create Student')
    
    def test_student_create_view_post(self):
        """Test student create view POST request"""
        data = {
            'name': 'New Student',
            'gender': 'Female',
            'level': self.level.id,
            'is_active': True
        }
        
        response = self.client.post(reverse('students:student_create'), data)
        
        self.assertEqual(response.status_code, 302)  # Redirect after creation
        
        # Check student was created
        from students.models import Student
        student = Student.objects.filter(name='New Student').first()
        self.assertIsNotNone(student)
        self.assertEqual(student.gender, 'Female')
    
    def test_unauthorized_access(self):
        """Test unauthorized access redirects to login"""
        self.client.logout()
        
        response = self.client.get(reverse('students:student_list'))
        
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)
```

### Class-Based View Tests
```python
# finances/tests/test_views.py
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model

from tests.factories import UserFactory, ReceiptFactory, FeeAccountFactory

User = get_user_model()

class ReceiptViewTest(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.client.force_login(self.user)
        
        self.fee_account = FeeAccountFactory()
        self.receipt = ReceiptFactory(fee_account=self.fee_account)
    
    def test_receipt_list_view(self):
        """Test receipt list view"""
        response = self.client.get(reverse('finances:receipt_list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.receipt.receipt_number)
    
    def test_receipt_search(self):
        """Test receipt search functionality"""
        response = self.client.get(
            reverse('finances:receipt_list'),
            {'search': self.receipt.student.name}
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.receipt.receipt_number)
    
    def test_receipt_pagination(self):
        """Test receipt pagination"""
        # Create multiple receipts
        ReceiptFactory.create_batch(30, fee_account=self.fee_account)
        
        response = self.client.get(reverse('finances:receipt_list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'page')
        
        # Test second page
        response = self.client.get(
            reverse('finances:receipt_list'),
            {'page': 2}
        )
        self.assertEqual(response.status_code, 200)
```

## 📝 Form Testing

```python
# students/tests/test_forms.py
from django.test import TestCase

from students.forms import StudentForm
from tests.factories import LevelFactory

class StudentFormTest(TestCase):
    def setUp(self):
        self.level = LevelFactory()
    
    def test_valid_form(self):
        """Test form with valid data"""
        form_data = {
            'name': 'Test Student',
            'gender': 'Male',
            'level': self.level.id,
            'is_active': True
        }
        
        form = StudentForm(data=form_data)
        
        self.assertTrue(form.is_valid())
    
    def test_missing_required_fields(self):
        """Test form with missing required fields"""
        form_data = {
            'gender': 'Male',
        }
        
        form = StudentForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
        self.assertIn('level', form.errors)
    
    def test_invalid_gender(self):
        """Test form with invalid gender"""
        form_data = {
            'name': 'Test Student',
            'gender': 'Invalid',
            'level': self.level.id,
        }
        
        form = StudentForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('gender', form.errors)
    
    def test_duplicate_name_validation(self):
        """Test custom validation for duplicate names"""
        from students.models import Student
        
        # Create existing student
        Student.objects.create(
            name='Existing Student',
            level=self.level
        )
        
        form_data = {
            'name': 'Existing Student',
            'gender': 'Male',
            'level': self.level.id,
        }
        
        form = StudentForm(data=form_data)
        
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
```

## 🔌 API Testing

```python
# api/tests/test_api.py
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
import json

from tests.factories import UserFactory, StudentFactory, LevelFactory

User = get_user_model()

class StudentAPITest(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.client.force_login(self.user)
        
        self.level = LevelFactory()
        self.student = StudentFactory(level=self.level)
    
    def test_get_student_list(self):
        """Test GET /api/v1/students/"""
        response = self.client.get('/api/v1/students/')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertEqual(data['status'], 'success')
        self.assertIn('data', data)
        self.assertIn('pagination', data.get('meta', {}))
    
    def test_get_student_detail(self):
        """Test GET /api/v1/students/{id}/"""
        response = self.client.get(f'/api/v1/students/{self.student.id}/')
        
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['data']['name'], self.student.name)
    
    def test_create_student(self):
        """Test POST /api/v1/students/"""
        student_data = {
            'name': 'API Test Student',
            'gender': 'Female',
            'level_id': self.level.id
        }
        
        response = self.client.post(
            '/api/v1/students/',
            json.dumps(student_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        
        data = json.loads(response.content)
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['data']['name'], 'API Test Student')
    
    def test_api_authentication_required(self):
        """Test API requires authentication"""
        self.client.logout()
        
        response = self.client.get('/api/v1/students/')
        
        self.assertEqual(response.status_code, 401)
        
        data = json.loads(response.content)
        self.assertEqual(data['status'], 'error')
        self.assertEqual(data['error']['code'], 'AUTHENTICATION_REQUIRED')
```

## 🚀 Running Tests

### Basic Test Commands
```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test students

# Run specific test class
python manage.py test students.tests.test_models.StudentModelTest

# Run specific test method
python manage.py test students.tests.test_models.StudentModelTest.test_student_creation

# Run tests with verbosity
python manage.py test --verbosity=2

# Keep test database
python manage.py test --keepdb

# Run tests in parallel
python manage.py test --parallel
```

### Coverage Analysis
```bash
# Install coverage
pip install coverage

# Run tests with coverage
coverage run --source='.' manage.py test

# Generate coverage report
coverage report

# Generate HTML coverage report
coverage html

# View coverage in browser
open htmlcov/index.html
```

### Performance Testing
```bash
# Install django-test-plus for performance testing
pip install django-test-plus

# Run performance tests
python manage.py test --timing

# Profile test performance
python -m cProfile manage.py test
```

## 📊 Test Data Management

### Fixtures
```python
# fixtures/test_data.json
[
    {
        "model": "students.level",
        "pk": 1,
        "fields": {
            "name": "Primary",
            "abbrv": "PRI",
            "is_active": true
        }
    },
    {
        "model": "students.student",
        "pk": 1,
        "fields": {
            "name": "Test Student",
            "gender": "Male",
            "level": 1,
            "is_active": true
        }
    }
]
```

### Loading Test Data
```python
# In test class
class MyTestCase(TestCase):
    fixtures = ['test_data.json']
    
    def test_with_fixture_data(self):
        from students.models import Student
        student = Student.objects.get(pk=1)
        self.assertEqual(student.name, 'Test Student')
```

## 🔍 Debugging Tests

### Test Debugging
```python
# Add debugging to tests
import pdb

class MyTestCase(TestCase):
    def test_something(self):
        # Set breakpoint
        pdb.set_trace()
        
        # Your test code here
        result = some_function()
        self.assertEqual(result, expected_value)
```

### Test Output
```python
# Print debug information
class MyTestCase(TestCase):
    def test_with_debug(self):
        print(f"Testing with data: {self.test_data}")
        
        # Use Django's built-in logging
        import logging
        logger = logging.getLogger(__name__)
        logger.debug("Debug message in test")
```

## 📋 Best Practices

### Test Organization
- Group related tests in test classes
- Use descriptive test method names
- Follow the Arrange-Act-Assert pattern
- Keep tests independent and isolated

### Test Data
- Use factories instead of fixtures when possible
- Create minimal test data for each test
- Clean up test data after each test
- Use meaningful test data that reflects real scenarios

### Performance
- Use `setUpClass` for expensive setup operations
- Use `--keepdb` flag during development
- Run tests in parallel when possible
- Profile slow tests and optimize

### Maintenance
- Keep tests up to date with code changes
- Remove obsolete tests
- Refactor common test code into utilities
- Document complex test scenarios

---

This testing guide provides comprehensive coverage for maintaining high code quality in the Receipt Generator system. Regular testing ensures reliability and helps catch issues early in development.
