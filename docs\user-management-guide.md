# 👥 User Management Guide

A comprehensive guide for administrators to manage users, roles, and permissions in the Tiny Feet MIS system.

## 📋 Table of Contents

- [Overview](#overview)
- [User Roles and Permissions](#user-roles-and-permissions)
- [Creating Users](#creating-users)
- [Managing User Roles](#managing-user-roles)
- [User Account Management](#user-account-management)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

The Tiny Feet MIS uses a Role-Based Access Control (RBAC) system to manage user permissions. This guide covers how to manage users through the web interface.

### Access Requirements
- **Administrator** or **Super Administrator** role required
- Access through: **Navigation Menu → Users**

## 🏷️ User Roles and Permissions

### Role Hierarchy (Level 1-6)

| Level | Role | Description | Default Redirect |
|-------|------|-------------|------------------|
| 6 | Super Administrator | Complete system access | Students Dashboard |
| 5 | Administrator | Full system administration | Students Dashboard |
| 4 | Finance Manager | Financial management and reporting | Finances Dashboard |
| 3 | Academic Coordinator | Academic oversight and coordination | Academics Dashboard |
| 2 | Teacher | Academic management and grading | Academics Dashboard |
| 1 | Student | Basic student access | Academics Dashboard |

### Permission Categories

#### 📚 Students (5 permissions)
- `view_students` - View student information
- `add_student` - Add new students
- `edit_student` - Edit student information
- `delete_student` - Delete students
- `manage_students` - Full student management access

#### 💰 Finances (7 permissions)
- `view_finances` - View financial information
- `add_fee` - Add new fees
- `edit_fee` - Edit fees
- `delete_fee` - Delete fees
- `manage_finances` - Full financial management access
- `manage_fee_collection` - Manage fee collection
- `view_financial_reports` - View financial reports

#### 🎓 Academics (7 permissions)
- `view_academics` - View academic information
- `add_subject` - Add new subjects
- `edit_subject` - Edit subjects
- `manage_academics` - Full academic management access
- `manage_assessments` - Manage assessments
- `view_grades` - View grades
- `edit_grades` - Edit grades

#### 👨‍🏫 Teachers (5 permissions)
- `view_teachers` - View teacher information
- `add_teacher` - Add new teachers
- `edit_teacher` - Edit teacher information
- `view_teacher_details` - View detailed teacher information
- `manage_teacher_assignments` - Manage teacher assignments

#### ⚙️ System (2 permissions)
- `manage_system` - Manage system operations
- `manage_academic_periods` - Manage academic periods

#### 📊 Reports (3 permissions)
- `view_reports` - View reports
- `view_financial_reports` - View financial reports
- `view_academic_reports` - View academic reports

## ➕ Creating Users

### Step 1: Access User Creation
1. Navigate to **Users** in the main menu
2. Click **"Create New User"** button
3. Fill in the user creation form

### Step 2: Required Information
```
Basic Information:
- Username (unique, no spaces)
- Email address
- First Name
- Last Name
- Password (will be auto-generated if left blank)

Optional Information:
- Phone number
- Address
- Date of birth
- Profile picture
```

### Step 3: Account Settings
- **Active Status**: Check to enable the account
- **Staff Status**: Check for administrative access
- **Email Notifications**: Enable/disable email notifications

### Step 4: Save and Assign Role
1. Click **"Create User"**
2. You'll be redirected to the user detail page
3. Click **"Assign Role"** to add roles

## 🎭 Managing User Roles

### Assigning Roles

#### Method 1: From User Detail Page
1. Go to **Users** → Select a user
2. Click **"Assign Role"** button
3. Select the role from dropdown
4. Add optional notes
5. Set expiration date (if needed)
6. Click **"Assign Role"**

#### Method 2: Bulk Role Assignment
1. Go to **Users** list
2. Select multiple users using checkboxes
3. Use **"Bulk Actions"** dropdown
4. Select **"Assign Role"**
5. Choose role and confirm

### Removing Roles
1. Go to user detail page
2. Find the role in **"Active Roles"** section
3. Click **"Remove"** next to the role
4. Confirm the action

### Role Inheritance
- Higher-level roles typically include permissions from lower levels
- Super Administrators have access to everything
- Administrators have broad access across all modules
- Specialized roles (Finance Manager, Academic Coordinator) have focused permissions

## 👤 User Account Management

### Editing User Information
1. Navigate to **Users** → Select user
2. Click **"Edit User"** button
3. Modify information as needed
4. Click **"Save Changes"**

### Resetting Passwords
1. Go to user detail page
2. Click **"Reset Password"**
3. Choose method:
   - **Auto-generate**: System creates secure password
   - **Manual**: Set specific password
4. Optionally send password via email

### Deactivating Users
1. Edit user account
2. Uncheck **"Active"** status
3. Save changes
4. User will be unable to log in

### Reactivating Users
1. Edit deactivated user account
2. Check **"Active"** status
3. Save changes
4. User can log in again

## 🔍 User Search and Filtering

### Search Options
- **Username**: Search by exact or partial username
- **Email**: Find users by email address
- **Name**: Search by first or last name
- **Role**: Filter by assigned roles

### Advanced Filtering
```
Filter by:
- Active/Inactive status
- Staff status
- Role level
- Date joined
- Last login date
```

## 📊 User Analytics

### User Statistics Dashboard
- Total users by role
- Active vs inactive users
- Recent user activity
- Login frequency reports

### Accessing Analytics
1. Go to **Users** main page
2. View summary cards at top
3. Click **"View Detailed Reports"** for more info

## 🚨 Troubleshooting

### Common Issues

#### User Cannot Log In
**Symptoms**: Login fails with correct credentials
**Solutions**:
1. Check if user account is active
2. Verify user has appropriate roles assigned
3. Check if password needs reset
4. Ensure user has required permissions

#### User Gets "Access Denied" Errors
**Symptoms**: User logs in but cannot access certain pages
**Solutions**:
1. Verify user has correct role assigned
2. Check role permissions match required access
3. Review RBAC middleware logs
4. Ensure role is active and not expired

#### Redirect Loops After Login
**Symptoms**: User gets stuck in infinite redirects
**Solutions**:
1. Check user's role level and permissions
2. Verify redirect destination is accessible
3. Review RBAC middleware configuration
4. Check for missing permissions

### Getting Help
- Check the **Debugging Guide** for technical issues
- Review **RBAC Guide** for permission problems
- Contact system administrator for complex issues

## 🔐 Security Best Practices

### Password Management
- Use strong, unique passwords
- Enable password expiration for sensitive roles
- Require password changes for new users
- Monitor failed login attempts

### Role Assignment
- Follow principle of least privilege
- Regularly review user roles and permissions
- Remove unnecessary roles promptly
- Document role changes for audit trail

### Account Monitoring
- Review user activity logs regularly
- Monitor for unusual access patterns
- Deactivate unused accounts
- Audit role assignments quarterly

## 📝 Quick Reference

### Common Tasks
```bash
# Create new user
Users → Create New User → Fill form → Save

# Assign role
Users → Select User → Assign Role → Choose role → Save

# Reset password
Users → Select User → Reset Password → Choose method → Save

# Deactivate user
Users → Select User → Edit → Uncheck Active → Save

# Search users
Users → Use search bar or filters → Apply
```

### Keyboard Shortcuts
- `Ctrl + N`: Create new user (when on users page)
- `Ctrl + F`: Focus search bar
- `Ctrl + S`: Save form (when editing)

---

**Remember**: Always test user access after making changes to ensure proper functionality. Keep user information up-to-date and review permissions regularly for security.
