from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db import transaction
from django.core.paginator import Paginator
from django.db.models import Q

from students.models import AcademicYear, Term
from students.forms.academic_year_forms import (
    AcademicYearForm, TermForm, CloseAcademicYearForm, ActivateTermForm
)


@login_required(login_url="accounts:login")
def academic_year_management(request):
    """
    Main academic year management dashboard
    """
    # Get search query
    search_query = request.GET.get('search', '').strip()
    
    # Base queryset for academic years
    academic_years = AcademicYear.objects.all().prefetch_related('academic_year')
    
    # Apply search filter
    if search_query:
        academic_years = academic_years.filter(
            Q(name__icontains=search_query)
        )
    
    # Order by most recent first
    academic_years = academic_years.order_by('-start_date')
    
    # Pagination
    paginator = Paginator(academic_years, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get active academic year and term
    active_academic_year = AcademicYear.objects.filter(is_active=True).first()
    active_term = Term.objects.filter(is_active=True).first()
    
    # Get recent terms
    recent_terms = Term.objects.select_related('academic_year').order_by('-start_date')[:5]
    
    context = {
        'academic_years': page_obj,
        'search_query': search_query,
        'total_count': academic_years.count(),
        'page_obj': page_obj,
        'active_academic_year': active_academic_year,
        'active_term': active_term,
        'recent_terms': recent_terms,
    }
    return render(request, 'students/academic_year_management.html', context)


@login_required(login_url="accounts:login")
def add_academic_year(request):
    """
    Add a new academic year
    """
    if request.method == 'POST':
        form = AcademicYearForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    academic_year = form.save()
                    messages.success(
                        request, 
                        f'Academic year "{academic_year.name}" has been created successfully!'
                    )
                    return redirect('students:academic_year_management')
            except Exception as e:
                messages.error(request, f'Error creating academic year: {str(e)}')
    else:
        form = AcademicYearForm()
    
    context = {
        'form': form,
        'title': 'Add New Academic Year',
        'submit_text': 'Create Academic Year'
    }
    return render(request, 'students/academic_year_form.html', context)


@login_required(login_url="accounts:login")
def edit_academic_year(request, pk):
    """
    Edit an existing academic year
    """
    academic_year = get_object_or_404(AcademicYear, pk=pk)
    
    if request.method == 'POST':
        form = AcademicYearForm(request.POST, instance=academic_year)
        if form.is_valid():
            try:
                with transaction.atomic():
                    academic_year = form.save()
                    messages.success(
                        request, 
                        f'Academic year "{academic_year.name}" has been updated successfully!'
                    )
                    return redirect('students:academic_year_management')
            except Exception as e:
                messages.error(request, f'Error updating academic year: {str(e)}')
    else:
        form = AcademicYearForm(instance=academic_year)
    
    context = {
        'form': form,
        'academic_year': academic_year,
        'title': f'Edit Academic Year: {academic_year.name}',
        'submit_text': 'Update Academic Year'
    }
    return render(request, 'students/academic_year_form.html', context)


@login_required(login_url="accounts:login")
def add_term(request):
    """
    Add a new term
    """
    if request.method == 'POST':
        form = TermForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    term = form.save()
                    messages.success(
                        request, 
                        f'Term "{term.term_name}" for {term.academic_year.name} has been created successfully!'
                    )
                    return redirect('students:academic_year_management')
            except Exception as e:
                messages.error(request, f'Error creating term: {str(e)}')
    else:
        form = TermForm()
    
    context = {
        'form': form,
        'title': 'Add New Term',
        'submit_text': 'Create Term'
    }
    return render(request, 'students/term_form.html', context)


@login_required(login_url="accounts:login")
def edit_term(request, pk):
    """
    Edit an existing term
    """
    term = get_object_or_404(Term, pk=pk)
    
    if request.method == 'POST':
        form = TermForm(request.POST, instance=term)
        if form.is_valid():
            try:
                with transaction.atomic():
                    term = form.save()
                    messages.success(
                        request, 
                        f'Term "{term.term_name}" has been updated successfully!'
                    )
                    return redirect('students:academic_year_management')
            except Exception as e:
                messages.error(request, f'Error updating term: {str(e)}')
    else:
        form = TermForm(instance=term)
    
    context = {
        'form': form,
        'term': term,
        'title': f'Edit Term: {term.term_name} - {term.academic_year.name}',
        'submit_text': 'Update Term'
    }
    return render(request, 'students/term_form.html', context)


@login_required(login_url="accounts:login")
def close_academic_year(request):
    """
    Close an academic year
    """
    if request.method == 'POST':
        form = CloseAcademicYearForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    academic_year = form.cleaned_data['academic_year']
                    
                    # Deactivate the academic year
                    academic_year.is_active = False
                    academic_year.save()
                    
                    # Deactivate all terms in this academic year
                    Term.objects.filter(academic_year=academic_year).update(is_active=False)
                    
                    messages.success(
                        request, 
                        f'Academic year "{academic_year.name}" has been closed successfully!'
                    )
                    return redirect('students:academic_year_management')
            except Exception as e:
                messages.error(request, f'Error closing academic year: {str(e)}')
    else:
        form = CloseAcademicYearForm()
    
    context = {
        'form': form,
        'title': 'Close Academic Year',
        'submit_text': 'Close Academic Year'
    }
    return render(request, 'students/close_academic_year.html', context)


@login_required(login_url="accounts:login")
def activate_term(request):
    """
    Activate a specific term
    """
    if request.method == 'POST':
        form = ActivateTermForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    term = form.cleaned_data['term']
                    
                    # Deactivate all other terms
                    Term.objects.all().update(is_active=False)
                    
                    # Activate the selected term
                    term.is_active = True
                    term.save()
                    
                    # Activate the academic year if not already active
                    if not term.academic_year.is_active:
                        AcademicYear.objects.all().update(is_active=False)
                        term.academic_year.is_active = True
                        term.academic_year.save()
                    
                    messages.success(
                        request, 
                        f'Term "{term.term_name}" for {term.academic_year.name} has been activated successfully!'
                    )
                    return redirect('students:academic_year_management')
            except Exception as e:
                messages.error(request, f'Error activating term: {str(e)}')
    else:
        form = ActivateTermForm()
    
    context = {
        'form': form,
        'title': 'Activate Term',
        'submit_text': 'Activate Term'
    }
    return render(request, 'students/activate_term.html', context)


@login_required(login_url="accounts:login")
def academic_year_details(request, pk):
    """
    View details of a specific academic year and its terms
    """
    academic_year = get_object_or_404(AcademicYear, pk=pk)
    terms = Term.objects.filter(academic_year=academic_year).order_by('start_date')
    
    context = {
        'academic_year': academic_year,
        'terms': terms,
    }
    return render(request, 'students/academic_year_details.html', context)
