{% load static %}
<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Bad Request - {{request.tenant.name|default:"Tiny Feet Academy"}}
    </title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
    />

    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-color: #7ab2d3;
        --primary-dark: #5a9bd4;
        --warning-color: #f59e0b;
        --warning-dark: #d97706;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .font-display {
        font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .floating {
        animation: float 6s ease-in-out infinite;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-20px);
        }
      }

      .pulse-warning {
        animation: pulse-warning 3s ease-in-out infinite;
      }

      @keyframes pulse-warning {
        0%,
        100% {
          box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
        }
        50% {
          box-shadow: 0 0 40px rgba(245, 158, 11, 0.6);
        }
      }

      .slide-in {
        animation: slideIn 0.8s ease-out forwards;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body
    class="min-h-screen bg-gradient-to-br from-orange-50 to-red-50 flex flex-col items-center justify-center p-4"
  >
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div
        class="absolute -top-40 -right-40 w-80 h-80 bg-orange-100 opacity-20 rounded-full floating"
      ></div>
      <div
        class="absolute -bottom-40 -left-40 w-96 h-96 bg-red-100 opacity-15 rounded-full floating"
        style="animation-delay: -3s"
      ></div>
      <div
        class="absolute top-1/2 left-1/4 w-32 h-32 bg-orange-100 opacity-25 rounded-full floating"
        style="animation-delay: -1s"
      ></div>
    </div>

    <!-- Error Content -->
    <div class="relative z-10 max-w-2xl mx-auto text-center slide-in">
      <!-- Error Icon -->
      <div class="mb-8">
        <div
          class="w-32 h-32 mx-auto bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-2xl pulse-warning"
        >
          <i class="fas fa-exclamation-circle text-white text-4xl"></i>
        </div>
      </div>

      <!-- Error Code -->
      <div class="mb-6">
        <h1
          class="font-display font-bold text-8xl md:text-9xl text-gray-800 mb-2"
        >
          400
        </h1>
        <div
          class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mx-auto"
        ></div>
      </div>

      <!-- Error Message -->
      <div class="mb-8">
        <h2
          class="font-display font-bold text-2xl md:text-3xl text-gray-800 mb-4"
        >
          Hmm, That Didn't Go As Planned... 🤔
        </h2>
        <p class="text-gray-600 text-lg leading-relaxed max-w-lg mx-auto mb-4">
          Your request was about as clear as a student's handwriting during a
          final exam. Something in what you sent didn't quite make sense to our
          server. No worries though - it happens to the best of us!
        </p>
        <div
          class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-xl max-w-md mx-auto"
        >
          <p class="text-blue-800 text-sm font-medium">
            <i class="fas fa-code mr-2"></i>
            <strong>Developer's Note:</strong> HTTP 400 errors occur when the
            server can't understand the request format. It's like submitting an
            essay in ancient hieroglyphics when the assignment clearly asked for
            English! 📜
          </p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <a
          href="/"
          class="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] text-white font-semibold py-3 px-8 rounded-xl hover:from-[var(--primary-dark)] hover:to-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-home mr-2"></i>
          Go Home
        </a>
        <button
          onclick="history.back()"
          class="bg-white text-gray-700 font-semibold py-3 px-8 rounded-xl border-2 border-gray-200 hover:border-orange-500 hover:text-orange-600 focus:ring-4 focus:ring-orange-500/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-arrow-left mr-2"></i>
          Go Back
        </button>
      </div>

      <!-- Help Information -->
      <div
        class="mt-12 p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-orange-200/50 shadow-lg"
      >
        <h3
          class="font-semibold text-gray-800 mb-3 flex items-center justify-center gap-2"
        >
          <i class="fas fa-lightbulb text-orange-500"></i>
          Common Causes
        </h3>
        <div class="text-sm text-gray-600 leading-relaxed space-y-2">
          <p>• Invalid form data or missing required fields</p>
          <p>• Malformed URL or incorrect parameters</p>
          <p>• File upload issues or size limitations</p>
          <p>• Browser cache or cookie problems</p>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="mt-8 text-center">
      <p class="text-sm text-gray-500 flex items-center justify-center gap-2">
        <i class="fas fa-graduation-cap text-[var(--primary-color)]"></i>
        Tiny Feet Academy MIS
      </p>
    </footer>
  </body>
</html>
