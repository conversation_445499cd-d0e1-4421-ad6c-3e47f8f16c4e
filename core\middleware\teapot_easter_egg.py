"""
Teapot Easter Egg Middleware
Detects coffee/tea-related search terms and redirects to the 418 "I'm a teapot" page
"""

from django.shortcuts import redirect
from django.utils.deprecation import MiddlewareMixin


class TeapotEasterEggMiddleware(MiddlewareMixin):
    """
    Middleware to detect coffee/tea search terms and redirect to 418 teapot page
    """
    
    # Coffee and tea related trigger words
    TEAPOT_TRIGGER_WORDS = [
        'coffee',
        'tea',
        'teapot',
        'brew',
        'brewing',
        'espresso',
        'latte',
        'cappuccino',
        'americano',
        'macchiato',
        'mocha',
        'frappuccino',
        'caffeine',
        'barista',
        'cafe',
        'coffeehouse',
        'starbucks',
        'dunkin',
        'green tea',
        'black tea',
        'herbal tea',
        'chai',
        'matcha',
        'earl grey',
        'chamomile',
        'oolong',
        'jasmine tea',
        'peppermint tea',
        'rooibos',
        'darjeeling',
        'assam',
        'english breakfast',
        'afternoon tea',
        'high tea',
        'tea time',
        'tea party',
        'kettle',
        'teacup',
        'saucer',
        'sugar',
        'cream',
        'milk',
        'honey',
        'lemon',
        'steeping',
        'infusion',
        'loose leaf',
        'tea bag',
        'tea leaves',
        'caffeine free',
        'decaf',
        'french press',
        'pour over',
        'drip coffee',
        'cold brew',
        'iced coffee',
        'iced tea',
        'bubble tea',
        'boba',
        'turkish coffee',
        'arabic coffee',
        'instant coffee',
        'coffee beans',
        'roast',
        'grind',
        'filter',
        'percolator',
        'moka pot',
        'aeropress',
        'chemex',
        'v60',
        'kalita',
        'siphon',
        'espresso machine',
        'coffee maker',
        'keurig',
        'nespresso',
        'dolce gusto',
        'tassimo',
        'coffee shop',
        'coffee break',
        'morning coffee',
        'wake up',
        'energy',
        'boost',
        'alertness',
        'java',
        'joe',
        'cuppa',
        'brew time',
        'steep',
        'hot beverage',
        'warm drink',
        'comfort drink',
        'cozy',
        'relaxing',
        'soothing',
        'aromatic',
        'fragrant',
        'rich',
        'smooth',
        'bold',
        'mild',
        'strong',
        'weak',
        'bitter',
        'sweet',
        'creamy',
        'frothy',
        'steamy',
        'piping hot',
        'refreshing'
    ]
    
    def process_request(self, request):
        """
        Check if the request contains coffee/tea search terms
        """
        # Skip if already on error pages or teapot/coffee pages
        if any(request.path.startswith(path) for path in ['/coffee/', '/teapot/', '/core/philosophical-moment/', '/core/musical-moment/']):
            return None
        
        # Skip for admin, API, and static files
        if any(request.path.startswith(path) for path in ['/admin/', '/api/', '/static/', '/media/']):
            return None
        
        # Check GET parameters for search terms
        search_params = [
            'search', 'q', 'query', 'search_query', 'term', 
            'subject_search', 'assessment_search', 'activity_search',
            'student_search', 'teacher_search', 'finance_search'
        ]
        
        for param in search_params:
            search_term = request.GET.get(param, '').strip()
            if search_term and self.is_teapot_search(search_term):
                # Redirect to teapot page (418 error)
                return redirect('/teapot/')
        
        # Check POST data for search terms (for forms)
        if request.method == 'POST':
            for param in search_params:
                search_term = request.POST.get(param, '').strip()
                if search_term and self.is_teapot_search(search_term):
                    # For POST requests, redirect to teapot page
                    return redirect('/teapot/')
        
        return None
    
    def is_teapot_search(self, search_term):
        """Check if search term contains coffee/tea trigger words"""
        if not search_term:
            return False
        
        search_lower = search_term.lower().strip()
        
        # Check for exact matches and partial matches
        for trigger in self.TEAPOT_TRIGGER_WORDS:
            if trigger in search_lower:
                return True
        
        return False
