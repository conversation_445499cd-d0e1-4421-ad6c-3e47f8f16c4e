from io import BytesIO
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A5
from reportlab.lib.units import mm

# Configuration
PAGE_SIZE = A5
MARGIN = 18 * mm
LINE_SPACING = 32

# Style constants
STYLE = {
    'font_reg': "Helvetica",
    'font_bold': "Helvetica-Bold",
    'font_italic': "Helvetica-Oblique",
    'size_header': 12,
    'size_label': 10,
    'size_value': 10,
    'size_footer': 10,
    'size_tagline': 8,
    'color_primary': '#60b8fc',
    'color_text': '#6b7280',
    'color_line': '#e5e7eb'
}

# Receipt data
DATA = {
    'logo': "assets/img/tinyfeet.jpg",
    'contact': "0999865060",
    'footer': "Payment Successful ✓",
    'tagline': "Nurturing the young minds"
}

# Utility functions


def set_font(c, face, size):
    c.setFont(face, size)
    c.setFillColor(STYLE['color_text'])


def draw_line(c, x1, y, x2, dashed=False):
    c.setStrokeColor(STYLE['color_line'])
    c.setLineWidth(1)
    if dashed:
        c.setDash(6, 3)
    else:
        c.setDash()
    c.line(x1, y, x2, y)
    c.setDash()


def draw_label_value(c, x_label, x_value, y, label, value, bold_value=False, dashed=False):
    # label
    set_font(c, STYLE['font_bold'], STYLE['size_label'])
    c.drawString(x_label, y, label)
    # value
    face = STYLE['font_bold'] if bold_value else STYLE['font_reg']
    set_font(c, face, STYLE['size_value'])
    c.drawRightString(x_value, y, value)
    # underline
    draw_line(c, x_label, y - 6, x_value, dashed=dashed)


# Sections

def draw_header(c, width, height):
    y = height - MARGIN
    # logo
    logo_size = 15 * mm
    c.drawImage(DATA['logo'], MARGIN, y - logo_size/2,
                logo_size, logo_size, preserveAspectRatio=True)
    # contact info
    x_label = (width - MARGIN) - (MARGIN * 2.3)
    x_value = width - MARGIN
    # use primary color for header text

    set_font(c, STYLE['font_bold'], STYLE['size_header'])
    c.drawString(x_label, y, "Contact:")
    set_font(c, STYLE['font_reg'], STYLE['size_header'])
    c.setFillColor(STYLE['color_primary'])
    c.drawRightString(x_value, y, DATA['contact'])
    # reset to text color for body
    c.setFillColor(STYLE['color_text'])
    return y - 60


def draw_fields(c, width, start_y, data):
    fields = [
        ("STUDENT ID", data.student.student_id),
        ("NAME", data.student.name),
        ("RECEIPT NO", data.receipt_number),
        ("TERM", f"{data.fee_account.term}"),
        ("AMOUNT", f"K {data.amount_paid:,}"),
        ("TOTAL", f"K {data.fee_account.total_due:,}"),
        ("BALANCE", f"K {data.fee_account.current_balance:,}"),
        ("CLASS", f"{data.student.level}"),
        ("DATE", f"{data.date.strftime('%B %d, %Y')}"),
        ("DESCRIPTION", f"{data.fee_account.category}")
    ]
    x_label = MARGIN
    x_value = width - MARGIN
    y = start_y

    for label, val in fields:
        dashed = label in ('TERM', 'DESCRIPTION')
        draw_label_value(c, x_label, x_value, y, label, val, dashed=dashed)
        y -= LINE_SPACING
    return y


def draw_footer(c, width):
    # tagline at bottom margin
    set_font(c, STYLE['font_italic'], STYLE['size_tagline'])
    c.drawCentredString(width / 2, MARGIN, DATA['tagline'])
    # footer above tagline
    foot_y = MARGIN + STYLE['size_tagline'] + 4
    set_font(c, STYLE['font_bold'], STYLE['size_footer'])
    c.setFillColor(STYLE['color_primary'])
    c.drawCentredString(width / 2, foot_y, DATA['footer'])
    # centered line between footer and tagline
    line_w = width * 0.4
    x1 = (width - line_w) / 2
    draw_line(c, x1, foot_y - 4, x1 + line_w)


# PDF creation

def create_pdf(data):
    buffer = BytesIO()
    c = canvas.Canvas(buffer, pagesize=PAGE_SIZE)
    width, height = PAGE_SIZE

    y_start = draw_header(c, width, height)
    draw_fields(c, width, y_start, data)
    draw_footer(c, width)

    c.showPage()
    c.save()
    buffer.seek(0)
    return buffer
