from django import forms
from django.core.exceptions import ValidationError
from django.db import transaction

from finances.book_keeping import Budget, BudgetLine, Ledger
from students.models import Term


class LedgerForm(forms.ModelForm):
    """Form for creating and editing ledgers"""
    
    class Meta:
        model = Ledger
        fields = ['code', 'name', 'ledger_type']
        
        widgets = {
            'code': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'placeholder': 'e.g., 1001, 2001, 3001'
            }),
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'placeholder': 'e.g., Cash at Bank, Tuition Fees, Office Supplies'
            }),
            'ledger_type': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
            }),
        }
        
        labels = {
            'code': 'Account Code',
            'name': 'Account Name',
            'ledger_type': 'Account Type',
        }
        
        help_texts = {
            'code': 'Unique account code (e.g., 1001 for assets, 2001 for liabilities)',
            'name': 'Descriptive name for the account',
            'ledger_type': 'Select the type of account (Asset, Liability, Equity, Revenue, Expense)',
        }

    def clean_code(self):
        code = self.cleaned_data.get('code')
        if code:
            # Check for duplicate codes (excluding current instance if editing)
            existing = Ledger.objects.filter(code=code)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError('An account with this code already exists.')
        
        return code

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            # Check for duplicate names (excluding current instance if editing)
            existing = Ledger.objects.filter(name=name)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError('An account with this name already exists.')
        
        return name


class BudgetForm(forms.ModelForm):
    """Form for creating and editing budgets"""
    
    class Meta:
        model = Budget
        fields = ['term', 'description']
        
        widgets = {
            'term': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'rows': 4,
                'placeholder': 'Enter budget description and notes...'
            }),
        }
        
        labels = {
            'term': 'Academic Term',
            'description': 'Budget Description',
        }
        
        help_texts = {
            'term': 'Select the academic term this budget applies to',
            'description': 'Optional description or notes about this budget',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only show active terms or the current one if editing
        if self.instance.pk:
            self.fields['term'].queryset = Term.objects.all()
        else:
            self.fields['term'].queryset = Term.objects.filter(academic_year__is_active=True)

    def clean_term(self):
        term = self.cleaned_data.get('term')
        if term:
            # Check for duplicate budgets for the same term (excluding current instance if editing)
            existing = Budget.objects.filter(term=term)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError(f'A budget for {term} already exists.')
        
        return term


class BudgetLineForm(forms.ModelForm):
    """Form for creating and editing budget lines"""
    
    class Meta:
        model = BudgetLine
        fields = ['account', 'amount']
        
        widgets = {
            'account': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
        }
        
        labels = {
            'account': 'Ledger Account',
            'amount': 'Budget Amount',
        }
        
        help_texts = {
            'account': 'Select the ledger account for this budget line',
            'amount': 'Enter the budgeted amount for this account',
        }

    def __init__(self, *args, **kwargs):
        budget = kwargs.pop('budget', None)
        super().__init__(*args, **kwargs)
        
        # Filter out accounts that already have budget lines for this budget
        if budget:
            existing_accounts = BudgetLine.objects.filter(budget=budget).values_list('account', flat=True)
            if self.instance.pk:
                # Exclude current instance when editing
                existing_accounts = existing_accounts.exclude(pk=self.instance.pk)
            
            self.fields['account'].queryset = Ledger.objects.exclude(pk__in=existing_accounts)

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount is not None and amount < 0:
            raise ValidationError('Budget amount cannot be negative.')
        
        return amount


class BulkLedgerDeleteForm(forms.Form):
    """Form for bulk deleting ledgers"""
    
    ledgers = forms.ModelMultipleChoiceField(
        queryset=Ledger.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'mr-2'
        }),
        label='Select Ledgers to Delete',
        help_text='Select the ledgers you want to delete. Warning: This will also delete all related transactions.'
    )
    
    confirm_deletion = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-4 h-4 text-[#F28C8C] bg-gray-100 border-gray-300 rounded focus:ring-[#F28C8C] focus:ring-2'
        }),
        label='I confirm that I want to delete the selected ledgers',
        help_text='This action cannot be undone and will delete all related transactions'
    )


class BulkBudgetDeleteForm(forms.Form):
    """Form for bulk deleting budgets"""
    
    budgets = forms.ModelMultipleChoiceField(
        queryset=Budget.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'mr-2'
        }),
        label='Select Budgets to Delete',
        help_text='Select the budgets you want to delete. This will also delete all budget lines.'
    )
    
    confirm_deletion = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-4 h-4 text-[#F28C8C] bg-gray-100 border-gray-300 rounded focus:ring-[#F28C8C] focus:ring-2'
        }),
        label='I confirm that I want to delete the selected budgets',
        help_text='This action cannot be undone and will delete all budget lines'
    )


class LedgerSearchForm(forms.Form):
    """Form for searching and filtering ledgers"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 pl-12 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]/60',
            'placeholder': 'Search by code, name, or type...'
        }),
        label='Search'
    )
    
    ledger_type = forms.ChoiceField(
        required=False,
        choices=[('', 'All Types')] + Ledger._meta.get_field('ledger_type').choices,
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
        }),
        label='Account Type'
    )


class BudgetSearchForm(forms.Form):
    """Form for searching and filtering budgets"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 pl-12 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]/60',
            'placeholder': 'Search by term or description...'
        }),
        label='Search'
    )
    
    term = forms.ModelChoiceField(
        required=False,
        queryset=Term.objects.all(),
        empty_label='All Terms',
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white'
        }),
        label='Academic Term'
    )
