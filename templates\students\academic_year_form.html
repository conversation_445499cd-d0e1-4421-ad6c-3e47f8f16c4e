{% extends 'base.html' %}
<!--  -->
{% block title %}{{ title }} | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div class="flex items-center gap-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-2xl flex items-center justify-center shadow-xl icon-float"
      >
        <i class="fas fa-calendar-alt text-white text-2xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
        >
          {{ title }}
        </h1>
        <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
          {% if academic_year %}
            Update academic year information and dates
          {% else %}
            Create a new academic year for the school
          {% endif %}
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
  </div>

  <!-- Form Section -->
  <div class="card-modern p-8 form-fade-in">
    <form method="POST" class="space-y-6">
      {% csrf_token %}
      
      <!-- Academic Year Name -->
      <div class="form-group">
        <label for="{{ form.name.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-3">
          <div class="flex items-center gap-2">
            <i class="fas fa-tag text-[#7AB2D3]"></i>
            {{ form.name.label }}
          </div>
        </label>
        {{ form.name }}
        {% if form.name.help_text %}
        <p class="text-sm text-[#40657F]/70 mt-2">{{ form.name.help_text }}</p>
        {% endif %}
        {% if form.name.errors %}
        <div class="mt-2">
          {% for error in form.name.errors %}
          <p class="text-sm text-[#F28C8C] flex items-center gap-2">
            <i class="fas fa-exclamation-circle"></i>
            {{ error }}
          </p>
          {% endfor %}
        </div>
        {% endif %}
      </div>

      <!-- Date Fields -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Start Date -->
        <div class="form-group">
          <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-3">
            <div class="flex items-center gap-2">
              <i class="fas fa-calendar-day text-[#74C69D]"></i>
              {{ form.start_date.label }}
            </div>
          </label>
          {{ form.start_date }}
          {% if form.start_date.help_text %}
          <p class="text-sm text-[#40657F]/70 mt-2">{{ form.start_date.help_text }}</p>
          {% endif %}
          {% if form.start_date.errors %}
          <div class="mt-2">
            {% for error in form.start_date.errors %}
            <p class="text-sm text-[#F28C8C] flex items-center gap-2">
              <i class="fas fa-exclamation-circle"></i>
              {{ error }}
            </p>
            {% endfor %}
          </div>
          {% endif %}
        </div>

        <!-- End Date -->
        <div class="form-group">
          <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-bold text-[#2C3E50] mb-3">
            <div class="flex items-center gap-2">
              <i class="fas fa-calendar-check text-[#F28C8C]"></i>
              {{ form.end_date.label }}
            </div>
          </label>
          {{ form.end_date }}
          {% if form.end_date.help_text %}
          <p class="text-sm text-[#40657F]/70 mt-2">{{ form.end_date.help_text }}</p>
          {% endif %}
          {% if form.end_date.errors %}
          <div class="mt-2">
            {% for error in form.end_date.errors %}
            <p class="text-sm text-[#F28C8C] flex items-center gap-2">
              <i class="fas fa-exclamation-circle"></i>
              {{ error }}
            </p>
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Form-wide Errors -->
      {% if form.non_field_errors %}
      <div class="bg-[#F28C8C]/10 border border-[#F28C8C]/30 rounded-xl p-4">
        <div class="flex items-center gap-2 mb-2">
          <i class="fas fa-exclamation-triangle text-[#F28C8C]"></i>
          <h4 class="font-bold text-[#F28C8C]">Please correct the following errors:</h4>
        </div>
        {% for error in form.non_field_errors %}
        <p class="text-sm text-[#F28C8C]">{{ error }}</p>
        {% endfor %}
      </div>
      {% endif %}

      <!-- Information Box -->
      <div class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] rounded-xl p-6 border border-[#B9D8EB]/50">
        <div class="flex items-center gap-3 mb-3">
          <i class="fas fa-info-circle text-[#7AB2D3] text-lg"></i>
          <h3 class="font-bold text-[#2C3E50]">Academic Year Guidelines</h3>
        </div>
        <ul class="text-[#40657F] text-sm space-y-2">
          <li class="flex items-start gap-2">
            <i class="fas fa-check text-[#74C69D] mt-1 text-xs"></i>
            Academic year should typically run for 300-400 days
          </li>
          <li class="flex items-start gap-2">
            <i class="fas fa-check text-[#74C69D] mt-1 text-xs"></i>
            Use format like "2024-2025" for the academic year name
          </li>
          <li class="flex items-start gap-2">
            <i class="fas fa-check text-[#74C69D] mt-1 text-xs"></i>
            End date must be after the start date
          </li>
          <li class="flex items-start gap-2">
            <i class="fas fa-check text-[#74C69D] mt-1 text-xs"></i>
            Each academic year name must be unique
          </li>
        </ul>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-[#B9D8EB]/30">
        <button
          type="submit"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-save group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>{{ submit_text }}</span>
        </button>
        <a
          href="{% url 'students:academic_year_management' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-arrow-left group-hover:scale-110 group-hover:-translate-x-1 transition-all duration-300"
          ></i>
          <span>Back to Management</span>
        </a>
      </div>
    </form>
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  /* Form Animation */
  .form-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: formFadeIn 0.8s ease-out 0.8s forwards;
  }

  /* Form Group Animation */
  .form-group {
    opacity: 0;
    transform: translateX(-20px);
    animation: formGroupSlideIn 0.4s ease-out forwards;
  }

  .form-group:nth-child(1) { animation-delay: 1s; }
  .form-group:nth-child(2) { animation-delay: 1.1s; }
  .form-group:nth-child(3) { animation-delay: 1.2s; }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes formFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes formGroupSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  /* Form Styling */
  .form-group input,
  .form-group select {
    transition: all 0.3s ease;
  }

  .form-group input:focus,
  .form-group select:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(122, 178, 211, 0.15);
  }
</style>

{% endblock %}
