from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.db.models import Q
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods

from accounts.decorators import admin_required, require_permission
from accounts.forms import CreateUserForm, EditUserForm, AssignRoleForm
from accounts.models import Role, UserRole
from accounts.utils import RBACManager

User = get_user_model()


@admin_required
def user_management_dashboard(request):
    """
    Main user management dashboard
    """
    # Get search query
    search_query = request.GET.get('q', '').strip()
    
    # Base queryset
    users = User.objects.all().select_related().prefetch_related('user_roles__role')
    
    # Apply search filter
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    stats = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'inactive_users': User.objects.filter(is_active=False).count(),
        'staff_users': User.objects.filter(is_staff=True).count(),
        'superusers': User.objects.filter(is_superuser=True).count(),
    }
    
    # Role statistics
    roles = Role.objects.filter(is_active=True).order_by('level')
    role_stats = []
    for role in roles:
        user_count = UserRole.objects.filter(role=role, is_active=True).count()
        role_stats.append({
            'role': role,
            'user_count': user_count
        })
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'stats': stats,
        'role_stats': role_stats,
        'total_count': paginator.count,
    }
    
    return render(request, 'accounts/user_management/dashboard.html', context)


@require_permission('add_teacher', 'manage_students', 'manage_system')
def create_user(request):
    """
    Create a new user with role assignments
    """
    if request.method == 'POST':
        form = CreateUserForm(request.POST, created_by=request.user)
        if form.is_valid():
            try:
                user = form.save()
                messages.success(
                    request, 
                    f'User "{user.get_full_name()}" has been created successfully!'
                )
                return redirect('accounts:user_detail', user_id=user.id)
            except Exception as e:
                messages.error(request, f'Error creating user: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = CreateUserForm(created_by=request.user)
    
    context = {
        'form': form,
        'available_roles': Role.objects.filter(is_active=True).order_by('level'),
        'user_max_level': request.user.get_highest_role_level(),
    }
    
    return render(request, 'accounts/user_management/create_user.html', context)


@admin_required
def user_detail(request, user_id):
    """
    View detailed information about a user
    """
    user = get_object_or_404(User, id=user_id)
    
    # Get user's active roles
    active_roles = user.get_active_roles()
    
    # Get user's permissions
    permissions = user.get_all_permissions()
    
    # Group permissions by category
    permissions_by_category = {}
    for perm in permissions:
        category = perm.category
        if category not in permissions_by_category:
            permissions_by_category[category] = []
        permissions_by_category[category].append(perm)
    
    # Get audit data
    audit_data = RBACManager.audit_user_permissions(user)
    
    context = {
        'user_obj': user,  # Using user_obj to avoid conflict with request.user
        'active_roles': active_roles,
        'permissions_by_category': permissions_by_category,
        'audit_data': audit_data,
        'can_edit': request.user.get_highest_role_level() >= user.get_highest_role_level(),
        'can_assign_roles': request.user.has_permission('manage_system'),
    }
    
    return render(request, 'accounts/user_management/user_detail.html', context)


@admin_required
def edit_user(request, user_id):
    """
    Edit user information
    """
    user = get_object_or_404(User, id=user_id)
    
    # Check if current user can edit this user
    if request.user.get_highest_role_level() < user.get_highest_role_level():
        messages.error(request, "You cannot edit a user with higher privileges than yours.")
        return redirect('accounts:user_detail', user_id=user.id)
    
    if request.method == 'POST':
        form = EditUserForm(request.POST, instance=user)
        if form.is_valid():
            try:
                form.save()
                messages.success(request, f'User "{user.get_full_name()}" has been updated successfully!')
                return redirect('accounts:user_detail', user_id=user.id)
            except Exception as e:
                messages.error(request, f'Error updating user: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = EditUserForm(instance=user)
    
    context = {
        'form': form,
        'user_obj': user,
    }
    
    return render(request, 'accounts/user_management/edit_user.html', context)


@require_permission('manage_system')
def assign_role(request, user_id):
    """
    Assign a role to a user
    """
    user = get_object_or_404(User, id=user_id)
    
    if request.method == 'POST':
        form = AssignRoleForm(request.POST, user=user, assigned_by=request.user)
        if form.is_valid():
            try:
                role = form.cleaned_data['role']
                expires_at = form.cleaned_data.get('expires_at')
                notes = form.cleaned_data.get('notes', '')
                
                RBACManager.assign_role_to_user(
                    user=user,
                    role=role,
                    assigned_by=request.user,
                    expires_at=expires_at,
                    notes=notes
                )
                
                messages.success(
                    request, 
                    f'Role "{role.display_name}" has been assigned to {user.get_full_name()}!'
                )
                return redirect('accounts:user_detail', user_id=user.id)
            except Exception as e:
                messages.error(request, f'Error assigning role: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = AssignRoleForm(user=user, assigned_by=request.user)
    
    # Filter roles that user can assign
    max_level = request.user.get_highest_role_level()
    available_roles = Role.objects.filter(
        is_active=True,
        level__lte=max_level
    ).order_by('level')
    
    form.fields['role'].queryset = available_roles
    
    context = {
        'form': form,
        'user_obj': user,
        'available_roles': available_roles,
    }
    
    return render(request, 'accounts/user_management/assign_role.html', context)


@require_permission('manage_system')
@require_http_methods(["POST"])
def remove_role(request, user_id, role_id):
    """
    Remove a role from a user (AJAX endpoint)
    """
    user = get_object_or_404(User, id=user_id)
    role = get_object_or_404(Role, id=role_id)
    
    try:
        # Check if current user can remove this role
        if role.level > request.user.get_highest_role_level():
            return JsonResponse({
                'success': False,
                'error': f'You cannot remove the "{role.display_name}" role as it\'s higher than your level.'
            })
        
        RBACManager.remove_role_from_user(user, role)
        
        return JsonResponse({
            'success': True,
            'message': f'Role "{role.display_name}" has been removed from {user.get_full_name()}.'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@admin_required
@require_http_methods(["POST"])
def toggle_user_status(request, user_id):
    """
    Toggle user active status (AJAX endpoint)
    """
    user = get_object_or_404(User, id=user_id)
    
    try:
        # Check if current user can modify this user
        if request.user.get_highest_role_level() < user.get_highest_role_level():
            return JsonResponse({
                'success': False,
                'error': 'You cannot modify a user with higher privileges than yours.'
            })
        
        # Don't allow deactivating superusers
        if user.is_superuser and user.is_active:
            return JsonResponse({
                'success': False,
                'error': 'Cannot deactivate superuser accounts.'
            })
        
        user.is_active = not user.is_active
        user.save()
        
        status = 'activated' if user.is_active else 'deactivated'
        
        return JsonResponse({
            'success': True,
            'message': f'User "{user.get_full_name()}" has been {status}.',
            'is_active': user.is_active
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })



