{% extends 'base.html' %} {% load humanize %} {% block title %}Expenditure
Statement | {% endblock %} {% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-chart-pie text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Expenditure Statement
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Financial expense analysis and reporting
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
    </div>

    <!-- Filter Form -->
    <form
      method="post"
      class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] rounded-2xl p-6 border border-[#B9D8EB]/50 mt-8 filter-form-slide-in"
    >
      {% csrf_token %}
      <div class="flex items-center gap-4 mb-6">
        <div
          class="w-10 h-10 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg filter-icon-float"
        >
          <i class="fas fa-filter text-white text-lg"></i>
        </div>
        <div>
          <h3 class="text-xl font-bold text-[#2C3E50] font-display">
            Filter Data
          </h3>
          <p class="text-[#40657F] text-sm">Select date range for analysis</p>
        </div>
      </div>

      <div class="flex flex-col lg:flex-row gap-4 itemes-center md:items-end ">
        <div class="flex flex-col md:flex-row gap-4 items-center flex-1 form-fields-fade-in w-full md:w-fit">
          {% for field in form %}
          <div class="form-field flex-1 w-full md:w-fit">{{field}}</div>
          {%endfor%}
        </div>
        <button
          type="submit"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-bold py-3 px-8 rounded-xl hover:from-[#e07575] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group button-slide-in"
        >
          <i
            class="fas fa-search text-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Fetch Data</span>
        </button>
      </div>
    </form>
  </div>
  <!-- Summary Card -->
  <div class="card-modern p-8 summary-card-fade-in">
    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6 mb-8">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center shadow-lg section-icon-float">
          <i class="fas fa-chart-bar text-white text-lg"></i>
        </div>
        <div>
          <h3 class="text-2xl font-bold text-[#2C3E50] font-display">Expense Summary</h3>
          <p class="text-[#40657F] text-sm">Overview of expenditure categories</p>
        </div>
      </div>

      <div class="flex items-center gap-4">
        {% if not start_date %}
        <a href="{% url 'finances:expenditure_report' %}"
           class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group">
          <i class="fas fa-download text-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"></i>
          <span>Download PDF</span>
        </a>
        {% else %}
        <a href="{% url 'finances:expenditure_pdf' start_date end_date %}"
           class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group">
          <i class="fas fa-download text-lg group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"></i>
          <span>Download PDF</span>
        </a>
        {% endif %}
      </div>
    </div>

    <!-- Total Expense Card -->
    <div class="bg-gradient-to-br from-[#F28C8C]/20 via-white to-[#E2F1F9] border-l-4 border-[#F28C8C] p-8 rounded-2xl mb-8 total-expense-card">
      <div class="flex items-center gap-4 mb-6">
        <div class="w-14 h-14 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-lg">
          <i class="fas fa-money-bill-wave text-white text-xl"></i>
        </div>
        <div>
          <h3 class="font-bold text-[#2C3E50] text-xl">Total Expenditure</h3>
          <p class="text-[#40657F] text-sm">Sum of all expenses</p>
        </div>
      </div>
      <p class="text-4xl md:text-5xl font-bold text-[#F28C8C] font-display counter-animate leading-none">
        MWK {{ total_expense|intcomma }}
      </p>
    </div>

    <!-- Category Breakdown Table -->
    <div class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg table-fade-in">
      <table class="min-w-full bg-white">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th class="px-8 py-6 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider">
              <div class="flex items-center gap-3">
                <i class="fas fa-list text-[#F28C8C]"></i>
                <span>Category</span>
              </div>
            </th>
            <th class="px-8 py-6 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider">
              <div class="flex items-center justify-end gap-3">
                <i class="fas fa-money-bill text-[#F28C8C]"></i>
                <span>Amount</span>
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#B9D8EB]/30">
          {% for expense in expenses %}
          <tr class="expense-row hover:bg-[#F28C8C]/5 transition-colors duration-200">
            <td class="px-8 py-4 text-[#2C3E50] font-medium">{{ expense.name }}</td>
            <td class="px-8 py-4 text-right font-bold text-[#F28C8C] text-lg">
              MWK {{ expense.total_amount|intcomma }}
            </td>
          </tr>
          {% endfor %}
          <tr class="bg-[#F28C8C]/20 font-bold total-row">
            <td class="px-8 py-5 text-[#2C3E50] text-lg">TOTAL EXPENDITURE</td>
            <td class="px-8 py-5 text-right text-[#F28C8C] text-xl font-display">
              MWK {{ total_expense|intcomma }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  </div>

  <!-- Detailed Expenditure Section -->
  <div class="card-modern p-8 detailed-section-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg section-icon-float">
        <i class="fas fa-list-alt text-white text-lg"></i>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-[#2C3E50] font-display">Detailed Expenditure</h3>
        <p class="text-[#40657F] text-sm">Individual transaction breakdown</p>
      </div>
      <div class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"></div>
    </div>

    <div class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg">
      <table class="min-w-full bg-white">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th class="px-8 py-6 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider">
              <div class="flex items-center gap-3">
                <i class="fas fa-hashtag text-[#7AB2D3]"></i>
                <span>Expense ID</span>
              </div>
            </th>
            <th class="px-8 py-6 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider">
              <div class="flex items-center gap-3">
                <i class="fas fa-file-alt text-[#74C69D]"></i>
                <span>Description</span>
              </div>
            </th>
            <th class="px-8 py-6 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider">
              <div class="flex items-center justify-center gap-3">
                <i class="fas fa-calendar text-[#F28C8C]"></i>
                <span>Date</span>
              </div>
            </th>
            <th class="px-8 py-6 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider">
              <div class="flex items-center justify-end gap-3">
                <i class="fas fa-money-bill text-[#F28C8C]"></i>
                <span>Amount</span>
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#B9D8EB]/30">
          {% for line in outflow %}
          <tr class="detail-row hover:bg-[#F28C8C]/5 transition-colors duration-200">
            <td class="px-8 py-4 text-[#7AB2D3] font-bold">
              {{ line.outflow_id }}
            </td>
            <td class="px-8 py-4 text-[#2C3E50] font-medium">{{ line.description }}</td>
            <td class="px-8 py-4 text-center text-[#40657F]">{{ line.date }}</td>
            <td class="px-8 py-4 text-right font-bold text-[#F28C8C] text-lg">
              MWK {{ line.calculate_total|intcomma }}
            </td>
          </tr>
          {% endfor %}
          <tr class="bg-[#F28C8C]/20 font-bold detail-total-row">
            <td class="px-8 py-5 text-[#2C3E50] text-lg">GRAND TOTAL</td>
            <td class="px-8 py-5"></td>
            <td class="px-8 py-5"></td>
            <td class="px-8 py-5 text-right text-[#F28C8C] text-xl font-display">
              MWK {{ total_expense|intcomma }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>

{% endblock %}

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  /* Filter Form Animations */
  .filter-form-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: filterFormSlideIn 0.8s ease-out 0.8s forwards;
  }

  .filter-icon-float {
    animation: filterIconFloat 4s ease-in-out infinite;
  }

  .form-fields-fade-in {
    opacity: 0;
    animation: formFieldsFadeIn 0.8s ease-out 1s forwards;
  }

  .button-slide-in {
    opacity: 0;
    transform: translateX(50px);
    animation: buttonSlideIn 0.8s ease-out 1.2s forwards;
  }

  /* Summary Card Animations */
  .summary-card-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: summaryCardFadeIn 0.8s ease-out 1.4s forwards;
  }

  .section-icon-float {
    animation: sectionIconFloat 4s ease-in-out infinite;
  }

  .total-expense-card {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    animation: totalExpenseCardSlideUp 0.8s ease-out 1.6s forwards;
  }

  .counter-animate {
    opacity: 0;
    animation: counterFadeIn 1s ease-out 1.8s forwards;
  }

  /* Table Animations */
  .table-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: tableFadeIn 0.8s ease-out 1.8s forwards;
  }

  .expense-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: expenseRowSlideIn 0.4s ease-out forwards;
  }

  .expense-row:nth-child(1) { animation-delay: 2s; }
  .expense-row:nth-child(2) { animation-delay: 2.1s; }
  .expense-row:nth-child(3) { animation-delay: 2.2s; }
  .expense-row:nth-child(4) { animation-delay: 2.3s; }
  .expense-row:nth-child(5) { animation-delay: 2.4s; }
  .expense-row:nth-child(6) { animation-delay: 2.5s; }

  .total-row {
    opacity: 0;
    animation: totalRowHighlight 0.8s ease-out 2.6s forwards;
  }

  /* Detailed Section Animations */
  .detailed-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: detailedSectionFadeIn 0.8s ease-out 2.8s forwards;
  }

  .detail-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: detailRowSlideIn 0.3s ease-out forwards;
  }

  .detail-row:nth-child(1) { animation-delay: 3s; }
  .detail-row:nth-child(2) { animation-delay: 3.1s; }
  .detail-row:nth-child(3) { animation-delay: 3.2s; }
  .detail-row:nth-child(4) { animation-delay: 3.3s; }
  .detail-row:nth-child(5) { animation-delay: 3.4s; }
  .detail-row:nth-child(6) { animation-delay: 3.5s; }
  .detail-row:nth-child(7) { animation-delay: 3.6s; }
  .detail-row:nth-child(8) { animation-delay: 3.7s; }

  .detail-total-row {
    opacity: 0;
    transform: scale(0.95);
    animation: detailTotalRowReveal 1s ease-out 3.8s forwards;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes filterFormSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes filterIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(5deg); }
  }

  @keyframes formFieldsFadeIn {
    to { opacity: 1; }
  }

  @keyframes buttonSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes summaryCardFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes sectionIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(3deg); }
  }

  @keyframes totalExpenseCardSlideUp {
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes counterFadeIn {
    to { opacity: 1; }
  }

  @keyframes tableFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes expenseRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes totalRowHighlight {
    to { opacity: 1; }
  }

  @keyframes detailedSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes detailRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes detailTotalRowReveal {
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Hover Enhancements */
  .total-expense-card:hover .counter-animate {
    animation: counterPulse 0.6s ease-in-out;
    opacity: 1 !important;
  }

  @keyframes counterPulse {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 1;
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .expense-row {
      animation-delay: 1.5s;
    }

    .expense-row:nth-child(n) {
      animation-delay: calc(1.5s + 0.1s * var(--row-index, 1));
    }

    .detail-row {
      animation-delay: 2s;
    }

    .detail-row:nth-child(n) {
      animation-delay: calc(2s + 0.1s * var(--row-index, 1));
    }
  }
</style>

<!-- scrpt -->
{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
{% endblock%}
