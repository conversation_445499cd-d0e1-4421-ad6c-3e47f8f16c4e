"""
Travel Easter Egg View
A delightful surprise for wanderers, adventurers, and travel enthusiasts
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
import json

from core.travel_quotes import get_random_travel_quote, is_travel_search, get_travel_quote_by_category, get_travel_quotes_by_author


@login_required(login_url="accounts:login")
def travel_easter_egg(request):
    """
    Display a random travel/adventure quote when triggered by travel search terms
    """
    # Get the search term that triggered this easter egg
    search_term = request.GET.get('search', '')
    quote_data = get_random_travel_quote()
    
    # Add some context about how they got here
    trigger_context = {
        'search_term': search_term,
        'message': f'Your search for "{search_term}" has awakened your wanderlust and spirit of adventure...'
    }
    
    context = {
        'quote': quote_data,
        'trigger': trigger_context,
        'page_title': 'A Wanderlust Moment of Adventure'
    }
    
    return render(request, 'core/travel_easter_egg.html', context)


@login_required(login_url="accounts:login")
@csrf_exempt
@require_http_methods(["POST"])
def get_new_travel_quote(request):
    """
    AJAX endpoint to get a new random travel quote
    """
    try:
        data = json.loads(request.body)
        category = data.get('category', None)
        author = data.get('author', None)
        
        if author:
            quotes = get_travel_quotes_by_author(author)
            if quotes:
                import random
                quote_data = random.choice(quotes)
            else:
                quote_data = get_random_travel_quote()
        elif category:
            quote_data = get_travel_quote_by_category(category)
        else:
            quote_data = get_random_travel_quote()
        
        return JsonResponse({
            'success': True,
            'quote': quote_data
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def check_travel_search(search_term):
    """
    Helper function to check if a search term should trigger the travel easter egg
    This can be imported and used in other views
    """
    return is_travel_search(search_term)
