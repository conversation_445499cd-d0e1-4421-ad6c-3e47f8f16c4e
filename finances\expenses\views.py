from datetime import datetime
from decimal import Decimal
from django.shortcuts import render
from django.contrib.auth.decorators import login_required


from finances.reports.forms import DatePicker
from finances.reports.utils import get_expenditure_data


@login_required(login_url="accounts:login")
def expenditure_statement(request):
    """
    Generate expenditure statement using modern Outflow system.
    This replaces the deprecated Expense/Expenditure model approach.
    """
    form = DatePicker(request.POST or None)
    start_date = None
    end_date = None

    if request.method == 'POST' and form.is_valid():
        start_date = form.cleaned_data['start_date']
        end_date = form.cleaned_data['end_date']
    from students.models import Term
    term = Term.objects.get_active()
    expenses, outflow = get_expenditure_data(start_date, end_date, term)
    total_expense = sum((line.calculate_total()
                        for line in outflow), Decimal())

    context = {
        "expenses": expenses,
        "outflow": outflow,
        "total_expense": total_expense,
        "start_date": start_date,
        "end_date": end_date,
        "form": form
    }

    return render(request, 'reports/expenditure_statement.html', context)


@login_required(login_url="accounts:login")
def generate_expenditure_statement(request, start_date=None, end_date=None):
    """
    Generate downloadable expenditure statement using modern Outflow system.
    This replaces the deprecated Expense/Expenditure model approach.
    """
    if isinstance(start_date, str):
        start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
    if isinstance(end_date, str):
        end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
    from students.models import Term
    term = Term.objects.get_active()

    expenses, outflow = get_expenditure_data(start_date, end_date, term)
    total_expense = sum((line.calculate_total()
                        for line in outflow), Decimal())

    context = {
        "outflow": outflow,
        "expenses": expenses,
        "start_date": start_date,
        "end_date": end_date,
        "total_expense": total_expense,
        "term": term
    }

    return render(request, 'reports/download_expenditure_statement.html', context)
