from django.contrib import admin
from academics.models import Activity, ActivityType, Assessment, AssessmentType, Enrollment, StudentActivityParticipation, Subject
from students.models import Student


@admin.action(description="Enroll Students to Subjects")
def enroll_students(modeladmin, request, queryset):
    students = Student.objects.filter(is_active=True).select_related("level")

    for student in students:
        for subject in queryset:
            # Check if the subject is taught in the student's level
            if subject.levels.filter(id=student.level.id).exists():
                enrollment, created = Enrollment.objects.get_or_create(
                    student=student,
                    subject=subject,
                    defaults={'term': None}  # Will be set by the save method
                )
                if created:
                    enrollment.save()


@admin.register(AssessmentType)
class AssessmentTypeAdmin(admin.ModelAdmin):
    list_display = ("name", "weight", "is_final", "sequence")
    search_fields = ["name"]


class AssessmentInline(admin.TabularInline):
    model = Assessment
    extra = 3


@admin.register(Enrollment)
class EnrollmentAdmin(admin.ModelAdmin):
    list_display = ("student__name", "subject", "term", "final_grade")
    list_filter = ("term", "subject")
    search_fields = ["student__name"]

    inlines = [AssessmentInline]


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ("name", "abbrv", "category", "get_levels")
    list_filter = ("category",)
    actions = [enroll_students]
    filter_horizontal = ('levels',)

    def get_levels(self, obj):
        """Display the levels where this subject is taught"""
        return ", ".join([level.level_name for level in obj.levels.all()])
    get_levels.short_description = 'Levels'


@admin.register(ActivityType)
class ActivityTypeAdmin(admin.ModelAdmin):
    list_display = ("name", "description")
    search_fields = ["name"]
    prepopulated_fields = {"slug": ("name",)}


class StudentActivityInline(admin.TabularInline):
    model = StudentActivityParticipation
    extra = 1


@admin.register(Activity)
class ActivityAdmin(admin.ModelAdmin):
    list_display = ("activity_type", "subject", "class_assigned", "date")
    list_filter = ("activity_type", "subject", "class_assigned")
    search_fields = ["activity_type__name", "subject__name"]
    inlines = [StudentActivityInline]
