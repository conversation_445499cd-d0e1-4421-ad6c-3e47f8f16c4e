# Reporting & Analytics Guide

## 📊 Overview

This guide provides comprehensive instructions for creating custom reports, data visualizations, and analytics features in the Receipt Generator system, including chart implementation, dashboard development, and export functionality.

## 📈 Chart and Visualization System

### Chart.js Integration

#### Basic Chart Setup
```html
<!-- Include Chart.js and custom chart styles/scripts -->
{% load static %}
<link rel="stylesheet" href="{% static 'css/charts.css' %}">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'js/charts.js' %}"></script>

<!-- Chart container with data attributes -->
<div class="chart-container">
    <h3 class="chart-title">Student Enrollment by Level</h3>
    <div class="chart-wrapper">
        <canvas id="studentEnrollmentChart"
                data-chart-type="bar"
                data-labels="{{ level_names|safe }}"
                data-data="{{ enrollment_counts|safe }}"
                data-label="Student Enrollment">
        </canvas>
    </div>
</div>

<!-- Charts are automatically initialized by charts.js -->
```

#### Gender Distribution Chart
```python
# views.py
def gender_distribution_chart(request):
    """Generate data for gender distribution chart"""
    from django.db.models import Count
    
    gender_data = Student.objects.filter(is_active=True).values('gender').annotate(
        count=Count('id')
    ).order_by('gender')
    
    chart_data = {
        'labels': [item['gender'] for item in gender_data],
        'data': [item['count'] for item in gender_data],
        'colors': ['#7AB2D3', '#F28C8C']  # Blue for Male, Pink for Female
    }
    
    return JsonResponse(chart_data)
```

```javascript
// Using the ChartManager class from charts.js
document.addEventListener('DOMContentLoaded', function() {
    // Fetch gender distribution data
    fetch('/api/gender-distribution/')
        .then(response => response.json())
        .then(data => {
            // Create gender distribution chart using ChartManager
            window.chartManager.createGenderDistributionChart('genderChart', data);
        });
});
```

### Financial Analytics Charts

#### Revenue Trends
```python
def revenue_trends_data(request):
    """Generate monthly revenue trends"""
    from django.db.models import Sum
    from django.utils import timezone
    from datetime import datetime, timedelta
    
    # Get last 12 months of data
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=365)
    
    monthly_revenue = Receipt.objects.filter(
        date_created__range=[start_date, end_date]
    ).extra(
        select={'month': "DATE_FORMAT(date_created, '%%Y-%%m')"}
    ).values('month').annotate(
        total=Sum('amount')
    ).order_by('month')
    
    chart_data = {
        'labels': [item['month'] for item in monthly_revenue],
        'data': [float(item['total']) for item in monthly_revenue],
        'currency': 'NGN'
    }
    
    return JsonResponse(chart_data)
```

```javascript
// Revenue trend line chart
function createRevenueChart(data) {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: [{
                label: `Revenue (${data.currency})`,
                data: data.data,
                borderColor: '#40657F',
                backgroundColor: 'rgba(64, 101, 127, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Monthly Revenue Trends'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('en-NG', {
                                style: 'currency',
                                currency: 'NGN'
                            }).format(value);
                        }
                    }
                }
            }
        }
    });
}
```

### Academic Performance Analytics

#### Grade Distribution Analysis
```python
def grade_distribution_data(request, subject_id=None):
    """Generate grade distribution data"""
    from academics.models import Assessment
    
    queryset = Assessment.objects.filter(enrollment__term__is_active=True)
    
    if subject_id:
        queryset = queryset.filter(enrollment__subject_id=subject_id)
    
    # Define grade ranges
    grade_ranges = [
        ('A', 80, 100),
        ('B', 70, 79),
        ('C', 60, 69),
        ('D', 50, 59),
        ('F', 0, 49)
    ]
    
    distribution = {}
    for grade, min_score, max_score in grade_ranges:
        count = queryset.filter(
            score__gte=min_score,
            score__lte=max_score
        ).count()
        distribution[grade] = count
    
    return JsonResponse({
        'labels': list(distribution.keys()),
        'data': list(distribution.values()),
        'colors': ['#74C69D', '#7AB2D3', '#B9D8EB', '#F28C8C', '#FF6B6B']
    })
```

## 📋 Custom Report Builder

### Report Configuration System

#### Report Model
```python
# models.py
class CustomReport(models.Model):
    REPORT_TYPES = [
        ('student', 'Student Report'),
        ('financial', 'Financial Report'),
        ('academic', 'Academic Report'),
        ('attendance', 'Attendance Report'),
    ]
    
    name = models.CharField(max_length=200)
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES)
    description = models.TextField(blank=True)
    filters = models.JSONField(default=dict)  # Store filter configuration
    columns = models.JSONField(default=list)  # Store column configuration
    created_by = models.ForeignKey('accounts.CustomUser', on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    is_public = models.BooleanField(default=False)
    
    def generate_report(self):
        """Generate report data based on configuration"""
        generator = ReportGenerator(self)
        return generator.generate()

class ReportGenerator:
    def __init__(self, report_config):
        self.config = report_config
        self.filters = report_config.filters
        self.columns = report_config.columns
    
    def generate(self):
        """Generate report data"""
        if self.config.report_type == 'student':
            return self.generate_student_report()
        elif self.config.report_type == 'financial':
            return self.generate_financial_report()
        elif self.config.report_type == 'academic':
            return self.generate_academic_report()
        else:
            raise ValueError(f"Unknown report type: {self.config.report_type}")
    
    def generate_student_report(self):
        """Generate student report"""
        queryset = Student.objects.select_related('level')
        
        # Apply filters
        if 'level' in self.filters:
            queryset = queryset.filter(level__name__in=self.filters['level'])
        
        if 'gender' in self.filters:
            queryset = queryset.filter(gender__in=self.filters['gender'])
        
        if 'is_active' in self.filters:
            queryset = queryset.filter(is_active=self.filters['is_active'])
        
        # Select only required columns
        if self.columns:
            values = []
            for col in self.columns:
                if col == 'level_name':
                    values.append('level__name')
                else:
                    values.append(col)
            queryset = queryset.values(*values)
        
        return list(queryset)
```

#### Report Builder Interface
```html
<!-- Report builder form -->
<form id="reportBuilderForm" method="post">
    {% csrf_token %}
    
    <!-- Report Type Selection -->
    <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700">Report Type</label>
        <select name="report_type" id="reportType" class="mt-1 block w-full rounded-md border-gray-300">
            <option value="student">Student Report</option>
            <option value="financial">Financial Report</option>
            <option value="academic">Academic Report</option>
        </select>
    </div>
    
    <!-- Column Selection -->
    <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700">Columns to Include</label>
        <div id="columnSelection" class="mt-2 space-y-2">
            <!-- Dynamic column checkboxes -->
        </div>
    </div>
    
    <!-- Filters -->
    <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700">Filters</label>
        <div id="filterSection" class="mt-2 space-y-2">
            <!-- Dynamic filter inputs -->
        </div>
    </div>
    
    <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">
        Generate Report
    </button>
</form>

<script>
// Dynamic report builder
document.getElementById('reportType').addEventListener('change', function() {
    const reportType = this.value;
    updateColumnOptions(reportType);
    updateFilterOptions(reportType);
});

function updateColumnOptions(reportType) {
    const columnOptions = {
        'student': [
            {value: 'name', label: 'Student Name'},
            {value: 'student_id', label: 'Student ID'},
            {value: 'gender', label: 'Gender'},
            {value: 'level_name', label: 'Class Level'},
            {value: 'is_active', label: 'Active Status'}
        ],
        'financial': [
            {value: 'student_name', label: 'Student Name'},
            {value: 'amount', label: 'Amount'},
            {value: 'balance', label: 'Balance'},
            {value: 'category', label: 'Fee Category'},
            {value: 'term', label: 'Term'}
        ]
    };
    
    const container = document.getElementById('columnSelection');
    container.innerHTML = '';
    
    columnOptions[reportType].forEach(option => {
        const checkbox = document.createElement('div');
        checkbox.innerHTML = `
            <label class="flex items-center">
                <input type="checkbox" name="columns" value="${option.value}" class="mr-2">
                ${option.label}
            </label>
        `;
        container.appendChild(checkbox);
    });
}
</script>
```

## 📄 Export Functionality

### PDF Report Generation

#### PDF Export with ReportLab
```python
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from django.http import HttpResponse

def export_student_report_pdf(request):
    """Export student report as PDF"""
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="student_report.pdf"'
    
    # Create PDF document
    doc = SimpleDocTemplate(response, pagesize=A4)
    elements = []
    
    # Styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    
    # Title
    title = Paragraph("Student Report", title_style)
    elements.append(title)
    elements.append(Spacer(1, 12))
    
    # Get data
    students = Student.objects.select_related('level').filter(is_active=True)
    
    # Create table data
    data = [['Name', 'Student ID', 'Gender', 'Level']]  # Header
    
    for student in students:
        data.append([
            student.name,
            student.student_id or 'N/A',
            student.gender,
            student.level.name
        ])
    
    # Create table
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    elements.append(table)
    
    # Build PDF
    doc.build(elements)
    return response
```

### Excel Export

#### Excel Export with openpyxl
```python
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from django.http import HttpResponse

def export_financial_report_excel(request):
    """Export financial report as Excel"""
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="financial_report.xlsx"'
    
    workbook = Workbook()
    worksheet = workbook.active
    worksheet.title = "Financial Report"
    
    # Header styling
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="40657F", end_color="40657F", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Headers
    headers = ['Student Name', 'Student ID', 'Level', 'Fee Category', 'Amount', 'Balance']
    for col, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Data
    from finances.fee_management.models import FeeAccount
    fee_accounts = FeeAccount.objects.select_related(
        'student', 'student__level', 'category'
    ).filter(term__is_active=True)
    
    for row, account in enumerate(fee_accounts, 2):
        worksheet.cell(row=row, column=1, value=account.student.name)
        worksheet.cell(row=row, column=2, value=account.student.student_id)
        worksheet.cell(row=row, column=3, value=account.student.level.name)
        worksheet.cell(row=row, column=4, value=account.category.name)
        worksheet.cell(row=row, column=5, value=float(account.amount))
        worksheet.cell(row=row, column=6, value=float(account.balance))
    
    # Auto-adjust column widths
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        worksheet.column_dimensions[column_letter].width = adjusted_width
    
    workbook.save(response)
    return response
```

## 📊 Dashboard Development

### Real-time Analytics Dashboard

#### Dashboard Data API
```python
def dashboard_analytics_api(request):
    """API endpoint for dashboard analytics"""
    from django.db.models import Count, Sum, Avg
    
    # Student statistics
    total_students = Student.objects.filter(is_active=True).count()
    students_by_gender = Student.objects.filter(is_active=True).values('gender').annotate(
        count=Count('id')
    )
    
    # Financial statistics
    total_revenue = Receipt.objects.aggregate(total=Sum('amount'))['total'] or 0
    outstanding_balance = FeeAccount.objects.aggregate(total=Sum('balance'))['total'] or 0
    
    # Academic statistics
    average_performance = Assessment.objects.aggregate(avg=Avg('score'))['avg'] or 0
    
    # Recent activities
    recent_receipts = Receipt.objects.select_related('fee_account__student').order_by('-date_created')[:5]
    recent_admissions = Student.objects.filter(is_active=True).order_by('-id')[:5]
    
    dashboard_data = {
        'statistics': {
            'total_students': total_students,
            'students_by_gender': list(students_by_gender),
            'total_revenue': float(total_revenue),
            'outstanding_balance': float(outstanding_balance),
            'average_performance': round(float(average_performance), 2)
        },
        'recent_activities': {
            'receipts': [
                {
                    'student': receipt.fee_account.student.name,
                    'amount': float(receipt.amount),
                    'date': receipt.date_created.strftime('%Y-%m-%d')
                }
                for receipt in recent_receipts
            ],
            'admissions': [
                {
                    'name': student.name,
                    'level': student.level.name,
                    'student_id': student.student_id
                }
                for student in recent_admissions
            ]
        }
    }
    
    return JsonResponse(dashboard_data)
```

#### Interactive Dashboard Components
```html
<!-- Dashboard grid layout -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Statistics Cards -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-users text-white"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Students</p>
                <p class="text-2xl font-semibold text-gray-900" id="totalStudents">-</p>
            </div>
        </div>
    </div>
    
    <!-- More cards... -->
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium mb-4">Student Enrollment</h3>
        <canvas id="enrollmentChart"></canvas>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium mb-4">Revenue Trends</h3>
        <canvas id="revenueChart"></canvas>
    </div>
</div>

<script>
// Auto-refresh dashboard data
function refreshDashboard() {
    fetch('/api/dashboard-analytics/')
        .then(response => response.json())
        .then(data => {
            updateStatistics(data.statistics);
            updateCharts(data.statistics);
            updateRecentActivities(data.recent_activities);
        })
        .catch(error => console.error('Error:', error));
}

// Update statistics cards
function updateStatistics(stats) {
    document.getElementById('totalStudents').textContent = stats.total_students;
    // Update other statistics...
}

// Refresh every 5 minutes
setInterval(refreshDashboard, 5 * 60 * 1000);

// Initial load
refreshDashboard();
</script>
```

## 🔧 Best Practices

### Report Development Guidelines
1. **Use efficient queries** with select_related() and prefetch_related()
2. **Implement pagination** for large datasets
3. **Cache frequently accessed reports** to improve performance
4. **Provide multiple export formats** (PDF, Excel, CSV)
5. **Include filters and search functionality** for better usability
6. **Use consistent styling** across all reports
7. **Implement proper error handling** for data export
8. **Add progress indicators** for long-running reports

### Chart and Visualization Best Practices
- Use appropriate chart types for data representation
- Implement responsive design for mobile compatibility
- Use consistent color schemes matching the application theme
- Provide interactive tooltips and legends
- Include data labels where appropriate
- Optimize chart performance for large datasets
- Implement real-time updates where beneficial

### Performance Considerations
- Cache chart data for frequently accessed reports
- Use database aggregations instead of Python calculations
- Implement lazy loading for dashboard components
- Optimize database queries with proper indexing
- Use pagination for large report datasets
- Implement background processing for complex reports

This guide provides the foundation for building comprehensive reporting and analytics features in the Receipt Generator system.
