# Tiny Feet Academy - School Management System

A comprehensive Django-based school management system designed to handle student enrollment, fee management, academic records, and financial operations with automated receipt generation and PDF export capabilities.

## 🎯 Overview

Receipt Generator is a full-featured school management system that streamlines administrative tasks for educational institutions. The system provides modules for student management, fee collection with automated receipt generation, academic record keeping, financial reporting, and comprehensive analytics.

## ✨ Key Features

### 📋 Student Management

- **Student Registration & Enrollment**: Complete student profile management with unique ID generation
- **Level/Grade Management**: Organize students by academic levels with customizable abbreviations
- **Academic Year & Term Management**: Flexible academic calendar with term-based operations
- **Student Status Tracking**: Active/inactive status management with automated fee account handling

### 💰 Financial Management

- **Automated Receipt Generation**: Generate unique receipt numbers with PDF export functionality
- **Fee Account Management**: Individual fee accounts per student with payment tracking
- **Payment Processing**: Multiple payment methods with detailed transaction records
- **Excel Integration**: Bulk payment processing via Excel file uploads
- **Fee Waivers**: Flexible fee waiver system for special circumstances

### 📊 Academic Records

- **Subject Management**: Course enrollment and subject assignment
- **Assessment System**: Multiple assessment types with grade management
- **Activity Tracking**: Student participation in academic and extracurricular activities
- **Performance Analytics**: Comprehensive grade analysis and reporting

### 📈 Reporting & Analytics

- **Financial Reports**: Income statements, expenditure tracking, and balance sheets
- **Academic Reports**: Grade reports, performance analytics, and progress tracking
- **Receipt Management**: Searchable receipt database with PDF generation
- **Data Visualization**: Charts and graphs using Chart.js for visual analytics

### 🎨 Modern UI/UX

- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Modern Interface**: Clean, professional design with smooth animations
- **Accessibility**: WCAG compliant with proper contrast and navigation
- **Print-Friendly**: Optimized layouts for document printing and PDF generation

## 🛠️ Technology Stack

### Backend

- **Django 5.1.2**: Modern Python web framework
- **PostgreSQL**: Production database (SQLite for development)
- **ReportLab**: PDF generation for receipts and reports
- **WeasyPrint**: Advanced PDF rendering capabilities
- **Pandas & NumPy**: Data analysis and processing
- **OpenPyXL**: Excel file processing

### Frontend

- **Tailwind CSS 3.4.14**: Utility-first CSS framework
- **Chart.js 4.4.7**: Interactive data visualization
- **Font Awesome**: Icon library
- **Vanilla JavaScript**: Enhanced user interactions

### Infrastructure

- **Gunicorn**: WSGI HTTP Server for production
- **Environment Variables**: Secure configuration management
- **Static File Management**: Optimized asset delivery

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Node.js 16+ (for Tailwind CSS)
- PostgreSQL (for production)

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd receipt_gen
   ```

2. **Set up Python environment**

   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Install Node.js dependencies**

   ```bash
   npm install
   ```

4. **Environment Configuration**
   Create a `.env` file in the root directory:

   ```env
   DJANGO_SECRET_KEY=your-secret-key-here
   DEBUG=True
   DEV_MODE=True
   DJANGO_ALLOWED_HOST=localhost,127.0.0.1
   DATABASE_URL=your-database-url-here  # For production
   ```

5. **Database Setup**

   ```bash
   python manage.py migrate
   python manage.py createsuperuser
   ```

6. **Compile Tailwind CSS**

   ```bash
   npx tailwindcss -i ./src/tailwind.css -o ./assets/css/tailwind.css --watch
   ```

7. **Run the development server**
   ```bash
   python manage.py runserver
   ```

Visit `http://127.0.0.1:8000` to access the application.

## 📁 Project Structure

```
receipt_gen/
├── academics/              # Academic records and assessment management
├── accounts/               # User authentication and account management
├── assets/                 # Static assets (CSS, JS, images)
├── core/                   # Core utilities and shared functionality
├── docs/                   # Project documentation
├── finances/               # Financial management and receipt generation
│   ├── book_keeping/       # Double-entry bookkeeping system
│   ├── expenses/           # Expenditure tracking
│   ├── fee_management/     # Fee collection and receipt generation
│   ├── income/             # Income tracking and reporting
│   └── reports/            # Financial reporting
├── helpers/                # Utility functions and tools
├── receipt_gen/            # Django project settings
├── students/               # Student management and enrollment
├── templates/              # HTML templates with modern design

```

## 🔧 Configuration

### Database Configuration

The system supports both SQLite (development) and PostgreSQL (production):

- **Development**: Set `DEV_MODE=True` in `.env` to use SQLite
- **Production**: Set `DATABASE_URL` with PostgreSQL connection string

### Static Files

Static files are managed through Django's static file system:

- Development: Files served from `assets/` directory
- Production: Collected to `static/` directory

### Session Management

- Sessions expire when browser closes
- 30-minute session timeout with automatic renewal
- Secure session handling for user authentication

## 📋 Usage Guide

### Student Management

1. **Add Students**: Navigate to student management and register new students
2. **Assign Levels**: Organize students by grade/level with automatic ID generation
3. **Manage Status**: Activate/deactivate students with automatic fee account handling

### Fee Management

1. **Payment Processing**: Record payments with automatic receipt generation
2. **Excel Uploads**: Bulk process payments via Excel file uploads
3. **PDF Generation**: Generate and download receipt PDFs
4. **Fee Waivers**: Apply waivers for special circumstances

### Academic Records

1. **Subject Enrollment**: Assign students to subjects and courses
2. **Grade Entry**: Record assessments and grades by level and subject
3. **Activity Tracking**: Monitor student participation in activities
4. **Performance Analysis**: Generate academic performance reports

### Financial Reporting

1. **Income Tracking**: Monitor fee collection and revenue
2. **Expense Management**: Track expenditures and operational costs
3. **Financial Statements**: Generate comprehensive financial reports
4. **Analytics Dashboard**: View financial trends and insights

## 🎨 Design System

The application uses a comprehensive design system with:

### Color Palette

- **Dark Blue (#40657F)**: Titles and accents
- **Base Blue (#7AB2D3)**: Primary elements
- **Light Blue (#B9D8EB)**: Backgrounds
- **Pale Blue (#E2F1F9)**: Cards and containers
- **Navy Gray (#2C3E50)**: Text content
- **Off-White (#F7FAFC)**: Page backgrounds
- **Accent Coral (#F28C8C)**: Warnings and alerts
- **Accent Green (#74C69D)**: Success messages

### Typography

- **Primary Font**: System fonts with fallbacks
- **Login Pages**: Roboto Sans for enhanced readability
- **Responsive Sizing**: Adaptive font sizes across devices

### Components

- **Modern Cards**: Rounded corners with subtle shadows
- **Gradient Buttons**: Interactive hover effects
- **Animated Elements**: Smooth transitions and micro-interactions
- **Glass Effects**: Modern glassmorphism design elements

## 🔒 Security Features

- **User Authentication**: Django's built-in authentication system
- **Session Management**: Secure session handling with timeouts
- **CSRF Protection**: Cross-site request forgery protection
- **Environment Variables**: Secure configuration management
- **Database Security**: Parameterized queries and ORM protection

## 📊 Analytics & Reporting

### Financial Analytics

- Revenue tracking and trends
- Expenditure analysis
- Profit/loss statements
- Payment collection rates

### Academic Analytics

- Grade distribution analysis
- Student performance trends
- Subject-wise performance
- Activity participation rates

### Visual Reporting

- Interactive charts with Chart.js
- Exportable PDF reports
- Print-friendly layouts
- Mobile-responsive dashboards

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow Django best practices
- Use Tailwind CSS for styling
- Write comprehensive tests
- Document new features
- Maintain responsive design principles

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Create an issue in the repository
- Check the documentation in the `docs/` directory
- Review the administration task guide for extending functionality

## 🔄 Version History

- **v1.0.0**: Initial release with core functionality
- **v1.1.0**: Enhanced UI/UX with Tailwind CSS
- **v1.2.0**: Advanced reporting and analytics
- **v1.3.0**: Excel integration and bulk operations

---

**North Form** - Streamlining school administration with modern technology and intuitive design.
