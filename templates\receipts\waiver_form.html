{% extends 'base.html' %} {% load static %}
<!--  -->
{% block title %}Fee Waiver Form | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-5xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex items-center gap-4 mb-4">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-lg icon-float"
      >
        <i class="fas fa-hand-holding-usd text-white text-xl icon-pulse"></i>
      </div>
      <div>
        <h1
          class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in"
        >
          Fee Waiver
        </h1>
        <div
          class="w-20 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full mt-2 accent-line-grow"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in">
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#40657F]">Receipts</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#F28C8C] font-semibold">Fee Waiver</span>
    </nav>
  </div>

  <!-- Main Form Section -->
  <div class="card-modern p-8 form-fade-in">
    <!-- Form Header -->
    <div class="text-center form-header-slide-in">
      <div
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white px-8 py-4 rounded-2xl shadow-lg"
      >
        <i class="fas fa-percent text-xl"></i>
        <h2 class="text-2xl font-bold font-display">Fee Waiver Application</h2>
      </div>
    </div>

    <!-- Main Form Card -->
    <div
      class="bg-white rounded-2xl shadow-xl border border-[#B9D8EB]/50 overflow-hidden main-form-slide-in"
    >
      <!-- Form Header Info -->
      <div
        class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] p-8 border-b border-[#B9D8EB]/50"
      >
        <div class="flex items-center gap-4 mb-6">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center shadow-lg"
          >
            <i class="fas fa-hand-holding-heart text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
              Application Details
            </h3>
            <p class="text-[#40657F] text-sm">
              Apply for fee reduction or waiver assistance
            </p>
          </div>
        </div>

        <!-- Information Card -->
        <div
          class="bg-gradient-to-r from-[#F28C8C]/10 to-[#e07575]/10 rounded-xl p-6 border border-[#F28C8C]/20"
        >
          <div class="flex items-center gap-3 mb-4">
            <div
              class="w-8 h-8 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-full flex items-center justify-center"
            >
              <i class="fas fa-info text-white text-sm"></i>
            </div>
            <h4 class="font-bold text-[#2C3E50] font-display">
              Important Information
            </h4>
          </div>
          <div class="space-y-3 text-sm">
            <div class="flex items-center gap-3">
              <div
                class="w-5 h-5 bg-[#74C69D] rounded-full flex items-center justify-center"
              >
                <i class="fas fa-check text-white text-xs"></i>
              </div>
              <p class="text-[#40657F]">
                Fee waivers are subject to approval by the administration
              </p>
            </div>
            <div class="flex items-center gap-3">
              <div
                class="w-5 h-5 bg-[#74C69D] rounded-full flex items-center justify-center"
              >
                <i class="fas fa-check text-white text-xs"></i>
              </div>
              <p class="text-[#40657F]">
                Provide accurate information and supporting documents
              </p>
            </div>
            <div class="flex items-center gap-3">
              <div
                class="w-5 h-5 bg-[#F28C8C] rounded-full flex items-center justify-center"
              >
                <i class="fas fa-clock text-white text-xs"></i>
              </div>
              <p class="text-[#40657F]">
                Processing may take 3-5 business days
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Fields -->
      <div class="p-8">
        <div class="flex items-center gap-4 mb-8">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg"
          >
            <i class="fas fa-edit text-white text-lg"></i>
          </div>
          <div>
            <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
              Application Form
            </h3>
            <p class="text-[#40657F] text-sm">
              Complete all required fields below
            </p>
          </div>
          <div
            class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
          ></div>
        </div>

        <form method="post" class="space-y-8 form-fields-fade-in">
          {% csrf_token %}

          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            {% for field in form %}
            <div
              class="form-field-group {% if field.field.widget.input_type == 'textarea' %}md:col-span-2{% endif %}"
            >
              <label class="form-label" for="{{ field.auto_id }}">
                <i
                  class="fas fa-{% if 'student' in field.name %}user{% elif 'amount' in field.name %}money-bill{% elif 'reason' in field.name %}comment{% elif 'date' in field.name %}calendar{% elif 'email' in field.name %}envelope{% elif 'phone' in field.name %}phone{% else %}info-circle{% endif %} mr-2 text-[#7AB2D3]"
                ></i>
                {{ field.label }} {% if field.field.required %}
                <span class="text-[#F28C8C] ml-1">*</span>
                {% endif %}
              </label>
              <div class="form-field-wrapper">
                {{ field }} {% if field.errors %}
                <div class="form-error">
                  <i class="fas fa-exclamation-triangle mr-1"></i>
                  {{ field.errors|striptags }}
                </div>
                {% endif %}
              </div>
            </div>
            {% endfor %}
          </div>

          <!-- Action Buttons -->
          <div
            class="flex flex-col sm:flex-row justify-center items-center gap-4 w-full pt-8 border-t border-[#B9D8EB]/30 buttons-slide-in"
          >
            <button
              type="submit"
              class="inline-flex items-center gap-3 bg-gradient-to-r from-[#F28C8C] to-[#e07575] text-white font-bold py-4 px-8 rounded-xl hover:from-[#e07575] hover:to-[#F28C8C] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group w-full sm:w-auto"
            >
              <i
                class="fas fa-paper-plane group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
              ></i>
              <span>Submit Application</span>
            </button>
            <button
              type="reset"
              class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group w-full sm:w-auto"
            >
              <i
                class="fas fa-undo group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
              ></i>
              <span>Reset Form</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>

<style>
  /* Header Animations */
  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: breadcrumbSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Form Animations */
  .form-fade-in {
    opacity: 0;
    animation: formFadeIn 0.8s ease-out 0.8s forwards;
  }

  .form-header-slide-in {
    opacity: 0;
    transform: translateY(-20px);
    animation: formHeaderSlideIn 0.8s ease-out 1s forwards;
  }

  .main-form-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: mainFormSlideIn 0.8s ease-out 1.2s forwards;
  }

  .form-fields-fade-in {
    opacity: 0;
    animation: formFieldsFadeIn 0.8s ease-out 1.4s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: buttonsSlideIn 0.8s ease-out 1.6s forwards;
  }

  /* Form Field Styling */
  .form-field-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-label {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  .form-field-wrapper {
    position: relative;
  }

  .form-field-wrapper input,
  .form-field-wrapper select,
  .form-field-wrapper textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #b9d8eb;
    border-radius: 0.75rem;
    background-color: #f7fafc;
    color: #2c3e50;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .form-field-wrapper input:focus,
  .form-field-wrapper select:focus,
  .form-field-wrapper textarea:focus {
    outline: none;
    border-color: #f28c8c;
    background-color: white;
    box-shadow: 0 0 0 4px rgba(242, 140, 140, 0.2);
  }

  .form-error {
    display: flex;
    align-items: center;
    color: #f28c8c;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.25rem;
  }

  /* Keyframe Definitions */
  @keyframes breadcrumbSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes formFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes formHeaderSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes mainFormSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes formFieldsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes buttonsSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .form-fields-fade-in {
      animation-delay: 1s;
    }
  }
</style>

{% endblock %}
