from django.core.management.base import BaseCommand
from django.db import transaction
from finances.fee_management.models import FeeAccount
from students.models import Student, Term
from finances.fee_management.models import FeeCategory


class Command(BaseCommand):
    help = 'Update is_paid status using dynamic amount calculations and verify system integrity'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )
        parser.add_argument(
            '--term-id',
            type=int,
            help='Only process fee accounts for specific term ID',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        term_id = options.get('term_id')

        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )

        # Filter fee accounts
        queryset = FeeAccount.objects.all()
        if term_id:
            queryset = queryset.filter(term_id=term_id)

        # Group by student, category, term for efficient processing
        groups = {}
        for fee_account in queryset:
            key = (fee_account.student_id, fee_account.category_id, fee_account.term_id)
            if key not in groups:
                groups[key] = []
            groups[key].append(fee_account)

        total_groups = len(groups)
        updated_accounts = 0
        inconsistent_accounts = []

        self.stdout.write(f'Processing {total_groups} fee account groups...')

        for i, (key, fee_accounts) in enumerate(groups.items(), 1):
            student_id, category_id, term_id = key
            
            try:
                student = Student.objects.get(id=student_id)
                category = FeeCategory.objects.get(id=category_id)
                term = Term.objects.get(id=term_id)
            except (Student.DoesNotExist, FeeCategory.DoesNotExist, Term.DoesNotExist):
                self.stdout.write(
                    self.style.ERROR(f'Missing related objects for group {key}')
                )
                continue

            if not dry_run:
                with transaction.atomic():
                    # Use the efficient group update method
                    FeeAccount.update_paid_status_for_group(student, category, term)
                    updated_accounts += len(fee_accounts)
            else:
                # In dry run, just verify the dynamic calculations work
                for fee_account in fee_accounts:
                    try:
                        dynamic_amount = fee_account.amount
                        current_balance = fee_account.current_balance
                        is_fully_paid = fee_account.is_fully_paid

                        # Log successful calculation
                        self.stdout.write(
                            f"  Account {fee_account.id}: amount={dynamic_amount}, "
                            f"balance={current_balance}, paid={is_fully_paid}"
                        )
                    except Exception as e:
                        inconsistent_accounts.append({
                            'id': fee_account.id,
                            'student': str(fee_account.student),
                            'category': str(fee_account.category),
                            'term': str(fee_account.term),
                            'error': str(e)
                        })

            if i % 10 == 0:
                self.stdout.write(f'Processed {i}/{total_groups} groups...')

        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'DRY RUN COMPLETE')
            )
            if inconsistent_accounts:
                self.stdout.write(
                    self.style.WARNING(f'Found {len(inconsistent_accounts)} accounts with calculation errors:')
                )
                for account in inconsistent_accounts[:10]:  # Show first 10
                    self.stdout.write(
                        f"  ID {account['id']}: {account['student']} - {account['category']} "
                        f"Error: {account['error']}"
                    )
                if len(inconsistent_accounts) > 10:
                    self.stdout.write(f"  ... and {len(inconsistent_accounts) - 10} more")
            else:
                self.stdout.write(
                    self.style.SUCCESS('All dynamic calculations working correctly')
                )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully updated {updated_accounts} fee accounts')
            )

        self.stdout.write(
            self.style.SUCCESS('Migration complete!')
        )
