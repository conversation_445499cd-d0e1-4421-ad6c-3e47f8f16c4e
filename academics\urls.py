from django.urls import path

# Import existing views from main views.py
from . import views as academics_views

# Import academic management views
from academics.academic_management_views import (
    academic_management_dashboard, add_subject, edit_subject, add_assessment_type,
    edit_assessment_type, add_activity_type, edit_activity_type, add_activity,
    edit_activity, bulk_delete_subjects, bulk_delete_assessment_types,
    bulk_delete_activities, subject_details, activity_details,
    activity_type_management, activity_type_details, bulk_delete_activity_types
)

# Import profile views
from accounts.views.profile_views import user_profile, edit_profile, change_password

app_name = 'academics'

urlpatterns = [
    path('', academics_views.dashboard, name="dashboard"),
    path('grades/', academics_views.view_grades, name="view_grades"),
    path(
        'grades/enter/<slug:level_slug>', academics_views.assessment_type, name="assessment_type"
    ),
    path('grades/enter/', academics_views.enter_by_levels, name="enter_by_level"),
    path(
        'grades/enter/<slug:level_slug>/<slug:type_slug>/', academics_views.enter_by_subjects, name="enter_by_subjects"
    ),
    path(
        'grades/enter/<slug:level_slug>/<slug:type_slug>/<slug:subject_slug>/', academics_views.enter_grades, name="enter_grades"
    ),
    path('grades/view/', academics_views.view_grades, name="view_grades"),
    path('students/', academics_views.students, name="students"),
    path('students/<slug:slug>/', academics_views.student_details, name="student_details"),
    path('levels/', academics_views.levels, name="levels"),
    path('levels/<slug:slug>/', academics_views.level_details, name="level_details"),
    path('activities/', academics_views.activities, name="activities"),
    path(
        'activities/<int:id>/enter_results/', academics_views.enter_results, name="enter_results"
    ),
    path('activities/results/', academics_views.view_activity_results,
         name="view_activity_results"),
    path('report-card/<slug:student_id>/select/',
         academics_views.report_card_selection, name="report_card_selection"),
    path('report-card/<slug:student_id>/',
         academics_views.generate_report_card, name="generate_report_card"),
    path('report-card/<slug:student_id>/<int:term_id>/',
         academics_views.generate_report_card, name="generate_report_card_term"),

    # Academic Management URLs
    path('management/', academic_management_dashboard, name="academic_management_dashboard"),

    # Subject Management URLs
    path('subjects/add/', add_subject, name="add_subject"),
    path('subjects/<int:pk>/edit/', edit_subject, name="edit_subject"),
    path('subjects/<int:pk>/details/', subject_details, name="subject_details"),
    path('subjects/bulk-delete/', bulk_delete_subjects, name="bulk_delete_subjects"),

    # Assessment Type Management URLs
    path('assessment-types/add/', add_assessment_type, name="add_assessment_type"),
    path('assessment-types/<int:pk>/edit/', edit_assessment_type, name="edit_assessment_type"),
    path('assessment-types/bulk-delete/', bulk_delete_assessment_types, name="bulk_delete_assessment_types"),

    # Activity Type Management URLs
    path('activity-types/', activity_type_management, name="activity_type_management"),
    path('activity-types/add/', add_activity_type, name="add_activity_type"),
    path('activity-types/<int:pk>/edit/', edit_activity_type, name="edit_activity_type"),
    path('activity-types/<int:pk>/details/', activity_type_details, name="activity_type_details"),
    path('activity-types/bulk-delete/', bulk_delete_activity_types, name="bulk_delete_activity_types"),

    # Activity Management URLs
    path('activities/add/', add_activity, name="add_activity"),
    path('activities/<int:pk>/edit/', edit_activity, name="edit_activity"),
    path('activities/<int:pk>/details/', activity_details, name="activity_details"),
    path('activities/bulk-delete/', bulk_delete_activities, name="bulk_delete_activities"),

    # Profile URLs (academics-specific)
    path('profile/', lambda request: user_profile(request, template_name='academics/profile/user_profile.html'), name="user_profile"),
    path('profile/edit/', lambda request: edit_profile(request, template_name='academics/profile/edit_profile.html'), name="edit_profile"),
    path('profile/change-password/', lambda request: change_password(request, template_name='academics/profile/change_password.html'), name="change_password"),

]
