{% extends 'base.html' %} {% load static %} {% block content %}
<section class="w-full max-w-6xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-6">
    <div class="flex items-center gap-3 mb-4">
      <div
        class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-users text-white text-lg"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-2xl text-gray-800">Students</h1>
        <div
          class="w-16 h-1 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] rounded-full"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-2 text-sm font-medium">
      <span class="text-gray-500">Home</span>
      <i class="fa-solid fa-chevron-right text-gray-400 text-xs"></i>
      <span class="text-[var(--primary-color)] font-semibold"
        >Student Admission Form</span
      >
    </nav>
  </div>

  <!-- Main Form Section -->
  <section class="card-modern p-8 space-y-8">
    <!-- Header -->
    <div
      class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6"
    >
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg"
        >
          <i class="fas fa-user-plus text-white text-xl"></i>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-800 font-display">
            Add New Student
          </h1>
          <p class="text-gray-600 font-medium">
            Fill in the student information below
          </p>
        </div>
      </div>
      <a
        class="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] text-white font-bold py-3 px-6 rounded-xl hover:from-[var(--primary-dark)] hover:to-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/30 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl btn-modern group w-full md:w-fit"
        href="{% url 'students:admission_by_excel' %}"
      >
        <i
          class="fas fa-file-excel mr-2 group-hover:scale-110 transition-transform"
        ></i>
        Upload Excel File
      </a>
    </div>

    <!-- Success Messages -->
    {% for message in messages %}
    <div
      class="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200/50 rounded-2xl p-5 flex items-center space-x-4 shadow-lg backdrop-blur-sm"
    >
      <div
        class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-check text-white text-sm"></i>
      </div>
      <div class="flex-1">
        <p class="text-green-700 font-semibold text-sm">Success</p>
        <p class="text-green-600 text-sm">{{message}}</p>
      </div>
    </div>
    {% endfor %}

    <!-- Form -->
    <form class="space-y-8" method="post">
      {% csrf_token %}

      <!-- Form Fields Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for field in form %}
        <div
          class="form-field space-y-3 {% if field.field.widget.input_type == 'textarea' %}md:col-span-2 lg:col-span-3{% endif %}"
        >
          <label
            class="flex items-center gap-2 font-bold text-gray-700 text-sm"
            for="{{ field.auto_id }}"
          >
            <div
              class="w-4 h-4 bg-gradient-to-br from-[var(--primary-color)] to-[var(--primary-dark)] rounded-lg flex items-center justify-center"
            >
              <i class="fas fa-circle text-white text-xs"></i>
            </div>
            {{ field.label }} {% if field.field.required %}
            <span class="text-red-500 text-xs">*</span>
            {% endif %}
          </label>
          <div class="relative">
            {{ field }}
            <div
              class="absolute inset-0 rounded-xl bg-gradient-to-r from-[var(--primary-color)]/5 to-[var(--primary-dark)]/5 opacity-0 transition-opacity duration-300 pointer-events-none focus-within:opacity-100"
            ></div>
          </div>
          {% if field.errors %}
          <div class="text-red-600 text-xs font-medium flex items-center gap-1">
            <i class="fas fa-exclamation-triangle"></i>
            {{ field.errors.0 }}
          </div>
          {% endif %}
        </div>
        {% endfor %}
      </div>

      <!-- Action Buttons -->
      <div
        class="flex flex-col sm:flex-row justify-center items-center gap-4 pt-8 border-t border-gray-200/50"
      >
        <button
          class="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] text-white font-bold py-4 px-8 rounded-2xl hover:from-[var(--primary-dark)] hover:to-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/30 transition-all duration-400 transform hover:scale-[1.02] active:scale-[0.98] shadow-xl hover:shadow-2xl btn-modern relative overflow-hidden group w-full sm:w-auto"
          type="submit"
        >
          <span class="relative z-10 flex items-center justify-center gap-3">
            <i
              class="fas fa-save group-hover:scale-110 transition-transform duration-300"
            ></i>
            <span class="font-['Poppins'] tracking-wide">Save Student</span>
          </span>
        </button>
        <button
          class="bg-gradient-to-r from-gray-500 to-gray-600 text-white font-bold py-4 px-8 rounded-2xl hover:from-gray-600 hover:to-gray-700 focus:ring-4 focus:ring-gray-500/30 transition-all duration-400 transform hover:scale-[1.02] active:scale-[0.98] shadow-xl hover:shadow-2xl btn-modern relative overflow-hidden group w-full sm:w-auto"
          type="reset"
        >
          <span class="relative z-10 flex items-center justify-center gap-3">
            <i
              class="fas fa-undo group-hover:rotate-180 transition-transform duration-300"
            ></i>
            <span class="font-['Poppins'] tracking-wide">Reset Form</span>
          </span>
        </button>
      </div>
    </form>
  </section>
</section>

{% endblock%}

<style>
  /* Enhanced form field styling */
  .form-field input,
  .form-field select,
  .form-field textarea {
    @apply w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/20 outline-none transition-all duration-300 bg-white/90 text-gray-800 font-medium placeholder-gray-400 shadow-inner hover:border-gray-300;
  }

  .form-field input:focus,
  .form-field select:focus,
  .form-field textarea:focus {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(122, 178, 211, 0.15);
  }

  .form-field textarea {
    min-height: 120px;
    resize: vertical;
  }

  /* Loading animation for form */
  .form-field {
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.6s ease-out forwards;
  }

  .form-field:nth-child(1) {
    animation-delay: 0.1s;
  }
  .form-field:nth-child(2) {
    animation-delay: 0.2s;
  }
  .form-field:nth-child(3) {
    animation-delay: 0.3s;
  }
  .form-field:nth-child(4) {
    animation-delay: 0.4s;
  }
  .form-field:nth-child(5) {
    animation-delay: 0.5s;
  }
  .form-field:nth-child(6) {
    animation-delay: 0.6s;
  }

  @keyframes slideInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>

{% block scripts%}
<script src="{% static 'js/form_style.js' %}"></script>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Add form validation feedback
    const form = document.querySelector("form");
    const submitBtn = form.querySelector('button[type="submit"]');

    form.addEventListener("submit", function (e) {
      submitBtn.innerHTML = `
        <span class="relative z-10 flex items-center justify-center gap-3">
          <i class="fas fa-spinner fa-spin"></i>
          <span class="font-['Poppins'] tracking-wide">Saving...</span>
        </span>
      `;
      submitBtn.disabled = true;
    });

    // Enhanced field interactions
    const fields = document.querySelectorAll(
      ".form-field input, .form-field select, .form-field textarea"
    );
    fields.forEach((field) => {
      field.addEventListener("focus", function () {
        this.parentElement.parentElement.classList.add("field-focused");
      });

      field.addEventListener("blur", function () {
        this.parentElement.parentElement.classList.remove("field-focused");
      });
    });
  });
</script>
{% endblock %}
