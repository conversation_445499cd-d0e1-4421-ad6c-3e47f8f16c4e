{% load static %}
<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Bad Gateway - {{request.tenant.name|default:"Tiny Feet Academy"}}
    </title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
    />

    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        --primary-color: #7ab2d3;
        --primary-dark: #5a9bd4;
        --gradient-primary: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--primary-dark) 100%
        );
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .font-display {
        font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .floating {
        animation: float 6s ease-in-out infinite;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        33% {
          transform: translateY(-15px) rotate(2deg);
        }
        66% {
          transform: translateY(-5px) rotate(-2deg);
        }
      }

      .pulse-gateway {
        animation: pulse-gateway 4s ease-in-out infinite;
      }

      @keyframes pulse-gateway {
        0%,
        100% {
          box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        }
        50% {
          box-shadow: 0 0 40px rgba(239, 68, 68, 0.6);
        }
      }

      .slide-in {
        animation: slideIn 0.8s ease-out forwards;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .broken-connection {
        animation: broken-connection 3s ease-in-out infinite;
      }

      @keyframes broken-connection {
        0%,
        100% {
          transform: translateX(0px) rotate(0deg);
          opacity: 1;
        }
        25% {
          transform: translateX(-5px) rotate(-2deg);
          opacity: 0.8;
        }
        75% {
          transform: translateX(5px) rotate(2deg);
          opacity: 0.6;
        }
      }
    </style>
  </head>
  <body
    class="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-pink-50 flex flex-col items-center justify-center p-4"
  >
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- Broken connection lines -->
      <div
        class="absolute top-1/4 left-1/4 w-32 h-1 bg-red-200 opacity-30 broken-connection"
      ></div>
      <div
        class="absolute top-1/2 right-1/3 w-24 h-1 bg-red-300 opacity-40 broken-connection"
        style="animation-delay: -1s"
      ></div>
      <div
        class="absolute bottom-1/3 left-1/2 w-40 h-1 bg-red-200 opacity-25 broken-connection"
        style="animation-delay: -2s"
      ></div>

      <!-- Floating elements -->
      <div
        class="absolute -top-40 -right-40 w-80 h-80 bg-red-100 opacity-20 rounded-full floating"
      ></div>
      <div
        class="absolute -bottom-40 -left-40 w-96 h-96 bg-orange-100 opacity-15 rounded-full floating"
        style="animation-delay: -3s"
      ></div>
      <div
        class="absolute top-1/2 left-1/4 w-32 h-32 bg-pink-100 opacity-25 rounded-full floating"
        style="animation-delay: -1s"
      ></div>
    </div>

    <!-- Error Content -->
    <div class="relative z-10 max-w-2xl mx-auto text-center slide-in">
      <!-- Error Icon -->
      <div class="mb-8">
        <div
          class="w-32 h-32 mx-auto bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center shadow-2xl pulse-gateway relative overflow-hidden"
        >
          <!-- Gateway effect -->
          <div
            class="absolute inset-0 bg-gradient-to-br from-red-400/20 to-transparent rounded-full"
          ></div>
          <div class="relative z-10">
            <i
              class="fas fa-exclamation-triangle text-white text-4xl broken-connection"
            ></i>
          </div>
          <!-- Connection lines -->
          <div
            class="absolute top-1/2 left-0 w-8 h-0.5 bg-white opacity-60 broken-connection"
          ></div>
          <div
            class="absolute top-1/2 right-0 w-8 h-0.5 bg-white opacity-60 broken-connection"
            style="animation-delay: -1s"
          ></div>
        </div>
      </div>

      <!-- Error Code -->
      <div class="mb-6">
        <h1
          class="font-display font-bold text-8xl md:text-9xl text-gray-800 mb-2"
        >
          502
        </h1>
        <div
          class="w-24 h-1 bg-gradient-to-r from-red-500 to-orange-600 rounded-full mx-auto"
        ></div>
      </div>

      <!-- Error Message -->
      <div class="mb-8">
        <h2
          class="font-display font-bold text-2xl md:text-3xl text-gray-800 mb-4"
        >
          Gateway Having a Bad Day! 🌉
        </h2>
        <p class="text-gray-600 text-lg leading-relaxed max-w-lg mx-auto mb-4">
          Looks like our gateway is having communication issues - kind of like
          when you're trying to explain a complex concept to a student, but
          there's a language barrier. The servers are playing telephone, and
          something got lost in translation!
        </p>
        <div
          class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-xl max-w-md mx-auto"
        >
          <p class="text-red-800 text-sm font-medium">
            <i class="fas fa-network-wired mr-2"></i>
            <strong>Network Lesson:</strong> HTTP 502 errors occur when a server
            receives an invalid response from another server. It's like when you
            ask a student to relay a message, but they accidentally turn
            "homework due tomorrow" into "no homework ever"! 📞
          </p>
        </div>
      </div>

      <!-- Gateway Status -->
      <div
        class="mb-8 p-6 bg-gradient-to-r from-red-100 to-orange-100 rounded-2xl border border-red-200 shadow-lg"
      >
        <h3
          class="font-semibold text-red-800 mb-3 flex items-center justify-center gap-2"
        >
          <i class="fas fa-satellite-dish text-red-600"></i>
          Gateway Status Report
        </h3>
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
          <div
            class="flex items-center justify-center gap-2 bg-white/50 rounded-lg px-3 py-2"
          >
            <i class="fas fa-server text-red-500"></i>
            <span class="text-red-700 font-medium">Server: Confused</span>
          </div>
          <div
            class="flex items-center justify-center gap-2 bg-white/50 rounded-lg px-3 py-2"
          >
            <i class="fas fa-wifi text-orange-500"></i>
            <span class="text-orange-700 font-medium">Connection: Wonky</span>
          </div>
          <div
            class="flex items-center justify-center gap-2 bg-white/50 rounded-lg px-3 py-2"
          >
            <i class="fas fa-tools text-blue-500"></i>
            <span class="text-blue-700 font-medium">Fix: In Progress</span>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div
        class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8"
      >
        <a
          href="/"
          class="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] text-white font-semibold py-3 px-8 rounded-xl hover:from-[var(--primary-dark)] hover:to-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-home mr-2"></i>
          Go Home
        </a>
        <button
          onclick="history.back()"
          class="bg-white text-gray-700 font-semibold py-3 px-8 rounded-xl border-2 border-gray-200 hover:border-red-500 hover:text-red-600 focus:ring-4 focus:ring-red-500/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-arrow-left mr-2"></i>
          Go Back
        </button>
        <button
          onclick="location.reload()"
          class="bg-gradient-to-r from-red-500 to-orange-600 text-white font-semibold py-3 px-8 rounded-xl hover:from-orange-600 hover:to-red-500 focus:ring-4 focus:ring-red-500/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <i class="fas fa-sync-alt mr-2"></i>
          Try Again
        </button>
      </div>

      <!-- Help Text -->
      <div
        class="p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-red-200/50 shadow-lg"
      >
        <h3
          class="font-semibold text-gray-800 mb-3 flex items-center justify-center gap-2"
        >
          <i class="fas fa-question-circle text-red-500"></i>
          What's Happening?
        </h3>
        <p class="text-sm text-gray-600 leading-relaxed mb-3">
          Our servers are having a miscommunication issue - like when the
          principal's announcement gets garbled over the intercom. The good
          news? Our tech team is already working on getting everyone back on the
          same page. Usually, these issues resolve themselves faster than a
          student can say "the dog ate my homework."
        </p>
        <div
          class="flex items-center justify-center gap-4 text-xs text-gray-500"
        >
          <span class="flex items-center gap-1">
            <i class="fas fa-clock"></i>
            ETA: A few minutes
          </span>
          <span class="flex items-center gap-1">
            <i class="fas fa-wrench"></i>
            Status: Being Fixed
          </span>
          <span class="flex items-center gap-1">
            <i class="fas fa-coffee"></i>
            Admin: On It
          </span>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="mt-8 text-center">
      <p class="text-sm text-gray-500 flex items-center justify-center gap-2">
        <i class="fas fa-graduation-cap text-[var(--primary-color)]"></i>
        Tiny Feet Academy MIS
      </p>
    </footer>

    <!-- Floating network icons -->
    <div class="fixed bottom-10 right-10 text-3xl opacity-20 broken-connection">
      🌐
    </div>
    <div
      class="fixed top-20 left-10 text-2xl opacity-15 broken-connection"
      style="animation-delay: -2s"
    >
      📡
    </div>
    <div
      class="fixed top-1/2 right-20 text-xl opacity-10 broken-connection"
      style="animation-delay: -4s"
    >
      🔗
    </div>
  </body>
</html>
