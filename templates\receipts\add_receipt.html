{% extends 'base.html' %} {% load static %} {% block content%}
<section
  class="flex flex-col py-8 gap-10 justify-center items-center w-full max-w-2xl mx-auto"
>
  <!-- Header Section -->
  <div class="card-modern p-8 w-full text-center">
    <div class="flex items-center justify-center gap-4 mb-6">
      <div
        class="w-16 h-16 bg-gradient-to-br from-[var(--primary-color)] to-[var(--primary-dark)] rounded-3xl flex items-center justify-center shadow-xl"
      >
        <i class="fas fa-plus text-white text-2xl"></i>
      </div>
      <div class="flex flex-col">
        <h2 class="text-2xl font-bold text-gray-800 font-display">
          Add Payment
        </h2>
        <div
          class="w-16 h-1 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] rounded-full mx-auto"
        ></div>
      </div>
    </div>
    <div
      class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200/50"
    >
      <div class="flex items-center justify-center gap-3 mb-2">
        <div
          class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center"
        >
          <i class="fas fa-user text-white text-sm"></i>
        </div>
        <span
          class="text-sm font-semibold text-gray-600 uppercase tracking-wider"
          >Student</span
        >
      </div>
      <h1 class="text-3xl font-bold text-[var(--primary-color)] font-display">
        {{student.name}}
      </h1>
    </div>
  </div>

  <!-- Form Section -->
  <div class="card-modern p-8 w-full">
    <form action="" class="flex flex-col gap-6 w-full" method="post">
      {% csrf_token %}

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {% for field in form %}
        <div
          class="flex flex-col w-full space-y-3 {% if forloop.last %}md:col-span-2{% endif %}"
        >
          <label
            class="flex items-center gap-2 font-bold text-gray-700 text-sm"
            for="{{field.auto_id}}"
          >
            <div
              class="w-4 h-4 bg-gradient-to-br from-[var(--primary-color)] to-[var(--primary-dark)] rounded-lg flex items-center justify-center"
            >
              <i class="fas fa-circle text-white text-xs"></i>
            </div>
            {{field.label}}
          </label>
          <div class="relative form-field">
            {{field}}
            <div
              class="absolute inset-0 rounded-xl bg-gradient-to-r from-[var(--primary-color)]/5 to-[var(--primary-dark)]/5 opacity-0 transition-opacity duration-300 pointer-events-none focus-within:opacity-100"
            ></div>
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Submit Button -->
      <div class="flex justify-center pt-6 border-t border-gray-200/50">
        <button
          type="submit"
          class="bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] text-white font-bold py-4 px-12 rounded-2xl hover:from-[var(--primary-dark)] hover:to-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/30 transition-all duration-400 transform hover:scale-[1.02] active:scale-[0.98] shadow-xl hover:shadow-2xl btn-modern relative overflow-hidden group"
        >
          <span class="relative z-10 flex items-center justify-center gap-3">
            <i
              class="fas fa-plus group-hover:rotate-90 transition-transform duration-300"
            ></i>
            <span class="font-['Poppins'] tracking-wide">Add Payment</span>
          </span>
        </button>
      </div>
    </form>
  </div>
</section>

<style>
  /* Custom form field styling */
  .form-field input,
  .form-field select,
  .form-field textarea {
    @apply w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-[var(--primary-color)] focus:ring-4 focus:ring-[var(--primary-color)]/20 outline-none transition-all duration-300 bg-white/90 text-gray-800 font-medium placeholder-gray-400 shadow-inner hover:border-gray-300;
  }

  .form-field input:focus,
  .form-field select:focus,
  .form-field textarea:focus {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(122, 178, 211, 0.15);
  }
</style>

{% endblock%}
