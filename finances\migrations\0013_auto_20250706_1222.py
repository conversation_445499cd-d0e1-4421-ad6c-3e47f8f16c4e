# Generated by Django 5.1.2 on 2025-07-06 10:22
# Remove deprecated models from Django's migration state

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('finances', '0012_remove_expense_category_remove_expense_term_and_more'),
    ]

    operations = [
        # State-only operations to remove deprecated models from Django's migration state
        # These models were already dropped from the database in previous migrations
        # We use state_operations to update Django's understanding without touching the database

        migrations.SeparateDatabaseAndState(
            state_operations=[
                migrations.DeleteModel(name='Income'),
                migrations.DeleteModel(name='IncomeTotal'),
                migrations.DeleteModel(name='Expense'),
                migrations.DeleteModel(name='ExpenseTotal'),
                migrations.DeleteModel(name='Expenditure'),
                migrations.DeleteModel(name='ExpenseCategory'),
            ],
            database_operations=[
                # No database operations needed - tables already dropped
            ],
        ),
    ]
