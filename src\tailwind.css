@tailwind base;
@tailwind components;
@tailwind utilities;

.link-underlined {
  @apply font-semibold underline underline-offset-2 hover:text-[#7AB2D3] transition-all duration-300 ease-out;
}

.text-primary {
  @apply text-[#7AB2D3];
}

.bg-primary {
  @apply bg-[#7AB2D3];
}

.border-primary {
  @apply border-[#7AB2D3];
}

.bg-success {
  @apply bg-[#1cc88a];
}

.text-success {
  @apply text-[#1cc88a];
}

.text-info {
  @apply text-[#36b9cc];
}

.text-danger {
  @apply text-[#e74a3b];
}

.bg-secondary {
  @apply bg-[#858796];
}

.text-secondary {
  @apply text-[#858796];
}

.django-checkbox {
  @apply flex flex-row w-full;
}

.django-label {
  @apply h-full w-full cursor-pointer bg-gray-100 rounded-md hover:bg-gray-300 duration-500 transition-all ease-in-out py-3 flex gap-4 px-2;
}

.checkbox-parent {
  @apply grid grid-cols-1 lg:grid-cols-3 w-full gap-4;
}

.field {
  @apply w-full;
}

.django-form {
  @apply w-full px-4 py-2 rounded-md outline-none bg-gray-100 text-gray-700 border border-transparent focus:border-[#7AB2D3] focus:outline-none focus:ring-0 transition-all duration-200 ease-in-out;
}

.django-file {
  @apply block justify-center items-center py-2 px-4 bg-gray-100 w-full text-slate-400 file:mr-4 file:py-2 file:px-4 file:border-2 file:border-transparent file:text-sm file:font-semibold file:bg-[#333] file:text-white file:transition-all file:duration-200;
}

.django-file:hover {
  @apply file:bg-transparent file:border-[#7AB2D3] file:text-[#7AB2D3] file:transition-all file:duration-200;
}

.tf-page-container {
  @apply flex flex-col justify-center items-center gap-4 px-8 py-6 bg-[#efefef] w-full;
}

.canvas {
  @apply flex flex-col justify-center items-center bg-gray-100 rounded shadow-md mt-20 py-6 px-12 gap-8 w-10/12;
}

/* Optional: use Tailwind or raw CSS */
.hover-buffer {
  @apply absolute top-full left-0 w-full py-2;

}

.nav-active {
  @apply text-[#7AB2D3] font-semibold;
}


.date-picker {
  @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:border-[#7AB2D3] block w-full ps-10 p-2.5 rounded-sm;
}

.download-btn {
  @apply py-1 px-6 bg-[#7AB2D3] text-white text-sm font-semibold border-2 border-transparent hover:text-[#333] hover:bg-transparent hover:border-[#7AB2D3] transition-colors duration-200 rounded-sm;
}