from datetime import datetime
from decimal import Decimal
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Q

from openpyxl import Workbook


from finances.book_keeping import JournalLine, Ledger
from finances.fee_management.models import FeeAccount
from finances.reports.forms import DatePicker, GetMothnlyReportForm
from finances.reports.utils import get_expenditure_data, get_month_number
from students.utils.date_utils import get_months\



@login_required(login_url="accounts:login")
def fee_reports(request):
    form = GetMothnlyReportForm(request.POST or None)
    months_data = dict(get_months())
    if request.method == 'POST' and form.is_valid():
        seleceted_month = int(form.cleaned_data['month_field'])
        _, month = get_months()[seleceted_month-1]
        month = month.lower()
        return redirect('finances:monthly_reports', month)

    context = {
        "form": form,
        "months": months_data,
    }
    return render(request, 'reports/fee_reports.html', context)


@login_required(login_url="accounts:login")
def monthly_reports(request, month):
    months_data = dict(get_months())
    month_number = get_month_number(month, months_data)
    revenue_journal_lines = JournalLine.objects.filter(
        account__ledger_type='Revenue',
        journal_entry__date__month=month_number,
    ).values(
        'account__name'
    ).annotate(total_amount=Sum('amount'))

    revenue_total = sum(line['total_amount'] for line in revenue_journal_lines)

    expenses_journal_lines = JournalLine.objects.filter(
        account__ledger_type='Expense',
        journal_entry__date__month=month_number,
    ).values(
        'account__name'
    ).annotate(total_amount=Sum('amount'))
    expense_total = sum(line['total_amount']
                        for line in expenses_journal_lines)

    context = {
        "revenue_ledgers": revenue_journal_lines,
        "revenue_total": revenue_total,
        "expense_ledgers": expenses_journal_lines,
        "expense_total": expense_total,
        "month": month.capitalize(),
    }
    return render(request, 'reports/monthly_reports.html', context)


@login_required(login_url="accounts:login")
def fee_monthly_report(request, month):
    months_data = dict(get_months())
    month_number = get_month_number(month, months_data)

    receipts = FeeAccount.objects.filter(
        Q(term__is_active=True) &
        (
            Q(billing_cycle="Termly") |
            Q(billing_cycle="Monthly", month__month=month_number)
        )
    ).order_by('student__name')

    accounts_paid = receipts.filter(is_paid=True).count()
    accounts_pending = receipts.filter(is_paid=False).count()
    total_amount = sum(receipt.amount for receipt in receipts)

    if f'export' in request.GET:
        return export_fee_month_to_excel(request, receipts, month)

    context = {
        "receipts": receipts,
        "month": month.capitalize(),
        "accounts_paid": accounts_paid,
        "accounts_pending": accounts_pending,
        "total_amount": total_amount,
    }
    return render(request, 'reports/fee_monthly_report.html', context)

@login_required(login_url="accounts:login")
def export_fee_month_to_excel(request, receipts, month):
    wb = Workbook()
    ws = wb.active
    ws.title = f"Fee History Report for {month}"

    headers = ["Student ID", "Student Name",
               "Fee Category", "Total Due", "Amounnt Paid", "Balance", "is_paid"]
    ws.append(headers)

    for receipt in receipts:
        ws.append([
            receipt.student.student_id,
            receipt.student.name,
            receipt.category.name,
            receipt.total_due,
            receipt.amount,
            receipt.current_balance,
            receipt.is_paid,
        ])

    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename="{month}_Fee_History.xlsx"'

    wb.save(response)

    return response


@login_required(login_url="accounts:login")
def fee_term_report(request):
    from students.models import Term
    term = Term.objects.get_active()
    student_data = FeeAccount.objects.filter(term=term)
    accounts_paid = student_data.filter(is_paid=True).count()
    accounts_pending = student_data.filter(is_paid=False).count()
    total_amount = sum(receipt.amount for receipt in student_data)
    
    

    if "export" in request.GET:
        return export_fee_term_to_excel(request, student_data, term)

    context = {
        "receipts": student_data,
        "term": term,
        "accounts_paid": accounts_paid,
        "accounts_pending": accounts_pending,
        "total_amount": total_amount,
    }

    return render(request, 'reports/fee_term_report.html', context)


@login_required(login_url="accounts:login")
def export_fee_term_to_excel(request, receipts, term):
    wb = Workbook()
    ws = wb.active
    ws.title = f"Fee History Report for {term}"

    headers = ["Student ID", "Student Name",
               "Fee Category", "Month", "Total Due", "Amounnt Paid", "Balance", "is_paid"]
    ws.append(headers)

    for receipt in receipts:
        month = receipt.month.strftime("%B") if receipt.month else "Termly"
        ws.append([
            receipt.student.student_id,
            receipt.student.name,
            receipt.category.name,
            month,
            receipt.total_due,
            receipt.amount,
            receipt.current_balance,
            receipt.is_paid,
        ])

    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename="{term}_payment.xlsx"'

    wb.save(response)

    return response


@login_required(login_url="accounts:login")
def expenditure_statement(request):
    form = DatePicker(request.POST or None)
    start_date = None
    end_date = None

    if request.method == 'POST' and form.is_valid():
        start_date = form.cleaned_data['start_date']
        end_date = form.cleaned_data['end_date']

    from students.models import Term
    term = Term.objects.get_active()

    expenses, outflow = get_expenditure_data(start_date, end_date, term)
    total_expense = sum((line.calculate_total()
                        for line in outflow), Decimal())

    context = {
        "expenses": expenses,
        "outflow": outflow,
        "total_expense": total_expense,
        "start_date": start_date,
        "end_date": end_date,
        "form": form
    }

    return render(request, 'reports/expenditure_statement.html', context)


@login_required(login_url="accounts:login")
def generate_expenditure_statement(request, start_date=None, end_date=None):
    if isinstance(start_date, str):
        start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
    if isinstance(end_date, str):
        end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

    from students.models import Term

    term = Term.objects.get_active()

    expenses, outflow = get_expenditure_data(start_date, end_date, term)
    total_expense = sum((line.calculate_total()
                        for line in outflow), Decimal())

    context = {
        "outflow": outflow,
        "expenses": expenses,
        "start_date": start_date,
        "end_date": end_date,
        "total_expense": total_expense,
        "term": term
    }

    return render(request, 'reports/download_expenditure_statement.html', context)


@login_required(login_url="accounts:login")
def income_statement(request):
    revenue = Ledger.objects.filter(ledger_type="Revenue").order_by('name')
    expenses = Ledger.objects.filter(ledger_type="Expense").order_by('name')

    total_revenue = sum(revenue.total_amount for revenue in revenue)
    total_expense = sum(expense.total_amount for expense in expenses)

    net_profit = total_revenue - total_expense

    context = {
        "revenue": revenue,
        "expenses": expenses,
        "total_revenue": total_revenue,
        "total_expense": total_expense,
        "net_profit": net_profit
    }
    return render(request, 'reports/income_statement.html', context)


@login_required(login_url="accounts:login")
def generate_income_statement(request):
    revenue = Ledger.objects.filter(ledger_type="Revenue").order_by('name')
    expenses = Ledger.objects.filter(ledger_type="Expense").order_by('name')

    total_revenue = sum(revenue.total_amount for revenue in revenue)
    total_expense = sum(expense.total_amount for expense in expenses)

    net_profit = total_revenue - total_expense

    from students.models import Term
    term = Term.objects.get_active()

    context = {
        "revenue": revenue,
        "expenses": expenses,
        "total_revenue": total_revenue,
        "total_expense": total_expense,
        "net_profit": net_profit,
        "term": term
    }
    return render(request, 'reports/download_income_statement.html', context)


@login_required(login_url="accounts:login")
def cash_flow_statement(request):
    pass
