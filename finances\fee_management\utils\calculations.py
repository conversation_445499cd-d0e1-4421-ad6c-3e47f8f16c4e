def sync_income(instance, month):
    """
    DEPRECATED: Income model has been removed.
    Income tracking is now handled through the modern Budget/Ledger system.

    This function is kept for backward compatibility but does nothing.
    Use the Budget/BudgetLine system for income tracking instead.
    """
    # from finances.income.models import Income  # REMOVED - deprecated model

    # Income model has been replaced with Budget/BudgetLine system
    # No action needed - income is now tracked through budget lines
    pass
