import traceback
from django.shortcuts import redirect, render
from django.db.models import Count, Q
from django.contrib.auth.decorators import login_required
from django.http import Http404, HttpResponse
from django.core.paginator import Paginator

from academics.models import Activity, ActivityType, StudentActivityParticipation, Subject, Assessment, AssessmentType, Enrollment

from students.models import Student, Level, Term
from students.utils import get_levels_student_count
from accounts.models import TeacherAssignment
from academics.utils.report_card import create_report_card_pdf


# Create your views here.


@login_required(login_url="accounts:login")
def dashboard(request):
    subject_count = Subject.objects.all().aggregate(count=Count('id'))['count']
    level_count = Level.objects.all().aggregate(count=Count('id'))['count']
    student_count = Student.objects.filter(
        is_active=True).aggregate(count=Count('id'))['count']
    context = {
        "subject_count": subject_count,
        "student_count": student_count,
        "level_count": level_count,
    }
    return render(request, 'academics/dashboard.html', context)


@login_required(login_url="accounts:login")
def view_grades(request):
    return render(request, 'academics/grades/view_grades.html')


# ?enter grades view cluster
@login_required(login_url="accounts:login")
def enter_grades(request,  level_slug, type_slug, subject_slug):
    subject = Subject.objects.get(slug=subject_slug)
    students = Student.objects.filter(
        is_active=True, level__slug=level_slug).order_by('name')
    assess_type = AssessmentType.objects.get(slug=type_slug)

    if request.method == "POST":

        max_marks = int(request.POST['max_marks'])
        for key, value in request.POST.items():
            if key == "csrfmiddlewaretoken" or key == "max_marks":
                continue

            if value:
                value = int(value)
                student = Student.objects.get(id=key)
                enroll = Enrollment.objects.get(
                    student=student, subject=subject,
                    term__is_active=True
                )

                assessment, _ = Assessment.objects.get_or_create(
                    enrollment=enroll, assessment_type=assess_type
                )
                assessment.score = value
                assessment.save()

        return redirect('academics:enter_grades', level_slug, type_slug, subject_slug)

    context = {
        'students': students,
        'level_slug': level_slug,
        'type_slug': type_slug,
        'subject': subject,
    }
    return render(request, 'academics/grades/enter_grades.html', context)


@login_required(login_url="accounts:login")
def assessment_type(request, level_slug):
    assessment_types = AssessmentType.objects.all().order_by('sequence')
    context = {
        'assessment_types': assessment_types,
        'level_slug': level_slug,
    }
    return render(request, 'academics/grades/enter/assessment_type.html', context)


@login_required(login_url="accounts:login")
def enter_by_levels(request):
    # If user is staff/admin, show all levels
    if request.user.is_staff or request.user.is_superuser:
        levels = Level.objects.all()
    else:
        # For teachers, only show levels they are assigned to
        teacher_assignments = TeacherAssignment.objects.filter(
            teacher=request.user,
            term__is_active=True
        ).select_related('class_assigned')

        # Get unique levels from assignments
        level_ids = teacher_assignments.values_list('class_assigned_id', flat=True).distinct()
        levels = Level.objects.filter(id__in=level_ids)

    context = {
        'levels': levels,
    }
    return render(request, 'academics/grades/enter/by_levels.html', context)


@login_required(login_url="accounts:login")
def enter_by_subjects(request, level_slug, type_slug):
    level = Level.objects.get(slug=level_slug)

    # If user is staff/admin, show all subjects for this level
    if request.user.is_staff or request.user.is_superuser:
        subjects = Subject.objects.filter(levels=level)
    else:
        # For teachers, only show subjects they are assigned to teach for this specific level
        teacher_assignments = TeacherAssignment.objects.filter(
            teacher=request.user,
            class_assigned=level,
            term__is_active=True
        ).select_related('subject')

        # Get subjects from assignments that are also taught in this level
        subject_ids = teacher_assignments.values_list('subject_id', flat=True)
        subjects = Subject.objects.filter(id__in=subject_ids, levels=level)

    context = {
        'subjects': subjects,
        'level_slug': level_slug,
        'type_slug': type_slug,
    }
    return render(request, 'academics/grades/enter/by_subjects.html', context)


# ?View Grades cluster
@login_required(login_url="accounts:login")
def view_grades(request):
    from students.models import Term

    levels = Level.objects.all()
    assessment_types = AssessmentType.objects.all().order_by('sequence')
    terms = Term.objects.all()
    subjects = Subject.objects.all()

    # Get filter values from query params
    level_id = request.GET.get('level')
    type_id = request.GET.get('assessment_type')
    term_id = request.GET.get('term')

    student_data = None  # Will hold the final structured data

    if level_id and type_id and term_id:
        level = Level.objects.get(id=level_id)
        assessment_type = AssessmentType.objects.get(id=type_id)

        # Filter subjects based on the selected level
        if request.user.is_staff or request.user.is_superuser:
            subjects = Subject.objects.filter(levels=level)
        else:
            # For teachers, filter by both level and their assignments
            teacher_assignments = TeacherAssignment.objects.filter(
                teacher=request.user,
                class_assigned=level,
                term__is_active=True
            ).select_related('subject')

            subject_ids = teacher_assignments.values_list('subject_id', flat=True)
            subjects = Subject.objects.filter(id__in=subject_ids, levels=level)

        enrollments = Enrollment.objects.filter(
            term__is_active=True,
            student__level=level
        ).select_related('student', 'subject')

        student_data = {}

        # Pre-fill each student's grade row
        for enrollment in enrollments:
            student = enrollment.student
            subject = enrollment.subject

            assessment = Assessment.objects.filter(
                enrollment=enrollment,
                assessment_type=assessment_type
            ).first()

            if student.id not in student_data:
                student_data[student.id] = {
                    'student': student,
                    'grades_list': [
                        {'subject_id': subj.id, 'score': "-"}
                        for subj in subjects
                    ]
                }

            # Find the right subject slot and update the score
            for grade in student_data[student.id]['grades_list']:
                if grade['subject_id'] == subject.id:
                    grade['score'] = assessment.score if assessment else "-"
                    break

    context = {
        "levels": levels,
        "assessment_types": assessment_types,
        "terms": terms,
        "subjects": subjects,
        "student_data": student_data,
        "selected_level": int(level_id) if level_id else None,
        "selected_assessment_type": int(type_id) if type_id else None,
        "selected_term": int(term_id) if term_id else None,
    }

    return render(request, "academics/grades/view_grades.html", context)


# ?students view cluster
@login_required(login_url="accounts:login")
def students(request):
    """
    Comprehensive student search and listing view for academics
    """
    # Get search query and filters from request
    search_query = request.GET.get('q', '').strip()
    level_filter = request.GET.get('level', '')
    gender_filter = request.GET.get('gender', '')

    # Base queryset with optimized select_related
    students = Student.objects.filter(
        is_active=True
    ).select_related('level')

    # Apply search filters
    if search_query:
        students = students.filter(
            Q(name__icontains=search_query) |
            Q(student_id__icontains=search_query) |
            Q(level__level_name__icontains=search_query)
        )

    # Apply level filter
    if level_filter:
        students = students.filter(level__level_name=level_filter)

    # Apply gender filter
    if gender_filter:
        students = students.filter(gender=gender_filter)

    # Order results
    students = students.order_by('name')

    # Add pagination
    paginator = Paginator(students, 25)  # Show 25 students per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options for dropdowns
    levels = Level.objects.all().values_list('level_name', flat=True).distinct()
    genders = Student.objects.filter(is_active=True).values_list(
        'gender', flat=True).distinct()

    context = {
        'students': page_obj,
        'search_query': search_query,
        'level_filter': level_filter,
        'gender_filter': gender_filter,
        'total_count': students.count(),
        'page_obj': page_obj,
        'levels': levels,
        'genders': genders,
    }
    return render(request, 'academics/students.html', context)


@login_required(login_url="accounts:login")
def student_details(request, slug):
    student = Student.objects.get(slug=slug)
    enrollments = Enrollment.objects.filter(student=student)
    assessments = Assessment.objects.filter(enrollment__in=enrollments)
    teachers = TeacherAssignment.objects.filter(
        class_assigned=student.level, term__is_active=True)

    context = {
        'student': student,
        'enrollments': enrollments,
        'assessments': assessments,
        'teachers': teachers,
    }
    return render(request, 'academics/student_details.html', context)


@login_required(login_url="accounts:login")
def levels(request):
    levels = Level.objects.all()
    user = request.user
    user_levels = user.subject_assignments.all(
    ).values_list('class_assigned', flat=True)
    if user.is_staff == False:
        levels = Level.objects.filter(id__in=user_levels)
    levels_student_count = get_levels_student_count(levels)

    context = {
        'levels': levels,
        'level_student_count': levels_student_count
    }
    return render(request, 'academics/levels.html', context)


@login_required(login_url="accounts:login")
def level_details(request, slug):
    levels = Level.objects.all()
    level = Level.objects.get(slug=slug)
    students = Student.objects.filter(
        level=level, is_active=True).order_by('name')

    context = {
        "levels": levels,
        'level': level,
        'students': students,

    }
    return render(request, 'academics/level_details.html', context)


@login_required(login_url="accounts:login")
def activities(request):
    """
    View to handle activities related to subjects and students.
    """
    # Filter activities based on user permissions
    if request.user.is_staff or request.user.is_superuser:
        # Staff/admin can see all activities
        activities = Activity.objects.filter(term__is_active=True).order_by('-date')
    else:
        # Teachers can only see activities for classes they are assigned to
        teacher_assignments = TeacherAssignment.objects.filter(
            teacher=request.user,
            term__is_active=True
        ).select_related('class_assigned')

        # Get unique class IDs from assignments
        class_ids = teacher_assignments.values_list('class_assigned_id', flat=True).distinct()

        # Filter activities by assigned classes
        activities = Activity.objects.filter(
            term__is_active=True,
            class_assigned_id__in=class_ids
        ).order_by('-date')

    context = {
        'activities': activities,
    }

    return render(request, 'academics/activities/activities.html', context)


@login_required(login_url="accounts:login")
def enter_results(request, id):
    """
    View to display details of a specific activity.
    """
    activity = Activity.objects.get(id=id)
    participants = Student.objects.filter(
        level=activity.class_assigned, is_active=True).order_by('name')
    if request.method == "POST":
        for key, value in request.POST.items():
            if key == "csrfmiddlewaretoken":
                continue

            status = "attended"
            if value == "":
                score = 0
                status = "Absent"

            student = Student.objects.get(id=key)
            score = int(value) if value else 0
            results = StudentActivityParticipation.objects.create(
                student=student,
                activity=activity,
                score=score,
                participation_status=status,
            )
            results.save()

        return redirect('academics:activities')

    context = {
        'activity': activity,
        'participants': participants,
    }

    return render(request, 'academics/activities/enter_results.html', context)


@login_required(login_url="accounts:login")
def view_activity_results(request):
    """
    View to display results of a specific activity.
    """
    from students.models import Term
    level_id = None
    type_id = None
    term_id = None

    activities = ActivityType.objects.all()
    levels = Level.objects.all()
    terms = Term.objects.all()
    results = []

    if request.method == "POST":
        level_id = request.POST.get('level')
        type_id = request.POST.get('activity_type')
        term_id = request.POST.get('term')

    if level_id and type_id and term_id:
        activity = Activity.objects.get(
            activity_type__id=type_id, class_assigned__id=level_id, term__id=term_id
        )
        results = StudentActivityParticipation.objects.filter(
            activity=activity
        ).order_by('-score')

    context = {
        'activities': activities,
        'levels': levels,
        'terms': terms,
        'results': results,

    }

    return render(request, 'academics/activities/view_results.html', context)


@login_required(login_url="accounts:login")
def generate_report_card(request, student_id, term_id=None):
    """Generate academic report card PDF for a student"""
    try:
        student = Student.objects.get(student_id=student_id)

        # Use provided term or active term
        if term_id:
            term = Term.objects.get(id=term_id)
        else:
            term = Term.objects.filter(is_active=True).first()

        if not term:
            return HttpResponse("No active term found", status=400)

        # Generate the PDF
        pdf_buffer = create_report_card_pdf(student, term)

        # Create response
        response = HttpResponse(pdf_buffer, content_type='application/pdf')
        filename = f"Report_Card_{student.name.replace(' ', '_')}_{term.term_name.replace(' ', '_')}.pdf"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    except Student.DoesNotExist:
        raise Http404("Student not found")
    except Term.DoesNotExist:
        raise Http404("Term not found")
    except Exception as e:
        error_message = str(e)
        tb = traceback.format_exc()  # full traceback as string
        context = {
            'error_message': error_message,
            'traceback': tb,
            'request_id': request.META.get('HTTP_X_REQUEST_ID', 'N/A'),
        }
        return render(request, 'errors/500.html', context, status=500)


@login_required(login_url="accounts:login")
def report_card_selection(request, student_id):
    """Display report card selection page for a student"""
    try:
        student = Student.objects.get(student_id=student_id)
        terms = Term.objects.all().order_by('-is_active', '-id')

        context = {
            'student': student,
            'terms': terms
        }
        return render(request, 'academics/report_card.html', context)

    except Student.DoesNotExist:
        return HttpResponse("Student not found", status=404)
