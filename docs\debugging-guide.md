# 🐛 Debugging and Bug Fixing Guide

A comprehensive guide for debugging and fixing common issues in the Tiny Feet MIS Django application.

## 📋 Table of Contents

- [General Debugging Approach](#general-debugging-approach)
- [Common Issues and Solutions](#common-issues-and-solutions)
- [Debugging Tools and Techniques](#debugging-tools-and-techniques)
- [Error Categories](#error-categories)
- [Step-by-Step Debugging Process](#step-by-step-debugging-process)
- [Prevention Strategies](#prevention-strategies)

## 🔍 General Debugging Approach

### 1. **Identify the Problem**
- Read error messages carefully
- Check the terminal/console logs
- Identify when the issue started occurring
- Determine if it's reproducible

### 2. **Gather Information**
- Check Django server logs
- Review browser developer tools (Network, Console tabs)
- Check database state if relevant
- Review recent code changes

### 3. **Isolate the Issue**
- Narrow down to specific views, models, or components
- Test with minimal reproduction steps
- Check if issue occurs in different environments

### 4. **Fix and Verify**
- Implement the fix
- Test thoroughly
- Verify no new issues are introduced
- Document the solution

## 🚨 Common Issues and Solutions

### **Redirect Loops**

**Symptoms:**
- <PERSON><PERSON><PERSON> shows "too many redirects" error
- Infinite 302 responses in terminal logs
- Page never loads

**Common Causes:**
```python
# Missing permissions for protected views
'students:home': ['view_students']  # User lacks view_students permission

# Incorrect redirect logic in middleware
if role_level >= 5:
    return redirect('students:home')  # But user can't access this view
```

**Debugging Steps:**
1. Check terminal logs for repeated redirect patterns
2. Verify user permissions: `user.get_all_permissions()`
3. Check RBAC middleware protected views
4. Verify role assignments

**Solution:**
```python
# Update role permissions
admin_role = Role.objects.get(name='administrator')
admin_role.permissions.add(Permission.objects.get(name='view_students'))
```

### **Template Syntax Errors**

**Symptoms:**
- `TemplateSyntaxError` in logs
- Pages fail to render
- Invalid block tag errors

**Common Causes:**
```django
<!-- Conditional extends (NOT ALLOWED) -->
{% if condition %}
  {% extends 'base.html' %}
{% else %}
  {% extends 'other.html' %}
{% endif %}

<!-- Missing endif/endfor -->
{% for item in items %}
  {{ item }}
<!-- Missing {% endfor %} -->
```

**Solution:**
```django
<!-- Use separate templates instead -->
{% extends 'base.html' %}

<!-- Or use template inheritance properly -->
{% extends base_template %}
```

### **Database Field Errors**

**Symptoms:**
- `FieldError: Cannot resolve keyword 'field_name'`
- Migration issues
- Model relationship errors

**Common Causes:**
```python
# Referencing removed/renamed fields
subjects.order_by('level')  # 'level' field was removed

# Incorrect field relationships
Subject.objects.filter(level='Primary')  # Changed to many-to-many
```

**Solution:**
```python
# Update queries to use new field structure
subjects.order_by('name')  # Use existing field
Subject.objects.filter(levels__level_name='Primary')  # Use new relationship
```

### **Permission Denied Issues**

**Symptoms:**
- 403 Forbidden errors
- Users can't access expected pages
- Redirect to login despite being logged in

**Debugging:**
```python
# Check user permissions in Django shell
user = User.objects.get(username='username')
print(user.get_all_permissions())
print(user.get_highest_role_level())
print(user.has_permission('specific_permission'))
```

### **Import Errors**

**Symptoms:**
- `ModuleNotFoundError`
- `ImportError`
- Circular import issues

**Common Causes:**
```python
# Missing imports
from accounts.models import TeacherAssignment  # Add missing import

# Circular imports
# models.py imports views.py, views.py imports models.py
```

**Solution:**
```python
# Use string references for foreign keys
class Model(models.Model):
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE)

# Import inside functions to avoid circular imports
def my_view(request):
    from .models import MyModel
    # Use MyModel here
```

## 🛠️ Debugging Tools and Techniques

### **1. Django Debug Toolbar**
```python
# settings/dev.py
if DEBUG:
    INSTALLED_APPS += ['debug_toolbar']
    MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
```

### **2. Logging**
```python
# Add to views for debugging
import logging
logger = logging.getLogger(__name__)

def my_view(request):
    logger.debug(f"User: {request.user}, Permissions: {request.user.get_all_permissions()}")
    logger.info(f"Accessing view with data: {request.GET}")
```

### **3. Django Shell**
```bash
python manage.py shell

# Test models and queries
>>> from accounts.models import User, Role
>>> user = User.objects.get(username='admin')
>>> user.get_active_roles()
>>> user.has_permission('view_students')
```

### **4. Browser Developer Tools**
- **Network Tab**: Check HTTP status codes, request/response headers
- **Console Tab**: Look for JavaScript errors
- **Application Tab**: Check cookies, session data

### **5. Database Inspection**
```bash
python manage.py dbshell

# Check data directly
SELECT * FROM accounts_userrole WHERE user_id = 1;
SELECT * FROM accounts_role WHERE name = 'administrator';
```

## 📊 Error Categories

### **Server Errors (5xx)**
- **500 Internal Server Error**: Code bugs, missing imports, database issues
- **502 Bad Gateway**: Server configuration issues
- **503 Service Unavailable**: Database connection issues

### **Client Errors (4xx)**
- **400 Bad Request**: Invalid form data, malformed requests
- **403 Forbidden**: Permission issues, RBAC problems
- **404 Not Found**: Missing URLs, incorrect routing
- **429 Too Many Requests**: Rate limiting issues

### **Redirect Issues (3xx)**
- **302 Found**: Normal redirects, check for loops
- **301 Moved Permanently**: URL structure changes

## 🔄 Step-by-Step Debugging Process

### **1. Read the Error Message**
```
FieldError: Cannot resolve keyword 'level' into field. 
Choices are: abbrv, activities, category, enrollment, events, id, levels, name, slug
```
- **What**: Field 'level' doesn't exist
- **Where**: Subject model
- **Why**: Field was renamed/removed
- **Fix**: Use 'levels' instead

### **2. Check Recent Changes**
```bash
git log --oneline -10  # Recent commits
git diff HEAD~1        # Changes in last commit
```

### **3. Verify Database State**
```python
# Check model fields
from academics.models import Subject
print([f.name for f in Subject._meta.fields])
print([f.name for f in Subject._meta.many_to_many])
```

### **4. Test Incrementally**
```python
# Start with simple queries
Subject.objects.all()  # Does basic query work?
Subject.objects.filter(name='Math')  # Does filtering work?
Subject.objects.filter(levels__level_name='Primary')  # Does relationship work?
```

### **5. Check Dependencies**
- Are migrations applied? `python manage.py showmigrations`
- Are required packages installed? `pip list`
- Is the database accessible? `python manage.py dbshell`

## 🛡️ Prevention Strategies

### **1. Code Review Checklist**
- [ ] All imports are present and correct
- [ ] Database queries use existing fields
- [ ] Permissions are properly assigned
- [ ] Templates use correct syntax
- [ ] Error handling is implemented

### **2. Testing Strategy**
```python
# Test critical paths
def test_admin_login_redirect(self):
    """Test that admin users can log in without redirect loops"""
    admin_user = User.objects.create_user('admin', password='test')
    admin_user.assign_role('administrator')
    
    self.client.login(username='admin', password='test')
    response = self.client.get('/')
    
    self.assertEqual(response.status_code, 200)
    self.assertNotEqual(response.status_code, 302)  # No redirect loop
```

### **3. Migration Safety**
```python
# Always create data migrations for field changes
from django.db import migrations

def migrate_subject_levels(apps, schema_editor):
    Subject = apps.get_model('academics', 'Subject')
    Level = apps.get_model('students', 'Level')
    
    for subject in Subject.objects.all():
        if subject.level == 'Primary':
            primary_levels = Level.objects.filter(education_stage='Primary')
            subject.levels.add(*primary_levels)

class Migration(migrations.Migration):
    operations = [
        migrations.RunPython(migrate_subject_levels),
    ]
```

### **4. Environment Consistency**
```python
# Use environment-specific settings
# settings/base.py - common settings
# settings/dev.py - development settings
# settings/prod.py - production settings

# Always test in environment similar to production
```

### **5. Monitoring and Logging**
```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': 'django_errors.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'ERROR',
            'propagate': True,
        },
    },
}
```

## 🎯 Quick Reference Commands

```bash
# Check server logs
tail -f django_errors.log

# Database inspection
python manage.py dbshell

# Check migrations
python manage.py showmigrations
python manage.py migrate --plan

# Test specific functionality
python manage.py shell
python manage.py test app.tests.test_specific_feature

# Reset database (development only)
python manage.py flush
python manage.py migrate

# Check user permissions
python manage.py shell -c "
from accounts.models import User
user = User.objects.get(username='admin')
print('Permissions:', user.get_all_permissions())
print('Roles:', [r.role.name for r in user.get_active_roles()])
"
```

## 🔧 Specific Bug Patterns

### **RBAC and Permission Issues**

**Pattern**: User can't access pages they should be able to access
```python
# Debug user permissions
def debug_user_permissions(username):
    user = User.objects.get(username=username)
    print(f"User: {user.username}")
    print(f"Is Staff: {user.is_staff}")
    print(f"Is Superuser: {user.is_superuser}")
    print(f"Role Level: {user.get_highest_role_level()}")

    print("\nActive Roles:")
    for user_role in user.get_active_roles():
        print(f"  - {user_role.role.display_name} (Level {user_role.role.level})")

    print("\nPermissions:")
    for perm in user.get_all_permissions():
        print(f"  - {perm}")

    print(f"\nCan access students:home: {user.has_permission('view_students')}")
```

### **Template and Static File Issues**

**Pattern**: Templates not loading or static files missing
```python
# Check template loading
TEMPLATES = [{
    'BACKEND': 'django.template.backends.django.DjangoTemplates',
    'DIRS': [BASE_DIR / 'templates'],  # Ensure this path exists
    'APP_DIRS': True,
    'OPTIONS': {
        'context_processors': [
            'django.template.context_processors.debug',
            'django.template.context_processors.request',
            'django.contrib.auth.context_processors.auth',
            'django.contrib.messages.context_processors.messages',
        ],
    },
}]

# Check static files
STATIC_URL = '/static/'
STATICFILES_DIRS = [BASE_DIR / 'static']
STATIC_ROOT = BASE_DIR / 'staticfiles'
```

### **Database Migration Issues**

**Pattern**: Model changes not reflected in database
```bash
# Check migration status
python manage.py showmigrations

# Create new migration
python manage.py makemigrations app_name

# Apply migrations
python manage.py migrate

# Check for unapplied migrations
python manage.py migrate --plan

# Rollback migration (if needed)
python manage.py migrate app_name 0004  # Rollback to migration 0004
```

### **Form Validation Errors**

**Pattern**: Forms not saving or validation failing
```python
# Debug form in view
def my_view(request):
    if request.method == 'POST':
        form = MyForm(request.POST)
        if form.is_valid():
            form.save()
        else:
            print("Form errors:", form.errors)
            print("Non-field errors:", form.non_field_errors())
            for field, errors in form.errors.items():
                print(f"Field {field}: {errors}")

    return render(request, 'template.html', {'form': form})
```

## 🚨 Emergency Debugging Checklist

When the site is completely broken:

### **1. Check Server Status**
```bash
# Is the server running?
ps aux | grep python
netstat -tlnp | grep :8000

# Check for syntax errors
python manage.py check
python manage.py check --deploy
```

### **2. Database Connectivity**
```bash
# Can we connect to database?
python manage.py dbshell

# Are migrations applied?
python manage.py showmigrations | grep "\[ \]"
```

### **3. Critical Settings**
```python
# Check DEBUG mode
print(settings.DEBUG)

# Check database settings
print(settings.DATABASES)

# Check installed apps
print(settings.INSTALLED_APPS)
```

### **4. Permission Reset (Last Resort)**
```python
# Reset admin permissions if completely locked out
from accounts.models import User, Role, Permission

# Get or create superuser
admin = User.objects.filter(is_superuser=True).first()
if not admin:
    admin = User.objects.create_superuser('admin', '<EMAIL>', 'password')

# Assign administrator role
admin_role = Role.objects.get(name='administrator')
admin.assign_role(admin_role)

print(f"Admin user: {admin.username}")
print(f"Can access students:home: {admin.has_permission('view_students')}")
```

## 📝 Bug Report Template

When reporting bugs, include:

```markdown
## Bug Report

**Environment:**
- Django Version:
- Python Version:
- Database:
- Browser:

**Steps to Reproduce:**
1.
2.
3.

**Expected Behavior:**


**Actual Behavior:**


**Error Messages:**
```
[Paste error messages here]
```

**Additional Context:**
- Recent changes made:
- User role/permissions:
- Browser console errors:

**Screenshots:**
[If applicable]
```

## 🔍 Advanced Debugging Techniques

### **1. Custom Management Commands**
```python
# management/commands/debug_permissions.py
from django.core.management.base import BaseCommand
from accounts.models import User

class Command(BaseCommand):
    help = 'Debug user permissions'

    def add_arguments(self, parser):
        parser.add_argument('username', type=str)

    def handle(self, *args, **options):
        username = options['username']
        user = User.objects.get(username=username)

        self.stdout.write(f"Debugging permissions for: {user.username}")
        # Add debugging logic here
```

### **2. Middleware Debugging**
```python
# middleware/debug_middleware.py
class DebugMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        print(f"Request: {request.method} {request.path}")
        print(f"User: {request.user}")

        response = self.get_response(request)

        print(f"Response: {response.status_code}")
        return response
```

### **3. Database Query Debugging**
```python
# Enable query logging in settings
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django.db.backends': {
            'level': 'DEBUG',
            'handlers': ['console'],
        },
    },
}
```

---

**Remember**: Always backup your database before making significant changes, and test fixes in a development environment first!

**Pro Tip**: Keep a debugging journal with common issues and their solutions for your specific project.
