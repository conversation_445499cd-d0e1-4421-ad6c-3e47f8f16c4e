from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from core.models import Notification, NotificationType
from django.utils import timezone
from datetime import datetime

User = get_user_model()


class Command(BaseCommand):
    help = 'Create a notification safely without admin interface'

    def add_arguments(self, parser):
        parser.add_argument('--title', type=str, required=True, help='Notification title')
        parser.add_argument('--message', type=str, required=True, help='Notification message')
        parser.add_argument('--type', type=str, default='General Info', help='Notification type name')
        parser.add_argument('--priority', type=str, default='medium', 
                          choices=['low', 'medium', 'high', 'urgent'], help='Priority level')
        parser.add_argument('--expires', type=str, help='Expiry date (YYYY-MM-DD HH:MM:SS)')
        parser.add_argument('--target-users', type=str, nargs='*', 
                          help='Usernames to target (leave empty for system-wide)')
        parser.add_argument('--inactive', action='store_true', help='Create as inactive')

    def handle(self, *args, **options):
        try:
            # Get or create notification type
            notification_type, created = NotificationType.objects.get_or_create(
                name=options['type'],
                defaults={
                    'icon': 'fas fa-info-circle',
                    'color': '#7AB2D3'
                }
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created new notification type: {notification_type.name}')
                )

            # Parse expiry date if provided
            expires_at = None
            if options['expires']:
                try:
                    expires_at = datetime.strptime(options['expires'], '%Y-%m-%d %H:%M:%S')
                    expires_at = timezone.make_aware(expires_at)
                except ValueError:
                    raise CommandError('Invalid date format. Use YYYY-MM-DD HH:MM:SS')

            # Create notification
            notification = Notification.objects.create(
                title=options['title'],
                message=options['message'],
                notification_type=notification_type,
                priority=options['priority'],
                expires_at=expires_at,
                is_active=not options['inactive']
            )

            # Add target users if specified
            if options['target_users']:
                users = User.objects.filter(username__in=options['target_users'])
                if users.count() != len(options['target_users']):
                    found_usernames = list(users.values_list('username', flat=True))
                    missing = set(options['target_users']) - set(found_usernames)
                    self.stdout.write(
                        self.style.WARNING(f'Warning: Users not found: {", ".join(missing)}')
                    )
                
                notification.target_users.set(users)
                self.stdout.write(
                    self.style.SUCCESS(f'Targeted {users.count()} users')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS('Created system-wide notification')
                )

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created notification: {notification.title} (ID: {notification.id})'
                )
            )

        except Exception as e:
            raise CommandError(f'Error creating notification: {str(e)}')
