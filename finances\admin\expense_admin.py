# Expense models removed - deprecated in favor of Outflow and Ledger system
# All expense tracking is now handled through the modern double-entry bookkeeping system:
# - Outflow model for expense transactions
# - Ledger model for account tracking
# - JournalEntry/JournalLine for double-entry bookkeeping

# from django.contrib import admin
# from finances.expenses.models import ExpenseCategory, Expense
#
# These models have been removed and replaced with:
# - finances.book_keeping.Outflow (for expense transactions)
# - finances.book_keeping.Ledger (for expense account tracking)
#
# See finances.admin.book_keeping_admin for the modern expense management interface.
