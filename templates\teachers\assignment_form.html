{% extends 'academics/base.html' %} {% load static %} {% block title %}Add
Assignment | {% endblock %} {% block content %}
<section class="w-full max-w-4xl mx-auto px-4 py-8 space-y-8">
  <!-- Breadcrumb -->
  <div class="card-modern p-6">
    <div class="flex items-center gap-3 mb-4">
      <div
        class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-chalkboard-teacher text-white text-lg"></i>
      </div>
      <div>
        <h1 class="font-display font-bold text-2xl text-gray-800">Teachers</h1>
        <div
          class="w-16 h-1 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-dark)] rounded-full"
        ></div>
      </div>
    </div>
    <nav class="flex items-center gap-2 text-sm font-medium">
      <span class="text-gray-500">Home</span>
      <i class="fa-solid fa-chevron-right text-gray-400 text-xs"></i>
      <span class="text-gray-500">Teachers</span>
      <i class="fa-solid fa-chevron-right text-gray-400 text-xs"></i>
      <span class="text-[var(--primary-color)] font-semibold"
        >Add Assignment</span
      >
    </nav>
  </div>

  <!-- Main Form Section -->
  <section class="card-modern p-8 space-y-8">
    <!-- Header -->
    <div class="flex items-center gap-4 mb-6">
      <div
        class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-plus text-white text-xl"></i>
      </div>
      <div>
        <h1 class="text-2xl font-bold text-gray-800 font-display">
          Add New Assignment
        </h1>
        <p class="text-gray-600 font-medium">Create a new teacher assignment</p>
      </div>
    </div>

    <!-- Error Messages -->
    {% if form.errors %}
    <div
      class="bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200/50 rounded-2xl p-5 flex items-center space-x-4 shadow-lg backdrop-blur-sm"
    >
      <div
        class="w-8 h-8 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-exclamation-triangle text-white text-sm"></i>
      </div>
      <div class="flex-1">
        <p class="text-red-700 font-semibold text-sm">
          Please correct the errors below
        </p>
        <p class="text-red-600 text-sm">{{ form.errors }}</p>
      </div>
    </div>
    {% endif %}
    <!-- Form -->
    <form method="post" enctype="multipart/form-data" class="space-y-6">
      {% csrf_token %}

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {% for field in form %}
        <div
          class="form-field space-y-3 {% if field.field.widget.input_type == 'textarea' %}md:col-span-2{% endif %}"
        >
          <label
            class="flex items-center gap-2 font-bold text-gray-700 text-sm"
            for="{{ field.auto_id }}"
          >
            <div
              class="w-4 h-4 bg-gradient-to-br from-[var(--primary-color)] to-[var(--primary-dark)] rounded-lg flex items-center justify-center"
            >
              <i class="fas fa-circle text-white text-xs"></i>
            </div>
            {{ field.label }} {% if field.field.required %}
            <span class="text-red-500 text-xs">*</span>
            {% endif %}
          </label>
          <div class="relative">
            {{ field }} {% if field.errors %}
            <div class="text-red-500 text-xs mt-1 flex items-center gap-1">
              <i class="fas fa-exclamation-circle"></i>
              {{ field.errors.0 }}
            </div>
            {% endif %}
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Form Actions -->
      <div
        class="flex flex-col sm:flex-row gap-4 justify-end pt-6 border-t border-gray-200"
      >
        <button
          type="reset"
          class="bg-gray-100 text-gray-700 font-semibold py-3 px-6 rounded-xl hover:bg-gray-200 focus:ring-4 focus:ring-gray-300/30 transition-all duration-300 border border-gray-200 hover:border-gray-300"
        >
          <i class="fas fa-undo mr-2"></i>
          Reset Form
        </button>
        <button
          type="submit"
          class="bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold py-3 px-6 rounded-xl hover:from-emerald-600 hover:to-green-500 focus:ring-4 focus:ring-green-500/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group"
        >
          <i
            class="fas fa-save mr-2 group-hover:scale-110 transition-transform duration-300"
          ></i>
          Save Assignment
        </button>
      </div>
    </form>
  </section>
</section>
{% endblock %}
