from django.views.static import serve
from django.contrib import admin
from django.urls import path, include
from django.conf.urls.static import static
from django.conf import settings

from core import views


urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include("students.urls")),
    path('finances/', include("finances.urls")),
    path('academics/', include("academics.urls")),
    path('core/', include('core.urls')),
    path('', include("accounts.urls")),
    path('coffee/', views.custom_418, name="coffe"),
    path('teapot/', views.custom_418, name="teapot"),
    path('502/', views.custom_502, name="custom_502"),
    path('429/', views.custom_429, name="custom_429"),
]


if not settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL,
                          document_root=settings.STATIC_ROOT)

handler400 = "core.views.errors.custom_400"
handler403 = "core.views.errors.custom_403"
handler404 = "core.views.errors.custom_404"
handler500 = "core.views.errors.custom_500"

