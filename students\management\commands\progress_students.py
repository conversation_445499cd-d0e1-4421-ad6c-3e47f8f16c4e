"""
Django management command to progress students to the next class level
when a new academic year is created.

Usage:
    python manage.py progress_students --academic-year "2024-2025"
    python manage.py progress_students --academic-year "2024-2025" --dry-run
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from students.models import AcademicYear
from students.utils.progression import progress_students_and_setup_new_year

User = get_user_model()


class Command(BaseCommand):
    help = 'Progress students to the next class level for a new academic year'

    def add_arguments(self, parser):
        parser.add_argument(
            '--academic-year',
            type=str,
            required=True,
            help='Name of the new academic year (e.g., "2024-2025")'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )
        
        parser.add_argument(
            '--user',
            type=str,
            help='Username of the user performing the action'
        )

    def handle(self, *args, **options):
        academic_year_name = options['academic_year']
        dry_run = options['dry_run']
        username = options.get('user')
        
        # Get or create the academic year
        try:
            academic_year, created = AcademicYear.objects.get_or_create(
                name=academic_year_name,
                defaults={'is_active': False}  # Don't activate immediately
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created new academic year: {academic_year_name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Academic year already exists: {academic_year_name}')
                )
                
        except Exception as e:
            raise CommandError(f'Error creating academic year: {e}')
        
        # Get user if specified
        user = None
        if username:
            try:
                user = User.objects.get(username=username)
                self.stdout.write(f'Action will be performed by user: {username}')
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'User {username} not found. Proceeding without user.')
                )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
            self.show_progression_preview(academic_year)
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Starting student progression for {academic_year_name}...')
            )
            self.perform_progression(academic_year, user)

    def show_progression_preview(self, academic_year):
        """Show what would happen in a dry run"""
        from students.models import Student
        from students.utils.progression import get_next_level
        
        self.stdout.write('\n' + '='*60)
        self.stdout.write('STUDENT PROGRESSION PREVIEW')
        self.stdout.write('='*60)
        
        active_students = Student.objects.filter(is_active=True)
        
        if not active_students.exists():
            self.stdout.write(self.style.WARNING('No active students found.'))
            return
        
        progression_summary = {
            'total': 0,
            'will_progress': 0,
            'will_repeat': 0,
            'will_graduate': 0
        }
        
        for student in active_students:
            current_level = student.level
            next_level = get_next_level(current_level)
            
            progression_summary['total'] += 1
            
            if next_level is None:
                status = 'GRADUATE'
                next_level_name = 'Completed'
                progression_summary['will_graduate'] += 1
            else:
                # For preview, assume all students will progress
                # In actual run, this would check academic performance
                status = 'PROGRESS'
                next_level_name = next_level.level.level_name
                progression_summary['will_progress'] += 1
            
            self.stdout.write(
                f'{student.name} ({student.student_id}): '
                f'{current_level.level_name} → {next_level_name} [{status}]'
            )
        
        self.stdout.write('\n' + '-'*40)
        self.stdout.write('SUMMARY:')
        self.stdout.write(f'Total Students: {progression_summary["total"]}')
        self.stdout.write(f'Will Progress: {progression_summary["will_progress"]}')
        self.stdout.write(f'Will Repeat: {progression_summary["will_repeat"]}')
        self.stdout.write(f'Will Graduate: {progression_summary["will_graduate"]}')
        self.stdout.write('-'*40)

    def perform_progression(self, academic_year, user):
        """Perform the actual student progression"""
        try:
            results = progress_students_and_setup_new_year(academic_year, user)
            
            self.stdout.write('\n' + '='*60)
            self.stdout.write('STUDENT PROGRESSION RESULTS')
            self.stdout.write('='*60)
            
            # Display progression summary
            summary = results['summary']
            self.stdout.write(f'Academic Year: {results["academic_year"]}')
            self.stdout.write(f'Total Students: {summary["total_students"]}')
            self.stdout.write(f'Progressed: {summary["progressed"]}')
            self.stdout.write(f'Repeated: {summary["repeated"]}')
            self.stdout.write(f'Graduated: {summary["graduated"]}')
            self.stdout.write(f'Progression Errors: {summary["progression_errors"]}')
            self.stdout.write(f'Fee Accounts Created: {summary["fee_accounts_created"]}')
            self.stdout.write(f'Fee Account Errors: {summary["fee_account_errors"]}')
            
            # Display detailed results
            self.stdout.write('\n' + '-'*40)
            self.stdout.write('DETAILED RESULTS:')
            self.stdout.write('-'*40)
            
            for detail in results['progression']['details']:
                student_name = detail['student']
                student_id = detail['student_id']
                result = detail['result']
                
                status_color = self.style.SUCCESS
                if result['status'] == 'error':
                    status_color = self.style.ERROR
                elif result['status'] == 'repeat':
                    status_color = self.style.WARNING
                elif result['status'] == 'graduated':
                    status_color = self.style.HTTP_INFO
                
                self.stdout.write(
                    status_color(f'{student_name} ({student_id}): {result["message"]}')
                )
            
            # Show any errors
            if summary['progression_errors'] > 0 or summary['fee_account_errors'] > 0:
                self.stdout.write('\n' + self.style.ERROR('ERRORS OCCURRED:'))
                for detail in results['progression']['details']:
                    if detail['result']['status'] == 'error':
                        self.stdout.write(
                            self.style.ERROR(f'- {detail["result"]["message"]}')
                        )
            
            self.stdout.write('\n' + self.style.SUCCESS('Student progression completed!'))
            
        except Exception as e:
            raise CommandError(f'Error during progression: {e}')
