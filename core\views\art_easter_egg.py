"""
Art Easter Egg View
A delightful surprise for artists, creatives, and art lovers
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
import json

from core.art_quotes import get_random_art_quote, is_art_search, get_art_quote_by_category, get_art_quotes_by_author


@login_required(login_url="accounts:login")
def art_easter_egg(request):
    """
    Display a random art/creativity quote when triggered by art search terms
    """
    # Get the search term that triggered this easter egg
    search_term = request.GET.get('search', '')
    quote_data = get_random_art_quote()
    
    # Add some context about how they got here
    trigger_context = {
        'search_term': search_term,
        'message': f'Your search for "{search_term}" has painted a canvas of creative inspiration...'
    }
    
    context = {
        'quote': quote_data,
        'trigger': trigger_context,
        'page_title': 'An Artistic Moment of Inspiration'
    }
    
    return render(request, 'core/art_easter_egg.html', context)


@login_required(login_url="accounts:login")
@csrf_exempt
@require_http_methods(["POST"])
def get_new_art_quote(request):
    """
    AJAX endpoint to get a new random art quote
    """
    try:
        data = json.loads(request.body)
        category = data.get('category', None)
        author = data.get('author', None)
        
        if author:
            quotes = get_art_quotes_by_author(author)
            if quotes:
                import random
                quote_data = random.choice(quotes)
            else:
                quote_data = get_random_art_quote()
        elif category:
            quote_data = get_art_quote_by_category(category)
        else:
            quote_data = get_random_art_quote()
        
        return JsonResponse({
            'success': True,
            'quote': quote_data
        })
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def check_art_search(search_term):
    """
    Helper function to check if a search term should trigger the art easter egg
    This can be imported and used in other views
    """
    return is_art_search(search_term)
