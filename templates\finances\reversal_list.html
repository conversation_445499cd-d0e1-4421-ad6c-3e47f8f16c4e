{% extends 'base.html' %} {% load static %} {% block title %}Transaction
Reversals | {% endblock %} {% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-6 space-y-6">
  <!-- Header Section -->
  <div
    class="bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 header-animation"
  >
    <div
      class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6"
    >
      <div class="space-y-2">
        <div class="flex items-center gap-4">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-lg icon-float"
          >
            <i class="fas fa-undo text-white text-lg icon-pulse"></i>
          </div>
          <div>
            <h1
              class="font-bold text-2xl md:text-3xl text-[#2C3E50] tracking-tight title-slide-in"
            >
              Transaction Reversals
            </h1>
            <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
              View all reversed transactions and their details
            </p>
            <div
              class="w-24 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full mt-2 accent-line-grow"
            ></div>
          </div>
        </div>
      </div>

      <div class="flex flex-col sm:flex-row gap-4 stats-slide-in">
        <div class="bg-[#E2F1F9] rounded-xl p-4 text-center">
          <div class="text-2xl font-bold text-[#2C3E50]">{{ total_count }}</div>
          <div class="text-sm text-[#40657F]">Total Reversals</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Search Section -->
  <div
    class="bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-6 search-fade-in"
  >
    <form method="GET" class="flex gap-4">
      <div class="flex-1">
        <input
          type="text"
          name="search"
          value="{{ search_query }}"
          placeholder="Search by voucher, reason, or user..."
          class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200"
        />
      </div>
      <button
        type="submit"
        class="px-6 py-3 bg-[#7AB2D3] text-white rounded-xl hover:bg-[#40657F] transition-colors duration-200"
      >
        <i class="fas fa-search"></i>
      </button>
    </form>
  </div>

  <!-- Content Section -->
  <div
    class="bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 overflow-hidden"
  >
    {% if page_obj %}
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <i class="fas fa-hashtag mr-2"></i>Voucher
            </th>
            <th
              class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <i class="fas fa-calendar mr-2"></i>Date
            </th>
            <th
              class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <i class="fas fa-comment mr-2"></i>Reason
            </th>
            <th
              class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <i class="fas fa-user mr-2"></i>Reversed By
            </th>
            <th
              class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <i class="fas fa-link mr-2"></i>Original Transaction
            </th>
            <th
              class="px-6 py-4 text-left text-xs font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <i class="fas fa-cog mr-2"></i>Actions
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#E2F1F9] ">
          {% for reversal in page_obj %}
          <tr class="hover:bg-[#E2F1F9]/50 transition-colors duration-200">
            <td class="px-6 py-4">
              <div class="font-bold text-[#2C3E50]">{{ reversal.voucher }}</div>
              <div class="text-sm text-[#40657F]">
                {{ reversal.description|truncatewords:5 }}
              </div>
            </td>
            <td class="px-6 py-4">
              <div class="text-[#2C3E50]">{{ reversal.date }}</div>
              <div class="text-sm text-[#40657F]">
                {{ reversal.created_at|date:"H:i" }}
              </div>
            </td>
            <td class="px-6 py-4">
              <div class="text-[#2C3E50]">
                {{ reversal.reason|truncatewords:8 }}
              </div>
            </td>
            <td class="px-6 py-4">
              <div class="flex items-center gap-2">
                <div
                  class="w-8 h-8 bg-[#7AB2D3] rounded-full flex items-center justify-center text-white text-sm font-bold"
                >
                  {{ reversal.created_by.username|first|upper }}
                </div>
                <div>
                  <div class="text-[#2C3E50] font-medium">
                    {{ reversal.created_by.username }}
                  </div>
                  <div class="text-sm text-[#40657F]">
                    {{ reversal.created_by.get_full_name|default:"Staff" }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4">
              {% if reversal.reverses %}
              <div class="text-[#2C3E50]">{{ reversal.reverses.voucher }}</div>
              <div class="text-sm text-[#40657F]">
                {{ reversal.reverses.date }}
              </div>
              {% else %}
              <span class="text-[#B9D8EB]">N/A</span>
              {% endif %}
            </td>
            <td class="px-6 py-4">
              <div class="flex items-center gap-2">
                <a
                  href="{% url 'finances:reversal_detail' reversal.voucher %}"
                  class="text-[#7AB2D3] hover:text-[#40657F] transition-colors duration-200"
                  title="View Details"
                >
                  <i class="fas fa-eye"></i>
                </a>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="px-6 py-4 border-t border-[#E2F1F9] bg-[#F7FAFC]">
      <div class="flex items-center justify-between">
        <div class="text-sm text-[#40657F]">
          Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{
          page_obj.paginator.count }} results
        </div>
        <div class="flex gap-2">
          {% if page_obj.has_previous %}
          <a
            href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
            class="px-3 py-2 bg-[#7AB2D3] text-white rounded-lg hover:bg-[#40657F] transition-colors duration-200"
          >
            Previous
          </a>
          {% endif %} {% if page_obj.has_next %}
          <a
            href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
            class="px-3 py-2 bg-[#7AB2D3] text-white rounded-lg hover:bg-[#40657F] transition-colors duration-200"
          >
            Next
          </a>
          {% endif %}
        </div>
      </div>
    </div>
    {% endif %} {% else %}
    <div class="p-12 text-center">
      <div
        class="w-16 h-16 bg-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-4"
      >
        <i class="fas fa-undo text-[#7AB2D3] text-2xl"></i>
      </div>
      <h3 class="text-lg font-bold text-[#2C3E50] mb-2">No reversals found</h3>
      <p class="text-[#40657F] mb-6">
        No transaction reversals have been recorded yet.
      </p>
    </div>
    {% endif %}
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .stats-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: statsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .search-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: searchFadeIn 0.8s ease-out 1s forwards;
  }

  /* Table row animations */
  .reversal-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: reversalRowSlideIn 0.6s ease-out forwards;
  }

  .reversal-row:nth-child(1) {
    animation-delay: 1.2s;
  }
  .reversal-row:nth-child(2) {
    animation-delay: 1.3s;
  }
  .reversal-row:nth-child(3) {
    animation-delay: 1.4s;
  }
  .reversal-row:nth-child(4) {
    animation-delay: 1.5s;
  }
  .reversal-row:nth-child(5) {
    animation-delay: 1.6s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 6rem;
    }
  }

  @keyframes statsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes searchFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes reversalRowSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
</style>
{% endblock %}
