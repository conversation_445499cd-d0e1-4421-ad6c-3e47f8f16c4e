from io import Bytes<PERSON>
from datetime import datetime
import os
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from reportlab.lib.colors import HexColor

# Minimalistic color palette for elegant school report cards
COLORS = {
    'primary': HexColor('#1a365d'),      # Deep Navy Blue
    'secondary': He<PERSON><PERSON><PERSON><PERSON>('#2d3748'),    # Charcoal Gray
    'accent': <PERSON><PERSON><PERSON><PERSON><PERSON>('#4a5568'),       # Medium Gray
    'light_accent': Hex<PERSON>olor('#e2e8f0'),  # Light Gray
    'text': Hex<PERSON>olor('#2d3748'),         # Dark Gray
    'success': HexColor('#38a169'),      # Professional Green
    'warning': HexColor('#d69e2e'),      # Amber
    'danger': Hex<PERSON><PERSON><PERSON>('#e53e3e'),       # Professional Red
    'border': HexColor('#cbd5e0'),       # Light Border
}

# Page configuration
PAGE_SIZE = A4
MARGIN = 25 * mm
LINE_HEIGHT = 16


def create_report_card_pdf(student, term):
    """
    Create a minimalistic and elegant academic report card PDF for a student
    """
    buffer = BytesIO()

    # Create canvas for custom layout
    from reportlab.pdfgen import canvas as pdf_canvas

    # Create the PDF with custom canvas
    c = pdf_canvas.Canvas(buffer, pagesize=PAGE_SIZE)
    width, height = PAGE_SIZE

    # Add subtle watermark logo
    try:
        logo_path = os.path.join(os.path.dirname(
            __file__), '..', '..', 'assets', 'img', 'tinyfeet.jpg')
        if os.path.exists(logo_path):
            # Draw very subtle watermark in center
            c.saveState()
            c.setFillAlpha(0.03)  # Extremely subtle
            logo_size = 200
            c.drawImage(logo_path,
                        (width - logo_size) / 2,
                        (height - logo_size) / 2,
                        width=logo_size,
                        height=logo_size,
                        preserveAspectRatio=True)
            c.restoreState()
    except:
        pass  # Continue without watermark if logo not found

    # Elegant Header Design with centered logo and text
    y_position = height - 70

    # Calculate positions for centered logo and text layout
    logo_size = 50
    title_font_size = 24
    subtitle_font_size = 14

    # School title text
    c.setFont("Helvetica-Bold", title_font_size)
    title_text = "TINY FEET ACADEMY"
    title_width = c.stringWidth(title_text, "Helvetica-Bold", title_font_size)

    # Calculate total width needed for logo + spacing + title
    spacing = 15  # Space between logo and text
    total_width = logo_size + spacing + title_width

    # Calculate starting position to center the entire header
    start_x = (width - total_width) / 2

    # Calculate top alignment for logo and title
    title_y = y_position + 35

    # Draw logo with top aligned to title top
    try:
        if os.path.exists(logo_path):
            logo_x = start_x
            # Align top of logo with top of title text
            logo_y = title_y - logo_size + title_font_size
            c.drawImage(logo_path, logo_x, logo_y, width=logo_size,
                        height=logo_size, preserveAspectRatio=True)
    except:
        pass

    # School title positioned next to logo with top alignment
    c.setFillColor(COLORS['primary'])
    title_x = start_x + logo_size + spacing
    c.drawString(title_x, title_y, title_text)

    # Subtitle centered below the logo and title
    c.setFont("Helvetica", subtitle_font_size)
    c.setFillColor(COLORS['secondary'])
    subtitle_text = "Academic Report Card"
    subtitle_width = c.stringWidth(
        subtitle_text, "Helvetica", subtitle_font_size)
    subtitle_x = (width - subtitle_width) / 2
    subtitle_y = y_position + 5
    c.drawString(subtitle_x, subtitle_y, subtitle_text)

    # Clean line under header
    c.setStrokeColor(COLORS['border'])
    c.setLineWidth(1)
    c.line(MARGIN, y_position - 8, width - MARGIN, y_position - 8)

    y_position -= 80

    # Elegant Student Information Section
    # Clean section header
    c.setFont("Helvetica-Bold", 13)
    c.setFillColor(COLORS['primary'])
    c.drawString(MARGIN, y_position, "STUDENT INFORMATION")

    # Underline for section
    c.setStrokeColor(COLORS['border'])
    c.setLineWidth(0.8)
    c.line(MARGIN, y_position - 4, MARGIN + 160, y_position - 4)

    y_position -= 30

    # Student info in properly aligned two-column layout
    c.setFont("Helvetica", 10)
    c.setFillColor(COLORS['text'])

    # Define consistent column positions and label widths
    left_col = MARGIN
    left_value_col = MARGIN + 90  # Consistent alignment for left values
    right_col = width / 2 + 20
    right_value_col = width / 2 + 110  # Consistent alignment for right values
    info_y = y_position

    # Student name (prominent)
    c.setFont("Helvetica-Bold", 11)
    c.setFillColor(COLORS['primary'])
    c.drawString(left_col, info_y, "Name:")
    c.setFont("Helvetica", 11)
    c.setFillColor(COLORS['text'])
    c.drawString(left_value_col, info_y, student.name)

    c.setFont("Helvetica-Bold", 10)
    c.setFillColor(COLORS['primary'])
    c.drawString(right_col, info_y, "Student ID:")
    c.setFont("Helvetica", 10)
    c.setFillColor(COLORS['text'])
    c.drawString(right_value_col, info_y, student.student_id)

    info_y -= 22
    c.setFont("Helvetica-Bold", 10)
    c.setFillColor(COLORS['primary'])
    c.drawString(left_col, info_y, "Class:")
    c.setFont("Helvetica", 10)
    c.setFillColor(COLORS['text'])
    c.drawString(left_value_col, info_y, str(student.level))

    c.setFont("Helvetica-Bold", 10)
    c.setFillColor(COLORS['primary'])
    c.drawString(right_col, info_y, "Term:")
    c.setFont("Helvetica", 10)
    c.setFillColor(COLORS['text'])
    c.drawString(right_value_col, info_y, term.term_name)

    info_y -= 22
    c.setFont("Helvetica-Bold", 10)
    c.setFillColor(COLORS['primary'])
    c.drawString(left_col, info_y, "Gender:")
    c.setFont("Helvetica", 10)
    c.setFillColor(COLORS['text'])
    c.drawString(left_value_col, info_y, student.gender)

    c.setFont("Helvetica-Bold", 10)
    c.setFillColor(COLORS['primary'])
    c.drawString(right_col, info_y, "Academic Year:")
    c.setFont("Helvetica", 10)
    c.setFillColor(COLORS['text'])
    c.drawString(right_value_col, info_y, str(term.academic_year))

    y_position = info_y - 35

    # Elegant Academic Performance Section
    c.setFont("Helvetica-Bold", 13)
    c.setFillColor(COLORS['primary'])
    c.drawString(MARGIN, y_position, "ACADEMIC PERFORMANCE")

    # Clean underline
    c.setStrokeColor(COLORS['border'])
    c.setLineWidth(0.8)
    c.line(MARGIN, y_position - 4, MARGIN + 190, y_position - 4)

    y_position -= 35

    # Get student's enrollments and assessments
    from academics.models import Enrollment

    enrollments = Enrollment.objects.filter(
        student=student,
        term=term
    ).select_related('subject').prefetch_related('assessments__assessment_type')

    if enrollments.exists():
        # Clean table design with better spacing
        table_width = width - (2 * MARGIN)
        table_x = MARGIN

        # Table headers with proper alignment
        c.setFont("Helvetica-Bold", 11)
        c.setFillColor(COLORS['primary'])

        headers = ["Subject", "End-Term Exam", "Grade", "Remarks"]
        # Define precise column positions for better alignment
        col_positions = [
            table_x,                    # Subject - left aligned
            table_x + 200,             # End-Term Exam - left aligned
            table_x + 320,             # Grade - left aligned
            table_x + 400              # Remarks - left aligned
        ]

        header_y = y_position
        for i, header in enumerate(headers):
            if i == 1:  # Center align "End-Term Exam" header
                header_width = c.stringWidth(header, "Helvetica-Bold", 11)
                c.drawString(
                    col_positions[i] + (100 - header_width) / 2, header_y, header)
            elif i == 2:  # Center align "Grade" header
                header_width = c.stringWidth(header, "Helvetica-Bold", 11)
                c.drawString(
                    col_positions[i] + (80 - header_width) / 2, header_y, header)
            else:
                c.drawString(col_positions[i], header_y, header)

        # Clean line under headers
        c.setStrokeColor(COLORS['border'])
        c.setLineWidth(0.8)
        c.line(table_x, header_y - 6, table_x + table_width, header_y - 6)

        y_position -= 25

        # Table data with clean styling and better spacing
        c.setFont("Helvetica", 10)

        total_subjects = 0
        total_score = 0
        passed_subjects = 0
        row_count = 0

        for enrollment in enrollments:
            # Very subtle alternating row background
            if row_count % 2 == 0:
                c.saveState()
                c.setFillColor(COLORS['light_accent'])
                c.rect(table_x, y_position - 4,
                       table_width, 20, fill=1, stroke=0)
                c.restoreState()

            subject_name = enrollment.subject.name[:32]  # Allow longer names

            # Get only end-term exam score
            final_score = "-"

            final_assessment = enrollment.assessments.filter(
                assessment_type__is_final=True
            ).first()
            if final_assessment:
                final_score = str(final_assessment.score)

            # Use final exam score as the grade
            subject_total = final_assessment.score if final_assessment else 0
            subject_grade = enrollment.grade

            # Determine remarks with professional color coding
            if enrollment.has_passed:
                remarks = "Pass"
                remarks_color = COLORS['success']
                passed_subjects += 1
            else:
                remarks = "Fail"
                remarks_color = COLORS['danger']

            # Draw row data with proper alignment
            c.setFillColor(COLORS['text'])
            c.drawString(col_positions[0], y_position, subject_name)

            # Center align score and grade
            score_width = c.stringWidth(final_score, "Helvetica", 10)
            c.drawString(
                col_positions[1] + (100 - score_width) / 2, y_position, final_score)

            grade_width = c.stringWidth(subject_grade, "Helvetica", 10)
            c.drawString(
                col_positions[2] + (80 - grade_width) / 2, y_position, subject_grade)

            # Professional remarks
            c.setFillColor(remarks_color)
            c.drawString(col_positions[3], y_position, remarks)

            y_position -= 20
            total_subjects += 1
            total_score += subject_total
            row_count += 1

        # Elegant summary section with better spacing
        y_position -= 20

        # Clean line separator
        c.setStrokeColor(COLORS['border'])
        c.setLineWidth(0.8)
        c.line(table_x, y_position, table_x + table_width, y_position)

        y_position -= 20

        # Summary data with elegant styling
        c.setFont("Helvetica-Bold", 11)
        c.setFillColor(COLORS['primary'])

        average_score = total_score / total_subjects if total_subjects > 0 else 0
        from academics.maps import get_grade
        overall_grade = get_grade(average_score)

        c.drawString(col_positions[0], y_position, "OVERALL PERFORMANCE")
        c.setFont("Helvetica", 11)
        c.setFillColor(COLORS['text'])

        # Center align summary values
        avg_score_text = f"{average_score:.1f}"
        avg_score_width = c.stringWidth(avg_score_text, "Helvetica", 11)
        c.drawString(
            col_positions[1] + (100 - avg_score_width) / 2, y_position, avg_score_text)

        grade_width = c.stringWidth(overall_grade, "Helvetica", 11)
        c.drawString(col_positions[2] + (80 -
                     grade_width) / 2, y_position, overall_grade)

        c.drawString(col_positions[3], y_position,
                     f"{passed_subjects}/{total_subjects}")

        y_position -= 35

        # Clean Performance Summary
        status = "PROMOTED" if passed_subjects >= total_subjects * 0.6 else "REPEAT"
        status_color = COLORS['success'] if status == "PROMOTED" else COLORS['danger']
        pass_rate = (passed_subjects/total_subjects *
                     100) if total_subjects > 0 else 0

        # Status section with clean design
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(COLORS['primary'])
        c.drawString(MARGIN, y_position, "PROMOTION STATUS:")

        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(status_color)
        c.drawString(MARGIN + 140, y_position, status)

        # Additional metrics in clean layout
        y_position -= 22
        c.setFont("Helvetica", 10)
        c.setFillColor(COLORS['text'])
        c.drawString(MARGIN, y_position, f"Pass Rate: {pass_rate:.1f}%")
        c.drawString(MARGIN + 160, y_position,
                     f"Overall Grade: {overall_grade}")

        y_position -= 40

    else:
        # Clean no records message
        c.setFont("Helvetica", 11)
        c.setFillColor(COLORS['text'])
        no_records_text = "No academic records found for this term."
        text_width = c.stringWidth(no_records_text, "Helvetica", 11)
        c.drawString((width - text_width) / 2, y_position, no_records_text)
        y_position -= 30

    # Elegant Signature Section with better spacing
    y_position -= 60

    # Clean section header
    c.setFont("Helvetica-Bold", 13)
    c.setFillColor(COLORS['primary'])
    c.drawString(MARGIN, y_position, "SIGNATURES")

    # Underline for section
    c.setStrokeColor(COLORS['border'])
    c.setLineWidth(0.8)
    c.line(MARGIN, y_position - 4, MARGIN + 110, y_position - 4)

    y_position -= 35

    # Signature content with proper alignment
    c.setFont("Helvetica", 11)
    c.setFillColor(COLORS['text'])

    sig_y = y_position
    # Define consistent signature positions
    left_sig_label = MARGIN
    left_sig_line_start = MARGIN + 100
    left_sig_line_end = MARGIN + 250

    right_sig_label = width / 2 + 40
    right_sig_line_start = width / 2 + 120
    right_sig_line_end = width / 2 + 220

    # Class Teacher signature
    c.drawString(left_sig_label, sig_y, "Class Teacher:")
    c.setStrokeColor(COLORS['border'])
    c.setLineWidth(0.8)
    c.line(left_sig_line_start, sig_y - 3, left_sig_line_end, sig_y - 3)

    # Date
    c.drawString(right_sig_label, sig_y, "Date:")
    c.line(right_sig_line_start, sig_y - 3, right_sig_line_end, sig_y - 3)

    sig_y -= 35
    # Principal signature
    c.drawString(left_sig_label, sig_y, "Principal:")
    c.line(left_sig_line_start, sig_y - 3, left_sig_line_end, sig_y - 3)

    # School Stamp
    c.drawString(right_sig_label, sig_y, "School Stamp:")

    # Clean Footer
    y_position = 50  # Fixed position at bottom
    c.setFont("Helvetica-Oblique", 8)
    c.setFillColor(COLORS['accent'])
    timestamp = datetime.now().strftime('%B %d, %Y')
    footer_text = f"Generated on {timestamp} • Tiny Feet Academy"
    footer_width = c.stringWidth(footer_text, "Helvetica-Oblique", 8)
    c.drawString((width - footer_width) / 2, y_position, footer_text)

    # Save the PDF
    c.save()
    buffer.seek(0)
    return buffer
