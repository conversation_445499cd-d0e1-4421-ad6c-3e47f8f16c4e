# Generated manually to add performance indexes for notifications

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        # Add indexes for better query performance
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_notification_active_expires ON core_notification(is_active, expires_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_notification_active_expires;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_notification_type_priority ON core_notification(notification_type_id, priority);",
            reverse_sql="DROP INDEX IF EXISTS idx_notification_type_priority;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_notification_created_at ON core_notification(created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_notification_created_at;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_notificationread_user_notification ON core_notificationread(user_id, notification_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_notificationread_user_notification;"
        ),
    ]
