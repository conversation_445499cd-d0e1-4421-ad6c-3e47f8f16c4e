<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} | Tiny Feet MIS</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .font-display { font-family: 'Dancing Script', cursive; }
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="overflow-x-hidden">

<section class="w-full min-h-screen flex items-center justify-center px-4 py-8 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-hidden">
  <!-- Animated Background Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <!-- Musical Notes -->
    <div class="musical-note note-1">♪</div>
    <div class="musical-note note-2">♫</div>
    <div class="musical-note note-3">♪</div>
    <div class="musical-note note-4">♬</div>
    <div class="musical-note note-5">♩</div>
    <div class="musical-note note-6">♪</div>
    <div class="musical-note note-7">♫</div>
    <div class="musical-note note-8">♪</div>
    
    <!-- Sound Waves -->
    <div class="sound-wave wave-1"></div>
    <div class="sound-wave wave-2"></div>
    <div class="sound-wave wave-3"></div>
    <div class="sound-wave wave-4"></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 max-w-4xl mx-auto text-center space-y-8 main-content-fade-in">
    
    <!-- Header Section -->
    <div class="space-y-6 header-slide-in">
      <!-- Music Icon -->
      <div class="flex justify-center">
        <div class="w-24 h-24 bg-gradient-to-br from-pink-400 via-purple-400 to-indigo-600 rounded-full flex items-center justify-center shadow-2xl icon-float">
          <i class="fas fa-music text-white text-4xl icon-pulse"></i>
        </div>
      </div>
      
      <!-- Title -->
      <div class="space-y-4">
        <h1 class="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-purple-400 to-indigo-600 font-display title-glow">
          🎵 Musical Moment
        </h1>
        <p class="text-xl md:text-2xl text-purple-200 font-light subtitle-fade-in">
          {{ trigger.message }}
        </p>
        <div class="w-32 h-1 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full mx-auto accent-line-grow"></div>
      </div>
    </div>

    <!-- Quote Card -->
    <div class="quote-card bg-white/10 backdrop-blur-lg border border-white/20 rounded-3xl p-8 md:p-12 shadow-2xl quote-slide-in">
      <!-- Quote Content -->
      <div class="space-y-6" id="quote-content">
        <!-- Quote Text -->
        <blockquote class="text-2xl md:text-3xl lg:text-4xl font-light text-white leading-relaxed quote-text-fade-in">
          <i class="fas fa-quote-left text-pink-400 text-2xl mr-4 opacity-60"></i>
          <span id="quote-text">{{ quote.quote }}</span>
          <i class="fas fa-quote-right text-pink-400 text-2xl ml-4 opacity-60"></i>
        </blockquote>
        
        <!-- Author & Context -->
        <div class="space-y-3 author-info-slide-in">
          <div class="text-xl md:text-2xl font-semibold text-pink-300" id="quote-author">
            — {{ quote.author }}
          </div>
          <div class="text-lg text-purple-200 opacity-80" id="quote-context">
            <i class="fas fa-microphone mr-2"></i>{{ quote.context }}
          </div>
          <div class="flex flex-wrap gap-2 justify-center">
            <div class="inline-flex items-center gap-2 bg-pink-500/20 text-pink-200 px-4 py-2 rounded-full text-sm font-medium" id="quote-theme">
              <i class="fas fa-heart"></i>
              {{ quote.theme }}
            </div>
            <div class="inline-flex items-center gap-2 bg-purple-500/20 text-purple-200 px-4 py-2 rounded-full text-sm font-medium" id="quote-genre">
              <i class="fas fa-compact-disc"></i>
              {{ quote.genre }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center buttons-slide-in">
      <!-- New Quote Button -->
      <button
        id="new-quote-btn"
        class="group bg-gradient-to-r from-pink-500 to-purple-500 text-white font-bold py-4 px-8 rounded-2xl hover:from-pink-600 hover:to-purple-600 focus:ring-4 focus:ring-pink-500/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl"
      >
        <i class="fas fa-random mr-3 group-hover:rotate-180 transition-transform duration-500"></i>
        Another Beat
      </button>
      
      <!-- Share Quote Button -->
      <button
        id="share-quote-btn"
        class="group bg-white/10 backdrop-blur-sm text-white font-bold py-4 px-8 rounded-2xl hover:bg-white/20 focus:ring-4 focus:ring-white/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl border border-white/20"
      >
        <i class="fas fa-share mr-3 group-hover:scale-110 transition-transform duration-300"></i>
        Share the Vibe
      </button>
      
      <!-- Return Button -->
      <a
        href="javascript:history.back()"
        class="group bg-slate-700/50 backdrop-blur-sm text-white font-bold py-4 px-8 rounded-2xl hover:bg-slate-600/50 focus:ring-4 focus:ring-slate-500/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl border border-white/10"
      >
        <i class="fas fa-arrow-left mr-3 group-hover:-translate-x-1 transition-transform duration-300"></i>
        Back to Reality
      </a>
    </div>

    <!-- Easter Egg Discovery Message -->
    <div class="bg-gradient-to-r from-pink-500/10 to-purple-500/10 border border-pink-400/30 rounded-2xl p-6 discovery-message-fade-in">
      <div class="flex items-center justify-center gap-3 text-pink-200">
        <i class="fas fa-egg text-2xl text-pink-400"></i>
        <div class="text-center">
          <p class="font-semibold">🎉 Congratulations! You've discovered a musical easter egg!</p>
          <p class="text-sm opacity-80 mt-1">
            Your search for "{{ trigger.search_term }}" has unlocked this hidden feature. 
            The system appreciates music lovers who bring rhythm to learning.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  /* Musical Background Animations */
  .musical-note {
    position: absolute;
    font-size: 2rem;
    color: rgba(236, 72, 153, 0.6);
    animation: musicalFloat 8s ease-in-out infinite;
    font-weight: bold;
  }

  .note-1 {
    top: 10%;
    left: 5%;
    animation-delay: 0s;
    font-size: 1.5rem;
  }

  .note-2 {
    top: 20%;
    right: 10%;
    animation-delay: 1s;
    font-size: 2.5rem;
  }

  .note-3 {
    top: 60%;
    left: 15%;
    animation-delay: 2s;
    font-size: 1.8rem;
  }

  .note-4 {
    bottom: 30%;
    right: 20%;
    animation-delay: 3s;
    font-size: 2rem;
  }

  .note-5 {
    top: 40%;
    left: 8%;
    animation-delay: 4s;
    font-size: 1.6rem;
  }

  .note-6 {
    bottom: 15%;
    left: 25%;
    animation-delay: 5s;
    font-size: 2.2rem;
  }

  .note-7 {
    top: 70%;
    right: 5%;
    animation-delay: 6s;
    font-size: 1.7rem;
  }

  .note-8 {
    top: 35%;
    right: 30%;
    animation-delay: 7s;
    font-size: 1.9rem;
  }

  .sound-wave {
    position: absolute;
    border: 2px solid rgba(168, 85, 247, 0.3);
    border-radius: 50%;
    animation: soundPulse 4s ease-in-out infinite;
  }

  .wave-1 {
    width: 100px;
    height: 100px;
    top: 25%;
    left: 20%;
    animation-delay: 0s;
  }

  .wave-2 {
    width: 150px;
    height: 150px;
    bottom: 25%;
    right: 15%;
    animation-delay: 1s;
  }

  .wave-3 {
    width: 80px;
    height: 80px;
    top: 50%;
    right: 25%;
    animation-delay: 2s;
  }

  .wave-4 {
    width: 120px;
    height: 120px;
    bottom: 40%;
    left: 10%;
    animation-delay: 3s;
  }

  /* Content Animations */
  .main-content-fade-in {
    opacity: 0;
    animation: mainContentFadeIn 1s ease-out 0.5s forwards;
  }

  .header-slide-in {
    opacity: 0;
    transform: translateY(-50px);
    animation: headerSlideIn 1s ease-out 0.8s forwards;
  }

  .icon-float {
    animation: iconFloat 4s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 3s ease-in-out infinite;
  }

  .title-glow {
    animation: titleGlow 3s ease-in-out infinite;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 1s ease-out 1.2s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 1s ease-out 1.5s forwards;
  }

  .quote-slide-in {
    opacity: 0;
    transform: translateY(50px);
    animation: quoteSlideIn 1s ease-out 1.8s forwards;
  }

  .quote-text-fade-in {
    opacity: 0;
    animation: quoteTextFadeIn 1s ease-out 2.2s forwards;
  }

  .author-info-slide-in {
    opacity: 0;
    transform: translateX(-30px);
    animation: authorInfoSlideIn 1s ease-out 2.5s forwards;
  }

  .buttons-slide-in {
    opacity: 0;
    transform: translateY(30px);
    animation: buttonsSlideIn 1s ease-out 2.8s forwards;
  }

  .discovery-message-fade-in {
    opacity: 0;
    animation: discoveryMessageFadeIn 1s ease-out 3.2s forwards;
  }

  /* Keyframe Definitions */
  @keyframes musicalFloat {
    0%, 100% { 
      transform: translateY(0px) rotate(0deg) scale(1); 
      opacity: 0.6;
    }
    25% { 
      transform: translateY(-30px) rotate(90deg) scale(1.1); 
      opacity: 0.8;
    }
    50% { 
      transform: translateY(-15px) rotate(180deg) scale(0.9); 
      opacity: 1;
    }
    75% { 
      transform: translateY(-25px) rotate(270deg) scale(1.05); 
      opacity: 0.7;
    }
  }

  @keyframes soundPulse {
    0%, 100% { 
      transform: scale(1); 
      opacity: 0.3;
    }
    50% { 
      transform: scale(1.3); 
      opacity: 0.1;
    }
  }

  @keyframes mainContentFadeIn {
    to { opacity: 1; }
  }

  @keyframes headerSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  @keyframes titleGlow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 8rem; }
  }

  @keyframes quoteSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes quoteTextFadeIn {
    to { opacity: 1; }
  }

  @keyframes authorInfoSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes buttonsSlideIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes discoveryMessageFadeIn {
    to { opacity: 1; }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .musical-note, .sound-wave {
      display: none;
    }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const newQuoteBtn = document.getElementById('new-quote-btn');
    const shareQuoteBtn = document.getElementById('share-quote-btn');

    // New Quote functionality
    newQuoteBtn.addEventListener('click', function() {
        // Add loading state
        const originalText = this.innerHTML;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-3"></i>Loading...';
        this.disabled = true;

        // Fetch new quote
        fetch('{% url "core:get_new_music_quote" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update quote content with animation
                const quoteContent = document.getElementById('quote-content');
                quoteContent.style.opacity = '0';
                quoteContent.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    document.getElementById('quote-text').textContent = data.quote.quote;
                    document.getElementById('quote-author').textContent = '— ' + data.quote.author;
                    document.getElementById('quote-context').innerHTML = '<i class="fas fa-microphone mr-2"></i>' + data.quote.context;
                    document.getElementById('quote-theme').innerHTML = '<i class="fas fa-heart"></i> ' + data.quote.theme;
                    document.getElementById('quote-genre').innerHTML = '<i class="fas fa-compact-disc"></i> ' + data.quote.genre;

                    quoteContent.style.opacity = '1';
                    quoteContent.style.transform = 'translateY(0)';
                }, 300);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        })
        .finally(() => {
            // Restore button
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 1000);
        });
    });

    // Share Quote functionality
    shareQuoteBtn.addEventListener('click', function() {
        const quote = document.getElementById('quote-text').textContent;
        const author = document.getElementById('quote-author').textContent;
        const shareText = `"${quote}" ${author}`;

        if (navigator.share) {
            navigator.share({
                title: 'Musical Inspiration',
                text: shareText,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareText).then(() => {
                // Show feedback
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check mr-3"></i>Copied!';
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            });
        }
    });
});
</script>

</body>
</html>
