# 🔐 Authentication & Login Guide

A comprehensive guide covering the authentication system, login process, and role-based redirects in the Tiny Feet MIS.

## 📋 Table of Contents

- [Overview](#overview)
- [Login Process](#login-process)
- [Role-Based Redirects](#role-based-redirects)
- [Authentication Flow](#authentication-flow)
- [Session Management](#session-management)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

The Tiny Feet MIS uses a sophisticated authentication system that automatically redirects users to appropriate dashboards based on their roles and permissions.

### Key Features
- **Role-based login redirects**
- **Permission-based access control**
- **Secure session management**
- **Automatic logout on inactivity**
- **Password reset functionality**

## 🚪 Login Process

### Step 1: Access Login Page
- Navigate to `/login/` or click **"Login"** from any page
- If already logged in, you'll be redirected to your dashboard

### Step 2: Enter Credentials
```
Required Information:
- Username (case-sensitive)
- Password

Optional:
- Remember me (extends session)
```

### Step 3: Automatic Redirect
After successful login, you'll be automatically redirected based on your role:

| Role Level | Role Name | Redirect Destination |
|------------|-----------|---------------------|
| 6 | Super Administrator | Students Dashboard (`/`) |
| 5 | Administrator | Students Dashboard (`/`) |
| 4 | Finance Manager | Finances Dashboard (`/finances/`) |
| 3 | Academic Coordinator | Academics Dashboard (`/academics/`) |
| 2 | Teacher | Academics Dashboard (`/academics/`) |
| 1 | Student | Academics Dashboard (`/academics/`) |

## 🎯 Role-Based Redirects

### Administrator Roles (Level 5-6)
**Redirect**: Students Dashboard
**Reason**: Administrators need overview of student data and system-wide metrics
**Access**: Full system access

### Finance Manager (Level 4)
**Redirect**: Finances Dashboard
**Reason**: Primary focus on financial management and reporting
**Access**: 
- Financial data and reports
- Student information (for billing)
- Academic information (for reporting)

### Academic Staff (Level 2-3)
**Redirect**: Academics Dashboard
**Reason**: Focus on academic management and teaching
**Access**:
- Academic subjects and assessments
- Student academic records
- Grade management

### Students (Level 1)
**Redirect**: Academics Dashboard
**Reason**: Access to academic information and grades
**Access**: Limited to academic viewing

## 🔄 Authentication Flow

### 1. Initial Request
```
User accesses protected page
↓
Check if authenticated
↓
If not authenticated → Redirect to login
If authenticated → Check permissions
```

### 2. Login Process
```
User submits credentials
↓
Validate username/password
↓
If valid → Create session + Role-based redirect
If invalid → Show error message
```

### 3. Permission Check
```
User accesses page
↓
RBAC Middleware checks permissions
↓
If authorized → Allow access
If denied → Redirect to appropriate dashboard
```

### 4. Session Management
```
User activity detected
↓
Update last activity timestamp
↓
Check session expiry
↓
If expired → Force logout
If valid → Continue session
```

## 🛡️ Session Management

### Session Duration
- **Default**: 2 weeks for "Remember me"
- **Standard**: Browser session (closes when browser closes)
- **Inactivity timeout**: 24 hours of no activity

### Session Security
- **CSRF Protection**: All forms include CSRF tokens
- **Secure Cookies**: HTTPS-only in production
- **Session Rotation**: New session ID on login
- **IP Validation**: Optional IP address checking

### Logout Process
- **Manual**: Click logout button
- **Automatic**: Session expiry or inactivity
- **Security**: Immediate session invalidation

## 🔧 Advanced Authentication Features

### Password Requirements
```
Minimum Requirements:
- 8 characters minimum
- Mix of letters and numbers recommended
- Special characters allowed
- Case-sensitive

Security Features:
- Password hashing (Django's PBKDF2)
- Brute force protection
- Account lockout after failed attempts
```

### Multi-Factor Authentication (Future)
- SMS verification
- Email confirmation
- TOTP (Time-based One-Time Password)
- Backup codes

### Single Sign-On (SSO) Integration
- LDAP/Active Directory support
- OAuth2 providers (Google, Microsoft)
- SAML integration
- Custom SSO solutions

## 🚨 Troubleshooting

### Common Login Issues

#### 1. Invalid Credentials
**Symptoms**: "Invalid username or password" error
**Solutions**:
- Verify username spelling (case-sensitive)
- Check caps lock status
- Try password reset if forgotten
- Contact administrator if account locked

#### 2. Redirect Loops
**Symptoms**: Page keeps redirecting, never loads
**Solutions**:
- Clear browser cache and cookies
- Check user role and permissions
- Verify user account is active
- Contact administrator for role issues

#### 3. Access Denied After Login
**Symptoms**: Login successful but can't access pages
**Solutions**:
- Check user role assignments
- Verify required permissions
- Review RBAC configuration
- Contact administrator for permission issues

#### 4. Session Expires Too Quickly
**Symptoms**: Frequent forced logouts
**Solutions**:
- Check "Remember me" option
- Verify session settings
- Check for browser cookie issues
- Contact administrator for session configuration

### Error Messages and Solutions

#### "Your account has been disabled"
- **Cause**: Account deactivated by administrator
- **Solution**: Contact administrator to reactivate

#### "Too many failed login attempts"
- **Cause**: Account temporarily locked for security
- **Solution**: Wait 15 minutes or contact administrator

#### "Session expired, please log in again"
- **Cause**: Session timeout or security logout
- **Solution**: Log in again, check "Remember me" if needed

#### "Access denied: Insufficient permissions"
- **Cause**: User lacks required permissions for page
- **Solution**: Contact administrator for role/permission review

## 🔍 Debugging Authentication Issues

### For Administrators

#### Check User Status
```python
# In Django shell
from accounts.models import User
user = User.objects.get(username='username')
print(f"Active: {user.is_active}")
print(f"Staff: {user.is_staff}")
print(f"Role Level: {user.get_highest_role_level()}")
print(f"Permissions: {list(user.get_all_permissions())}")
```

#### Review Login Logs
- Check server logs for authentication attempts
- Monitor RBAC middleware logs
- Review failed login patterns
- Check for security alerts

#### Test User Access
1. Create test user with specific role
2. Test login and redirect behavior
3. Verify page access permissions
4. Document any issues found

### For Users

#### Browser Troubleshooting
1. **Clear Cache**: Ctrl+Shift+Delete
2. **Disable Extensions**: Try incognito/private mode
3. **Check JavaScript**: Ensure JavaScript is enabled
4. **Update Browser**: Use latest browser version

#### Network Issues
- Check internet connection
- Verify server accessibility
- Test from different network
- Contact IT support if needed

## 📊 Authentication Monitoring

### Login Analytics
- Track login frequency by user
- Monitor failed login attempts
- Analyze peak usage times
- Generate security reports

### Security Metrics
- Failed login rate
- Account lockout frequency
- Session duration patterns
- Permission denial rates

### Audit Trail
- All login/logout events logged
- Permission changes tracked
- Role assignments recorded
- Security incidents documented

## 🔐 Security Best Practices

### For Users
- Use strong, unique passwords
- Log out when finished
- Don't share credentials
- Report suspicious activity
- Keep browser updated

### For Administrators
- Regular password policy reviews
- Monitor authentication logs
- Implement account lockout policies
- Regular security audits
- User access reviews

### For Developers
- Implement proper session management
- Use HTTPS in production
- Regular security updates
- Input validation and sanitization
- Proper error handling

## 📝 Quick Reference

### Login URLs
- **Main Login**: `/login/`
- **Logout**: `/logout/`
- **Password Reset**: `/password-reset/`
- **Admin Login**: `/admin/`

### Default Redirects
```
Super Admin/Admin → /
Finance Manager → /finances/
Academic Staff → /academics/
Students → /academics/
```

### Emergency Access
- **Superuser Creation**: `python manage.py createsuperuser`
- **Password Reset**: `python manage.py changepassword username`
- **Role Assignment**: Through Django admin or management commands

---

**Remember**: Always log out when using shared computers and report any authentication issues to your system administrator immediately.
