from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.contrib.auth.decorators import login_required
from django.db.models import Q

from openpyxl import Workbook


from finances.book_keeping import Ledger
from finances.fee_management.models import FeeAccount
from finances.reports.forms import GetMothnlyReportForm
from finances.reports.utils import get_month_number
from students.utils.date_utils import get_months


@login_required(login_url="accounts:login")
def fee_reports(request):
    form = GetMothnlyReportForm(request.POST or None)
    months_data = dict(get_months())
    if request.method == 'POST' and form.is_valid():
        seleceted_month = int(form.cleaned_data['month_field'])
        _, month = get_months()[seleceted_month-1]
        month = month.lower()
        return redirect('finances:monthly_reports', month)

    context = {
        "form": form,
        "months": months_data,
    }
    return render(request, 'reports/fee_reports.html', context)


@login_required(login_url="accounts:login")
def income_statement(request):
    revenue = Ledger.objects.filter(ledger_type="Revenue").order_by('name')
    expenses = Ledger.objects.filter(ledger_type="Expense").order_by('name')

    total_revenue = sum(revenue.total_amount for revenue in revenue)
    total_expense = sum(expense.total_amount for expense in expenses)

    net_profit = total_revenue - total_expense

    context = {
        "revenue": revenue,
        "expenses": expenses,
        "total_revenue": total_revenue,
        "total_expense": total_expense,
        "net_profit": net_profit
    }
    return render(request, 'reports/income_statement.html', context)


@login_required(login_url="accounts:login")
def generate_income_statement(request):
    from students.models import Term
    revenue = Ledger.objects.filter(ledger_type="Revenue").order_by('name')
    expenses = Ledger.objects.filter(ledger_type="Expense").order_by('name')

    term = Term.objects.get_active()

    total_revenue = sum(revenue.total_amount for revenue in revenue)
    total_expense = sum(expense.total_amount for expense in expenses)

    net_profit = total_revenue - total_expense

    context = {
        "revenue": revenue,
        "expenses": expenses,
        "total_revenue": total_revenue,
        "total_expense": total_expense,
        "net_profit": net_profit,
        "term": term
    }
    return render(request, 'reports/download_income_statement.html', context)



